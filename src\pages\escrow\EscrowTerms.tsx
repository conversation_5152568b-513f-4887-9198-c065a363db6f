
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import LegalContent from '@/components/LegalContent';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Shield, Check } from 'lucide-react';

const EscrowTerms = () => {
  const navigate = useNavigate();

  return (
    <DashboardLayout pageTitle="Escrow Terms & Conditions">
      <div className="max-w-4xl mx-auto">
        <Button 
          variant="ghost" 
          className="mb-4 flex items-center text-kojaGray font-katina"
          onClick={() => navigate('/escrow')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Escrow
        </Button>
        
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center gap-4">
            <Shield className="h-8 w-8 text-kojaPrimary" />
            <CardTitle className="font-katina">Escrow Protection</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-kojaGray font-katina">
              Our escrow service provides a secure way to handle transactions between parties. The funds are held by KojaPay as a trusted third party and only released when all conditions of the sale are met.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
              <div className="flex items-start gap-2">
                <Check className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h3 className="font-medium font-katina">Secure Payments</h3>
                  <p className="text-sm text-kojaGray font-katina">Your money is held securely until you approve release</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <Check className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h3 className="font-medium font-katina">Verification</h3>
                  <p className="text-sm text-kojaGray font-katina">We verify all parties involved in the transaction</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <Check className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h3 className="font-medium font-katina">Dispute Resolution</h3>
                  <p className="text-sm text-kojaGray font-katina">Fair and efficient resolution if issues arise</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <Check className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <h3 className="font-medium font-katina">Transparent Fees</h3>
                  <p className="text-sm text-kojaGray font-katina">Clear fee structure with no hidden charges</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <LegalContent type="escrow" />
        
        <div className="flex justify-center mt-8">
          <Button 
            className="px-8 py-6 text-lg bg-kojaPrimary hover:bg-kojaPrimary/90 font-katina"
            onClick={() => navigate('/escrow')}
          >
            I Understand and Accept
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default EscrowTerms;
