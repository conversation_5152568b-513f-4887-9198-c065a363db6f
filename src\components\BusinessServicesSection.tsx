
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Send, Zap, CreditCard, Wallet, Download, Upload } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardContent } from './ui/card';
import BusinessServiceCard from './BusinessServiceCard';

const BusinessServicesSection = () => {
  const navigate = useNavigate();

  return (
    <Card className="modern-card">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg font-medium">Quick Actions</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
          <BusinessServiceCard 
            title="Send Money"
            description="Transfer funds to other accounts quickly"
            icon={Send}
            route="/transactions"
            color="blue"
          />
          
          <BusinessServiceCard 
            title="Pay Bills"
            description="Pay utilities and other bills"
            icon={Zap}
            route="/bill-payment"
          />
          
          <BusinessServiceCard 
            title="Card Services"
            description="Manage your cards and limits"
            icon={CreditCard}
            route="/card-services"
          />
          
          <BusinessServiceCard 
            title="Savings"
            description="Set up and manage your savings"
            icon={Wallet}
            route="/savings"
          />
          
          <BusinessServiceCard 
            title="Request Money"
            description="Request funds from other users"
            icon={Download}
            route="/transactions"
            color="yellow"
          />
          
          <BusinessServiceCard 
            title="Top Up"
            description="Add money to your account"
            icon={Upload}
            route="/dashboard"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default BusinessServicesSection;
