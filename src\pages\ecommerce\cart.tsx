
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import ShoppingCart from '@/components/Ecommerce/ShoppingCart';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useCart } from '@/contexts/CartContext';

const CartPage: React.FC = () => {
  const navigate = useNavigate();
  const { cart } = useCart();

  return (
    <div className="container py-8 max-w-4xl mx-auto">
      <Helmet>
        <title>Shopping Cart | KojaPay</title>
      </Helmet>
      
      {/* Back button */}
      <Button 
        variant="ghost" 
        className="mb-6" 
        onClick={() => navigate('/ecommerce')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Shop
      </Button>

      {/* Cart content */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <ShoppingCart />
      </div>

      {cart.items.length > 0 && (
        <div className="mt-8 text-center text-sm text-muted-foreground">
          <p>All transactions are secured with KojaPay Escrow protection.</p>
          <p>Need help? Contact our support <NAME_EMAIL></p>
        </div>
      )}
    </div>
  );
};

export default CartPage;
