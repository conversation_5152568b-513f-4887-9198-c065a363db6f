import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ShoppingCart, Heart, ShieldCheck, Star, ArrowLeft, Share2, Info, Truck } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Product, ecommerceService, EcommerceError } from '@/services/ecommerceService';
import EcommerceCheckout from './EcommerceCheckout';
import { Skeleton } from '@/components/ui/skeleton';
import { useCart } from '@/contexts/CartContext';

// Add mock session functionality since next-auth is not available
const useSession = () => {
  // Mock session data
  return {
    user: { id: 'user-1', name: 'Test User', email: '<EMAIL>' }
  };
};

const ProductDetail = () => {
  const { productId } = useParams<{ productId: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [quantity, setQuantity] = useState(1);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { addToCart, setIsCartOpen } = useCart();
  const { user: session } = useSession();

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        if (!productId) return;

        // Fetch product details
        const productData = await ecommerceService.getProductById(productId);
        
        if ('message' in productData) {
          // Handle error
          toast({
            title: 'Error',
            description: productData.message,
            variant: 'destructive',
          });
        } else {
          setProduct(productData as Product);
          
          // Fetch related products from the same category
          const allProducts = await ecommerceService.getProducts();
          const related = allProducts
            .filter(p => p.category === (productData as Product).category && p.id !== (productData as Product).id)
            .slice(0, 4);
          setRelatedProducts(related);
        }
      } catch (error) {
        console.error('Error fetching product:', error);
        toast({
          title: 'Error',
          description: 'Failed to load product details',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [productId, toast]);

  const handleAddToCart = () => {
    if (!product) return;
    
    addToCart(product, quantity);
    
    toast({
      title: 'Added to Cart',
      description: `${product.name} has been added to your cart`,
    });
    
    // Reset quantity after adding to cart
    setQuantity(1);
    
    // Open cart drawer
    setIsCartOpen(true);
  };

  const handleAddToWishlist = () => {
    toast({
      title: 'Added to Wishlist',
      description: `${product?.name} has been added to your wishlist`,
    });
  };

  const handleCheckoutComplete = (order: any) => {
    toast({
      title: 'Order Placed',
      description: `Your order #${order.id} has been placed successfully`,
    });
    
    // Navigate to order confirmation page
    navigate(`/account/orders/${order.id}`);
  };

  if (loading) {
    return (
      <div className="container py-8 max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Skeleton className="h-[400px] w-full rounded-lg" />
          <div className="space-y-4">
            <Skeleton className="h-10 w-3/4" />
            <Skeleton className="h-6 w-1/4" />
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-24 w-full" />
            <div className="flex gap-3">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="container py-8 max-w-6xl mx-auto text-center">
        <h2 className="text-2xl font-bold mb-4">Product Not Found</h2>
        <p className="text-muted-foreground mb-6">The product you're looking for doesn't exist or has been removed.</p>
        <Button onClick={() => navigate('/ecommerce')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Shop
        </Button>
      </div>
    );
  }

  return (
    <div className="container py-8 max-w-6xl mx-auto">
      {/* Back button */}
      <Button 
        variant="ghost" 
        className="mb-6" 
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back
      </Button>

      {/* Product details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        {/* Product image */}
        <div className="bg-white rounded-lg overflow-hidden border p-4 flex items-center justify-center">
          <img 
            src={product.image} 
            alt={product.name} 
            className="max-h-[400px] object-contain"
          />
        </div>

        {/* Product info */}
        <div className="space-y-4">
          <div>
            <h1 className="text-2xl font-bold mb-1">{product.name}</h1>
            <div className="flex items-center gap-2 mb-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    className={`h-4 w-4 ${i < Math.floor(product.rating || 0) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
                  />
                ))}
              </div>
              <span className="text-sm text-muted-foreground">{product.rating} ({Math.floor(Math.random() * 100) + 20} reviews)</span>
            </div>
            <Badge className="bg-[#fde314] text-black hover:bg-[#fde314]/90">
              {product.category}
            </Badge>
          </div>

          <div className="text-2xl font-bold">
            ₦{product.price.toLocaleString()}
          </div>

          <Separator />

          <div>
            <h3 className="font-medium mb-2">Description</h3>
            <p className="text-muted-foreground">
              {product.description || 
                `Experience the premium quality and exceptional performance of the ${product.name}. 
                This product is designed to meet your needs with its outstanding features and reliable functionality.`}
            </p>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Truck className="h-4 w-4" />
            <span>Delivery within 3-5 business days</span>
          </div>

          <div className="flex items-center gap-2 text-sm text-green-600">
            <ShieldCheck className="h-4 w-4" />
            <span>Secure transaction with KojaPay Escrow</span>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">Seller:</span>
            <span>{product.storeName}</span>
          </div>

          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">Status:</span>
            <Badge variant={(product.status === 'In Stock') ? 'default' : 'destructive'}>
              {product.status}
            </Badge>
          </div>

          <Separator />

          <div className="flex gap-3">
            <div className="flex items-center border rounded-md">
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-10 w-10 rounded-none" 
                onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
                disabled={quantity <= 1}
              >
                -
              </Button>
              <div className="w-12 text-center">{quantity}</div>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-10 w-10 rounded-none" 
                onClick={() => setQuantity(prev => Math.min(product.inventory || 10, prev + 1))}
                disabled={quantity >= (product.inventory || 10)}
              >
                +
              </Button>
            </div>

            <Button 
              className="flex-1"
              onClick={handleAddToCart}
              disabled={(product.status !== 'In Stock') || (product.inventory === 0)}
            >
              <ShoppingCart className="mr-2 h-4 w-4" />
              Add to Cart
            </Button>

            <Button 
              variant="outline" 
              size="icon"
              onClick={handleAddToWishlist}
            >
              <Heart className="h-4 w-4" />
            </Button>
          </div>

          {(product.status !== 'In Stock') && (
            <div className="text-sm text-red-500 flex items-center gap-1">
              <Info className="h-4 w-4" />
              <span>This product is currently {product.status?.toLowerCase()}</span>
            </div>
          )}

          {session && (product.status === 'In Stock') && (
            <div className="mt-2">
              <EcommerceCheckout 
                products={[product]}
                customerId={session?.id || 'guest'}
                customerName={session?.name || 'Guest User'}
                onCheckoutComplete={handleCheckoutComplete}
              >
                <Button variant="secondary" className="w-full">
                  Buy Now
                </Button>
              </EcommerceCheckout>
            </div>
          )}

          <div className="flex justify-end">
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-muted-foreground"
              onClick={() => {
                navigator.clipboard.writeText(window.location.href);
                toast({
                  title: 'Link Copied',
                  description: 'Product link copied to clipboard',
                });
              }}
            >
              <Share2 className="mr-1 h-4 w-4" />
              Share
            </Button>
          </div>
        </div>
      </div>

      {/* Product tabs */}
      <Tabs defaultValue="details" className="mb-12">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="specifications">Specifications</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
        </TabsList>
        <TabsContent value="details" className="p-4 border rounded-md mt-2">
          <h3 className="font-bold mb-2">Product Details</h3>
          <p className="text-muted-foreground">
            {product.description || `Detailed information about the ${product.name} including its features, benefits, and use cases.`}
          </p>
        </TabsContent>
        <TabsContent value="specifications" className="p-4 border rounded-md mt-2">
          <h3 className="font-bold mb-2">Technical Specifications</h3>
          <ul className="list-disc pl-5 text-muted-foreground space-y-1">
            <li>Category: {product.category}</li>
            <li>Brand: {product.storeName}</li>
            <li>Model: {product.id.toString().padStart(4, '0')}</li>
            <li>Warranty: 12 months</li>
            <li>Package Contents: 1 x {product.name}</li>
          </ul>
        </TabsContent>
        <TabsContent value="reviews" className="p-4 border rounded-md mt-2">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-bold">Customer Reviews</h3>
            <div className="flex items-center gap-1">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star 
                    key={i} 
                    className={`h-4 w-4 ${i < Math.floor(product.rating || 0) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
                  />
                ))}
              </div>
              <span className="text-sm">{product.rating} out of 5</span>
            </div>
          </div>
          <div className="text-center py-8 text-muted-foreground">
            <p>No reviews yet. Be the first to review this product.</p>
          </div>
        </TabsContent>
      </Tabs>

      {/* Related products */}
      {relatedProducts.length > 0 && (
        <div>
          <h2 className="text-xl font-bold mb-4">Related Products</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {relatedProducts.map((relatedProduct) => (
              <Card key={relatedProduct.id} className="overflow-hidden">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={relatedProduct.image} 
                    alt={relatedProduct.name} 
                    className="w-full h-full object-cover transition-transform hover:scale-105"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-medium truncate">{relatedProduct.name}</h3>
                  <div className="flex items-center justify-between mt-2">
                    <span className="font-bold">₦{relatedProduct.price.toLocaleString()}</span>
                    <Button 
                      size="sm" 
                      variant="ghost"
                      onClick={() => navigate(`/ecommerce/product/${relatedProduct.id}`)}
                    >
                      View
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductDetail;
