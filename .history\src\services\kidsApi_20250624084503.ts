
import axios from 'axios';
import { CreateChildDTO, FirstLoginDTO, SetupChildCredentialsDTO, LoginDTO, ChildTransferDTO, Child, ChildCredentials, ChildTransaction } from '@/types/kids';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL;

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errorCode?: string;
}

class ApiError extends Error {
  constructor(message: string, public errorCode?: string) {
    super(message);
    this.name = 'ApiError';
  }
}

const handleApiError = (error: unknown): never => {
  if (error instanceof ApiError) {
    throw error;
  }

  if (axios.isAxiosError(error)) {
    const errorData = error.response?.data;
    const message = errorData?.message || error.message || 'API request failed';
    const errorCode = errorData?.errorCode;
    throw new ApiError(message, errorCode);
  }

  if (error instanceof Error) {
    throw new ApiError(error.message);
  }

  throw new ApiError('An unknown error occurred');
};

/**
 * Create a new child account (Parent only)
 */
export const createChildAccount = async (token: string, childData: CreateChildDTO): Promise<ChildCredentials> => {
  try {
    const response = await axios.post<ApiResponse<ChildCredentials>>(
      `${API_BASE_URL}/api/v1/kids/create`,
      childData,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to create child account', response.data.errorCode);
    }

    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Verify first-time login credentials
 */
export const verifyFirstLogin = async (credentials: FirstLoginDTO): Promise<boolean> => {
  try {
    const response = await axios.post<ApiResponse<{ isValid: boolean }>>(
      `${API_BASE_URL}/api/v1/kids/verify-first-login`,
      credentials
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Verification failed', response.data.errorCode);
    }

    return response.data.data!.isValid;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Setup child credentials after first login
 */
export const setupChildCredentials = async (setupData: SetupChildCredentialsDTO): Promise<void> => {
  try {
    const response = await axios.post<ApiResponse<void>>(
      `${API_BASE_URL}/api/v1/kids/setup-credentials`,
      setupData
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to setup credentials', response.data.errorCode);
    }
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Regular child login
 */
export const loginChild = async (loginData: LoginDTO): Promise<{ token: string; child: Child }> => {
  try {
    const response = await axios.post<ApiResponse<{ token: string; child: Child }>>(
      `${API_BASE_URL}/api/v1/kids/login`,
      loginData
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Login failed', response.data.errorCode);
    }

    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Get child account details
 */
export const getChildAccount = async (token: string): Promise<Child> => {
  try {
    const response = await axios.get<ApiResponse<Child>>(
      `${API_BASE_URL}/api/v1/kids/account`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to fetch account details', response.data.errorCode);
    }

    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Get child's transaction history
 */
export const getChildTransactions = async (token: string): Promise<ChildTransaction[]> => {
  try {
    const response = await axios.get<ApiResponse<ChildTransaction[]>>(
      `${API_BASE_URL}/api/v1/kids/transactions`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to fetch transactions', response.data.errorCode);
    }

    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Make a transfer (Child with PIN verification)
 */
export const makeChildTransfer = async (token: string, transferData: ChildTransferDTO): Promise<void> => {
  try {
    const response = await axios.post<ApiResponse<void>>(
      `${API_BASE_URL}/api/v1/kids/transfer`,
      transferData,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Transfer failed', response.data.errorCode);
    }
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Get parent's children (Parent only)
 */
export const getParentChildren = async (token: string): Promise<Child[]> => {
  try {
    const response = await axios.get<ApiResponse<Child[]>>(
      `${API_BASE_URL}/api/v1/kids/parent/children`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to fetch children', response.data.errorCode);
    }

    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Get all transactions for parent's children (Parent only)
 */
export const getParentChildrenTransactions = async (token: string): Promise<ChildTransaction[]> => {
  try {
    const response = await axios.get<ApiResponse<ChildTransaction[]>>(
      `${API_BASE_URL}/api/v1/kids/parent/transactions`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to fetch transactions', response.data.errorCode);
    }

    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Fund child account (Parent only)
 */
export const fundChildAccount = async (token: string, childId: string, amount: number): Promise<void> => {
  try {
    const response = await axios.post<ApiResponse<void>>(
      `${API_BASE_URL}/api/v1/kids/parent/fund`,
      { childId, amount },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to fund account', response.data.errorCode);
    }
  } catch (error) {
    throw handleApiError(error);
  }
};
