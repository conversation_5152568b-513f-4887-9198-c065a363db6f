import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ArrowUp, ArrowDown } from "lucide-react";

export interface HistoryItem {
  id: number;
  date: string;
  description: string;
  points: string;
  status: 'credited' | 'debited';
}

interface RewardHistoryProps {
  history: HistoryItem[];
}

const RewardHistory: React.FC<RewardHistoryProps> = ({ history }) => {
  return (
    <Card className="modern-card">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="text-left py-3 px-4 font-medium text-kojaGray">Date</th>
                <th className="text-left py-3 px-4 font-medium text-kojaGray">Description</th>
                <th className="text-right py-3 px-4 font-medium text-kojaGray">Points</th>
                <th className="text-right py-3 px-4 font-medium text-kojaGray">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {history.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="py-3 px-4 text-sm">{item.date}</td>
                  <td className="py-3 px-4 text-sm">{item.description}</td>
                  <td className="py-3 px-4 text-sm text-right font-medium">
                    <div className="flex items-center justify-end gap-1">
                      {item.status === 'credited' ? (
                        <ArrowUp size={16} className="text-green-500" />
                      ) : (
                        <ArrowDown size={16} className="text-red-500" />
                      )}
                      {item.points}
                    </div>
                  </td>
                  <td className="py-3 px-4 text-right">
                    <Badge className={item.status === 'credited' 
                      ? 'bg-green-100 text-green-800 hover:bg-green-100' 
                      : 'bg-red-100 text-red-800 hover:bg-red-100'}>
                      {item.status === 'credited' ? 'Earned' : 'Redeemed'}
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default RewardHistory;
