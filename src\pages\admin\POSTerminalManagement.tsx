import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  CreditCard, 
  MapPin, 
  Package, 
  Settings, 
  Terminal, 
  CircleAlert,
  Check,
  Filter,
  ChevronDown,
  Map,
  Phone,
  UserCheck,
  Edit,
  Trash,
  Search,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface TerminalData {
  id: string;
  merchant: string;
  location: string;
  status: 'Active' | 'Inactive' | 'Maintenance' | 'Pending';
  lastActive: string;
}

const POSTerminalManagement = () => {
  const { toast } = useToast();
  const [terminals, setTerminals] = useState<TerminalData[]>([
    {
      id: 'T12345',
      merchant: 'KojaPay Merchant',
      location: '123 Main Street, Lagos',
      status: 'Active',
      lastActive: '2023-11-15 14:30',
    },
    {
      id: 'T67890',
      merchant: 'InovCrest Ltd',
      location: '456 Market Avenue, Abuja',
      status: 'Inactive',
      lastActive: '2023-11-10 09:00',
    },
    {
      id: 'T24680',
      merchant: 'GlobalTech Solutions',
      location: '789 Innovation Road, PH',
      status: 'Maintenance',
      lastActive: '2023-11-12 18:45',
    },
    {
      id: 'T13579',
      merchant: 'FutureForward Enterprises',
      location: '101 Tech Hub, Kano',
      status: 'Pending',
      lastActive: '2023-11-14 11:15',
    },
  ]);

  const handleViewTerminal = (id: string) => {
    toast({
      title: 'View Terminal',
      description: `Viewing terminal details for terminal ID: ${id}`,
    });
  };

  const handleEditTerminal = (id: string) => {
    toast({
      title: 'Edit Terminal',
      description: `Navigating to edit terminal with ID: ${id}`,
    });
  };

  const handleDeleteTerminal = (id: string) => {
    toast({
      title: 'Delete Terminal',
      description: `Initiating deletion process for terminal ID: ${id}`,
    });
  };

  return (
    <AdminLayout pageTitle="POS Terminal Management">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-[#1E293B]">POS Terminal Management</h1>
        <p className="text-[#64748B]">Manage and monitor all POS terminals across the network</p>
      </div>

      <Tabs defaultValue="terminals" className="space-y-4">
        <TabsList className="grid grid-cols-4 w-full max-w-2xl">
          <TabsTrigger value="terminals">Terminals</TabsTrigger>
          <TabsTrigger value="requests">Requests</TabsTrigger>
          <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Terminals Tab */}
        <TabsContent value="terminals" className="space-y-4">
          <div className="flex flex-col md:flex-row justify-between gap-4 mb-6">
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                Filter
                <ChevronDown size={14} />
              </Button>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input placeholder="Search terminals..." className="pl-9 min-w-[240px]" />
              </div>
            </div>
            <Button>Add New Terminal</Button>
          </div>

          {/* Terminal Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[
              { label: 'Active Terminals', value: '348', icon: <Terminal className="text-green-500" /> },
              { label: 'Inactive Terminals', value: '42', icon: <CircleAlert className="text-amber-500" /> },
              { label: 'Maintenance', value: '15', icon: <Settings className="text-blue-500" /> },
              { label: 'Deployment Rate', value: '92%', icon: <Check className="text-purple-500" /> },
            ].map((stat, i) => (
              <Card key={i}>
                <CardContent className="flex items-center pt-6">
                  <div className="p-2 bg-gray-100 rounded-full mr-4">
                    {stat.icon}
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">{stat.label}</p>
                    <h3 className="text-2xl font-bold">{stat.value}</h3>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Terminals Table */}
          <Card>
            <CardHeader>
              <CardTitle>POS Terminals</CardTitle>
              <CardDescription>
                Complete list of all POS terminals in the network
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Terminal ID</TableHead>
                    <TableHead>Merchant</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {terminals.map((terminal) => (
                    <TableRow key={terminal.id}>
                      <TableCell className="font-medium">{terminal.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Avatar className="h-7 w-7">
                            <div className="bg-gray-200 text-gray-700 w-full h-full flex items-center justify-center font-medium">
                              {terminal.merchant.substring(0, 1)}
                            </div>
                          </Avatar>
                          {terminal.merchant}
                        </div>
                      </TableCell>
                      <TableCell>{terminal.location}</TableCell>
                      <TableCell>
                        <Badge variant={
                          terminal.status === 'Active' ? 'success' :
                          terminal.status === 'Inactive' ? 'destructive' :
                          terminal.status === 'Maintenance' ? 'warning' : 'outline'
                        }>
                          {terminal.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{terminal.lastActive}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button variant="ghost" size="icon" onClick={() => handleViewTerminal(terminal.id)}>
                            <Eye size={16} />
                          </Button>
                          <Button variant="ghost" size="icon" onClick={() => handleEditTerminal(terminal.id)}>
                            <Edit size={16} />
                          </Button>
                          <Button variant="ghost" size="icon" className="text-red-500" onClick={() => handleDeleteTerminal(terminal.id)}>
                            <Trash size={16} />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Requests Tab */}
        <TabsContent value="requests">
          <Card>
            <CardHeader>
              <CardTitle>Pending Terminal Requests</CardTitle>
              <CardDescription>
                Approve or deny new terminal requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>No pending terminal requests at this time.</p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Tab */}
        <TabsContent value="maintenance">
          <Card>
            <CardHeader>
              <CardTitle>Terminals Under Maintenance</CardTitle>
              <CardDescription>
                List of terminals currently undergoing maintenance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>No terminals are currently under maintenance.</p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Terminal Analytics</CardTitle>
              <CardDescription>
                Insights and analytics for terminal performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Analytics data will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default POSTerminalManagement;
