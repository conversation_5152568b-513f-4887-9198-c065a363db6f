
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { CreditCard } from 'lucide-react';
import type { EnhancedVirtualCardProps } from '@/types/wallet';

const EnhancedVirtualCard: React.FC<EnhancedVirtualCardProps> = ({ name, onManageCard }) => {
  return (
    <Card className="overflow-hidden border-2 border-primary/20">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CreditCard className="h-8 w-8 text-primary" />
            <div>
              <h3 className="font-semibold text-lg">{name}</h3>
              <p className="text-sm text-muted-foreground">Virtual Card</p>
            </div>
          </div>
          <Button onClick={onManageCard} size="sm">
            Manage
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedVirtualCard;
