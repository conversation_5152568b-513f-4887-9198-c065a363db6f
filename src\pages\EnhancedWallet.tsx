
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DollarSign, ArrowUp, ArrowDown, TrendingUp, TrendingDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import UserLayout from '@/layouts/UserLayout';
import AccountSwitcher from '@/components/AccountSwitcher';
import KidsAccountManager from '@/components/KidsAccountManager';

const EnhancedWallet = () => {
  const { user, accountType } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleDeposit = () => {
    toast({
      title: "Deposit Initiated",
      description: "Redirecting to deposit page...",
    });
    setTimeout(() => {
      navigate('/deposit');
    }, 1000);
  };

  const handleWithdrawal = () => {
    toast({
      title: "Withdrawal Initiated",
      description: "Redirecting to withdrawal page...",
    });
    setTimeout(() => {
      navigate('/withdraw');
    }, 1000);
  };

  return (
    <UserLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Wallet</h1>
            <p className="text-gray-600">Manage your finances with ease</p>
          </div>
          <AccountSwitcher />
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">
                Current Balance
              </CardTitle>
              <DollarSign className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₦{user?.balance?.toLocaleString() || '0.00'}</div>
              <p className="text-sm text-gray-500">
                {accountType === 'business' ? 'Business Account' : 'Personal Account'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">
                Today's Income
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-500">+₦2,450.00</div>
              <p className="text-sm text-gray-500">
                <Badge variant="secondary" className="bg-green-100 text-green-500 border-0">
                  +12%
                </Badge>{' '}
                than yesterday
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-md hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium">
                Today's Expenses
              </CardTitle>
              <TrendingDown className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-500">-₦1,780.00</div>
              <p className="text-sm text-gray-500">
                <Badge variant="secondary" className="bg-red-100 text-red-500 border-0">
                  -8%
                </Badge>{' '}
                than yesterday
              </p>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-between">
          <Button onClick={handleDeposit} className="bg-green-500 hover:bg-green-600 text-white">
            <ArrowUp className="h-4 w-4 mr-2" />
            Deposit Funds
          </Button>
          <Button onClick={handleWithdrawal} className="bg-red-500 hover:bg-red-600 text-white">
            <ArrowDown className="h-4 w-4 mr-2" />
            Withdraw Funds
          </Button>
        </div>

        {/* Kids Accounts Section for Personal Account Users */}
        {accountType === 'personal' && (
          <div>
            <KidsAccountManager />
          </div>
        )}

        <Card className="bg-white shadow-md">
          <CardHeader className="py-4">
            <CardTitle>Recent Transactions</CardTitle>
          </CardHeader>
          <CardContent className="py-2">
            <p className="text-sm text-gray-500">No recent transactions found.</p>
          </CardContent>
        </Card>
      </div>
    </UserLayout>
  );
};

export default EnhancedWallet;
