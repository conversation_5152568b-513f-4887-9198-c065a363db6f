import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { Dialog, DialogTrigger } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { motion } from 'framer-motion';
import { 
  ArrowDown, ArrowRight, ArrowUp, ArrowUpRight, ChevronRight, 
  CreditCard, Download, History, Landmark, MoreVertical, Plus, 
  QrCode, RefreshCw, Send, Wallet as WalletIcon, Eye, EyeOff, 
  Settings, Receipt, FileText, ShoppingBag, PiggyBank 
} from "lucide-react";
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import TransferDialog from '@/components/TransferDialog';
import { useIsMobile } from '@/hooks/use-mobile';
import TransactionTable, { Transaction } from '@/components/TransactionTable';
import EnhancedVirtualCard from '@/components/EnhancedVirtualCard';
import BillPaymentDialog from '@/components/BillPaymentDialog';
import LoansDialog from '@/components/LoansDialog';
import CardsDialog from '@/components/CardsDialog';

const Wallet = () => {
  const [transferOpen, setTransferOpen] = useState(false);
  const [showCardDetails, setShowCardDetails] = useState(false);
  const [billPaymentOpen, setBillPaymentOpen] = useState(false);
  const [loansOpen, setLoansOpen] = useState(false);
  const [cardsOpen, setCardsOpen] = useState(false);
  const [showBalance, setShowBalance] = useState(true);
  const {
    user
  } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const {
    toast
  } = useToast();
  const firstName = (user as any)?.fullName?.split(' ')[0] || 'User';
  const accountNumber = (user as any)?.accountNumber || '**********';
  const accountData = {
    name: (user as any)?.fullName || "Daramola Olalekan",
    balance: `₦${(user as any)?.balance?.toLocaleString() || '120,000'}`,
    accountNumber: accountNumber,
    income: "₦120,000",
    expenses: "₦60,000"
  };
  const walletData = {
    balance: accountData.balance,
    income: accountData.income,
    expenses: accountData.expenses,
    accountNumber: accountData.accountNumber
  };
  const recentTransactions: Transaction[] = [{
    id: '1',
    recipient: 'Netflix Subscription',
    date: '2023-06-15',
    amount: 5000,
    status: 'success',
    initials: 'NF',
    image: 'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/227_Netflix_logo-512.png',
    type: 'outgoing'
  }, {
    id: '2',
    recipient: 'Transfer from Ahmed',
    date: '2023-06-14',
    amount: 25000,
    status: 'success',
    initials: 'AH',
    type: 'incoming'
  }, {
    id: '3',
    recipient: 'Shoprite Purchase',
    date: '2023-06-12',
    amount: 15300,
    status: 'success',
    initials: 'SP',
    type: 'outgoing'
  }, {
    id: '4',
    recipient: 'Salary Credit',
    date: '2023-06-01',
    amount: 180000,
    status: 'success',
    initials: 'SC',
    type: 'incoming'
  }];
  const bankAccounts = [{
    id: '1',
    bankName: 'KojaBank',
    accountNumber: accountNumber,
    isPrimary: true,
    balance: accountData.balance
  }];
  const fadeInUp = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  const handleViewAllTransactions = () => {
    navigate('/transactions');
  };

  const handleViewReceipt = (transaction: Transaction) => {
    navigate(`/transactions/${transaction.id}`);
  };

  const handleQuickActions = (action: string) => {
    switch (action) {
      case 'send':
        setTransferOpen(true);
        break;
      case 'billpayment':
        setBillPaymentOpen(true);
        break;
      case 'loans':
        setLoansOpen(true);
        break;
      case 'cards':
        setCardsOpen(true);
        break;
      default:
        break;
    }
  };

  const toggleCardDetails = () => {
    setShowCardDetails(!showCardDetails);
  };

  const toggleBalance = () => {
    setShowBalance(!showBalance);
  };

  return <DashboardLayout pageTitle="Wallet">
      <div className="py-6 space-y-8 flex flex-col min-h-[calc(100vh-4rem)] mb-6">
        <div className="flex-grow space-y-8">
          <motion.div className="glass-morph p-6 rounded-[20px]" initial="hidden" animate="visible" variants={fadeInUp}>
            <div className="flex flex-col md:flex-row md:items-center justify-between">
              <div className="space-y-2">
                <h1 className="text-2xl font-bold text-neutral-800 font-unica">Hi, {firstName}!</h1>
                <p className="text-neutral-600 font-unica">Welcome to your KojaPay Wallet</p>
              </div>
              <div className="mt-4 md:mt-0 flex space-x-3">
                <Button onClick={() => setTransferOpen(true)} className="bg-kojaPrimary hover:bg-kojaPrimary/90 font-unica">
                  <Send className="mr-2 h-4 w-4" />
                  Send Money
                </Button>
              </div>
            </div>
          </motion.div>

          <motion.div 
            variants={fadeInUp} 
            initial="hidden" 
            animate="visible" 
            transition={{ delay: 0.1 }}
          >
            <Card className="wallet-balance-card border-none shadow-xl bg-gradient-to-br from-[#1231b8] to-[#09125a] text-white rounded-[20px] overflow-hidden">
              <CardContent className="p-6">
                <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full blur-3xl -mr-32 -mt-32"></div>
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full blur-2xl -ml-24 -mb-24"></div>
                
                <div className="flex flex-col lg:flex-row lg:items-center justify-between relative z-10">
                  <div className="space-y-4 mb-6 lg:mb-0">
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-white/20 rounded-full">
                        <WalletIcon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-white font-unica">KojaPay Wallet</h3>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-white/80 text-sm font-unica">Available Balance</p>
                      <div className="flex items-center gap-2">
                        <h2 className="text-4xl font-bold text-white font-unica">
                          {showBalance ? accountData.balance : '****'}
                        </h2>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-white/80 hover:text-white hover:bg-white/10" 
                          onClick={toggleBalance}
                        >
                          {showBalance ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </Button>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <p className="text-white/80 text-sm font-unica">Account Number</p>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-white font-unica">{accountData.accountNumber}</p>
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-white/80 hover:text-white hover:bg-white/10">
                          <RefreshCw className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-2 gap-3 w-full lg:w-auto">
                    <Button variant="outline" className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-[#fde314] text-white hover:bg-white/20 hover:border-[#fde314] font-unica" onClick={() => handleQuickActions('send')}>
                      <Send className="h-5 w-5 mb-2 text-[#fde314]" />
                      <span className="text-sm">Send</span>
                    </Button>
                    
                    <Button variant="outline" className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" onClick={() => handleQuickActions('billpayment')}>
                      <Receipt className="h-5 w-5 mb-2" />
                      <span className="text-sm">Bill Payment</span>
                    </Button>
                    
                    <Button variant="outline" className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" onClick={() => handleQuickActions('loans')}>
                      <Landmark className="h-5 w-5 mb-2" />
                      <span className="text-sm">Loans</span>
                    </Button>
                    
                    <Button variant="outline" className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" onClick={() => handleQuickActions('cards')}>
                      <CreditCard className="h-5 w-5 mb-2" />
                      <span className="text-sm">Cards</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.15 }}>
            <h3 className="text-lg font-medium mb-3 sm:mb-4 font-unica text-gray-800">Virtual Card</h3>
            <Card className="wallet-balance-card glass-morph border-kojaPrimary/20 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-4 sm:p-6">
                <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                  <div className="w-full md:w-auto">
                    <EnhancedVirtualCard cardNumber="**** **** **** 7690" name={(user as any)?.fullName?.toUpperCase() || "JOHN DOE"} expiryDate="08/26" cardType="personal" variant="virtual" className="mx-auto" />
                  </div>
                  <div className="w-full md:w-auto space-y-4">
                    <div className="space-y-2">
                      <p className="text-gray-600 text-sm font-unica">Card Balance</p>
                      <h3 className="text-2xl font-bold font-unica text-gray-900">₦85,000</h3>
                      <p className="text-sm text-gray-600 font-unica">Limit: ₦200,000</p>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button variant="outline" className="flex items-center gap-2 border-kojaPrimary/20 text-kojaPrimary shadow-sm hover:shadow-md font-unica" onClick={toggleCardDetails}>
                        {showCardDetails ? <EyeOff size={16} /> : <Eye size={16} />}
                        <span>{showCardDetails ? 'Hide Details' : 'Card Details'}</span>
                      </Button>
                      <Button onClick={() => navigate('/cards')} className="flex items-center gap-2 shadow-sm hover:shadow-md font-unica bg-[#fde314] text-[#1231b8]">
                        <Settings size={16} />
                        <span>Manage Card</span>
                      </Button>
                    </div>
                    
                    {showCardDetails && <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3 border border-gray-100 shadow-sm">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 font-unica">Card Number</span>
                          <span className="text-sm font-medium text-gray-800 font-unica">4539 **** **** 7690</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 font-unica">CVV</span>
                          <span className="text-sm font-medium text-gray-800 font-unica">***</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 font-unica">Expiry Date</span>
                          <span className="text-sm font-medium text-gray-800 font-unica">08/26</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-600 font-unica">Status</span>
                          <span className="text-sm font-medium text-green-600 font-unica">Active</span>
                        </div>
                      </div>}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <motion.div className="col-span-1 lg:col-span-2 space-y-6" variants={fadeInUp} initial="hidden" animate="visible" transition={{
            delay: 0.2
          }}>
              <Card className="wallet-balance-card modern-card mb-8 bg-white text-[#000000]">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium text-[#1231B8] font-unica">Financial Summary</CardTitle>
                  <Button variant="ghost" className="text-[#1231B8] hover:text-[#1231B8]/80 hover:bg-[#1231B8]/5 flex items-center gap-1 font-unica" onClick={() => navigate('/analysis')}>
                    View Analysis
                    <ChevronRight size={16} />
                  </Button>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="bg-[#1231B8]/5 p-4 rounded-xl">
                      <div className="flex items-center gap-2">
                        <div className="bg-[#1231B8]/10 p-2 rounded-lg">
                          <ArrowUp className="text-[#1231B8]" size={18} />
                        </div>
                        <span className="text-sm font-medium text-[#1231B8] font-unica">Income</span>
                      </div>
                      <p className="mt-2 text-2xl font-semibold text-[#000000] font-unica">{walletData.income}</p>
                      <p className="text-xs text-green-600 mt-1 font-unica">+15% from last month</p>
                    </div>
                    
                    <div className="bg-[#1231B8]/5 p-4 rounded-xl">
                      <div className="flex items-center gap-2">
                        <div className="bg-[#1231B8]/10 p-2 rounded-lg">
                          <ArrowDown className="text-[#1231B8]" size={18} />
                        </div>
                        <span className="text-sm font-medium text-[#1231B8] font-unica">Expenses</span>
                      </div>
                      <p className="mt-2 text-2xl font-semibold text-[#000000] font-unica">{walletData.expenses}</p>
                      <p className="text-xs text-red-600 mt-1 font-unica">-5% from last month</p>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-[#1231B8]/5 rounded-xl">
                    <h3 className="text-sm font-medium mb-3 text-[#1231B8] font-unica">Monthly Spending</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-700 font-unica">Food & Dining</span>
                        <span className="text-xs font-medium text-[#000000] font-unica">₦25,000</span>
                      </div>
                      <div className="w-full h-2 bg-[#1231B8]/10 rounded-full overflow-hidden">
                        <div className="h-full bg-[#1231B8]/70" style={{
                        width: '40%'
                      }}></div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-700 font-unica">Entertainment</span>
                        <span className="text-xs font-medium text-[#000000] font-unica">₦15,000</span>
                      </div>
                      <div className="w-full h-2 bg-[#1231B8]/10 rounded-full overflow-hidden">
                        <div className="h-full bg-[#1231B8]/70" style={{
                        width: '25%'
                      }}></div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-700 font-unica">Transportation</span>
                        <span className="text-xs font-medium text-[#000000] font-unica">₦12,000</span>
                      </div>
                      <div className="w-full h-2 bg-[#1231B8]/10 rounded-full overflow-hidden">
                        <div className="h-full bg-[#1231B8]/70" style={{
                        width: '20%'
                      }}></div>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-700 font-unica">Shopping</span>
                        <span className="text-xs font-medium text-[#000000] font-unica">₦8,000</span>
                      </div>
                      <div className="w-full h-2 bg-[#1231B8]/10 rounded-full overflow-hidden">
                        <div className="h-full bg-[#1231B8]/70" style={{
                        width: '15%'
                      }}></div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="modern-card">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium text-[#1231B8] font-unica">Recent Transactions</CardTitle>
                  <Button variant="ghost" className="text-[#1231B8] hover:text-[#1231B8]/80 hover:bg-[#1231B8]/5 flex items-center gap-1 font-unica" onClick={handleViewAllTransactions}>
                    View all
                    <ChevronRight size={16} />
                  </Button>
                </CardHeader>
                <CardContent>
                  <TransactionTable transactions={recentTransactions} onViewReceipt={handleViewReceipt} />
                </CardContent>
              </Card>
            </motion.div>

            <motion.div className="space-y-6 mb-8" variants={fadeInUp} initial="hidden" animate="visible" transition={{
            delay: 0.3
          }}>
              <Card className="wallet-balance-card modern-card">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium text-[#1231B8] font-unica">Bank Accounts</CardTitle>
                  <Button variant="ghost" size="sm" className="text-[#1231B8] font-unica">
                    <Plus size={16} className="mr-1" />
                    Add
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  {bankAccounts.map((account, index) => <div key={index} className="p-4 border border-[#1231B8]/10 rounded-xl bg-[#1231B8]/5 relative">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-[#1231B8] font-unica">{account.bankName}</h3>
                          <p className="text-sm text-gray-600 font-unica">{account.accountNumber}</p>
                        </div>
                        {account.isPrimary && <span className="text-xs bg-[#1231B8]/20 text-[#1231B8] px-2 py-1 rounded-full font-unica">
                            Primary
                          </span>}
                      </div>
                      <p className="mt-2 text-xl font-semibold text-gray-900 font-unica">{account.balance}</p>
                    </div>)}
                  
                  {bankAccounts.length === 0 && <div className="flex flex-col items-center justify-center py-6 text-center">
                      <Landmark className="text-kojaGray mb-2" size={40} />
                      <p className="text-kojaGray font-unica">No linked accounts</p>
                      <Button variant="outline" size="sm" className="mt-2 text-[#1231B8] border-[#1231B8]/20 font-unica">
                        Link an account
                      </Button>
                    </div>}
                </CardContent>
              </Card>
              
              <Card className="wallet-balance-card border-none shadow-xl bg-gradient-to-br from-[#1231b8] to-[#09125a] text-white rounded-[20px] overflow-hidden">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium text-white font-unica">Quick Services</CardTitle>
                </CardHeader>
                <CardContent className="bg-[#173bbb]/[0.03]">
                  <div className="grid grid-cols-2 gap-3">
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 border-white/20 bg-white/10 hover:bg-white/20 text-white font-unica" 
                      onClick={() => navigate('/qr-code')}
                    >
                      <QrCode className="h-6 w-6 mb-2 text-[#FDE314]" />
                      <span className="text-sm">QR Code</span>
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 border-white/20 bg-white/10 hover:bg-white/20 text-white font-unica" 
                      onClick={() => navigate('/savings')}
                    >
                      <PiggyBank className="h-6 w-6 mb-2 text-[#FDE314]" />
                      <span className="text-sm">Savings</span>
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 border-white/20 bg-white/10 hover:bg-white/20 text-white font-unica" 
                      onClick={() => navigate('/transactions')}
                    >
                      <FileText className="h-6 w-6 mb-2 text-[#FDE314]" />
                      <span className="text-sm">Statement</span>
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      className="flex flex-col items-center justify-center h-24 border-white/20 bg-white/10 hover:bg-white/20 text-white font-unica" 
                      onClick={() => navigate('/ecommerce')}
                    >
                      <ShoppingBag className="h-6 w-6 mb-2 text-[#FDE314]" />
                      <span className="text-sm">E-commerce</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>

      <TransferDialog open={transferOpen} onOpenChange={setTransferOpen} isDrawer={isMobile} />
      
      <BillPaymentDialog open={billPaymentOpen} onOpenChange={setBillPaymentOpen} accountNumber={accountNumber} />

      <LoansDialog open={loansOpen} onOpenChange={setLoansOpen} accountNumber={accountNumber} />

      <CardsDialog open={cardsOpen} onOpenChange={setCardsOpen} userName={accountData.name} />
    </DashboardLayout>;
};

export default Wallet;

