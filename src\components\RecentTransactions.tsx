
import React from 'react';
import { Button } from '@/components/ui/button';
import TransactionItem from './TransactionItem';

const transactions = [
  {
    id: '1',
    recipient: 'Netflix Subscription',
    date: 'Jan 12, 2023',
    amount: '₦1,200',
    status: 'pending' as const,
    initials: 'NF',
    image: 'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/227_Netflix_logo-512.png'
  },
  {
    id: '2',
    recipient: 'Figma Subscription',
    date: 'Jan 12, 2023',
    amount: '₦1,200',
    status: 'success' as const,
    initials: 'FG',
    image: 'https://cdn.icon-icons.com/icons2/2699/PNG/512/figma_logo_icon_170157.png'
  },
  {
    id: '3',
    recipient: 'Sent to <PERSON>',
    date: 'Jan 12, 2023',
    amount: '₦1,200',
    status: 'success' as const,
    initials: 'AL'
  }
];

const RecentTransactions = () => {
  return (
    <div className="p-5 glass-card">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-kojaDark">Recent Transactions</h2>
        <Button variant="ghost" className="text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5">
          View all
        </Button>
      </div>
      
      <div className="grid grid-cols-3 gap-4 mb-6 text-xs font-medium text-kojaGray">
        <div>Recipient</div>
        <div className="text-center">Date</div>
        <div className="text-right">Amount</div>
      </div>
      
      <div className="space-y-1">
        {transactions.map((transaction) => (
          <TransactionItem
            key={transaction.id}
            recipient={transaction.recipient}
            date={transaction.date}
            amount={transaction.amount}
            status={transaction.status}
            initials={transaction.initials}
            image={transaction.image}
          />
        ))}
      </div>
    </div>
  );
};

export default RecentTransactions;
