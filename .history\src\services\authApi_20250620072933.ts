import { UserRole, AccountType, ActivityLog } from '../types/account';
import axios, { AxiosError } from 'axios';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL;

interface LoginParams {
  phoneNumber: string;
  pin: string;
  role: UserRole;
  department?: string;
}

interface LoginResponse {
  token: string;
  user: {
    id: string;
    fullName: string;
    role: UserRole;
    department?: string;
    permissions: string[];
  };
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errorCode?: string;
  error?: any;
}

class ApiError extends Error {
  constructor(message: string, public errorCode?: string) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Handle API errors in a standardized way following BankOne API error handling patterns
 */
const handleApiError = (error: unknown): never => {
  // If it's already an ApiError, just rethrow it
  if (error instanceof ApiError) {
    throw error;
  }

  // Handle Axios errors
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<ApiResponse<any>>;
    const errorData = axiosError.response?.data;
    const message = errorData?.message || axiosError.message || 'API request failed';
    const errorCode = errorData?.errorCode;
    throw new ApiError(message, errorCode);
  }

  // Handle standard errors
  if (error instanceof Error) {
    throw new ApiError(error.message);
  }

  // Handle unknown errors
  throw new ApiError('An unknown error occurred');
};

/**
 * Authenticate user and return token with role-based permissions
 */
export const login = async (params: { phoneNumber: string; pin: string }): Promise<LoginResponse> => {
  try {
    const response = await axios.post<ApiResponse<LoginResponse>>(
      `${API_BASE_URL}/api/v1/personal/auth/login`,
      {
        email: params.phoneNumber, // If you have email, use it here
        password: params.pin
      }
    );
    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Authentication failed', response.data.errorCode);
    }
    return response.data.data!;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Check if user has required permissions
 */
export const checkPermissions = async (token: string, requiredPermissions: string[]): Promise<boolean> => {
  try {
    const response = await axios.post<ApiResponse<{ hasPermissions: boolean }>>(
      `${API_BASE_URL}/api/v1/auth/check-permissions`,
      { permissions: requiredPermissions },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Permission check failed', response.data.errorCode);
    }

    return response.data.data!.hasPermissions;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Check department-based access
 */
export const checkDepartmentAccess = async (token: string, departmentId: string): Promise<boolean> => {
  try {
    const response = await axios.post<ApiResponse<{ hasAccess: boolean }>>(
      `${API_BASE_URL}/api/v1/auth/check-department-access`,
      { departmentId },
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Department access check failed', response.data.errorCode);
    }

    return response.data.data!.hasAccess;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Get activity logs (Admin only)
 */
export const checkActivityLogs = async (token: string): Promise<ActivityLog[]> => {
  try {
    const response = await axios.get<ApiResponse<{ logs: ActivityLog[] }>>(
      `${API_BASE_URL}/api/v1/auth/activity-logs`,
      {
        headers: { Authorization: `Bearer ${token}` }
      }
    );

    if (!response.data.success) {
      throw new ApiError(response.data.message || 'Failed to fetch activity logs', response.data.errorCode);
    }

    return response.data.data!.logs;
  } catch (error) {
    throw handleApiError(error);
  }
};

/**
 * Role-based permission mapping
 */
const rolePermissions: Record<UserRole, string[]> = {
  STAFF: ['view_transactions', 'process_payments'],
  MANAGER: ['view_transactions', 'process_payments', 'manage_staff', 'view_reports'],
  ACCOUNT: ['view_transactions', 'process_payments', 'manage_accounts'],
  MANAGEMENT: ['view_transactions', 'process_payments', 'manage_staff', 'view_reports', 'manage_settings'],
  ADMIN: ['all_permissions'],
  admin: ['all_permissions'],
  superadmin: ['all_permissions']
};

/**
 * Validate user permissions based on role
 */
export const validateRolePermissions = (role: UserRole, requiredPermissions: string[]): boolean => {
  const userPermissions = rolePermissions[role] || [];
  return requiredPermissions.every(permission => userPermissions.includes(permission));
};
