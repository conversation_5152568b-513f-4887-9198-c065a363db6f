
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[40px] text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md active:scale-95",
  {
    variants: {
      variant: {
        default: "bg-primary/90 backdrop-blur-md text-primary-foreground hover:bg-primary/80",
        destructive: "bg-destructive/90 backdrop-blur-md text-destructive-foreground hover:bg-destructive/80",
        outline: "border border-input/80 bg-background/50 backdrop-blur-md hover:bg-accent/20 hover:text-accent-foreground",
        secondary: "bg-secondary/90 backdrop-blur-md text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent/20 hover:text-accent-foreground backdrop-blur-sm",
        link: "text-primary underline-offset-4 hover:underline backdrop-blur-sm",
        primary: "bg-[#1231B8] text-white hover:bg-[#1231B8]/90",
        yellow: "bg-[#FDE314] text-kojaDark hover:bg-[#FDE314]/90",
        admin: "bg-gradient-to-r from-[#1231B8] to-[#1231B8]/80 text-white hover:bg-[#1231B8]/90",
        adminOutline: "border border-[#1231B8]/20 bg-white/50 backdrop-blur-md text-[#1231B8] hover:bg-[#1231B8]/5",
        tier1: "bg-gradient-to-r from-gray-200 to-gray-100 text-gray-800 hover:from-gray-100 hover:to-gray-200",
        tier2: "bg-gradient-to-r from-gray-300 to-gray-200 text-gray-800 hover:from-gray-200 hover:to-gray-300",
        tier3: "bg-gradient-to-r from-[#FDE314] to-amber-300 text-gray-800 hover:from-amber-300 hover:to-[#FDE314]",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-[40px] px-3",
        lg: "h-11 rounded-[40px] px-8",
        icon: "h-10 w-10 rounded-[40px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
