
import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const notificationBadgeVariants = cva(
  "inline-flex items-center justify-center rounded-full text-xs font-medium transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-kojaPrimary text-white",
        secondary: "bg-kojaYellow text-kojaDark",
        success: "bg-green-500 text-white",
        destructive: "bg-red-500 text-white",
        outline: "bg-transparent border border-current",
        warning: "bg-amber-500 text-white",
        info: "bg-blue-500 text-white",
      },
      size: {
        default: "h-5 min-w-5 px-1.5",
        sm: "h-4 min-w-4 px-1",
        lg: "h-6 min-w-6 px-2",
        dot: "h-2.5 w-2.5 p-0",
      },
      animation: {
        none: "",
        pulse: "animate-pulse",
        bounce: "animate-bounce",
        scale: "animate-scale-bounce",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
    },
  }
);

export interface NotificationBadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof notificationBadgeVariants> {
  count?: number;
  maxCount?: number;
  showZero?: boolean;
}

const NotificationBadge = React.forwardRef<HTMLSpanElement, NotificationBadgeProps>(
  ({ className, variant, size, animation, count, maxCount = 99, showZero = false, children, ...props }, ref) => {
    // If count is provided, show the count, otherwise show children
    const content = count !== undefined ? 
      (count > maxCount ? `${maxCount}+` : count) : 
      children;
    
    // Don't render if count is 0 and showZero is false
    if (count === 0 && !showZero && size !== "dot") {
      return null;
    }

    return (
      <span
        className={cn(notificationBadgeVariants({ variant, size, animation, className }))}
        ref={ref}
        {...props}
      >
        {size !== "dot" ? content : null}
      </span>
    );
  }
);
NotificationBadge.displayName = "NotificationBadge";

export { NotificationBadge, notificationBadgeVariants };
