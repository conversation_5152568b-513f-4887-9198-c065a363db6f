-- CreateEnum
CREATE TYPE "FeeType" AS ENUM ('TRA<PERSON>FER', 'WIT<PERSON>RAWAL', 'DEPOSIT', 'LOAN_PROCESSING', 'CARD_ISSUANCE', 'ACCOUNT_MAINTENANCE', 'POS_TRANSACTION', 'ESCROW', '<PERSON><PERSON><PERSON>_PAYMENT', 'INTERNATIONAL_TRANSFER');

-- CreateEnum
CREATE TYPE "EscrowStatus" AS ENUM ('PENDING', 'FUNDED', 'RELEASED', 'DISPUTED', 'REFUNDED', 'CANCELLED', 'EXPIRED');

-- CreateEnum
CREATE TYPE "EscrowType" AS ENUM ('GOODS', 'SERVICES', 'RENTAL', 'FREELANCE', 'MARKETPLACE', 'CUSTOM');

-- CreateEnum
CREATE TYPE "DisputeStatus" AS ENUM ('NONE', 'OPENED', 'UNDER_REVIEW', 'RESOLVED', 'CLOSED');

-- CreateTable
CREATE TABLE "fee_settings" (
    "id" TEXT NOT NULL,
    "feeType" "FeeType" NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "percentage" DOUBLE PRECISION,
    "fixedAmount" DOUBLE PRECISION,
    "minAmount" DOUBLE PRECISION,
    "maxAmount" DOUBLE PRECISION,
    "currencyCode" TEXT NOT NULL DEFAULT 'NGN',
    "applicableToUserTypes" "UserRole"[],
    "applicableToAccountTypes" "AccountType"[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "fee_settings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "notification_preferences" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "emailNotifications" BOOLEAN NOT NULL DEFAULT true,
    "pushNotifications" BOOLEAN NOT NULL DEFAULT true,
    "smsNotifications" BOOLEAN NOT NULL DEFAULT false,
    "inAppNotifications" BOOLEAN NOT NULL DEFAULT true,
    "marketingNotifications" BOOLEAN NOT NULL DEFAULT false,
    "securityNotifications" BOOLEAN NOT NULL DEFAULT true,
    "transactionNotifications" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "notification_preferences_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "escrows" (
    "id" TEXT NOT NULL,
    "reference" TEXT NOT NULL,
    "payerAccountId" TEXT NOT NULL,
    "payeeAccountId" TEXT NOT NULL,
    "amount" DOUBLE PRECISION NOT NULL,
    "currency" TEXT NOT NULL DEFAULT 'NGN',
    "title" TEXT NOT NULL,
    "description" TEXT,
    "type" "EscrowType" NOT NULL,
    "terms" TEXT,
    "status" "EscrowStatus" NOT NULL DEFAULT 'PENDING',
    "disputeStatus" "DisputeStatus" NOT NULL DEFAULT 'NONE',
    "expectedCompletionDate" TIMESTAMP(3),
    "expiryDate" TIMESTAMP(3),
    "fundedAt" TIMESTAMP(3),
    "releasedAt" TIMESTAMP(3),
    "refundedAt" TIMESTAMP(3),
    "cancelledAt" TIMESTAMP(3),
    "expiredAt" TIMESTAMP(3),
    "disputedAt" TIMESTAMP(3),
    "disputeReason" TEXT,
    "disputeDescription" TEXT,
    "disputeEvidence" TEXT,
    "releaseReason" TEXT,
    "refundReason" TEXT,
    "cancelReason" TEXT,
    "metadata" JSONB,
    "fundedBy" TEXT,
    "releasedBy" TEXT,
    "refundedBy" TEXT,
    "cancelledBy" TEXT,
    "disputedBy" TEXT,
    "fundingTransactionId" TEXT,
    "releaseTransactionId" TEXT,
    "refundTransactionId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "escrows_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "notification_preferences_userId_key" ON "notification_preferences"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "escrows_reference_key" ON "escrows"("reference");

-- AddForeignKey
ALTER TABLE "notification_preferences" ADD CONSTRAINT "notification_preferences_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escrows" ADD CONSTRAINT "escrows_payerAccountId_fkey" FOREIGN KEY ("payerAccountId") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "escrows" ADD CONSTRAINT "escrows_payeeAccountId_fkey" FOREIGN KEY ("payeeAccountId") REFERENCES "accounts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
