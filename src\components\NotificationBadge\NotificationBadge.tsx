
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Bell } from 'lucide-react';
import { NotificationBadge as Badge } from '@/components/ui/notification-badge';
import notificationService from '@/services/notificationService';
import { useAuth } from '@/contexts/AuthContext';

interface NotificationBadgeProps {
  onClick: () => void;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({ onClick }) => {
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUnreadCount = async () => {
      if (user?.id) {
        try {
          setLoading(true);
          const response = await notificationService.getUnreadCount(user.id);
          if (response.success) {
            setUnreadCount(response.data.count);
          } else {
            setError('Failed to fetch unread notifications');
          }
        } catch (err) {
          console.error('Error fetching unread notifications:', err);
          setError('An error occurred while fetching notifications');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchUnreadCount();

    // Set up polling to check for new notifications every minute
    const intervalId = setInterval(fetchUnreadCount, 60000);

    return () => clearInterval(intervalId);
  }, [user]);

  // Handle notification badge click
  const handleBadgeClick = () => {
    onClick();
  };

  return (
    <Button 
      variant="ghost" 
      size="icon" 
      onClick={handleBadgeClick}
      aria-label="notifications"
      data-testid="notification-badge"
      className="relative"
    >
      <Bell className="h-5 w-5" />
      {unreadCount > 0 && (
        <Badge 
          count={unreadCount} 
          maxCount={99}
          variant="destructive" 
          size="default"
          className="absolute -top-1 -right-1"
        />
      )}
    </Button>
  );
};

export default NotificationBadge;
