import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, ShieldAlert, UserX, Activity } from 'lucide-react';
import { AdminSecurityService } from '@/services/adminSecurity';
import { FraudDetectionService } from '@/services/fraudDetection';

interface SecurityAlert {
  id: string;
  type: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  timestamp: string;
  status: 'NEW' | 'INVESTIGATING' | 'RESOLVED';
}

interface SecurityMetrics {
  totalAlerts: number;
  criticalAlerts: number;
  blockedAttempts: number;
  suspiciousActivities: number;
}

export const SecurityMonitoring: React.FC = () => {
  const [metrics, setMetrics] = useState<SecurityMetrics>({
    totalAlerts: 0,
    criticalAlerts: 0,
    blockedAttempts: 0,
    suspiciousActivities: 0
  });

  const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
  const [selectedTab, setSelectedTab] = useState('realtime');

  useEffect(() => {
    // Initialize security services
    const adminSecurity = new AdminSecurityService();
    const fraudDetection = FraudDetectionService.getInstance();

    // Fetch initial data
    fetchSecurityMetrics();
    fetchSecurityAlerts();

    // Set up real-time monitoring
    const monitoringInterval = setInterval(fetchSecurityMetrics, 30000);
    return () => clearInterval(monitoringInterval);
  }, []);

  const fetchSecurityMetrics = async () => {
    // Implement real metrics fetching
    setMetrics({
      totalAlerts: 15,
      criticalAlerts: 3,
      blockedAttempts: 47,
      suspiciousActivities: 8
    });
  };

  const fetchSecurityAlerts = async () => {
    // Implement real alerts fetching
    setAlerts([
      {
        id: 'ALERT001',
        type: 'SUSPICIOUS_LOGIN',
        severity: 'HIGH',
        message: 'Multiple failed login attempts detected from unknown IP',
        timestamp: new Date().toISOString(),
        status: 'NEW'
      },
      {
        id: 'ALERT002',
        type: 'FRAUD_DETECTION',
        severity: 'CRITICAL',
        message: 'Unusual transaction pattern detected',
        timestamp: new Date().toISOString(),
        status: 'INVESTIGATING'
      }
    ]);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'bg-blue-100 text-blue-800';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800';
      case 'HIGH': return 'bg-orange-100 text-orange-800';
      case 'CRITICAL': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW': return 'bg-red-100 text-red-800';
      case 'INVESTIGATING': return 'bg-yellow-100 text-yellow-800';
      case 'RESOLVED': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">Total Alerts</CardTitle>
            <CardDescription>Last 24 hours</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-500" />
              <span className="text-2xl font-bold">{metrics.totalAlerts}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">Critical Alerts</CardTitle>
            <CardDescription>Requiring immediate action</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <ShieldAlert className="h-5 w-5 text-red-500" />
              <span className="text-2xl font-bold">{metrics.criticalAlerts}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">Blocked Attempts</CardTitle>
            <CardDescription>Prevented security threats</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <UserX className="h-5 w-5 text-green-500" />
              <span className="text-2xl font-bold">{metrics.blockedAttempts}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">Suspicious Activities</CardTitle>
            <CardDescription>Under investigation</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Activity className="h-5 w-5 text-orange-500" />
              <span className="text-2xl font-bold">{metrics.suspiciousActivities}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Security Monitoring</CardTitle>
          <CardDescription>Real-time security alerts and monitoring</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="realtime">Real-time Monitoring</TabsTrigger>
              <TabsTrigger value="alerts">Security Alerts</TabsTrigger>
              <TabsTrigger value="blocked">Blocked Activities</TabsTrigger>
            </TabsList>

            <TabsContent value="realtime">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Alert ID</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Message</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Action</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {alerts.map((alert) => (
                      <TableRow key={alert.id}>
                        <TableCell>{alert.id}</TableCell>
                        <TableCell>{alert.type}</TableCell>
                        <TableCell>
                          <Badge className={getSeverityColor(alert.severity)}>
                            {alert.severity}
                          </Badge>
                        </TableCell>
                        <TableCell>{alert.message}</TableCell>
                        <TableCell>{new Date(alert.timestamp).toLocaleString()}</TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(alert.status)}>
                            {alert.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button variant="outline" size="sm">
                            Investigate
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="alerts">
              {/* Similar table for historical alerts */}
            </TabsContent>

            <TabsContent value="blocked">
              {/* Similar table for blocked activities */}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
