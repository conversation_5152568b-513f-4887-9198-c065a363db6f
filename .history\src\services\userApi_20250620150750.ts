import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL;

export const fetchUserInfo = async (token: string) => {
  const response = await axios.get(`${API_BASE_URL}/api/v1/user`, {
    headers: { Authorization: `Bearer ${token}` }
  });
  // Accept both 'success' and 'status' as true for compatibility
  const isSuccess = response.data.success === true || response.data.status === true;
  if (!isSuccess) {
    throw new Error(response.data.message || 'Failed to fetch user info');
  }
  let data = response.data.data;
  if (data && data.data) data = data.data;
  return data;
};
