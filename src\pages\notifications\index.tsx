
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { Bell, CheckCircle2, Filter, LayoutList, Settings, Shield, AlertTriangle, CreditCard, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import notificationService, { Notification, NotificationType } from '@/services/notificationService';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';

interface ComponentNotification extends Omit<Notification, 'isRead'> {
  read: boolean;
}

const NotificationsPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [notifications, setNotifications] = useState<ComponentNotification[]>([]);
  const [filteredNotifications, setFilteredNotifications] = useState<ComponentNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOrder, setSortOrder] = useState<'newest' | 'oldest'>('newest');
  
  // Fetch notifications
  useEffect(() => {
    const fetchNotifications = async () => {
      if (!user?.id) return;
      
      setIsLoading(true);
      try {
        const response = await notificationService.getNotifications(user.id);
        if (response.success && response.data) {
          // Map server notifications to component notifications (isRead → read)
          const componentNotifications = response.data.map(notification => ({
            ...notification,
            read: notification.isRead
          }));
          
          setNotifications(componentNotifications);
          setFilteredNotifications(componentNotifications);
        }
      } catch (error) {
        console.error('Error fetching notifications:', error);
        toast({
          title: 'Error',
          description: 'Failed to load notifications',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchNotifications();
  }, [user]);
  
  // Filter notifications by type
  useEffect(() => {
    let filtered = notifications;
    
    if (activeTab !== 'all') {
      filtered = notifications.filter(n => n.type === activeTab);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(n =>
        n.title.toLowerCase().includes(query) ||
        n.message.toLowerCase().includes(query)
      );
    }
    
    if (sortOrder === 'newest') {
      filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    } else {
      filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
    }
    
    setFilteredNotifications(filtered);
  }, [notifications, activeTab, searchQuery, sortOrder]);
  
  // Mark notification as read
  const handleMarkAsRead = async (id: string) => {
    try {
      await notificationService.markAsRead(id);
      setNotifications(prev =>
        prev.map(n => (n.id === id ? { ...n, read: true } : n))
      );
      toast({
        title: 'Notification Marked',
        description: 'Notification marked as read',
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read',
        variant: 'destructive',
      });
    }
  };
  
  // Delete notification
  const handleDeleteNotification = async (id: string) => {
    try {
      await notificationService.deleteNotification(id);
      setNotifications(prev => prev.filter(n => n.id !== id));
      toast({
        title: 'Notification Deleted',
        description: 'Notification deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete notification',
        variant: 'destructive',
      });
    }
  };
  
  // Get notification icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.TRANSACTION:
        return <CreditCard className="h-4 w-4 text-blue-500" />;
      case NotificationType.SECURITY:
        return <Shield className="h-4 w-4 text-red-500" />;
      case NotificationType.SYSTEM:
        return <Settings className="h-4 w-4 text-gray-500" />;
      case NotificationType.LOAN:
        return <AlertTriangle className="h-4 w-4 text-green-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };
  
  return (
    <DashboardLayout>
      <div className="container py-8">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-2xl font-bold">Notifications</h1>
          <Button variant="outline" onClick={() => navigate('/notifications/preferences')}>
            <Settings className="mr-2 h-4 w-4" />
            Preferences
          </Button>
        </div>
        
        <Card>
          <CardHeader className="flex items-center justify-between">
            <CardTitle className="text-lg">
              <LayoutList className="mr-2 h-5 w-5" />
              All Notifications
            </CardTitle>
            <div className="flex items-center space-x-3">
              <Input
                type="search"
                placeholder="Search notifications..."
                className="max-w-md"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
              />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Sort By
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setSortOrder('newest')}>Newest First</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSortOrder('oldest')}>Oldest First</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" className="space-y-4">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="transaction">Transactions</TabsTrigger>
                <TabsTrigger value="security">Security</TabsTrigger>
                <TabsTrigger value="system">System</TabsTrigger>
                <TabsTrigger value="loan">Loans</TabsTrigger>
              </TabsList>
              <TabsContent value="all">
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-4 w-60" />
                          <Skeleton className="h-3 w-40" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No notifications found
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredNotifications.map(notification => (
                      <div
                        key={notification.id}
                        className="flex items-start space-x-4 p-4 border rounded-md hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type as NotificationType)}
                        </div>
                        <div>
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">{notification.title}</h3>
                            <div className="flex space-x-2">
                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <CheckCircle2 className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteNotification(notification.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{notification.message}</p>
                          <div className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(notification.createdAt), {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
              <TabsContent value="transaction">
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-4 w-60" />
                          <Skeleton className="h-3 w-40" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No transaction notifications found
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredNotifications.map(notification => (
                      <div
                        key={notification.id}
                        className="flex items-start space-x-4 p-4 border rounded-md hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type as NotificationType)}
                        </div>
                        <div>
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">{notification.title}</h3>
                            <div className="flex space-x-2">
                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <CheckCircle2 className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteNotification(notification.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{notification.message}</p>
                          <div className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(notification.createdAt), {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
              <TabsContent value="security">
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-4 w-60" />
                          <Skeleton className="h-3 w-40" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No security notifications found
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredNotifications.map(notification => (
                      <div
                        key={notification.id}
                        className="flex items-start space-x-4 p-4 border rounded-md hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type as NotificationType)}
                        </div>
                        <div>
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">{notification.title}</h3>
                            <div className="flex space-x-2">
                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <CheckCircle2 className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteNotification(notification.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{notification.message}</p>
                          <div className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(notification.createdAt), {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
              <TabsContent value="system">
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-4 w-60" />
                          <Skeleton className="h-3 w-40" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No system notifications found
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredNotifications.map(notification => (
                      <div
                        key={notification.id}
                        className="flex items-start space-x-4 p-4 border rounded-md hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type as NotificationType)}
                        </div>
                        <div>
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">{notification.title}</h3>
                            <div className="flex space-x-2">
                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <CheckCircle2 className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteNotification(notification.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{notification.message}</p>
                          <div className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(notification.createdAt), {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
              <TabsContent value="loan">
                {isLoading ? (
                  <div className="space-y-3">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-4">
                        <Skeleton className="h-10 w-10 rounded-full" />
                        <div>
                          <Skeleton className="h-4 w-60" />
                          <Skeleton className="h-3 w-40" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : filteredNotifications.length === 0 ? (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No loan notifications found
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredNotifications.map(notification => (
                      <div
                        key={notification.id}
                        className="flex items-start space-x-4 p-4 border rounded-md hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex-shrink-0">
                          {getNotificationIcon(notification.type as NotificationType)}
                        </div>
                        <div>
                          <div className="flex items-center justify-between">
                            <h3 className="text-lg font-medium">{notification.title}</h3>
                            <div className="flex space-x-2">
                              {!notification.read && (
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <CheckCircle2 className="h-4 w-4" />
                                </Button>
                              )}
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => handleDeleteNotification(notification.id)}
                              >
                                Delete
                              </Button>
                            </div>
                          </div>
                          <p className="text-sm text-gray-500">{notification.message}</p>
                          <div className="text-xs text-gray-400">
                            {formatDistanceToNow(new Date(notification.createdAt), {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default NotificationsPage;
