
// Banking type definitions

export enum TransactionType {
  CREDIT = 'credit',
  DEBIT = 'debit'
}

export enum TransactionStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export enum AccountType {
  SAVINGS = 'savings',
  CURRENT = 'current'
}

export enum PaymentMethod {
  BANK_TRANSFER = 'bank_transfer',
  CARD = 'card',
  WALLET = 'wallet'
}

export interface BankingTransaction {
  id: string;
  amount: number;
  type: TransactionType;
  description: string;
  date: string;
  reference: string;
  status: TransactionStatus;
  accountId: string;
}

export interface BankingAccount {
  id: string;
  accountNumber: string;
  accountName: string;
  balance: number;
  bankName: string;
  accountType: AccountType;
  userId: string;
}

export interface PaymentRequest {
  amount: number;
  recipientAccount: string;
  recipientBank: string;
  narration: string;
  method: PaymentMethod;
}
