
/**
 * Determines if the back button should be shown on a given route
 * @param pathname The current route path
 * @returns boolean indicating whether to show back button
 */
export const shouldShowBackButton = (pathname: string): boolean => {
  // List of dashboard routes where back button should not be shown
  const dashboardRoutes = [
    '/dashboard',
    '/business-dashboard',
    '/',
    '/home'
  ];
  
  // Don't show back button on dashboard routes
  return !dashboardRoutes.includes(pathname);
};
