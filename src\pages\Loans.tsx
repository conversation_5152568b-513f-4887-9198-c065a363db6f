
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import LoanApplication from '@/components/LoanApplication';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CreditCard, Wallet, Link2, AlertCircle, Clock, CheckCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

const Loans = () => {
  const [linkedBank, setLinkedBank] = useState(false);
  const [bankName, setBankName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [isEligible, setIsEligible] = useState(false);
  const [isLinkingCard, setIsLinkingCard] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardLinked, setCardLinked] = useState(false);
  const { toast } = useToast();

  // In a real app, this would be determined by backend APIs
  const checkEligibility = () => {
    if (linkedBank) {
      // Simulate checking user's transaction history and credit score
      setTimeout(() => {
        setIsEligible(true);
        toast({
          title: "Eligibility Check Complete",
          description: "Congratulations! You are eligible for a loan.",
          variant: "success"
        });
      }, 1500);
    } else {
      toast({
        title: "Bank account required",
        description: "Please link your bank account first",
        variant: "destructive"
      });
    }
  };

  const handleLinkBank = (e: React.FormEvent) => {
    e.preventDefault();
    if (!bankName || !accountNumber) {
      toast({
        title: "Missing information",
        description: "Please enter both bank name and account number",
        variant: "destructive"
      });
      return;
    }

    // Simulate linking bank account
    setTimeout(() => {
      setLinkedBank(true);
      toast({
        title: "Bank Account Linked",
        description: "Your bank account has been successfully linked",
        variant: "success"
      });
    }, 1500);
  };

  const handleLinkCard = (e: React.FormEvent) => {
    e.preventDefault();
    if (!cardNumber || !expiryDate || !cvv) {
      toast({
        title: "Missing information",
        description: "Please enter all card details",
        variant: "destructive"
      });
      return;
    }

    // Simulate card linking and validation fee
    setTimeout(() => {
      setCardLinked(true);
      toast({
        title: "Card Linked Successfully",
        description: "Your card has been linked. A ₦500 validation fee has been charged.",
        variant: "success"
      });
    }, 1500);
  };

  return (
    <DashboardLayout pageTitle="Loans">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-kojaDark">Loans</h1>
        <p className="text-kojaGray mt-1">Apply for personal loans based on your eligibility</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <CardTitle>Loan Eligibility</CardTitle>
              <CardDescription>Check if you qualify for a personal loan</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {!linkedBank ? (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Link2 className="h-5 w-5 text-blue-500 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-blue-800">Link Your Bank Account</h3>
                      <p className="text-sm text-blue-600 mt-1">
                        Link a Nigerian bank account to check your loan eligibility
                      </p>
                      <form onSubmit={handleLinkBank} className="mt-4 space-y-4">
                        <div>
                          <select 
                            className="w-full p-2 border rounded-md"
                            value={bankName}
                            onChange={(e) => setBankName(e.target.value)}
                          >
                            <option value="">Select Bank</option>
                            <option value="access">Access Bank</option>
                            <option value="gtb">GTBank</option>
                            <option value="zenith">Zenith Bank</option>
                            <option value="uba">UBA</option>
                            <option value="firstbank">First Bank</option>
                            <option value="stanbic">Stanbic IBTC</option>
                          </select>
                        </div>
                        <div>
                          <Input 
                            type="text" 
                            placeholder="Account Number" 
                            value={accountNumber}
                            onChange={(e) => setAccountNumber(e.target.value)}
                          />
                        </div>
                        <Button type="submit" className="w-full">
                          Link Bank Account
                        </Button>
                      </form>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-green-800">Bank Account Linked</h3>
                      <p className="text-sm text-green-600 mt-1">
                        Your bank account has been successfully linked
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {linkedBank && !isEligible && (
                <Button 
                  onClick={checkEligibility} 
                  className="w-full"
                >
                  Check Eligibility
                </Button>
              )}

              {isEligible && !cardLinked && (
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <CreditCard className="h-5 w-5 text-yellow-500 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-yellow-800">Link a Card for Verification</h3>
                      <p className="text-sm text-yellow-600 mt-1">
                        A one-time validation fee of ₦500 will be charged
                      </p>
                      <form onSubmit={handleLinkCard} className="mt-4 space-y-4">
                        <div>
                          <Input 
                            type="text" 
                            placeholder="Card Number" 
                            value={cardNumber}
                            onChange={(e) => setCardNumber(e.target.value)}
                          />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <Input 
                            type="text" 
                            placeholder="MM/YY" 
                            value={expiryDate}
                            onChange={(e) => setExpiryDate(e.target.value)}
                          />
                          <Input 
                            type="text" 
                            placeholder="CVV" 
                            value={cvv}
                            onChange={(e) => setCvv(e.target.value)}
                          />
                        </div>
                        <Button type="submit" className="w-full">
                          Link Card & Pay ₦500
                        </Button>
                      </form>
                    </div>
                  </div>
                </div>
              )}

              {isEligible && cardLinked && (
                <div className="space-y-6">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <h3 className="font-medium text-green-800">You're Eligible!</h3>
                        <p className="text-sm text-green-600 mt-1">
                          You can now apply for a personal loan
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-500 mb-2">Loan Eligibility Status:</p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-500">Credit Score:</span>
                        <div className="font-medium text-green-600 mt-1">Good (720)</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-500">Monthly Income:</span>
                        <div className="font-medium text-kojaDark mt-1">₦350,000</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-500">Max Loan Amount:</span>
                        <div className="font-medium text-kojaDark mt-1">₦500,000</div>
                      </div>
                      <div className="p-3 bg-gray-50 rounded-lg">
                        <span className="text-gray-500">Repayment Period:</span>
                        <div className="font-medium text-kojaDark mt-1">Up to 12 months</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {isEligible && cardLinked && (
            <LoanApplication type="personal" />
          )}
        </div>
        
        <div className="space-y-6">
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <CardTitle>Loan Status</CardTitle>
              <CardDescription>Track your loan applications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="h-8 w-8 rounded-full bg-yellow-100 text-yellow-500 flex items-center justify-center">
                    <Clock size={16} />
                  </div>
                  <div>
                    <div className="font-medium">No Active Loans</div>
                    <div className="text-xs text-kojaGray">Apply for a loan to get started</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <CardTitle>Loan Tips</CardTitle>
              <CardDescription>Make the most of your loan</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                <AlertCircle className="h-5 w-5 text-kojaPrimary mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium">Borrow Responsibly</h4>
                  <p className="text-xs text-kojaGray mt-1">Only borrow what you can comfortably repay</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                <AlertCircle className="h-5 w-5 text-kojaPrimary mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium">Make Timely Payments</h4>
                  <p className="text-xs text-kojaGray mt-1">Pay on time to maintain a good credit score</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                <AlertCircle className="h-5 w-5 text-kojaPrimary mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium">Early Repayment</h4>
                  <p className="text-xs text-kojaGray mt-1">Save on interest by paying early when possible</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Loans;
