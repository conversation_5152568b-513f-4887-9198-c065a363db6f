
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import PersonalSidebar from '@/components/PersonalSidebar';
import BusinessSidebar from '@/components/BusinessSidebar';
import { AdminSidebar } from '@/components/AdminSidebar';
import Header from '@/components/Header/Header';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { accountType } = useAuth();

  const renderSidebar = () => {
    switch (accountType) {
      case 'business':
        return <BusinessSidebar />;
      case 'admin':
        return <AdminSidebar />;
      default:
        return <PersonalSidebar />;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden">
      {renderSidebar()}
      <div className="flex flex-col flex-1 overflow-hidden">
        <Header />
        <main className="flex-1 overflow-y-auto p-4">{children}</main>
      </div>
    </div>
  );
};

export default DashboardLayout;
