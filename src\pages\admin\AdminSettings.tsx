
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  User,
  Lock,
  Bell,
  Globe,
  Mail,
  Shield,
  Smartphone,
  CreditCard,
  HardDrive,
  BarChart3,
  Clock,
  FileText,
  AlertOctagon,
  CheckCircle,
  Database,
  Server,
  Upload,
  Download,
  Trash2,
  RefreshCw,
  Languages,
  Calendar,
  BellRing,
  UserCog,
  ShieldCheck,
  Key,
  ShieldAlert,
  ShieldQuestion,
  ServerCrash,
  Loader,
  PowerOff,
  ClipboardCheck,
  Text,
  FileCode,
  FileJson,
  Keyboard,
  Wrench,
  BookOpen,
  HelpCircle,
  Briefcase,
  ScrollText,
  Landmark,
  Banknote,
  DollarSign,
  Percent,
  Receipt,
  PiggyBank,
  Wallet
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

const AdminSettings = () => {
  const [activeTab, setActiveTab] = useState("general");
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [darkModeEnabled, setDarkModeEnabled] = useState(true);
  const [systemUpdates, setSystemUpdates] = useState(true);
  const [autoBackup, setAutoBackup] = useState(true);
  const [multiFactorAuth, setMultiFactorAuth] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [systemLogs, setSystemLogs] = useState(true);
  const [auditTrail, setAuditTrail] = useState(true);
  const [ipWhitelist, setIpWhitelist] = useState(false);
  const [geolocationRestrictions, setGeolocationRestrictions] = useState(false);

  const handleSaveSettings = () => {
    toast.success("Settings saved successfully!");
  };

  const handleMaintenanceModeToggle = () => {
    setMaintenanceMode(!maintenanceMode);
    toast.info(`Maintenance mode ${!maintenanceMode ? 'activated' : 'deactivated'}.`);
  };

  const triggerSystemRestart = () => {
    toast.loading("System restart initiated...");
    
    setTimeout(() => {
      toast.success("System restarted successfully!");
    }, 3000);
  };

  return (
    <AdminLayout pageTitle="System Settings">
      <Helmet>
        <title>System Settings | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">System Settings</h1>
            <p className="text-gray-500">Manage global settings and configurations</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button 
              variant="destructive" 
              onClick={handleMaintenanceModeToggle}
            >
              {maintenanceMode ? <CheckCircle size={16} className="mr-2" /> : <AlertOctagon size={16} className="mr-2" />}
              {maintenanceMode ? 'Deactivate Maintenance Mode' : 'Activate Maintenance Mode'}
            </Button>
          </div>
        </div>

        <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 mb-4">
            <TabsTrigger value="general">
              <Wrench size={14} className="mr-2" />
              <span className="hidden sm:inline">General</span>
            </TabsTrigger>
            <TabsTrigger value="security">
              <ShieldCheck size={14} className="mr-2" />
              <span className="hidden sm:inline">Security</span>
            </TabsTrigger>
            <TabsTrigger value="notifications">
              <BellRing size={14} className="mr-2" />
              <span className="hidden sm:inline">Notifications</span>
            </TabsTrigger>
            <TabsTrigger value="appearance">
              <Text size={14} className="mr-2" />
              <span className="hidden sm:inline">Appearance</span>
            </TabsTrigger>
            <TabsTrigger value="system">
              <HardDrive size={14} className="mr-2" />
              <span className="hidden sm:inline">System</span>
            </TabsTrigger>
            <TabsTrigger value="finance">
              <DollarSign size={14} className="mr-2" />
              <span className="hidden sm:inline">Finance</span>
            </TabsTrigger>
            <TabsTrigger value="backup">
              <Database size={14} className="mr-2" />
              <span className="hidden sm:inline">Backup</span>
            </TabsTrigger>
            <TabsTrigger value="advanced">
              <FileCode size={14} className="mr-2" />
              <span className="hidden sm:inline">Advanced</span>
            </TabsTrigger>
          </TabsList>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {activeTab === "general" && <Wrench className="h-5 w-5" />}
                {activeTab === "security" && <ShieldCheck className="h-5 w-5" />}
                {activeTab === "notifications" && <BellRing className="h-5 w-5" />}
                {activeTab === "appearance" && <Text className="h-5 w-5" />}
                {activeTab === "system" && <HardDrive className="h-5 w-5" />}
                {activeTab === "finance" && <DollarSign className="h-5 w-5" />}
                {activeTab === "backup" && <Database className="h-5 w-5" />}
                {activeTab === "advanced" && <FileCode className="h-5 w-5" />}
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Settings
              </CardTitle>
              <CardDescription>
                Configure {activeTab} settings for the entire platform
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <TabsContent value="general" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex flex-col gap-1">
                    <Label htmlFor="platformName">Platform Name</Label>
                    <Input id="platformName" defaultValue="KojaPay" />
                  </div>
                  
                  <div className="flex flex-col gap-1">
                    <Label htmlFor="supportEmail">Support Email</Label>
                    <Input id="supportEmail" type="email" defaultValue="<EMAIL>" />
                  </div>
                  
                  <div className="flex flex-col gap-1">
                    <Label htmlFor="contactPhone">Contact Phone</Label>
                    <Input id="contactPhone" type="tel" defaultValue="+234 800 KOJAPAY" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Dark Mode Default</Label>
                      <div className="text-sm text-gray-500">Set dark mode as default theme</div>
                    </div>
                    <Switch checked={darkModeEnabled} onCheckedChange={setDarkModeEnabled} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Automatic System Updates</Label>
                      <div className="text-sm text-gray-500">Allow system to update automatically</div>
                    </div>
                    <Switch checked={systemUpdates} onCheckedChange={setSystemUpdates} />
                  </div>
                  
                  <div className="flex flex-col gap-1">
                    <Label htmlFor="timezone">Default Timezone</Label>
                    <select id="timezone" className="form-select rounded-md border-gray-300">
                      <option value="Africa/Lagos">Africa/Lagos (GMT+1)</option>
                      <option value="UTC">UTC (GMT+0)</option>
                      <option value="America/New_York">America/New_York (GMT-5)</option>
                      <option value="Europe/London">Europe/London (GMT+0)</option>
                      <option value="Asia/Tokyo">Asia/Tokyo (GMT+9)</option>
                    </select>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleSaveSettings}>Save Changes</Button>
                </div>
              </TabsContent>
              
              <TabsContent value="security" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">Multi-Factor Authentication</Label>
                      <div className="text-sm text-gray-500">Require MFA for all admin accounts</div>
                    </div>
                    <Switch checked={multiFactorAuth} onCheckedChange={setMultiFactorAuth} />
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">IP Whitelist</Label>
                      <div className="text-sm text-gray-500">Restrict admin access to specific IP addresses</div>
                    </div>
                    <Switch checked={ipWhitelist} onCheckedChange={setIpWhitelist} />
                  </div>
                  
                  {ipWhitelist && (
                    <div className="ml-6 border-l-2 pl-4 border-blue-200">
                      <div className="space-y-2">
                        <Label htmlFor="ipAddresses">Allowed IP Addresses</Label>
                        <Input id="ipAddresses" placeholder="e.g. ***********, ********" />
                        <div className="text-xs text-gray-500">Enter comma-separated list of IP addresses</div>
                      </div>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">Geolocation Restrictions</Label>
                      <div className="text-sm text-gray-500">Restrict access based on geographical location</div>
                    </div>
                    <Switch checked={geolocationRestrictions} onCheckedChange={setGeolocationRestrictions} />
                  </div>
                  
                  {geolocationRestrictions && (
                    <div className="ml-6 border-l-2 pl-4 border-blue-200">
                      <div className="space-y-2">
                        <Label htmlFor="allowedCountries">Allowed Countries</Label>
                        <select id="allowedCountries" className="form-select rounded-md border-gray-300 w-full" multiple>
                          <option value="NG">Nigeria</option>
                          <option value="GH">Ghana</option>
                          <option value="KE">Kenya</option>
                          <option value="ZA">South Africa</option>
                          <option value="US">United States</option>
                          <option value="GB">United Kingdom</option>
                        </select>
                        <div className="text-xs text-gray-500">Hold Ctrl/Cmd to select multiple countries</div>
                      </div>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <Label htmlFor="passwordPolicy">Password Policy</Label>
                    <select id="passwordPolicy" className="form-select rounded-md border-gray-300 w-full">
                      <option value="standard">Standard (8+ chars, 1 uppercase, 1 number)</option>
                      <option value="strong">Strong (10+ chars, uppercase, number, symbol)</option>
                      <option value="very-strong">Very Strong (12+ chars, uppercase, lowercase, number, symbol)</option>
                      <option value="custom">Custom Policy</option>
                    </select>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                    <Input id="sessionTimeout" type="number" defaultValue="30" min="5" max="120" />
                    <div className="text-xs text-gray-500">Time of inactivity before automatic logout</div>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleSaveSettings}>Save Changes</Button>
                </div>
              </TabsContent>
              
              <TabsContent value="notifications" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">Email Notifications</Label>
                      <div className="text-sm text-gray-500">Send system alerts via email</div>
                    </div>
                    <Switch checked={emailNotifications} onCheckedChange={setEmailNotifications} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">SMS Notifications</Label>
                      <div className="text-sm text-gray-500">Send critical alerts via SMS</div>
                    </div>
                    <Switch checked={smsNotifications} onCheckedChange={setSmsNotifications} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">Push Notifications</Label>
                      <div className="text-sm text-gray-500">Send in-app notifications</div>
                    </div>
                    <Switch checked={pushNotifications} onCheckedChange={setPushNotifications} />
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">Notification Events</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <input type="checkbox" id="notifyLogin" className="mr-2" defaultChecked />
                          <Label htmlFor="notifyLogin">Admin Login Attempts</Label>
                        </div>
                        <div className="flex items-center">
                          <input type="checkbox" id="notifyConfig" className="mr-2" defaultChecked />
                          <Label htmlFor="notifyConfig">Configuration Changes</Label>
                        </div>
                        <div className="flex items-center">
                          <input type="checkbox" id="notifyUser" className="mr-2" defaultChecked />
                          <Label htmlFor="notifyUser">User Registration</Label>
                        </div>
                        <div className="flex items-center">
                          <input type="checkbox" id="notifyKYC" className="mr-2" defaultChecked />
                          <Label htmlFor="notifyKYC">KYC Submissions</Label>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center">
                          <input type="checkbox" id="notifyTransaction" className="mr-2" defaultChecked />
                          <Label htmlFor="notifyTransaction">Large Transactions</Label>
                        </div>
                        <div className="flex items-center">
                          <input type="checkbox" id="notifySuspicious" className="mr-2" defaultChecked />
                          <Label htmlFor="notifySuspicious">Suspicious Activities</Label>
                        </div>
                        <div className="flex items-center">
                          <input type="checkbox" id="notifySystem" className="mr-2" defaultChecked />
                          <Label htmlFor="notifySystem">System Errors</Label>
                        </div>
                        <div className="flex items-center">
                          <input type="checkbox" id="notifyUpdate" className="mr-2" defaultChecked />
                          <Label htmlFor="notifyUpdate">System Updates</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <Label htmlFor="emailFrom">Email From Address</Label>
                    <Input id="emailFrom" defaultValue="<EMAIL>" />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="smsFrom">SMS From Name</Label>
                    <Input id="smsFrom" defaultValue="KojaPay" maxLength={11} />
                    <div className="text-xs text-gray-500">Maximum 11 characters for SMS sender ID</div>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleSaveSettings}>Save Changes</Button>
                </div>
              </TabsContent>
              
              <TabsContent value="system" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">System Logs</Label>
                      <div className="text-sm text-gray-500">Enable detailed system logging</div>
                    </div>
                    <Switch checked={systemLogs} onCheckedChange={setSystemLogs} />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">Audit Trail</Label>
                      <div className="text-sm text-gray-500">Track all admin actions</div>
                    </div>
                    <Switch checked={auditTrail} onCheckedChange={setAuditTrail} />
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="logRetention">Log Retention Period (days)</Label>
                      <Input id="logRetention" type="number" defaultValue="90" min="30" max="365" />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="logLevel">Default Log Level</Label>
                      <select id="logLevel" className="form-select rounded-md border-gray-300 w-full">
                        <option value="error">Error Only</option>
                        <option value="warning">Warning & Error</option>
                        <option value="info" selected>Info, Warning & Error</option>
                        <option value="debug">Debug (All Levels)</option>
                      </select>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <h3 className="text-base font-medium">System Maintenance</h3>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-3">
                      <Button variant="outline" className="w-full justify-start" onClick={triggerSystemRestart}>
                        <RefreshCw size={16} className="mr-2" />
                        Restart System
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start" onClick={() => toast.info("Cache cleared successfully!")}>
                        <Trash2 size={16} className="mr-2" />
                        Clear Cache
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start" onClick={() => toast.info("Log files cleared successfully!")}>
                        <FileText size={16} className="mr-2" />
                        Clear Logs
                      </Button>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">System Status</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span>System Version</span>
                        <Badge variant="outline">v2.5.3</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Last Update</span>
                        <Badge variant="outline">2023-10-15</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Database Status</span>
                        <Badge variant="outline" className="bg-green-100 text-green-800">Healthy</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>API Status</span>
                        <Badge variant="outline" className="bg-green-100 text-green-800">Operational</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span>Cache Status</span>
                        <Badge variant="outline" className="bg-green-100 text-green-800">Connected</Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="finance" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="defaultCurrency">Default Currency</Label>
                    <select id="defaultCurrency" className="form-select rounded-md border-gray-300 w-full">
                      <option value="NGN" selected>Nigerian Naira (₦)</option>
                      <option value="USD">US Dollar ($)</option>
                      <option value="EUR">Euro (€)</option>
                      <option value="GBP">British Pound (£)</option>
                      <option value="GHS">Ghanaian Cedi (₵)</option>
                      <option value="KES">Kenyan Shilling (KSh)</option>
                    </select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="transactionFee">Default Transaction Fee (%)</Label>
                    <Input id="transactionFee" type="number" defaultValue="1.5" min="0" max="100" step="0.1" />
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">Payment Processors</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <input type="checkbox" id="paystack" className="mr-2" defaultChecked />
                        <Label htmlFor="paystack">Paystack</Label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="flutterwave" className="mr-2" defaultChecked />
                        <Label htmlFor="flutterwave">Flutterwave</Label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="stripe" className="mr-2" defaultChecked />
                        <Label htmlFor="stripe">Stripe</Label>
                      </div>
                      <div className="flex items-center">
                        <input type="checkbox" id="paypal" className="mr-2" />
                        <Label htmlFor="paypal">PayPal</Label>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">Financial Limits</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="minTransaction">Minimum Transaction (₦)</Label>
                        <Input id="minTransaction" type="number" defaultValue="100" min="0" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="maxTransaction">Maximum Transaction (₦)</Label>
                        <Input id="maxTransaction" type="number" defaultValue="1000000" min="0" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="dailyLimit">Daily Transaction Limit (₦)</Label>
                        <Input id="dailyLimit" type="number" defaultValue="5000000" min="0" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="withdrawalLimit">Withdrawal Limit (₦)</Label>
                        <Input id="withdrawalLimit" type="number" defaultValue="2000000" min="0" />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleSaveSettings}>Save Changes</Button>
                </div>
              </TabsContent>
              
              <TabsContent value="backup" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">Automatic Backups</Label>
                      <div className="text-sm text-gray-500">Schedule automatic system backups</div>
                    </div>
                    <Switch checked={autoBackup} onCheckedChange={setAutoBackup} />
                  </div>
                  
                  {autoBackup && (
                    <div className="ml-6 border-l-2 pl-4 border-blue-200">
                      <div className="space-y-2">
                        <Label htmlFor="backupFrequency">Backup Frequency</Label>
                        <select id="backupFrequency" className="form-select rounded-md border-gray-300 w-full">
                          <option value="daily">Daily</option>
                          <option value="weekly" selected>Weekly</option>
                          <option value="biweekly">Bi-Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>
                      </div>
                      
                      <div className="space-y-2 mt-4">
                        <Label htmlFor="backupTime">Backup Time</Label>
                        <Input id="backupTime" type="time" defaultValue="02:00" />
                        <div className="text-xs text-gray-500">Time in 24-hour format (recommended during off-peak hours)</div>
                      </div>
                      
                      <div className="space-y-2 mt-4">
                        <Label htmlFor="retentionCount">Backup Retention Count</Label>
                        <Input id="retentionCount" type="number" defaultValue="10" min="1" max="100" />
                        <div className="text-xs text-gray-500">Number of backups to keep before deleting old ones</div>
                      </div>
                    </div>
                  )}
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <Label htmlFor="backupStorage">Backup Storage Location</Label>
                    <select id="backupStorage" className="form-select rounded-md border-gray-300 w-full">
                      <option value="local">Local Server</option>
                      <option value="s3" selected>Amazon S3</option>
                      <option value="gcs">Google Cloud Storage</option>
                      <option value="azure">Azure Blob Storage</option>
                    </select>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-3">
                    <h3 className="text-base font-medium">Manual Backup</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <Button 
                        variant="outline" 
                        className="w-full" 
                        onClick={() => toast.success("Backup initiated successfully!")}
                      >
                        <Upload size={16} className="mr-2" />
                        Create Backup Now
                      </Button>
                      
                      <Button 
                        variant="outline" 
                        className="w-full" 
                        onClick={() => toast.success("Latest backup downloaded successfully!")}
                      >
                        <Download size={16} className="mr-2" />
                        Download Latest Backup
                      </Button>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">Recent Backups</h3>
                    <div className="border rounded-md overflow-hidden">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">2023-10-15 02:00</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">256 MB</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant="outline" className="bg-green-100 text-green-800">Success</Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <Download size={16} />
                              </Button>
                            </td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">2023-10-08 02:00</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">254 MB</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant="outline" className="bg-green-100 text-green-800">Success</Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <Download size={16} />
                              </Button>
                            </td>
                          </tr>
                          <tr>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">2023-10-01 02:00</td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">252 MB</td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <Badge variant="outline" className="bg-green-100 text-green-800">Success</Badge>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <Download size={16} />
                              </Button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="advanced" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="apiThrottling">API Rate Limiting (requests per minute)</Label>
                    <Input id="apiThrottling" type="number" defaultValue="100" min="10" max="1000" />
                    <div className="text-xs text-gray-500">Limit API requests per minute per IP address</div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="apiTimeout">API Timeout (seconds)</Label>
                    <Input id="apiTimeout" type="number" defaultValue="30" min="5" max="120" />
                    <div className="text-xs text-gray-500">Maximum time for API request processing</div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">JSON Configuration</h3>
                    <div className="border rounded-md bg-gray-50 p-3">
                      <pre className="text-xs overflow-auto">
{`{
  "system": {
    "debug_mode": false,
    "maintenance_mode": ${maintenanceMode},
    "api_version": "v2",
    "cache_ttl": 3600,
    "task_queue": {
      "workers": 5,
      "retry_attempts": 3
    }
  }
}`}
                      </pre>
                    </div>
                    <div className="text-xs text-gray-500">Manual configuration requires system restart</div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium text-amber-700">Danger Zone</h3>
                    <div className="border border-red-200 rounded-md p-4 bg-red-50">
                      <h4 className="text-sm font-medium text-red-800 mb-2">Reset System</h4>
                      <p className="text-sm text-red-600 mb-3">This action will reset all system configurations to default values. This cannot be undone.</p>
                      <Button 
                        variant="destructive" 
                        onClick={() => {
                          if (window.confirm("Are you sure? This will reset all system configurations to their default values.")) {
                            toast.success("System reset successfully!");
                          }
                        }}
                      >
                        <AlertOctagon size={16} className="mr-2" />
                        Reset All Settings
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleSaveSettings}>Save Changes</Button>
                </div>
              </TabsContent>
              
              <TabsContent value="appearance" className="space-y-6 mt-0">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input id="primaryColor" type="color" defaultValue="#1231B8" className="w-16 h-10" />
                      <Input defaultValue="#1231B8" className="flex-1" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input id="secondaryColor" type="color" defaultValue="#FDE314" className="w-16 h-10" />
                      <Input defaultValue="#FDE314" className="flex-1" />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex gap-2">
                      <Input id="accentColor" type="color" defaultValue="#FF4757" className="w-16 h-10" />
                      <Input defaultValue="#FF4757" className="flex-1" />
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fontFamily">Font Family</Label>
                      <select id="fontFamily" className="form-select rounded-md border-gray-300 w-full">
                        <option value="unica">Unica One</option>
                        <option value="poppins">Poppins</option>
                        <option value="roboto">Roboto</option>
                        <option value="inter">Inter</option>
                        <option value="system">System Default</option>
                      </select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="borderRadius">Border Radius (px)</Label>
                      <Input id="borderRadius" type="number" defaultValue="20" min="0" max="40" />
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <h3 className="text-base font-medium">Logo & Favicon</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="logoUpload">Upload Logo</Label>
                        <div className="border-2 border-dashed border-gray-200 rounded-md p-4 text-center">
                          <div className="w-32 h-32 mx-auto bg-gray-100 rounded-md flex items-center justify-center">
                            <img src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" alt="Logo" className="max-w-full max-h-full object-contain" />
                          </div>
                          <Input id="logoUpload" type="file" className="mt-2" />
                          <div className="text-xs text-gray-500 mt-1">Recommended size: 512x512px</div>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="faviconUpload">Upload Favicon</Label>
                        <div className="border-2 border-dashed border-gray-200 rounded-md p-4 text-center">
                          <div className="w-32 h-32 mx-auto bg-gray-100 rounded-md flex items-center justify-center">
                            <img src="/lovable-uploads/6d533269-1146-41c3-ad78-0141d73073a6.png" alt="Favicon" className="max-w-full max-h-full object-contain" />
                          </div>
                          <Input id="faviconUpload" type="file" className="mt-2" />
                          <div className="text-xs text-gray-500 mt-1">Recommended size: 32x32px</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline">Cancel</Button>
                  <Button onClick={handleSaveSettings}>Save Changes</Button>
                </div>
              </TabsContent>
            </CardContent>
          </Card>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminSettings;
