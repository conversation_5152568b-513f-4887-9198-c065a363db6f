
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Award, Gift, ShoppingBag, Coffee, Plane, Gift as GiftIcon, ArrowRight, ChevronRight } from 'lucide-react';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const RewardPoints = () => {
  const navigate = useNavigate();
  
  const fadeInUp = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };
  
  const rewardHistory = [
    { activity: 'Card Transaction', date: 'Aug 15, 2023', points: '+5' },
    { activity: 'Bill Payment', date: 'Aug 10, 2023', points: '+10' },
    { activity: 'Redeem - Coffee Voucher', date: 'Jul 28, 2023', points: '-50' },
    { activity: 'Account Anniversary', date: 'Jul 15, 2023', points: '+100' },
    { activity: 'Referral Bonus', date: 'Jul 02, 2023', points: '+150' },
  ];
  
  const rewardOffers = [
    { title: 'Coffee Voucher', points: 50, icon: <Coffee className="h-8 w-8 text-amber-600" /> },
    { title: 'Shopping Discount', points: 100, icon: <ShoppingBag className="h-8 w-8 text-emerald-600" /> },
    { title: 'Gift Card', points: 250, icon: <GiftIcon className="h-8 w-8 text-red-600" /> },
    { title: 'Travel Miles', points: 500, icon: <Plane className="h-8 w-8 text-blue-600" /> },
  ];

  return (
    <DashboardLayout pageTitle="Reward Points">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-katina">Reward Points</h2>
          <p className="text-muted-foreground">Earn and redeem points for exciting rewards</p>
        </div>
        
        {/* Reward Points Card - Moved from Dashboard */}
        <motion.div variants={fadeInUp} initial="hidden" animate="visible">
          <EnhancedCard variant="glass" animation="float" className="overflow-hidden">
            <div className="relative py-6 px-6 flex flex-col md:flex-row items-center justify-between bg-gradient-to-r from-kojaYellow/90 to-kojaYellow text-kojaDark">
              <div className="z-10 mb-4 md:mb-0">
                <h3 className="text-2xl font-bold mb-2">Your Reward Points</h3>
                <p className="text-kojaDark/80 max-w-md">Earn points on every transaction. Redeem for exciting rewards.</p>
              </div>
              <div className="flex items-center justify-center p-4">
                <div className="h-24 w-24 rounded-full bg-white flex items-center justify-center shadow-xl">
                  <span className="text-3xl font-bold text-[#1231b8]">750</span>
                </div>
              </div>
            </div>
          </EnhancedCard>
        </motion.div>
        
        {/* How to Earn Card */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5 text-kojaYellow" />
              How to Earn Points
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 rounded-lg border border-gray-100 bg-white">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-blue-100 p-3 rounded-full mb-3">
                    <ShoppingBag className="h-6 w-6 text-blue-600" />
                  </div>
                  <h4 className="font-medium">Transactions</h4>
                  <p className="text-xs text-gray-500 mt-1">1 point for every ₦2,000 spent</p>
                </div>
              </div>
              
              <div className="p-4 rounded-lg border border-gray-100 bg-white">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-green-100 p-3 rounded-full mb-3">
                    <Gift className="h-6 w-6 text-green-600" />
                  </div>
                  <h4 className="font-medium">Referrals</h4>
                  <p className="text-xs text-gray-500 mt-1">150 points for each successful referral</p>
                </div>
              </div>
              
              <div className="p-4 rounded-lg border border-gray-100 bg-white">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-purple-100 p-3 rounded-full mb-3">
                    <Award className="h-6 w-6 text-purple-600" />
                  </div>
                  <h4 className="font-medium">Special Events</h4>
                  <p className="text-xs text-gray-500 mt-1">Bonus points on account anniversary</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Available Rewards Card */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
          <CardHeader>
            <CardTitle>Available Rewards</CardTitle>
            <CardDescription>Redeem your points for these exciting offers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {rewardOffers.map((offer, index) => (
                <div key={index} className="border border-gray-100 rounded-lg p-4 bg-white hover:shadow-md transition-shadow">
                  <div className="flex flex-col items-center text-center">
                    <div className="mb-3">
                      {offer.icon}
                    </div>
                    <h4 className="font-medium">{offer.title}</h4>
                    <p className="text-sm text-kojaGray">{offer.points} points</p>
                    <Button variant="ghost" size="sm" className="mt-2 text-xs">
                      Redeem
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button variant="outline" className="text-kojaPrimary">
              View All Rewards <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </CardFooter>
        </Card>
        
        {/* Reward History Card */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
          <CardHeader>
            <CardTitle>Reward Activity</CardTitle>
            <CardDescription>Recent point earning and redemption history</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rewardHistory.map((item, index) => (
                <div key={index} className="flex justify-between items-center border-b border-gray-100 pb-3 last:border-0 last:pb-0">
                  <div>
                    <p className="font-medium">{item.activity}</p>
                    <p className="text-xs text-gray-500">{item.date}</p>
                  </div>
                  <div className={`font-semibold ${item.points.startsWith('+') ? 'text-green-600' : 'text-red-600'}`}>
                    {item.points}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button variant="outline">
              View Full History
            </Button>
          </CardFooter>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default RewardPoints;
