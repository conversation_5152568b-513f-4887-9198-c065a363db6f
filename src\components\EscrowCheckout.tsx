
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { ShieldCheck, ArrowRight, CheckCircle2, Package, Truck, Banknote, User, AlertCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { EnhancedCard } from '@/components/ui/enhanced-card';

type EscrowStage = 'initial' | 'payment' | 'processing' | 'complete' | 'failed';

interface EscrowCheckoutProps {
  children: React.ReactNode;
  productName: string;
  productPrice: number;
  sellerName: string;
}

const EscrowCheckout: React.FC<EscrowCheckoutProps> = ({ 
  children, 
  productName, 
  productPrice,
  sellerName
}) => {
  const [open, setOpen] = useState(false);
  const [stage, setStage] = useState<EscrowStage>('initial');
  const [progress, setProgress] = useState(0);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const { toast } = useToast();

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      // Reset the state when closing
      setTimeout(() => {
        setStage('initial');
        setProgress(0);
        setAgreedToTerms(false);
        setDeliveryAddress('');
      }, 300);
    }
    setOpen(isOpen);
  };

  const handleInitiatePurchase = () => {
    if (!agreedToTerms || !deliveryAddress) {
      toast({
        title: "Required Fields Missing",
        description: "Please fill in delivery address and agree to the terms.",
        variant: "destructive",
      });
      return;
    }
    setStage('payment');
  };

  const handleStartPayment = () => {
    setStage('processing');
    
    // Simulate payment processing
    const interval = setInterval(() => {
      setProgress((prev) => {
        const newProgress = prev + 5;
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            // Randomly succeed or fail for demo purposes
            if (Math.random() > 0.2) {
              setStage('complete');
            } else {
              setStage('failed');
            }
          }, 500);
        }
        return Math.min(newProgress, 100);
      });
    }, 200);
  };

  const handleClose = () => {
    handleOpenChange(false);
  };

  const handleRetry = () => {
    setStage('payment');
    setProgress(0);
  };

  // Calculate fees
  const fees = productPrice * 0.02; // 2% escrow fee
  const totalAmount = productPrice + fees;

  // Render different stages
  const renderContent = () => {
    switch (stage) {
      case 'initial':
        return (
          <div className="space-y-4">
            <Card className="border border-kojaPrimary/20">
              <CardContent className="p-4 space-y-4">
                <div className="flex justify-between items-center">
                  <div className="font-medium">{productName}</div>
                  <div className="font-bold">₦{productPrice.toLocaleString()}</div>
                </div>
                <Separator />
                <div className="text-sm text-muted-foreground">
                  <div className="flex justify-between items-center mb-2">
                    <div>Seller:</div>
                    <div className="font-medium">{sellerName}</div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div>Escrow Fee (2%):</div>
                    <div className="font-medium">₦{fees.toLocaleString()}</div>
                  </div>
                </div>
                <Separator />
                <div className="flex justify-between items-center font-bold">
                  <div>Total:</div>
                  <div>₦{totalAmount.toLocaleString()}</div>
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="delivery-address">Delivery Address</Label>
                <Input 
                  id="delivery-address" 
                  placeholder="Enter your delivery address"
                  value={deliveryAddress}
                  onChange={(e) => setDeliveryAddress(e.target.value)}
                  className="bg-white"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="terms" 
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => {
                    setAgreedToTerms(checked === true);
                  }}
                />
                <label 
                  htmlFor="terms" 
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I agree to the <a href="/escrow/terms" className="text-kojaPrimary hover:underline" target="_blank">escrow terms & conditions</a>
                </label>
              </div>
            </div>
            
            <div className="pt-4">
              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
                disabled={!agreedToTerms || !deliveryAddress}
                onClick={handleInitiatePurchase}
              >
                Secure Purchase with Escrow
              </Button>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-lg border border-kojaPrimary/10">
              <div className="flex items-start gap-2">
                <ShieldCheck className="h-5 w-5 text-kojaPrimary mt-0.5" />
                <p className="text-xs text-muted-foreground">
                  Our escrow service holds your payment until you confirm receipt of the item in good condition. This protects both buyers and sellers.
                </p>
              </div>
            </div>
          </div>
        );
        
      case 'payment':
        return (
          <div className="space-y-4">
            <EnhancedCard variant="gradient" animation="none" className="p-4">
              <div className="space-y-3">
                <div className="text-center">
                  <h3 className="font-medium text-lg mb-1">Payment Details</h3>
                  <p className="text-sm text-muted-foreground">Complete your purchase securely</p>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Product:</div>
                  <div className="font-medium">{productName}</div>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Seller:</div>
                  <div>{sellerName}</div>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Product Price:</div>
                  <div>₦{productPrice.toLocaleString()}</div>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Escrow Fee:</div>
                  <div>₦{fees.toLocaleString()}</div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between items-center font-bold">
                  <div>Total Amount:</div>
                  <div>₦{totalAmount.toLocaleString()}</div>
                </div>
              </div>
            </EnhancedCard>
            
            <div className="bg-gray-50 p-3 rounded-lg border border-kojaPrimary/10">
              <div className="flex items-start gap-2">
                <ShieldCheck className="h-5 w-5 text-kojaPrimary mt-0.5" />
                <div className="text-xs text-muted-foreground">
                  <p className="mb-1">How our escrow service works:</p>
                  <ol className="list-decimal pl-4 space-y-1">
                    <li>You pay now and we hold the funds securely</li>
                    <li>Seller ships the product to your delivery address</li>
                    <li>You inspect the product and confirm receipt</li>
                    <li>We release the payment to the seller</li>
                  </ol>
                </div>
              </div>
            </div>
            
            <div className="pt-2">
              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
                onClick={handleStartPayment}
              >
                Pay ₦{totalAmount.toLocaleString()} <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </div>
        );
        
      case 'processing':
        return (
          <div className="space-y-6 py-4">
            <div className="text-center">
              <h3 className="font-medium text-lg mb-1">Processing Payment</h3>
              <p className="text-sm text-muted-foreground mb-4">Please wait while we process your payment</p>
              
              <div className="w-full bg-gray-100 h-2 rounded-full overflow-hidden mb-1">
                <div 
                  className="h-full bg-kojaPrimary rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <p className="text-xs text-muted-foreground">{progress}% complete</p>
            </div>
            
            <div className="flex flex-col space-y-4">
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${progress >= 25 ? 'bg-kojaPrimary text-white' : 'bg-gray-100'}`}>
                  <User className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">Account Verification</p>
                  <p className="text-xs text-muted-foreground">Verifying your account details</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${progress >= 50 ? 'bg-kojaPrimary text-white' : 'bg-gray-100'}`}>
                  <Banknote className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">Payment Processing</p>
                  <p className="text-xs text-muted-foreground">Processing your payment</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${progress >= 75 ? 'bg-kojaPrimary text-white' : 'bg-gray-100'}`}>
                  <ShieldCheck className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">Escrow Setup</p>
                  <p className="text-xs text-muted-foreground">Setting up escrow protection</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${progress >= 100 ? 'bg-kojaPrimary text-white' : 'bg-gray-100'}`}>
                  <Package className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">Order Confirmation</p>
                  <p className="text-xs text-muted-foreground">Confirming your order</p>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 'complete':
        return (
          <div className="space-y-4 py-4">
            <div className="text-center">
              <div className="bg-green-50 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                <CheckCircle2 className="h-8 w-8 text-green-500" />
              </div>
              <h3 className="font-medium text-lg mb-1">Purchase Successful!</h3>
              <p className="text-sm text-muted-foreground mb-4">Your escrow purchase has been completed</p>
            </div>
            
            <Card className="border border-kojaPrimary/20">
              <CardContent className="p-4 space-y-3">
                <div className="text-center font-medium">{productName}</div>
                <Separator />
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Order ID:</div>
                  <div className="font-mono text-sm">ES-{Math.floor(Math.random() * 1000000)}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Amount:</div>
                  <div>₦{totalAmount.toLocaleString()}</div>
                </div>
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Status:</div>
                  <div className="text-green-600 text-sm font-medium">Paid & Protected</div>
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-3">
              <div className="flex items-start gap-2 bg-amber-50 p-3 rounded-lg border border-amber-200">
                <Truck className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-amber-800">Next Steps</p>
                  <p className="text-xs text-amber-700">
                    The seller will ship your item to {deliveryAddress}. You'll receive updates on your order status.
                  </p>
                </div>
              </div>
              
              <div className="pt-2 space-y-2">
                <Button 
                  className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
                  onClick={() => window.location.href = '/escrow'}
                >
                  View Order in Escrow Dashboard
                </Button>
                <Button 
                  variant="outline"
                  className="w-full"
                  onClick={handleClose}
                >
                  Close
                </Button>
              </div>
            </div>
          </div>
        );
        
      case 'failed':
        return (
          <div className="space-y-4 py-4">
            <div className="text-center">
              <div className="bg-red-50 rounded-full h-16 w-16 flex items-center justify-center mx-auto mb-4">
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
              <h3 className="font-medium text-lg mb-1">Payment Failed</h3>
              <p className="text-sm text-muted-foreground mb-4">There was an issue processing your payment</p>
            </div>
            
            <div className="bg-red-50 p-4 rounded-lg border border-red-200">
              <p className="text-sm text-red-800 mb-2">
                Your payment could not be processed due to one of the following reasons:
              </p>
              <ul className="list-disc pl-5 text-xs text-red-700 space-y-1">
                <li>Insufficient funds in your account</li>
                <li>Connection timeout during payment processing</li>
                <li>Card issuer declined the transaction</li>
                <li>Technical issue with the payment gateway</li>
              </ul>
            </div>
            
            <div className="pt-2 space-y-2">
              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
                onClick={handleRetry}
              >
                Try Again
              </Button>
              <Button 
                variant="outline"
                className="w-full"
                onClick={handleClose}
              >
                Cancel
              </Button>
            </div>
          </div>
        );
    }
  };

  return (
    <>
      <div onClick={() => setOpen(true)}>
        {children}
      </div>
      
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="max-w-md mx-auto">
          <DialogHeader>
            <DialogTitle>
              {stage === 'initial' && 'Secure Purchase with Escrow'}
              {stage === 'payment' && 'Complete Payment'}
              {stage === 'processing' && 'Processing Payment'}
              {stage === 'complete' && 'Purchase Successful'}
              {stage === 'failed' && 'Payment Failed'}
            </DialogTitle>
            {stage === 'initial' && (
              <DialogDescription>
                Shop safely with our escrow protection service
              </DialogDescription>
            )}
          </DialogHeader>
          
          {renderContent()}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EscrowCheckout;
