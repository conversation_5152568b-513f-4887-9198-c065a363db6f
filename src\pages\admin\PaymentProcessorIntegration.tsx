import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Toggle } from '@/components/ui/toggle';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle2, AlertCircle, CreditCard, Globe, Server, Shield, Link2, Check, X, Settings, Activity, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface IntegrationStatusProps {
  title: string;
  description: string;
  status: 'active' | 'inactive' | 'pending';
}

const IntegrationStatus: React.FC<IntegrationStatusProps> = ({ title, description, status }) => {
  let statusIcon;
  let statusColor;

  switch (status) {
    case 'active':
      statusIcon = <CheckCircle2 className="h-5 w-5 text-green-500 mr-2" />;
      statusColor = "text-green-500";
      break;
    case 'inactive':
      statusIcon = <AlertCircle className="h-5 w-5 text-red-500 mr-2" />;
      statusColor = "text-red-500";
      break;
    case 'pending':
      statusIcon = <Activity className="h-5 w-5 text-yellow-500 mr-2 animate-spin" />;
      statusColor = "text-yellow-500";
      break;
    default:
      statusIcon = null;
      statusColor = "";
  }

  return (
    <Card className="shadow-md">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center">
          {statusIcon}
          {title}
        </CardTitle>
        <div className={`uppercase text-xs font-bold ${statusColor}`}>
          {status}
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
};

const PaymentProcessorIntegration = () => {
  const { toast } = useToast();
  const [isLive, setIsLive] = useState(false);
  const [apiKey, setApiKey] = useState('sk_test_XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX');
  const [webhookUrl, setWebhookUrl] = useState('https://your-app.com/webhooks');
  const [isWebhookEnabled, setIsWebhookEnabled] = useState(true);

  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApiKey(e.target.value);
  };

  const handleWebhookUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setWebhookUrl(e.target.value);
  };

  const toggleWebhook = () => {
    setIsWebhookEnabled(!isWebhookEnabled);
    toast({
      title: "Webhook Status Updated",
      description: isWebhookEnabled ? "Webhooks disabled" : "Webhooks enabled",
    });
  };

  const saveSettings = () => {
    toast({
      title: "Settings Saved",
      description: "Your payment processor settings have been saved.",
    });
  };

  return (
    <AdminLayout pageTitle="Payment Processor Integration">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-[#1E293B]">Payment Processor Integration</h1>
        <p className="text-[#64748B]">Configure and manage your payment processor integration</p>
      </div>

      <Tabs defaultValue="settings" className="space-y-4">
        <TabsList className="grid grid-cols-2 w-full max-w-md">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="status">Status</TabsTrigger>
        </TabsList>

        <TabsContent value="settings" className="space-y-4">
          <Card className="shadow-md">
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>Configure your payment processor details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="api-key">API Key</Label>
                <Input id="api-key" value={apiKey} onChange={handleApiKeyChange} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="webhook-url">Webhook URL</Label>
                <Input id="webhook-url" value={webhookUrl} onChange={handleWebhookUrlChange} />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="webhook-enabled" className="mr-2">Enable Webhooks</Label>
                <Switch id="webhook-enabled" checked={isWebhookEnabled} onCheckedChange={toggleWebhook} />
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings}>Save Changes</Button>
            </CardFooter>
          </Card>

          <Card className="shadow-md">
            <CardHeader>
              <CardTitle>Advanced Settings</CardTitle>
              <CardDescription>Customize advanced integration options</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="live-mode" className="mr-2">Live Mode</Label>
                <Toggle id="live-mode" pressed={isLive} onPressedChange={setIsLive} />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <IntegrationStatus
              title="API Connection"
              description="Status of the connection to the payment processor API"
              status="active"
            />
            <IntegrationStatus
              title="Webhook Delivery"
              description="Status of webhook delivery to your application"
              status={isWebhookEnabled ? "active" : "inactive"}
            />
            <IntegrationStatus
              title="SSL Certificate"
              description="SSL certificate status for secure communication"
              status="active"
            />
            <IntegrationStatus
              title="Data Encryption"
              description="Status of data encryption during transmission"
              status="active"
            />
          </div>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default PaymentProcessorIntegration;
