import * as React from 'react';
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { User, Building2, Moon, Sun, HelpCircle, UserPlus, Building, Phone, Baby, Users, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Index = () => {
  const [darkMode, setDarkMode] = useState(false);
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };
  return <div className={`min-h-screen bg-gradient-to-b from-blue-50 to-white dark:from-[#09125a] dark:to-[#1231B8] text-slate-900 dark:text-white relative overflow-hidden ${darkMode ? 'dark' : ''}`}>
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,transparent)] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,transparent,white)]" />
      
      <header className="container mx-auto px-4 py-6 flex justify-between items-center relative z-10">
        <div className="flex items-center gap-2">
          <img alt="KojaPay Logo" className="w-8 h-8 object-contain" src="/lovable-uploads/037922b1-235b-42f7-86e4-7a09b957d9ee.png" />
          <span className="font-bold text-xl">KojaPay</span>
        </div>
        <div className="flex items-center gap-4">
          <button onClick={toggleDarkMode} className="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-white/10 transition-colors" aria-label="Toggle dark mode">
            {darkMode ? <Sun className="h-5 w-5 text-yellow-400" /> : <Moon className="h-5 w-5 text-slate-700" />}
          </button>
          <Button variant="ghost" size="icon">
            <HelpCircle className="h-5 w-5" />
          </Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12 md:py-20 relative z-10">
        <div className="max-w-2xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 tracking-tight">
            Banking Made Simple for Everyone
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-300">
            Experience seamless digital banking with enhanced security and innovative features for the whole family.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {/* Adult Banking - Consolidated Login */}
            <div className="bg-white/80 dark:bg-white/10 backdrop-blur-xl p-8 rounded-[40px] shadow-xl border border-slate-200 dark:border-white/20">
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-semibold mb-2"></h2>
                  <p className="text-slate-600 dark:text-slate-300">Full-featured banking for adults</p>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-center">ACCOUNT LOGIN</h3>
                  <Link to="/unified-login" className="group block">
                    <div className="w-full p-6 rounded-[40px] bg-gradient-to-r from-[#1231B8] to-[#1231B8]/80 text-white hover:from-[#1231B8]/90 hover:to-[#1231B8]/70 transition-all duration-300 shadow-lg hover:shadow-xl">
                      <div className="flex items-center justify-center space-x-4">
                        <User className="h-8 w-8" />
                        <div className="text-center">
                          <span className="text-xl font-bold block">LOGIN HERE</span>
                          <span className="text-sm opacity-90">Personal & Business Access</span>
                        </div>
                        <Building2 className="h-8 w-8" />
                      </div>
                    </div>
                  </Link>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-4">New to KojaPay?</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <Link to="/create-account" className="group">
                      <div className="h-full p-4 rounded-[20px] bg-slate-50 dark:bg-white/5 border border-slate-200 dark:border-white/10 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                        <div className="flex flex-col items-center">
                          <UserPlus className="h-6 w-6 mb-2 text-blue-600 dark:text-blue-400" />
                          <span className="font-medium">Personal</span>
                        </div>
                      </div>
                    </Link>
                    <Link to="/create-account" className="group">
                      <div className="h-full p-4 rounded-[20px] bg-slate-50 dark:bg-white/5 border border-slate-200 dark:border-white/10 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                        <div className="flex flex-col items-center">
                          <Building className="h-6 w-6 mb-2 text-blue-600 dark:text-blue-400" />
                          <span className="font-medium">Business</span>
                        </div>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Kids Banking */}
            <div className="bg-white/80 dark:bg-white/10 backdrop-blur-xl p-8 rounded-[40px] shadow-xl border border-slate-200 dark:border-white/20 flex flex-col justify-between">
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-2xl font-semibold mb-2 flex items-center justify-center gap-2"><Baby className="h-7 w-7 text-pink-500" /> Kids Banking</h2>
                  <p className="text-slate-600 dark:text-slate-300">Safe, fun, and secure banking for children</p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-center">CHILD LOGIN</h3>
                  <Link to="/kids-login" className="group block">
                    <div className="w-full p-6 rounded-[40px] bg-gradient-to-r from-pink-500 to-pink-400 text-white hover:from-pink-600 hover:to-pink-500 transition-all duration-300 shadow-lg hover:shadow-xl">
                      <div className="flex items-center justify-center space-x-4">
                        <Baby className="h-8 w-8" />
                        <div className="text-center">
                          <span className="text-xl font-bold block">KIDS LOGIN</span>
                          <span className="text-sm opacity-90">For children with a KojaPay Kids account</span>
                        </div>
                      </div>
                    </div>
                  </Link>
                </div>
                <div className="mt-6 text-center">
                  <Link to="/parental-control" className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 font-medium hover:underline">
                    <Users className="h-5 w-5" /> Parental Control
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-center space-x-4 mt-8">
            <a href="https://apps.apple.com/app/kojapay/id1234567890" target="_blank" rel="noopener noreferrer" className="transform transition-transform hover:scale-105">
              <Button variant="outline" className="flex items-center gap-2 rounded-[40px]">
                <Phone className="h-5 w-5" />
                App Store
              </Button>
            </a>
            <a href="https://play.google.com/store/apps/details?id=com.kojapay.mobile" target="_blank" rel="noopener noreferrer" className="transform transition-transform hover:scale-105">
              <Button variant="outline" className="flex items-center gap-2 rounded-[40px]">
                <Phone className="h-5 w-5" />
                Google Play
              </Button>
            </a>
          </div>

          <p className="text-center text-sm text-slate-600 dark:text-slate-400 mt-6">
            By proceeding, you agree to our{' '}
            <Link to="/terms" className="text-blue-600 dark:text-blue-400 hover:underline">Terms</Link>
            {' '}and{' '}
            <Link to="/privacy" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</Link>
          </p>
        </div>
      </main>
    </div>;
};
export default Index;
