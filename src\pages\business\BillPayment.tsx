
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Tv, 
  Smartphone, 
  Wifi, 
  Zap, 
  Globe, 
  Droplet, 
  School, 
  AirplayIcon, 
  Building, 
  Gamepad2,
  ChevronRight
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useNavigate } from 'react-router-dom';
import TransactionStatus from '@/components/TransactionStatus';

interface BillCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  suggestedAmounts?: number[];
}

const BusinessBillPayment = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [selectedBiller, setSelectedBiller] = useState<string | null>(null);
  const [amount, setAmount] = useState('');
  const [customerId, setCustomerId] = useState('');
  const [showSuccess, setShowSuccess] = useState(false);
  
  const billCategories: BillCategory[] = [
    { id: 'tv', name: 'Cable TV', icon: <Tv size={24} />, color: 'bg-[#1231b8] text-white' },
    { id: 'internet', name: 'Internet', icon: <Wifi size={24} />, color: 'bg-[#fde314] text-kojaDark', suggestedAmounts: [5000, 8000, 10000, 15000] },
    { id: 'electricity', name: 'Electricity', icon: <Zap size={24} />, color: 'bg-[#1231b8] text-white' },
    { id: 'water', name: 'Water', icon: <Droplet size={24} />, color: 'bg-[#fde314] text-kojaDark' },
    { id: 'mobile', name: 'Mobile Data', icon: <Smartphone size={24} />, color: 'bg-[#1231b8] text-white', suggestedAmounts: [1000, 2000, 3000, 5000] },
    { id: 'education', name: 'Education', icon: <School size={24} />, color: 'bg-[#fde314] text-kojaDark' },
    { id: 'subscription', name: 'Subscriptions', icon: <AirplayIcon size={24} />, color: 'bg-[#1231b8] text-white' },
    { id: 'tax', name: 'Tax & Levies', icon: <Building size={24} />, color: 'bg-[#fde314] text-kojaDark' },
    { id: 'gaming', name: 'Gaming', icon: <Gamepad2 size={24} />, color: 'bg-[#1231b8] text-white' },
    { id: 'airtime', name: 'Airtime', icon: <Smartphone size={24} />, color: 'bg-[#fde314] text-kojaDark', suggestedAmounts: [100, 200, 500, 1000, 2000, 5000] },
    { id: 'other', name: 'Others', icon: <Globe size={24} />, color: 'bg-[#1231b8] text-white' },
  ];
  
  const billers = {
    tv: ['DSTV', 'GOTV', 'StarTimes', 'ShowMax'],
    internet: ['Spectranet', 'Swift', 'Smile', 'iPNX'],
    electricity: ['IKEDC', 'EKEDC', 'AEDC', 'PHEDC', 'JEDC'],
    water: ['Lagos Water', 'Abuja Water Board', 'Kaduna Water'],
    mobile: ['MTN', 'Airtel', 'Glo', '9Mobile'],
    education: ['WAEC', 'JAMB', 'School Fees'],
    subscription: ['Netflix', 'Spotify', 'YouTube Premium'],
    tax: ['Company Tax', 'VAT', 'PAYE'],
    gaming: ['Steam', 'PlayStation', 'Xbox'],
    airtime: ['MTN', 'Airtel', 'Glo', '9Mobile'],
    other: ['Charity', 'Associations', 'Clubs']
  };
  
  const handleCategorySelect = (categoryId: string) => {
    setActiveCategory(categoryId);
    setSelectedBiller(null);
  };
  
  const handleBillerSelect = (biller: string) => {
    setSelectedBiller(biller);
  };

  // Get the appropriate label for customer ID field based on category
  const getCustomerIdLabel = () => {
    if (!activeCategory) return "Customer ID / Reference Number";
    
    if (['internet', 'mobile', 'airtime'].includes(activeCategory)) {
      return "Phone Number";
    } else if (activeCategory === 'electricity') {
      return "Meter Number";
    } else {
      return "Customer ID / Reference Number";
    }
  };

  // Get suggested amounts for the selected category
  const getSuggestedAmounts = () => {
    if (!activeCategory) return [];
    const category = billCategories.find(cat => cat.id === activeCategory);
    return category?.suggestedAmounts || [];
  };
  
  const handlePayBill = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || !customerId || !selectedBiller) {
      toast({
        title: "Missing Information",
        description: "Please fill all required fields",
        variant: "destructive"
      });
      return;
    }
    
    // Simulate payment processing
    toast({
      title: "Processing Payment",
      description: "Please wait while we process your payment",
    });
    
    // Simulate successful payment after 2 seconds
    setTimeout(() => {
      setShowSuccess(true);
      toast({
        title: "Payment Successful",
        description: `Your payment of ₦${parseInt(amount).toLocaleString()} to ${selectedBiller} has been processed`,
        variant: "success"
      });
    }, 2000);
  };
  
  const handleMakeAnotherPayment = () => {
    setShowSuccess(false);
    setActiveCategory(null);
    setSelectedBiller(null);
    setAmount('');
    setCustomerId('');
  };

  return (
    <BusinessLayout pageTitle="Bill Payment">
      <div className="space-y-6">
        {showSuccess ? (
          <Card className="shadow-lg">
            <CardContent className="pt-6">
              <div className="flex flex-col items-center justify-center py-4">
                <TransactionStatus 
                  status="success" 
                  message={`Your payment of ₦${parseInt(amount).toLocaleString()} to ${selectedBiller} has been processed successfully.`} 
                />
                
                <div className="flex gap-4 mt-8">
                  <Button 
                    variant="outline" 
                    onClick={handleMakeAnotherPayment}
                    className="shadow-sm hover:shadow-md"
                  >
                    Make Another Payment
                  </Button>
                  <Button 
                    onClick={() => navigate('/business/transactions')}
                    className="shadow-sm hover:shadow-md"
                  >
                    View Transactions
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            <Tabs defaultValue="single" className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="single">Single Bill Payment</TabsTrigger>
                <TabsTrigger value="bulk">Bulk Payment</TabsTrigger>
              </TabsList>
              
              <TabsContent value="single">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="md:col-span-1">
                    <Card className="shadow-lg hover:shadow-xl transition-all duration-300">
                      <CardHeader>
                        <CardTitle>Bill Categories</CardTitle>
                        <CardDescription>Select a category</CardDescription>
                      </CardHeader>
                      <CardContent className="grid grid-cols-2 sm:grid-cols-1 gap-3">
                        {billCategories.map(category => (
                          <Button
                            key={category.id}
                            variant="outline"
                            className={`justify-start h-auto p-3 shadow-sm hover:shadow-md ${activeCategory === category.id ? 'border-[#1231b8] bg-[#1231b8]/5' : ''}`}
                            onClick={() => handleCategorySelect(category.id)}
                          >
                            <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-3 ${category.color}`}>
                              {category.icon}
                            </div>
                            <span className="font-roboto-condensed">{category.name}</span>
                          </Button>
                        ))}
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="md:col-span-2">
                    {activeCategory ? (
                      <div className="space-y-6">
                        <Card className="shadow-lg hover:shadow-xl transition-all duration-300">
                          <CardHeader>
                            <CardTitle>Select Biller</CardTitle>
                            <CardDescription>Choose your service provider</CardDescription>
                          </CardHeader>
                          <CardContent className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                            {billers[activeCategory as keyof typeof billers].map(biller => (
                              <Button
                                key={biller}
                                variant="outline"
                                className={`h-auto py-3 justify-center shadow-sm hover:shadow-md ${selectedBiller === biller ? 'border-[#1231b8] bg-[#1231b8]/5' : ''}`}
                                onClick={() => handleBillerSelect(biller)}
                              >
                                {biller}
                              </Button>
                            ))}
                          </CardContent>
                        </Card>
                        
                        {selectedBiller && (
                          <Card className="shadow-lg hover:shadow-xl transition-all duration-300">
                            <CardHeader>
                              <CardTitle>Payment Details</CardTitle>
                              <CardDescription>Enter your payment information for {selectedBiller}</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <form onSubmit={handlePayBill} className="space-y-4">
                                <div className="space-y-2">
                                  <Label htmlFor="customerId">{getCustomerIdLabel()}</Label>
                                  <Input
                                    id="customerId"
                                    placeholder={`Enter ${getCustomerIdLabel().toLowerCase()}`}
                                    value={customerId}
                                    onChange={(e) => setCustomerId(e.target.value)}
                                  />
                                </div>
                                
                                <div className="space-y-2">
                                  <Label htmlFor="amount">Amount (₦)</Label>
                                  
                                  {getSuggestedAmounts().length > 0 && (
                                    <div className="flex flex-wrap gap-2 mb-2">
                                      {getSuggestedAmounts().map(suggestedAmount => (
                                        <Button 
                                          key={suggestedAmount}
                                          type="button" 
                                          variant="outline" 
                                          size="sm"
                                          className="rounded-full shadow-sm hover:shadow-md"
                                          onClick={() => setAmount(suggestedAmount.toString())}
                                        >
                                          ₦{suggestedAmount.toLocaleString()}
                                        </Button>
                                      ))}
                                    </div>
                                  )}
                                  
                                  <Input
                                    id="amount"
                                    type="number"
                                    placeholder="Enter amount"
                                    value={amount}
                                    onChange={(e) => setAmount(e.target.value)}
                                  />
                                </div>
                                
                                <Button type="submit" className="w-full bg-[#1231b8] shadow-md hover:shadow-lg">
                                  Pay Now
                                </Button>
                              </form>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    ) : (
                      <Card className="h-full flex items-center justify-center text-center p-6 shadow-lg">
                        <div>
                          <Globe className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                          <h3 className="text-lg font-medium font-roboto-condensed mb-2">Select a Bill Category</h3>
                          <p className="text-gray-500 font-roboto">
                            Choose a category from the left panel to view available billers
                          </p>
                        </div>
                      </Card>
                    )}
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="bulk">
                <Card className="shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <CardTitle>Bulk Bill Payment</CardTitle>
                    <CardDescription>Pay multiple bills at once</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-col items-center justify-center py-10 text-center">
                      <p className="mb-4 font-roboto">Upload a CSV file with payment details or use our template</p>
                      <div className="flex flex-wrap gap-4 justify-center">
                        <Button variant="outline" className="shadow-sm hover:shadow-md">
                          Download Template
                        </Button>
                        <Button className="shadow-sm hover:shadow-md">
                          Upload CSV
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="bg-[#1231b8] text-white shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium font-roboto-condensed">Recent Payments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold font-roboto-condensed">₦120,000</div>
                  <div className="text-sm mt-1 flex justify-between items-center">
                    <span>Last 30 days</span>
                    <Button variant="ghost" size="sm" className="text-white p-0 h-auto">
                      View All <ChevronRight size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-[#fde314] text-kojaDark shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium font-roboto-condensed">Scheduled Payments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold font-roboto-condensed">12</div>
                  <div className="text-sm mt-1 flex justify-between items-center">
                    <span>Upcoming bills</span>
                    <Button variant="ghost" size="sm" className="text-kojaDark p-0 h-auto">
                      Manage <ChevronRight size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-[#1231b8] text-white shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium font-roboto-condensed">Saved Billers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold font-roboto-condensed">8</div>
                  <div className="text-sm mt-1 flex justify-between items-center">
                    <span>Quick access</span>
                    <Button variant="ghost" size="sm" className="text-white p-0 h-auto">
                      View All <ChevronRight size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-[#fde314] text-kojaDark shadow-lg hover:shadow-xl transition-all duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium font-roboto-condensed">Bill Reminders</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold font-roboto-condensed">3</div>
                  <div className="text-sm mt-1 flex justify-between items-center">
                    <span>Due this week</span>
                    <Button variant="ghost" size="sm" className="text-kojaDark p-0 h-auto">
                      Configure <ChevronRight size={16} />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </BusinessLayout>
  );
};

export default BusinessBillPayment;
