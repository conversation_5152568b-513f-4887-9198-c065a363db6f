import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { User } from '../user/entities/user.entity';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import {
  PhysicalCardProviders,
  PhysicalCardStatus,
} from './dto/physicalCardCreate.dto';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Between,
  IsNull,
  LessThanOrEqual,
  MoreThanOrEqual,
  Not,
  Repository,
} from 'typeorm';
import { WalletService } from '../wallet/wallet.service';
import { Transaction } from '../transaction/entities/transaction.entity';
import * as mime from 'mime-types';
import {
  TransactionPurpose,
  TransactionStatus,
  TransactionType,
} from '../transaction/dto/transaction.dto';
import { PhysicalCard } from './entities/physical-card.entity';
import { DebitResponse } from 'src/utilities/transactions/debit-transaction';
import { RequestContextService } from 'src/utilities/request-context.service';
import { HelperService } from '../helper/helper.service';
import { ActivityService } from '../activity/activity.service';
import { FindPhysicalCardDto } from './dto/find-physical-card.dto';
import { PageOptionsDto } from 'src/utilities/pagination/dtos';
import { PageMetaDto } from 'src/utilities/pagination/page-meta.dto';
import { PageDto } from 'src/utilities/pagination/page.dto';
import { SummaryPhysicalCardDto } from './dto/summary-physical-card.dto';
import { KojapayCardService } from './providers/kojapay/kojapay-card.service';

@Injectable()
export class PhysicalCardService {
  constructor(
    private httpService: HttpService,
    private _wallet: WalletService,
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Transaction)
    private transactionRepository: Repository<Transaction>,
    private kojapay: KojapayCardService,
    @InjectRepository(PhysicalCard)
    private physicalCardRepository: Repository<PhysicalCard>,
    private requestContext: RequestContextService,
    private helperService: HelperService,
    private activityService: ActivityService,
  ) {}
  
  async createCard(user: User) {
    const currency = 'NGN';
    const cardCreationFee = 1000;
    // Check if the user already has a physical card
    const cardExist = await this.physicalCardRepository.findOne({
      where: { userId: user.id },
    });

    if (cardExist) {
      throw new ForbiddenException('User already has a physical card');
    }

    // Charge the user for the card creation
    const transaction = await this.chargeUser({
      user,
      currency,
      amount: cardCreationFee,
      narration: 'physical/card/create',
    });

    try {
      let card = await this.physicalCardRepository.findOneBy({
        userId: IsNull(),
        provider: await this.paymentProvider(),
      });
      if (card) {
        await this.physicalCardRepository.update(card.id, {
          userId: user.id,
        });
      } else {
        // Request the physical card
        const requestedCardReference = await (
          await this.provider()
        ).requestCard(user);

        // Create and save the physical card details in the repository
        const cardCreate = this.physicalCardRepository.create({
          reference: requestedCardReference,
          provider: await this.paymentProvider(),
          userId: user.id,
          currency: 'NGN',
          status: 'pending',
        });
        card = await this.physicalCardRepository.save(cardCreate);
      }

      // Update the user to indicate they now have a USD physical card
      await this.userRepository.update(user.id, {
        physicalCard: true,
      });

      // Return the created physical card details
      return { reference: card.reference, status: 'pending' };
    } catch (error) {
      // Log the error
      await this.activityService.error(error, this.constructor.name, 'createCard');

      const card = await this.physicalCardRepository.findOneBy({
        userId: user.id,
      });
      if (!card) {
        // Credit the user's wallet if an error occurred
        await this._wallet.credit({
          business: this.requestContext.business,
          user,
          currency: transaction.currency,
          amount: transaction.amountCharged,
          pending: false,
        });

        // Delete the transaction
        await this.transactionRepository.delete(transaction.id);
      }

      // Throw an error indicating the card creation process failed
      throw new BadRequestException(error);
    }
  }

  /**
   * Charges a user by debiting their wallet and creating a transaction record.
   *
   * @param {Object} data - The data required to charge the user.
   * @param {User} data.user - The user to be charged.
   * @param {string} data.currency - The currency in which the amount is to be charged.
   * @param {number} data.amount - The amount to be charged.
   * @param {string} data.narration - The narration for the transaction.
   * @param {number} data.fee - The fee for the transaction.
   * @param {number} data.rate - The exchange rate for the transaction.
   * @returns {Promise<Transaction>} The created transaction record.
   * @throws {Error} If there is an error during the wallet debit or transaction creation.
   */
  async chargeUser(data: {
    user: User;
    currency: string;
    amount: number;
    narration: string;
  }): Promise<Transaction> {
    const amountCharged = data.amount;
    const amountSettled = data.amount;

    // Debit the user's wallet
    const debit: DebitResponse = await this._wallet.debit(
      {
        business: this.requestContext.business,
        user: data.user,
        amount: amountCharged,
        currency: data.currency,
        pending: false,
      }
    );

    // Create the transaction record
    const reference = 'trx' + this.helperService.generateRandomAlphaNum(22);
    const trx: Transaction = {
      userId: data.user.id,
      amount: Number(data.amount.toFixed(2)),
      amountCharged: Number(amountCharged.toFixed(2)),
      amountSettled: Number(amountSettled.toFixed(2)),
      currency: data.currency,
      reference,
      providerReference: reference,
      type: TransactionType.DEBIT,
      purpose: TransactionPurpose.PHYSICAL_CARD_CREATION,
      status: TransactionStatus.SUCCESS,
      _status: TransactionStatus.SUCCESS,
      narration: data.narration,
      paymentProvider: await this.paymentProvider(),
      fee: 0,
      balanceBefore: debit ? debit.balanceBefore : null,
      balanceAfter: debit ? debit.balanceAfter : null,
    };

    // Create the transaction in the database
    return await this.transactionRepository.save(trx);
  }

 
  async fund(user: User, cardRef: string, amount: number): Promise<any> {
    // Validate that the user is the owner of the card
    const card = await this.validateOwner(user.id, cardRef);

    const currency = 'NGN';

    // Charge the user for the transaction
    const transaction = await this.chargeUser({
      user,
      currency,
      amount,
      narration: `physical/card/fund/${cardRef}`,
    });

    try {
      const response = await (
        await this.provider()
      ).fund(user, amount, cardRef);

      // Return the response data from the card funding request
      return response;
    } catch (error) {
      // Credit the user's wallet and delete the transaction if the request fails
      await this._wallet.credit(
        {
          business: this.requestContext.business,
          user,
          amount: transaction.amountCharged,
          currency: transaction.currency,
          pending: false,
        }
      );
      await this.transactionRepository.delete(transaction.id);

      // Throw an error indicating the card funding request failed
      throw new BadRequestException('Card funding failed');
    }
  }

  async findOne(
    user: User,
    cardRef: string,
    source: 'internal' | 'external' = null,
  ): Promise<PhysicalCard> {
    const card = await this.validateOwner(user?.id, cardRef);

    if (source === 'internal') {
      card.details = card.details
        ? JSON.parse(await this.helperService.decrypt(card.details))
        : card.details;
      return card;
    }

    user = card.user;

    return await (
      await this.provider()
    ).findOne(cardRef);
  }

  /**
   * Validates the ownership of a physical card by checking the user ID and card reference.
   *
   * @param {number | null} userId - The ID of the user to validate.
   * @param {string} reference - The reference of the card to validate.
   * @throws {ForbiddenException} If the user does not have access to the card.
   */
  async validateOwner(
    userId: string = null,
    reference: string,
  ): Promise<PhysicalCard> {
    const card = await this.physicalCardRepository.findOne({
      where: { reference },
      relations: ['user'],
    });
    if (!card) {
      throw new NotFoundException('invalid card reference');
    }
    if (userId) {
      if (card.userId != userId) {
        throw new ForbiddenException('you have no access to this resources');
      }
    }
    return card;
  }

  /**
   * Retrieves the details of a user's card by its ID.
   *
   * @param {User} user - The user whose card details are being retrieved.
   * @param {number} id - The ID of the card to be retrieved.
   * @returns {Promise<any>} The card details.
   * @throws {NotFoundException} If the card does not exist.
   * @throws {BadRequestException} If there is an error during the card retrieval request.
   */
  async findOneById(user: User, id: string): Promise<PhysicalCard> {
    // Find the card data by its ID
    const cardData = await this.physicalCardRepository.findOneBy({ id });

    // Throw an error if the card does not exist
    if (!cardData) {
      throw new NotFoundException('card does not exist');
    }

    // Retrieve and return the card details using its reference
    return (await this.findOne(
      user,
      cardData.reference,
      'internal',
    )) as PhysicalCard;
  }

  /**
   * Retrieves a paginated list of physical cards based on the provided filters.
   *
   * @param {FindPhysicalCardDto} filter - The filter criteria for retrieving physical cards.
   * @param {PageOptionsDto} pageOptionsDto - The pagination options for the request.
   * @returns {Promise<PageDto>} The paginated list of physical cards.
   */
  async findAll(filter: FindPhysicalCardDto, pageOptionsDto: PageOptionsDto) {
    // Create a query builder for retrieving physical cards with the provided filters
    const cards = this.physicalCardRepository
      .createQueryBuilder('card')
      .where(
        filter.userId ? 'card.userId = :userId' : 'card.userId IS NOT NULL',
        filter.userId ? { userId: filter.userId } : {},
      )
      .andWhere(
        filter.requested_delete
          ? 'card.requestedDelete = :requestedDelete'
          : '1=1',
        {
          requested_delete: filter.requested_delete,
        },
      )
      .andWhere(
        filter.requested_freeze
          ? 'card.requestedFreeze = :requestedFreeze'
          : '1=1',
        {
          requested_freeze: filter.requested_freeze,
        },
      )
      .andWhere(
        filter.requested_activation
          ? 'card.requestedActivation = :requestedActivation'
          : '1=1',
        {
          requested_activation: filter.requested_activation,
        },
      )
      .andWhere(filter.status ? 'card.status = :status' : '1=1', {
        status: filter.status,
      })
      .andWhere(filter.from ? `card.createdAt >= :fromDate` : '1=1', {
        fromDate: filter.from,
      })
      .andWhere(filter.to ? `card.createdAt <= :toDate` : '1=1', {
        toDate: filter.to,
      })
      .leftJoinAndSelect('card.user', 'user')
      .orderBy('card.createdAt', pageOptionsDto.order)
      .skip(pageOptionsDto.skip)
      .take(pageOptionsDto.take);

    // Get the total count of items matching the filters
    const itemCount = await cards.getCount();

    // Get the entities and raw data from the query
    let { entities } = await cards.getRawAndEntities();

    entities = await Promise.all(
      entities.map(async (entity: PhysicalCard) => {
        entity.details = entity.details
          ? JSON.parse(await this.helperService.decrypt(entity.details))
          : entity.details;
        if (entity.details) {
          delete (entity.details as any).pan;
        }
        return entity;
      }),
    );

    // Create pagination metadata
    const pageMetaDto = new PageMetaDto({ itemCount, pageOptionsDto });

    // Return the paginated list of physical cards
    return new PageDto(entities, pageMetaDto);
  }

  /**
   * Converts an image from a URL to a base64 encoded string.
   *
   * @param {string} url - The URL of the image to convert.
   * @returns {Promise<string>} A promise that resolves to the base64 encoded string of the image.
   */
  async getBase64Image(url: string): Promise<string> {
    const blob = await firstValueFrom(
      this.httpService.get(url, {
        responseType: 'arraybuffer',
      }),
    );
    const mimeType = mime.lookup(url);
    const base64data = Buffer.from(blob.data).toString('base64');
    const base64WithMime = `data:${mimeType};base64,${base64data}`;
    return base64WithMime;
  }

  /**
   * Freezes a physical card by updating its status to 'suspended'.
   *
   * @param {number} id - The ID of the physical card to freeze.
   * @returns {Promise<UpdateResult>} A promise that resolves to the update result.
   */
  async freeze(id: number) {
    return await this.physicalCardRepository.update(id, {
      status: 'suspended',
      requestedFreeze: false,
    });
  }

  /**
   * Activates a physical card by updating its status to 'active'.
   *
   * @param {number} id - The ID of the physical card to activate.
   * @returns {Promise<UpdateResult>} A promise that resolves to the update result.
   */
  async activate(id: number) {
    return await this.physicalCardRepository.update(id, {
      status: 'active',
      requestedActivation: false,
    });
  }

  /**
   * Requests to freeze a physical card by setting the requested_freeze flag to true.
   *
   * @param {User} user - The user requesting the freeze.
   * @param {number} id - The ID of the physical card to freeze.
   * @returns {Promise<UpdateResult>} A promise that resolves to the update result.
   */
  async requestFreeze(user: User, id: string) {
    const card = await this.findOneById(user, id);
    if (card.status != PhysicalCardStatus.active) {
      throw new BadRequestException('physical card is currently not active');
    }
    const frozen = await (
      await this.provider()
    ).deactivate(card.reference, user);
    await this.physicalCardRepository.update(id, {
      requestedFreeze: frozen.status ? true : false,
      status: frozen.status ? PhysicalCardStatus.suspended : card.status,
    });
  }

  /**
   * Requests to activate a physical card by setting the requested_activation flag to true.
   *
   * @param {User} user - The user requesting the activation.
   * @param {number} id - The ID of the physical card to activate.
   * @returns {Promise<UpdateResult>} A promise that resolves to the update result.
   */
  async requestActivate(user: User, id: string) {
    const card = await this.findOneById(user, id);
    // if (card.status != PhysicalCardStatus.suspended) {
    //   throw new BadRequestException('physical card is currently not suspended');
    // }
    const unfrozen = await (
      await this.provider()
    ).activate(card.reference, user);
    await this.physicalCardRepository.update(id, {
      requestedFreeze: unfrozen.status ? false : true,
      status: unfrozen.status ? PhysicalCardStatus.active : card.status,
    });
  }

  /**
   * Handles the notification of card creation by updating the card's status and details,
   * sending a notification to the user if the card has been activated.
   *
   * @param {Object} data - The event data containing:
   *   @property {string} event - The type of event (e.g., 'card_issuance.active').
   *   @property {string} reference - The unique reference identifier of the card.
   * @param {Function} resolve - Function to call upon successful processing.
   * @param {Function} reject - Function to call in case of an error.
   * @throws {Error} Throws an error if the card reference does not exist or if an error occurs during processing.
   */
  // @OnEvent('physical_card.update_history')
  // async notifyCardCreation(
  //   data: { event: string; reference: string; resetDeclineCount?: boolean },
  //   resolve: (response: any) => void,
  //   reject: (error: any) => void,
  // ) {
  //   try {
  //     const card = await this.physicalCardRepository.findOne({
  //       where: { reference: data.reference },
  //       relations: ['user'],
  //     });
  //     if (!card) {
  //       this.logger.debug(
  //         `card reference ${data.reference} does not exist`,
  //         this.constructor.name,
  //         'notifyCardCreation',
  //       );
  //       return;
  //     }
  //     const details = (await this.findOne(
  //       card.user,
  //       card.reference,
  //     )) as ProviderCardDetails;
  //     await this.physicalCardRepository.update(card.id, {
  //       decline_count: data.resetDeclineCount ? 0 : card.decline_count,
  //       status: details.status,
  //       requested_freeze:
  //         details.status == 'suspended' ? true : card.requested_freeze,
  //       balance: Number(details.balance),
  //       details: await this.helperService.encrypt(
  //         JSON.stringify({
  //           type: details.type,
  //           first_six: details.first_six,
  //           last_four: details.last_four,
  //           brand: details.brand,
  //           pan: details.pan,
  //           cvv: details.cvv,
  //           expiry_month: details.expiry_month,
  //           expiry_year: details.expiry_year,
  //           currency: details.currency,
  //           billing: details.billing,
  //         }),
  //       ),
  //     });
  //     await this.updateCardTransactions(card);
  //     if (data.event === 'card_issuance.active') {
  //       this._ng.sendCardCreationMail(card.user, `****${details.last_four}`);
  //     }
  //     if (data.event === 'card_issuance.suspended') {
  //       this._ng.sendCardSuspensionMail(card.user, `****${details.last_four}`);
  //     }
  //     resolve({ message: 'success', reference: data.reference });
  //   } catch (error) {
  //     reject(error);
  //   }
  // }

  /**
   * Retrieves all physical cards associated with users.
   *
   * @returns {Promise<PhysicalCard[]>} - A promise that resolves to a list of physical cards.
   */
  all() {
    return this.physicalCardRepository.findBy({ userId: Not(IsNull()) });
  }

  /**
   * Generates a summary of physical cards and transactions for a specific user or for all users.
   *
   * @param {number | null} userId - The ID of the user to filter the summary by (optional).
   * If null, the summary will include data for all users.
   * @returns {Promise<any>} - A promise that resolves to an object containing a summary of physical card counts, transactions, and balances.
   *
   * The returned object includes:
   * - `total`: The total number of physical cards.
   * - `active`: The number of active physical cards.
   * - `terminated`: The number of terminated physical cards.
   * - `transactions`: An object containing the transaction counts and sums, broken down by status and type.
   * - `balance`: The total balance of the user's physical cards (or all users).
   */
  async summary(filter: SummaryPhysicalCardDto) {
    const whereConditions: any = {};

    if (filter.from && filter.to) {
      whereConditions.createdAt = Between(filter.from, filter.to);
    } else if (filter.from) {
      whereConditions.createdAt = MoreThanOrEqual(filter.from);
    } else if (filter.to) {
      whereConditions.createdAt = LessThanOrEqual(filter.to);
    }
    const data = {
      total: await this.physicalCardRepository
        .createQueryBuilder('card')
        .leftJoin('card.user', 'user')
        .andWhere(filter.userId ? 'card.userId = :userId' : '1=1', {
          userId: filter.userId,
        })
        .andWhere(filter.from ? `card.createdAt >= :fromDate` : '1=1', {
          fromDate: filter.from,
        })
        .andWhere(filter.to ? `card.createdAt <= :toDate` : '1=1', {
          toDate: filter.to,
        })
        .getCount(),
      active: await this.physicalCardRepository
        .createQueryBuilder('card')
        .leftJoin('card.user', 'user')
        .andWhere(filter.userId ? 'card.userId = :userId' : '1=1', {
          userId: filter.userId,
        })
        .andWhere(filter.from ? `card.createdAt >= :fromDate` : '1=1', {
          fromDate: filter.from,
        })
        .andWhere(filter.to ? `card.createdAt <= :toDate` : '1=1', {
          toDate: filter.to,
        })
        .andWhere('card.status = :status', {
          status: PhysicalCardStatus.active,
        })
        .getCount(),
      terminated: await this.physicalCardRepository
        .createQueryBuilder('card')
        .leftJoin('card.user', 'user')
        .andWhere(filter.userId ? 'card.userId = :userId' : '1=1', {
          userId: filter.userId,
        })
        .andWhere(filter.from ? `card.createdAt >= :fromDate` : '1=1', {
          fromDate: filter.from,
        })
        .andWhere(filter.to ? `card.createdAt <= :toDate` : '1=1', {
          toDate: filter.to,
        })
        .andWhere('card.status = :status', {
          status: PhysicalCardStatus.terminated,
        })
        .getCount(),
      balance: await this.getCardBalance(filter.userId),
    };
    return data;
  }


  /**
   * Retrieves the total balance of all physical cards for a specific user or all users.
   *
   * @param {number | null} userId - The ID of the user (optional). If null, retrieves for all users.
   * @returns {Promise<number>} - A promise that resolves to the total balance of physical cards.
   */
  async getCardBalance(userId: number) {
    const balance = await this.physicalCardRepository
      .createQueryBuilder('card')
      .innerJoin('card.user', 'user') // Join with User entity
      .select('COALESCE(SUM(card.balance), 0)', 'sum')
      .andWhere(userId ? 'card.userId = :userId' : '1=1', { userId }) // Optional filter for userId
      .getRawOne();

    return parseFloat(balance.sum) || 0;
  }

  async paymentProvider() {
    return PhysicalCardProviders.kojapay;
  }

  async provider() {
    const provider = await this.paymentProvider();
    if (provider) {
      switch (provider) {
        case PhysicalCardProviders.kojapay:
          return this.kojapay;
        default:
          throw new BadRequestException('No provider found');
      }
    }
  }

  async simulateCharge(user: User, cardRef: string, amount: number) {
    const card = (await this.findOne(user, cardRef, 'internal')) as PhysicalCard;
    if (card.provider != PhysicalCardProviders.kojapay) {
      throw new BadRequestException('invalid card');
    }
    return await this.kojapay.charge(card, amount);
  }

  public async getCardsForUser(userId: string) {
    return this.physicalCardRepository.find({ where: { userId } });
  }
}
