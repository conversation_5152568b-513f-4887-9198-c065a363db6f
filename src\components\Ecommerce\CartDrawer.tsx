
import React from 'react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { ShoppingCart as CartIcon } from 'lucide-react';
import ShoppingCart from './ShoppingCart';
import { useCart } from '@/contexts/CartContext';

interface CartDrawerProps {
  children?: React.ReactNode;
}

const CartDrawer: React.FC<CartDrawerProps> = ({ children }) => {
  const { cart, isCartOpen, setIsCartOpen } = useCart();

  const handleClose = () => {
    setIsCartOpen(false);
  };

  // Using optional chaining to safely access cart properties in case they're not fully loaded
  const totalItems = cart?.items?.length > 0 
    ? cart.items.reduce((total, item) => total + item.quantity, 0) 
    : 0;

  return (
    <Sheet open={isCartOpen} onOpenChange={setIsCartOpen}>
      <SheetTrigger asChild>
        {children || (
          <Button variant="ghost" size="icon" className="relative">
            <CartIcon className="h-5 w-5" />
            {totalItems > 0 && (
              <span className="absolute -top-1 -right-1 bg-kojaPrimary text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">
                {totalItems > 99 ? '99+' : totalItems}
              </span>
            )}
          </Button>
        )}
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-md p-0">
        <div className="h-full p-4">
          <ShoppingCart isDrawer onClose={handleClose} />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default CartDrawer;
