
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Landmark, Loader2 } from "lucide-react";
import { useNavigate } from 'react-router-dom';
import LoanEligibilityCard from './loan/LoanEligibilityCard';
import LoanAmountSelector from './loan/LoanAmountSelector';
import LoanSummaryCard from './loan/LoanSummaryCard';

interface LoansDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountNumber: string;
}

const LoansDialog = ({ open, onOpenChange, accountNumber }: LoansDialogProps) => {
  const navigate = useNavigate();
  const [amount, setAmount] = useState<number>(50000);
  const [term, setTerm] = useState<number>(3);
  const [isApplying, setIsApplying] = useState<boolean>(false);
  
  const minAmount = 10000;
  const maxAmount = 500000;
  const minTerm = 1;
  const maxTerm = 12;
  const interestRate = 15;
  const monthlyRate = interestRate / 12 / 100;
  
  const calculateMonthlyPayment = () => {
    const monthlyPayment = amount * (monthlyRate * Math.pow(1 + monthlyRate, term)) / (Math.pow(1 + monthlyRate, term) - 1);
    return Math.round(monthlyPayment);
  };
  
  const calculateTotalPayable = () => {
    return calculateMonthlyPayment() * term;
  };
  
  const calculateTotalInterest = () => {
    return calculateTotalPayable() - amount;
  };
  
  const handleAmountChange = (newValue: number[]) => {
    setAmount(newValue[0]);
  };
  
  const handleTermChange = (newValue: number[]) => {
    setTerm(newValue[0]);
  };
  
  const handleApplyLoan = () => {
    setIsApplying(true);
    setTimeout(() => {
      setIsApplying(false);
      console.log("Loan Application Submitted: Your loan application has been received and is under review");
      onOpenChange(false);
      navigate('/loans');
    }, 2000);
  };
  
  const formatCurrency = (value: number) => {
    return `₦${value.toLocaleString()}`;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white/95 backdrop-blur-xl border border-[#D3E4FD] shadow-lg rounded-[20px] sm:max-w-[520px] w-[95%] mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-unica text-gray-900 flex items-center">
            <Landmark className="mr-2 text-kojaPrimary" size={20} /> 
            Personal Loan
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Get quick access to funds for your personal needs
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <LoanEligibilityCard />
          
          <div className="space-y-6">
            <LoanAmountSelector 
              amount={amount}
              minAmount={minAmount}
              maxAmount={maxAmount}
              onAmountChange={handleAmountChange}
              formatCurrency={formatCurrency}
            />
            
            <LoanAmountSelector 
              amount={term}
              minAmount={minTerm}
              maxAmount={maxTerm}
              onAmountChange={handleTermChange}
              formatCurrency={(value) => `${value} ${value === 1 ? 'month' : 'months'}`}
            />
          </div>
          
          <LoanSummaryCard 
            monthlyPayment={calculateMonthlyPayment()}
            totalPayable={calculateTotalPayable()}
            totalInterest={calculateTotalInterest()}
            firstPaymentDate={new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)}
            formatCurrency={formatCurrency}
          />
          
          <Button 
            className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
            onClick={handleApplyLoan}
            disabled={isApplying}
          >
            {isApplying ? (
              <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
            ) : (
              <><Landmark className="mr-2 h-4 w-4" /> Apply for Loan</>
            )}
          </Button>
          
          <p className="text-xs text-center text-gray-500">
            By proceeding, you agree to our loan terms and conditions. 
            Approval is subject to creditworthiness and eligibility criteria.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoansDialog;
