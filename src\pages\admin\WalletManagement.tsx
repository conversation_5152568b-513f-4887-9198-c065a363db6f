
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  LockIcon, 
  UnlockIcon,
  Wallet,
  User,
  DollarSign,
  Calendar,
  AlertTriangle,
  CheckCircle,
  AlertCircle,
  Clock,
  HistoryIcon,
  Store,
  X
} from 'lucide-react';

const WalletManagement = () => {
  const wallets = [
    { 
      id: 'WAL001', 
      accountNumber: '**********',
      owner: '<PERSON>',
      type: 'Personal',
      balance: '$5,247.35',
      currency: 'USD',
      status: 'Active',
      createdDate: '2023-01-15'
    },
    { 
      id: 'WAL002', 
      accountNumber: '**********',
      owner: 'Sarah Johnson',
      type: 'Personal',
      balance: '$1,852.67',
      currency: 'USD',
      status: 'Active',
      createdDate: '2022-11-20'
    },
    { 
      id: 'WAL003', 
      accountNumber: '**********',
      owner: 'TechSolutions Inc.',
      type: 'Business',
      balance: '$28,541.90',
      currency: 'USD',
      status: 'Frozen',
      createdDate: '2023-02-05'
    },
    { 
      id: 'WAL004', 
      accountNumber: '**********',
      owner: 'Green Grocers',
      type: 'Business',
      balance: '$12,369.45',
      currency: 'USD',
      status: 'Active',
      createdDate: '2023-03-10'
    },
    { 
      id: 'WAL005', 
      accountNumber: '**********',
      owner: 'Alice Cooper',
      type: 'Personal',
      balance: '$374.21',
      currency: 'USD',
      status: 'Dormant',
      createdDate: '2022-08-12'
    },
  ];

  return (
    <AdminLayout pageTitle="Wallet Management">
      <Helmet>
        <title>Wallet Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Wallets</CardTitle>
              <CardDescription>Currently in use</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">1,243</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Frozen</CardTitle>
              <CardDescription>Temporarily suspended</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">18</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Dormant</CardTitle>
              <CardDescription>Inactive accounts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">57</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Balance</CardTitle>
              <CardDescription>All accounts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">$4.2M</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Wallet Management</CardTitle>
              <CardDescription>Manage user and business wallets across the platform</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Create Wallet
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search by owner name or account number..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <CheckCircle size={12} />
                  <span>Active</span>
                  <X size={12} className="ml-1" />
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <User size={12} />
                  <span>Personal</span>
                  <X size={12} className="ml-1" />
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>User wallets on the platform</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Wallet ID</TableHead>
                    <TableHead>Account Number</TableHead>
                    <TableHead>Owner</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Balance</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {wallets.map((wallet) => (
                    <TableRow key={wallet.id}>
                      <TableCell className="font-medium">{wallet.id}</TableCell>
                      <TableCell>{wallet.accountNumber}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {wallet.type === 'Personal' ? 
                            <User size={16} className="text-blue-600" /> : 
                            <Store size={16} className="text-purple-600" />
                          }
                          {wallet.owner}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {wallet.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 font-semibold">
                          <DollarSign size={14} />
                          {wallet.balance}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          wallet.status === 'Active' ? 'outline' : 
                          wallet.status === 'Frozen' ? 'destructive' : 
                          'secondary'
                        }>
                          {wallet.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                          {wallet.status === 'Frozen' && <AlertCircle size={12} className="mr-1" />}
                          {wallet.status === 'Pending' && <Clock size={12} className="mr-1" />}
                          {wallet.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{wallet.createdDate}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <HistoryIcon className="mr-2 h-4 w-4" />
                              <span>Transaction History</span>
                            </DropdownMenuItem>
                            {wallet.status === 'Active' ? (
                              <DropdownMenuItem className="text-amber-600">
                                <LockIcon className="mr-2 h-4 w-4" />
                                <span>Freeze Wallet</span>
                              </DropdownMenuItem>
                            ) : wallet.status === 'Frozen' ? (
                              <DropdownMenuItem className="text-green-600">
                                <UnlockIcon className="mr-2 h-4 w-4" />
                                <span>Unfreeze Wallet</span>
                              </DropdownMenuItem>
                            ) : null}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <AlertTriangle className="mr-2 h-4 w-4" />
                              <span>Flag for Review</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default WalletManagement;
