
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  Clock, 
  XCircle,
  MessageSquare,
  HeadphonesIcon,
  Users,
  UserCircle,
  MessageCircle,
  HelpCircle,
  FileText,
  ThumbsUp,
  ThumbsDown,
  User,
  Phone,
  AlarmClock
} from 'lucide-react';

const HelpSupportManagement = () => {
  const tickets = [
    { 
      id: 'TKT001', 
      subject: 'Cannot access my account', 
      category: 'Login Issues',
      user: 'John Smith',
      userType: 'Personal',
      priority: 'High',
      status: 'Open',
      createdTime: '2023-06-15 09:45',
      waitTime: '12 minutes',
      assignedTo: 'Unassigned'
    },
    { 
      id: 'TKT002', 
      subject: 'Failed transaction but money deducted', 
      category: 'Transaction Issues',
      user: 'TechSolutions Inc.',
      userType: 'Business',
      priority: 'Critical',
      status: 'In Progress',
      createdTime: '2023-06-15 08:30',
      waitTime: '5 minutes',
      assignedTo: 'Sarah Johnson'
    },
    { 
      id: 'TKT003', 
      subject: 'How to upgrade my account?', 
      category: 'Account Management',
      user: 'Robert Wilson',
      userType: 'Personal',
      priority: 'Medium',
      status: 'In Progress',
      createdTime: '2023-06-14 15:20',
      waitTime: '2 minutes',
      assignedTo: 'David Thompson'
    },
    { 
      id: 'TKT004', 
      subject: 'Disputing a fraudulent charge', 
      category: 'Fraud',
      user: 'Alice Cooper',
      userType: 'Personal',
      priority: 'High',
      status: 'Open',
      createdTime: '2023-06-15 10:15',
      waitTime: '8 minutes',
      assignedTo: 'Unassigned'
    },
    { 
      id: 'TKT005', 
      subject: 'API integration questions', 
      category: 'Technical Support',
      user: 'Global Traders Ltd',
      userType: 'Business',
      priority: 'Medium',
      status: 'Resolved',
      createdTime: '2023-06-12 13:45',
      waitTime: 'N/A',
      assignedTo: 'Michael Chen'
    },
  ];

  const faqArticles = [
    {
      id: 'FAQ001',
      title: 'How to reset your password',
      category: 'Account Access',
      views: 2543,
      helpful: '95%',
      lastUpdated: '2023-05-20'
    },
    {
      id: 'FAQ002',
      title: 'Understanding transaction fees',
      category: 'Billing',
      views: 1879,
      helpful: '88%',
      lastUpdated: '2023-06-01'
    },
    {
      id: 'FAQ003',
      title: 'Setting up two-factor authentication',
      category: 'Security',
      views: 1564,
      helpful: '92%',
      lastUpdated: '2023-04-15'
    },
    {
      id: 'FAQ004',
      title: 'Business account verification process',
      category: 'Verification',
      views: 1232,
      helpful: '90%',
      lastUpdated: '2023-05-10'
    }
  ];

  return (
    <AdminLayout pageTitle="Help & Support Management">
      <Helmet>
        <title>Help & Support Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Open Tickets</CardTitle>
              <CardDescription>Awaiting response</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">48</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">In Progress</CardTitle>
              <CardDescription>Being handled</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">35</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Resolved Today</CardTitle>
              <CardDescription>Successfully closed</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">62</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Avg. Response Time</CardTitle>
              <CardDescription>Today's average</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">8 min</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="tickets" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="tickets">Support Tickets</TabsTrigger>
            <TabsTrigger value="knowledge">Knowledge Base</TabsTrigger>
            <TabsTrigger value="agents">Support Agents</TabsTrigger>
          </TabsList>
          
          <TabsContent value="tickets">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Support Tickets</CardTitle>
                  <CardDescription>Manage customer support requests</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Assign Tickets
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search tickets..." 
                      className="pl-9"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <XCircle size={12} className="text-red-500" />
                      <span>Open</span>
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <UserCircle size={12} />
                      <span>Personal</span>
                    </Badge>
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>List of support tickets</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Ticket ID</TableHead>
                        <TableHead>Subject</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Wait Time</TableHead>
                        <TableHead>Assigned To</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {tickets.map((ticket) => (
                        <TableRow key={ticket.id}>
                          <TableCell className="font-medium">{ticket.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <MessageCircle size={16} className="text-blue-600" />
                              {ticket.subject}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {ticket.category}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {ticket.userType === 'Personal' ? (
                                <UserCircle size={14} className="text-blue-600" />
                              ) : (
                                <Users size={14} className="text-purple-600" />
                              )}
                              {ticket.user}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              ticket.priority === 'Low' ? 'outline' : 
                              ticket.priority === 'Medium' ? 'secondary' : 
                              ticket.priority === 'High' ? 'destructive' : 
                              'destructive'
                            }>
                              {ticket.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              ticket.status === 'Resolved' ? 'outline' : 
                              ticket.status === 'In Progress' ? 'secondary' : 
                              'destructive'
                            }>
                              {ticket.status === 'Resolved' && <CheckCircle size={12} className="mr-1 text-green-600" />}
                              {ticket.status === 'In Progress' && <Clock size={12} className="mr-1" />}
                              {ticket.status === 'Open' && <XCircle size={12} className="mr-1" />}
                              {ticket.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <AlarmClock size={14} className={
                                ticket.status === 'Resolved' ? 'text-green-600' : 
                                parseInt(ticket.waitTime) > 10 ? 'text-red-500' : 
                                'text-amber-500'
                              } />
                              {ticket.waitTime}
                            </div>
                          </TableCell>
                          <TableCell>{ticket.assignedTo}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <MessageSquare className="mr-2 h-4 w-4" />
                                  <span>Respond</span>
                                </DropdownMenuItem>
                                {ticket.status !== 'Resolved' && (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                    <span className="text-green-600">Mark as Resolved</span>
                                  </DropdownMenuItem>
                                )}
                                {ticket.assignedTo === 'Unassigned' && (
                                  <DropdownMenuItem>
                                    <User className="mr-2 h-4 w-4 text-blue-600" />
                                    <span className="text-blue-600">Assign to Agent</span>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="knowledge">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Knowledge Base</CardTitle>
                  <CardDescription>Manage help articles and FAQs</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Add New Article
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search articles..." 
                      className="pl-9"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <HelpCircle size={12} />
                      <span>Account Access</span>
                    </Badge>
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>Knowledge base articles</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead>Helpful Rating</TableHead>
                        <TableHead>Last Updated</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {faqArticles.map((article) => (
                        <TableRow key={article.id}>
                          <TableCell className="font-medium">{article.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <FileText size={16} className="text-blue-600" />
                              {article.title}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {article.category}
                            </Badge>
                          </TableCell>
                          <TableCell>{article.views}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {parseInt(article.helpful) >= 90 ? (
                                <ThumbsUp size={14} className="text-green-600" />
                              ) : parseInt(article.helpful) >= 80 ? (
                                <ThumbsUp size={14} className="text-amber-500" />
                              ) : (
                                <ThumbsDown size={14} className="text-red-500" />
                              )}
                              {article.helpful}
                            </div>
                          </TableCell>
                          <TableCell>{article.lastUpdated}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <MessageSquare className="mr-2 h-4 w-4" />
                                  <span>Edit</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <XCircle className="mr-2 h-4 w-4" />
                                  <span>Unpublish</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="agents">
            <Card>
              <CardHeader>
                <CardTitle>Support Agents</CardTitle>
                <CardDescription>Manage your support team and workload</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <HeadphonesIcon size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Support Team Management</h3>
                  <p className="text-gray-500 mb-6">Manage agent scheduling, performance metrics, and team assignments</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Manage Support Team</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default HelpSupportManagement;
