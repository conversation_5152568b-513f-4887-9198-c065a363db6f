@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

@import "./styles/base.css";
@import "./styles/components.css";
@import "./styles/utilities.css";
@import "./styles/cards.css";
@import "./styles/animations.css";
@import "./styles/legal.css";

/* Custom font styles */
body {
  font-family: 'Poppins', sans-serif;
}

.font-futura {
  font-family: 'Poppins', sans-serif;
}

.business-name {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
}

.sidebar-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
}

.sidebar-item {
  font-family: 'Poppins', sans-serif;
}

.nav-label {
  font-family: 'Poppins', sans-serif;
}

/* Card specific font styles */
.card-title, 
.card-balance, 
.card-number, 
.card-holder,
.card-details,
.card-expiry {
  font-family: 'Poppins', sans-serif;
}

.card-holder-name {
  font-family: 'Poppins', sans-serif;
  letter-spacing: 1px;
}

/* Button text in card sections */
.card-button-text {
  font-family: 'Poppins', sans-serif;
}

/* PWA specific styles */
.pwa-mode body {
  /* Apply overscroll behavior to prevent bounce effects in PWA mode */
  overscroll-behavior: none;
  /* Add safe area insets for notched devices */
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari, Opera */
}

/* Admin layout specific styles */
.admin-content {
  /* Add padding to account for fixed navbar */
  padding-top: 4rem;
}

/* PWA install prompt styling */
.pwa-install-prompt {
  position: fixed;
  bottom: 1rem;
  left: 1rem;
  right: 1rem;
  background-color: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  padding: 1rem;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* NGN currency styling */
.ngn-currency {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Animate list item entries for better UX */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.list-item-enter {
  animation: fadeIn 0.3s ease forwards;
}

/* Fix for iOS PWA status bar */
@supports (padding-top: env(safe-area-inset-top)) {
  .pwa-mode .fixed-header {
    padding-top: env(safe-area-inset-top);
    height: calc(4rem + env(safe-area-inset-top));
  }
  
  .pwa-mode .admin-content {
    padding-top: calc(4rem + env(safe-area-inset-top));
  }
}
