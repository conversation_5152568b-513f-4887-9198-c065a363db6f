@tailwind utilities;

@layer utilities {
  .gradient-bg {
    @apply bg-gradient-to-br from-kojaPrimary/90 to-kojaPrimary;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-kojaPrimary to-banklyBlue bg-clip-text text-transparent;
  }
  
  .gradient-yellow-text {
    @apply bg-gradient-to-r from-kojaYellow to-amber-500 bg-clip-text text-transparent;
  }

  .notification-badge {
    @apply absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full text-white text-xs flex items-center justify-center;
  }

  .section-title {
    @apply text-lg font-roboto-condensed font-semibold text-kojaDark mb-4;
  }

  .section-subtitle {
    @apply text-sm text-kojaGray mb-6;
  }

  .warning-box {
    @apply bg-amber-50/90 backdrop-blur-sm border-l-4 border-amber-400 p-4 rounded-r-lg my-4;
  }

  .info-box {
    @apply bg-blue-50/90 backdrop-blur-sm border-l-4 border-blue-400 p-4 rounded-r-lg my-4;
  }

  .security-box {
    @apply bg-green-50/90 backdrop-blur-sm border-l-4 border-green-400 p-4 rounded-r-lg my-4;
  }
  
  /* Hide scrollbars but allow scrolling */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari, Opera */
  }
  
  /* Modern shadows */
  .shadow-soft {
    @apply shadow-[0_4px_14px_rgba(0,0,0,0.05)];
  }
  
  .shadow-hover {
    @apply hover:shadow-[0_6px_20px_rgba(0,0,0,0.08)] transition-shadow duration-300;
  }
  
  .shadow-card {
    @apply shadow-[0_5px_15px_rgba(0,0,0,0.06)];
  }
  
  /* Glass effects */
  .glass-effect {
    @apply bg-white/70 backdrop-blur-md border border-white/30 shadow-sm hover:shadow-md transition-all duration-300;
  }
  
  .glass-card-effect {
    @apply glass-effect rounded-xl;
  }
  
  .glass-morph {
    @apply bg-white/80 backdrop-blur-md border border-white/40 shadow-md hover:shadow-lg transition-all duration-300;
  }
  
  .glass-morph-dark {
    @apply bg-kojaDark/80 backdrop-blur-md border border-white/10 text-white shadow-md hover:shadow-lg transition-all duration-300;
  }
  
  .glass-button {
    @apply bg-white/60 backdrop-blur-sm border border-white/40 shadow-sm hover:shadow-md transition-all duration-300 hover:bg-white/80 active:scale-95;
  }
  
  /* Modern border radiuses */
  .rounded-modern {
    @apply rounded-2xl;
  }
  
  .rounded-soft {
    @apply rounded-xl;
  }
  
  /* Business specific utilities */
  .business-gradient-bg {
    @apply bg-gradient-to-br from-white to-gray-50;
  }
  
  .business-section-divider {
    @apply border-b border-gray-200 my-6;
  }
  
  .business-highlight-text {
    @apply text-kojaPrimary font-semibold;
  }
  
  /* Pure color utilities */
  .koja-yellow-bg {
    @apply bg-[#fde314];
  }
  
  .koja-yellow-text {
    @apply text-[#fde314];
  }
  
  .koja-yellow-border {
    @apply border-[#fde314];
  }
  
  /* Notification-specific utilities */
  .notification-unread {
    @apply border-l-4 border-kojaPrimary;
  }
  
  .business-notification-unread {
    @apply border-l-4 border-kojaYellow;
  }
  
  /* Staggered animation utilities */
  .stagger-children > * {
    @apply opacity-0;
    animation: stagger-fade-in 0.5s ease forwards;
  }
  
  .stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
  .stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
  .stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
  .stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
  .stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
  .stagger-children > *:nth-child(6) { animation-delay: 0.6s; }
  
  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-kojaPrimary/30 focus:ring-offset-2 transition-all duration-300;
  }
  
  /* Interaction utilities */
  .press-effect {
    @apply active:scale-95 transition-transform duration-300;
  }
  
  .hover-lift {
    @apply hover:translate-y-[-3px] transition-transform duration-300;
  }
  
  .hover-glow {
    @apply hover:shadow-[0_0_15px_rgba(18,49,184,0.3)] transition-shadow duration-300;
  }
  
  /* Modern button effects */
  .btn-scale {
    @apply transform transition-transform duration-200 hover:scale-105 active:scale-95;
  }
  
  .btn-lift {
    @apply transform transition-all duration-200 hover:-translate-y-1 hover:shadow-lg active:translate-y-0 active:shadow-md;
  }
  
  /* Masonry grid */
  .masonry-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
    grid-template-rows: masonry;
  }
  
  /* Dashboard card layouts */
  .dashboard-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }
  
  .dashboard-stats-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6;
  }
  
  /* Modern layout utilities */
  .center-content {
    @apply flex items-center justify-center;
  }
  
  .flex-between {
    @apply flex items-center justify-between;
  }
  
  .flex-center {
    @apply flex items-center justify-center;
  }
  
  /* Typography utilities */
  .text-heading {
    @apply font-roboto-condensed text-2xl md:text-3xl font-bold;
  }
  
  .text-subheading {
    @apply font-roboto-condensed text-xl font-semibold;
  }
  
  .text-body {
    @apply font-roboto text-base;
  }
  
  .text-caption {
    @apply font-roboto text-sm text-kojaGray;
  }
  
  /* Futuristic utilities */
  .backdrop-blur {
    @apply backdrop-blur-md;
  }
  
  .glass-backdrop {
    @apply backdrop-blur-md bg-white/80 border border-white/30;
  }
  
  .frosted-glass {
    @apply backdrop-blur-lg bg-white/60 border border-white/40;
  }
  
  .gradient-overlay {
    @apply bg-gradient-to-br from-white/10 to-white/30 backdrop-blur-sm;
  }
  
  .noise-bg {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
    @apply bg-blend-overlay opacity-5;
  }
  
  /* Card utilities */
  .soft-shadow {
    @apply shadow-[0_8px_30px_rgba(0,0,0,0.04)];
  }
  
  .medium-shadow {
    @apply shadow-[0_8px_30px_rgba(0,0,0,0.08)];
  }
  
  .strong-shadow {
    @apply shadow-[0_8px_30px_rgba(0,0,0,0.12)];
  }
  
  .hover-shadow-effect {
    @apply hover:shadow-[0_12px_40px_rgba(0,0,0,0.1)] transition-shadow duration-300;
  }
  
  /* QR code utilities */
  .qr-container {
    @apply p-4 bg-white/90 backdrop-blur-sm rounded-lg shadow-sm border border-gray-100;
  }
  
  /* KYC verification utilities */
  .kyc-verified {
    @apply text-green-600 flex items-center gap-1;
  }
  
  .kyc-pending {
    @apply text-amber-600 flex items-center gap-1;
  }
  
  .kyc-rejected {
    @apply text-red-600 flex items-center gap-1;
  }
  
  /* Invoice utilities */
  .invoice-status-paid {
    @apply bg-green-100/90 backdrop-blur-sm text-green-800 px-2 py-0.5 rounded text-xs;
  }
  
  .invoice-status-pending {
    @apply bg-amber-100/90 backdrop-blur-sm text-amber-800 px-2 py-0.5 rounded text-xs;
  }
  
  .invoice-status-overdue {
    @apply bg-red-100/90 backdrop-blur-sm text-red-800 px-2 py-0.5 rounded text-xs;
  }
  
  /* Hide scrollbar while maintaining functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}
