
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Smartphone, 
  Wifi, 
  Baby,
  ArrowLeft,
  CheckCircle
} from 'lucide-react';
import { Helmet } from 'react-helmet-async';
import { Link } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

const KidsBillPayment = () => {
  const [selectedService, setSelectedService] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const services = [
    { id: 'airtime', name: 'Airtime Top-up', icon: Smartphone },
    { id: 'data', name: 'Data Bundle', icon: Wifi },
  ];

  const providers = [
    { id: 'mtn', name: 'MTN', color: '#FFD700' },
    { id: 'glo', name: 'Glo', color: '#00B04F' },
    { id: 'airtel', name: 'Airtel', color: '#ED1C24' },
    { id: '9mobile', name: '9mobile', color: '#00A651' },
  ];

  const dataBundles = [
    { id: '100mb', name: '100MB - 1 Day', price: 50 },
    { id: '350mb', name: '350MB - 7 Days', price: 100 },
    { id: '1gb', name: '1GB - 30 Days', price: 300 },
    { id: '2gb', name: '2GB - 30 Days', price: 500 },
    { id: '5gb', name: '5GB - 30 Days', price: 1000 },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedService || !selectedProvider || !phoneNumber || !amount) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Mock payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'Payment Successful! 🎉',
        description: `${selectedService === 'airtime' ? 'Airtime' : 'Data'} purchase completed successfully`,
      });
      
      // Reset form
      setSelectedService('');
      setSelectedProvider('');
      setPhoneNumber('');
      setAmount('');
    } catch (error) {
      toast({
        title: 'Payment Failed',
        description: 'Please try again or contact support',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FDE314]/20 via-blue-50 to-[#1231B8]/20 p-4">
      <Helmet>
        <title>Kids Bill Payment | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <Link to="/kids-dashboard" className="inline-flex items-center justify-center mb-6">
            <ArrowLeft className="h-6 w-6 mr-2 text-[#1231B8]" />
            <span className="text-[#1231B8] font-medium">Back to Dashboard</span>
          </Link>
          <div className="flex items-center justify-center mb-4">
            <Baby className="h-12 w-12 text-[#1231B8] mr-3" />
            <h1 className="text-3xl font-bold text-[#1231B8]">Kids Bill Payment</h1>
          </div>
          <p className="text-gray-600">Buy airtime and data bundles safely</p>
        </div>

        {/* Service Selection */}
        <Card className="rounded-[40px] mb-6">
          <CardHeader>
            <CardTitle className="text-[#1231B8]">Select Service</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {services.map((service) => {
                const Icon = service.icon;
                return (
                  <button
                    key={service.id}
                    onClick={() => setSelectedService(service.id)}
                    className={`p-6 rounded-[20px] border-2 transition-all ${
                      selectedService === service.id
                        ? 'border-[#1231B8] bg-[#1231B8]/10'
                        : 'border-gray-200 hover:border-[#1231B8]/50'
                    }`}
                  >
                    <Icon className={`h-8 w-8 mx-auto mb-2 ${
                      selectedService === service.id ? 'text-[#1231B8]' : 'text-gray-500'
                    }`} />
                    <span className={`font-medium ${
                      selectedService === service.id ? 'text-[#1231B8]' : 'text-gray-700'
                    }`}>
                      {service.name}
                    </span>
                  </button>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {selectedService && (
          <Card className="rounded-[40px] mb-6">
            <CardHeader>
              <CardTitle className="text-[#1231B8]">Payment Details</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <Label htmlFor="provider">Network Provider</Label>
                  <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                    <SelectTrigger className="mt-1 rounded-[40px]">
                      <SelectValue placeholder="Select network provider" />
                    </SelectTrigger>
                    <SelectContent>
                      {providers.map((provider) => (
                        <SelectItem key={provider.id} value={provider.id}>
                          <div className="flex items-center">
                            <div 
                              className="w-4 h-4 rounded-full mr-2"
                              style={{ backgroundColor: provider.color }}
                            />
                            {provider.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    placeholder="08012345678"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="mt-1 rounded-[40px]"
                    maxLength={11}
                    required
                  />
                </div>

                {selectedService === 'data' ? (
                  <div>
                    <Label>Data Bundle</Label>
                    <div className="grid gap-3 mt-2">
                      {dataBundles.map((bundle) => (
                        <button
                          key={bundle.id}
                          type="button"
                          onClick={() => setAmount(bundle.price.toString())}
                          className={`p-4 rounded-[20px] border text-left transition-all ${
                            amount === bundle.price.toString()
                              ? 'border-[#1231B8] bg-[#1231B8]/10'
                              : 'border-gray-200 hover:border-[#1231B8]/50'
                          }`}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium">{bundle.name}</div>
                              <div className="text-sm text-gray-500">₦{bundle.price}</div>
                            </div>
                            {amount === bundle.price.toString() && (
                              <CheckCircle className="h-5 w-5 text-[#1231B8]" />
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div>
                    <Label htmlFor="amount">Amount (₦)</Label>
                    <Select value={amount} onValueChange={setAmount}>
                      <SelectTrigger className="mt-1 rounded-[40px]">
                        <SelectValue placeholder="Select amount" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="50">₦50</SelectItem>
                        <SelectItem value="100">₦100</SelectItem>
                        <SelectItem value="200">₦200</SelectItem>
                        <SelectItem value="500">₦500</SelectItem>
                        <SelectItem value="1000">₦1000</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 text-white py-3 rounded-[40px]"
                >
                  {isLoading ? 'Processing...' : `Pay ₦${amount || '0'}`}
                </Button>
              </form>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default KidsBillPayment;
