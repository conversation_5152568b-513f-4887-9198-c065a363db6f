
import React from 'react';
import { cn } from '@/lib/utils';

interface VirtualCardProps {
  cardNumber: string;
  name: string;
  expiryDate: string;
  type: 'personal' | 'business';
  variant: 'physical' | 'virtual';
}

const VirtualCard: React.FC<VirtualCardProps> = ({ 
  cardNumber, 
  name, 
  expiryDate, 
  type, 
  variant 
}) => {
  // Determine card color based on type and variant
  const getCardBackground = () => {
    if (type === 'personal') {
      return variant === 'physical' 
        ? 'bg-gradient-to-r from-kojaYellow to-yellow-500' 
        : 'bg-kojaYellow';
    } else {
      return variant === 'physical' 
        ? 'bg-gradient-to-r from-banklyBlue to-blue-600' 
        : variant === 'virtual' 
          ? 'bg-banklyBlue' 
          : 'bg-gradient-to-r from-banklyBlue to-kojaYellow';
    }
  };

  return (
    <div className={cn(
      'virtual-card w-full max-w-md h-56 relative rounded-xl overflow-hidden shadow-lg p-6 text-white font-katina transform transition-all duration-500 hover:scale-105',
      getCardBackground()
    )}>
      <div className="absolute top-0 left-0 w-full h-full opacity-10 pattern-dots"></div>
      
      {variant === 'virtual' && (
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
      )}
      
      <div className="absolute top-5 right-5 flex space-x-1">
        <div className="h-7 w-12 bg-white/20 rounded-md backdrop-blur-sm"></div>
      </div>
      
      <div className="flex flex-col justify-between h-full">
        <div className="flex justify-between items-start">
          <div>
            <img 
              src="/lovable-uploads/166f8337-467d-454c-92ec-7a2cbf29e615.png" 
              alt="KojaPay Logo" 
              className="w-10 h-10 mb-4" 
            />
            <div className="text-sm opacity-80">{type === 'personal' ? 'Personal' : 'Business'}</div>
            <div className="font-medium">{variant === 'physical' ? 'Debit Card' : 'Virtual Card'}</div>
          </div>
          <div className="h-10 w-10 rounded-full border-2 border-white/50"></div>
        </div>
        
        <div className="space-y-4">
          <div>
            <div className="text-xs opacity-70">Card Number</div>
            <div className="text-lg font-mono tracking-wider">{cardNumber}</div>
          </div>
          
          <div className="flex justify-between items-end">
            <div>
              <div className="text-xs opacity-70">Card Holder</div>
              <div className="font-medium">{name}</div>
            </div>
            <div>
              <div className="text-xs opacity-70">Expires</div>
              <div>{expiryDate}</div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="absolute bottom-4 right-6">
        <div className="flex gap-1">
          <div className="w-7 h-7 bg-red-500 rounded-full opacity-80"></div>
          <div className="w-7 h-7 bg-yellow-400 rounded-full opacity-80"></div>
        </div>
      </div>
      
      <div className="absolute top-3 right-20 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
        {variant === 'physical' ? 'Physical' : 'Virtual'}
      </div>
    </div>
  );
};

export default VirtualCard;
