
import React from 'react';
import { Avatar } from '@/components/ui/avatar';

interface TransactionItemProps {
  recipient: string;
  date: string;
  amount: string;
  status: 'pending' | 'success' | 'failed';
  initials?: string;
  image?: string;
}

const TransactionItem: React.FC<TransactionItemProps> = ({
  recipient,
  date,
  amount,
  status,
  initials,
  image
}) => {
  const getStatusBadge = () => {
    switch (status) {
      case 'pending':
        return <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Pending</span>;
      case 'success':
        return <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Success</span>;
      case 'failed':
        return <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Failed</span>;
      default:
        return null;
    }
  };

  return (
    <div className="grid grid-cols-3 items-center py-2 border-b border-gray-100 last:border-0">
      <div className="flex items-center gap-3">
        {image ? (
          <Avatar className="h-8 w-8">
            <img src={image} alt={recipient} />
          </Avatar>
        ) : (
          <Avatar className="h-8 w-8 bg-gray-100 text-kojaDark">
            <span className="text-xs font-medium">{initials}</span>
          </Avatar>
        )}
        <span className="text-sm font-medium truncate">{recipient}</span>
      </div>
      <div className="text-sm text-kojaGray">{date}</div>
      <div className="text-right flex items-center justify-end gap-2">
        <span className="text-sm font-medium">{amount}</span>
        <div className="ml-2">{getStatusBadge()}</div>
      </div>
    </div>
  );
};

export default TransactionItem;
