
import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { QrCode, Copy, Share, Check, RefreshCw } from "lucide-react";
import { motion } from "framer-motion";
import QRCodeGenerator from './QRCodeGenerator';

interface QRPayDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountNumber: string;
  accountName: string;
}

const QRPayDialog = ({ open, onOpenChange, accountNumber, accountName }: QRPayDialogProps) => {
  const [amount, setAmount] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [showQRCode, setShowQRCode] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const handleGenerateQR = () => {
    setLoading(true);
    setTimeout(() => {
      setShowQRCode(true);
      setLoading(false);
    }, 1000);
  };

  const handleReset = () => {
    setShowQRCode(false);
    setAmount("");
    setDescription("");
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    console.log("Copied to clipboard: Payment link has been copied to clipboard");
    setTimeout(() => setCopied(false), 2000);
  };

  const shareQR = () => {
    if (navigator.share) {
      navigator.share({
        title: "KojaPay QR Payment",
        text: `Pay ${accountName} (${accountNumber}) the amount of ₦${amount} via KojaPay`,
        url: window.location.href,
      })
      .then(() => {
        console.log("Shared successfully: QR code has been shared");
      })
      .catch(error => {
        console.error("Error sharing QR code: There was an error sharing the QR code");
      });
    } else {
      copyToClipboard(`Pay ${accountName} (${accountNumber}) the amount of ₦${amount} via KojaPay: https://kojapay.com/pay/${accountNumber}?amount=${amount}`);
    }
  };

  const paymentData = {
    accountName: accountName,
    accountNumber: accountNumber,
    amount: amount,
    description: description,
    timestamp: new Date().toISOString()
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white/95 backdrop-blur-xl border border-[#D3E4FD] shadow-lg rounded-[20px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-unica text-gray-900 flex items-center">
            <QrCode className="mr-2 text-kojaPrimary" size={20} /> 
            QR Payment
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Generate a QR code for receiving payments
          </DialogDescription>
        </DialogHeader>

        {!showQRCode ? (
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-gray-700">Amount (₦)</Label>
              <Input 
                id="amount" 
                type="number" 
                placeholder="Enter amount" 
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="border-gray-200 focus:border-kojaPrimary"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-gray-700">Description (Optional)</Label>
              <Input 
                id="description" 
                placeholder="e.g. Payment for services" 
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="border-gray-200 focus:border-kojaPrimary"
              />
            </div>

            <div className="pt-2">
              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
                onClick={handleGenerateQR}
                disabled={!amount || loading}
              >
                {loading ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : <QrCode className="mr-2 h-4 w-4" />}
                Generate QR Code
              </Button>
            </div>
          </div>
        ) : (
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex flex-col items-center py-2"
          >
            <Card className="w-full max-w-[250px] mx-auto mb-4 bg-white border border-[#D3E4FD]">
              <CardContent className="p-4 flex flex-col items-center">
                <QRCodeGenerator 
                  data={JSON.stringify(paymentData)} 
                  size={200}
                  logo="/lovable-uploads/6d533269-1146-41c3-ad78-0141d73073a6.png"
                />
                <div className="mt-4 text-center">
                  <p className="text-sm font-medium text-gray-800">{accountName}</p>
                  <p className="text-gray-600 text-sm">{accountNumber}</p>
                  <p className="text-kojaPrimary font-bold mt-1">₦{parseFloat(amount).toLocaleString()}</p>
                  {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
                </div>
              </CardContent>
            </Card>

            <div className="flex gap-2 w-full">
              <Button 
                variant="outline" 
                className="flex-1 border-gray-200 text-gray-700 hover:text-kojaPrimary hover:border-kojaPrimary"
                onClick={() => copyToClipboard(`Pay ${accountName} (${accountNumber}) the amount of ₦${amount} via KojaPay: https://kojapay.com/pay/${accountNumber}?amount=${amount}`)}
              >
                {copied ? <Check className="mr-1 h-4 w-4" /> : <Copy className="mr-1 h-4 w-4" />}
                Copy Link
              </Button>
              <Button 
                variant="outline" 
                className="flex-1 border-gray-200 text-gray-700 hover:text-kojaPrimary hover:border-kojaPrimary"
                onClick={shareQR}
              >
                <Share className="mr-1 h-4 w-4" />
                Share
              </Button>
            </div>

            <Button 
              variant="ghost" 
              className="mt-4 text-gray-600 hover:text-kojaPrimary"
              onClick={handleReset}
            >
              <RefreshCw className="mr-1 h-4 w-4" />
              Generate New
            </Button>
          </motion.div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default QRPayDialog;
