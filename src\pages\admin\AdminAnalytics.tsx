
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  Users, 
  CreditCard, 
  FileText, 
  Shield, 
  AlertTriangle, 
  Activity,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Calendar,
  Clock,
  Filter
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell
} from 'recharts';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

// Demo data for charts
const userGrowthData = [
  { month: 'Jan', users: 1200, businesses: 250 },
  { month: 'Feb', users: 1900, businesses: 350 },
  { month: 'Mar', users: 3000, businesses: 450 },
  { month: 'Apr', users: 4780, businesses: 550 },
  { month: 'May', users: 5890, businesses: 650 },
  { month: 'Jun', users: 6390, businesses: 750 },
  { month: 'Jul', users: 7490, businesses: 850 },
  { month: 'Aug', users: 9000, businesses: 950 },
  { month: 'Sep', users: 10500, businesses: 1050 },
  { month: 'Oct', users: 11900, businesses: 1150 },
  { month: 'Nov', users: 13500, businesses: 1250 },
  { month: 'Dec', users: 15342, businesses: 1400 },
];

const transactionData = [
  { month: 'Jan', volume: 2500000, count: 5400 },
  { month: 'Feb', volume: 3800000, count: 8200 },
  { month: 'Mar', volume: 5300000, count: 12100 },
  { month: 'Apr', volume: 7800000, count: 18400 },
  { month: 'May', volume: 9200000, count: 22600 },
  { month: 'Jun', volume: 10500000, count: 28900 },
  { month: 'Jul', volume: 12800000, count: 34500 },
  { month: 'Aug', volume: 13400000, count: 42800 },
  { month: 'Sep', volume: 14200000, count: 48900 },
  { month: 'Oct', volume: 14800000, count: 52400 },
  { month: 'Nov', volume: 15100000, count: 58200 },
  { month: 'Dec', volume: 15300000, count: 62300 },
];

const healthData = [
  { name: 'Server Uptime', value: 98.7 },
  { name: 'API Response', value: 99.2 },
  { name: 'Database', value: 99.5 },
  { name: 'Frontend', value: 99.8 },
];

const kycData = [
  { status: 'Approved', count: 8450 },
  { status: 'Pending', count: 142 },
  { status: 'Rejected', count: 340 },
  { status: 'Incomplete', count: 567 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const AdminAnalytics = () => {
  const [timeRange, setTimeRange] = useState('month');
  
  return (
    <>
      <Helmet>
        <title>Analytics | KojaPay Admin Portal</title>
      </Helmet>
      <AdminLayout pageTitle="Analytics Dashboard">
        <div className="space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">Analytics Dashboard</h1>
              <p className="text-gray-600">Comprehensive overview of platform metrics and trends</p>
            </div>
            <div className="flex gap-3">
              <Select defaultValue="month">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Last Week</SelectItem>
                  <SelectItem value="month">Last Month</SelectItem>
                  <SelectItem value="quarter">Last Quarter</SelectItem>
                  <SelectItem value="year">Last Year</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="flex items-center gap-2">
                <FileText size={16} />
                Export
              </Button>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-500" />
                  User Growth
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">15,342</div>
                <div className="text-sm text-green-500 flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +12.5% from last month
                </div>
                <div className="h-40 mt-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={userGrowthData}
                      margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                    >
                      <defs>
                        <linearGradient id="colorUsers" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                          <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <XAxis dataKey="month" tick={{ fontSize: 10 }} />
                      <YAxis tick={{ fontSize: 10 }} />
                      <Tooltip />
                      <Area type="monotone" dataKey="users" stroke="#3b82f6" fillOpacity={1} fill="url(#colorUsers)" />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-500" />
                  Transaction Volume
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">₦145.3M</div>
                <div className="text-sm text-green-500 flex items-center mt-1">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +8.2% from last month
                </div>
                <div className="h-40 mt-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={transactionData}
                      margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" tick={{ fontSize: 10 }} />
                      <YAxis tick={{ fontSize: 10 }} />
                      <Tooltip 
                        formatter={(value) => [`₦${(Number(value)/1000000).toFixed(1)}M`, 'Volume']}
                      />
                      <Bar dataKey="volume" fill="#22c55e" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Card className="shadow-md">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg font-medium flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  System Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">98.7%</div>
                <div className="text-sm text-yellow-500 flex items-center mt-1">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  -0.3% from last week
                </div>
                <div className="h-40 mt-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={healthData}
                        cx="50%"
                        cy="50%"
                        innerRadius={40}
                        outerRadius={60}
                        fill="#8884d8"
                        paddingAngle={3}
                        dataKey="value"
                        label={({name, percent}) => `${name}: ${(percent * 100).toFixed(1)}%`}
                        labelLine={false}
                      >
                        {healthData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Tabs defaultValue="users" className="w-full">
            <TabsList className="mb-4 grid grid-cols-2 md:grid-cols-4 w-full max-w-3xl">
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="transactions">Transactions</TabsTrigger>
              <TabsTrigger value="kyc">KYC Verification</TabsTrigger>
              <TabsTrigger value="security">Security</TabsTrigger>
            </TabsList>
            
            <TabsContent value="users" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>User Growth Analytics</CardTitle>
                  <CardDescription>Detailed analysis of user acquisition and growth</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={userGrowthData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis yAxisId="left" />
                        <YAxis yAxisId="right" orientation="right" />
                        <Tooltip />
                        <Legend />
                        <Line
                          yAxisId="left"
                          type="monotone"
                          dataKey="users"
                          name="Personal Users"
                          stroke="#3b82f6"
                          activeDot={{ r: 8 }}
                        />
                        <Line
                          yAxisId="right"
                          type="monotone"
                          dataKey="businesses"
                          name="Business Users"
                          stroke="#f97316"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="text-lg font-medium mb-4">User Statistics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-500">Total Users</p>
                              <p className="text-2xl font-bold">15,342</p>
                            </div>
                            <Users className="h-10 w-10 text-blue-500 bg-blue-100 p-2 rounded-full" />
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-500">Active Users</p>
                              <p className="text-2xl font-bold">12,893</p>
                            </div>
                            <Activity className="h-10 w-10 text-green-500 bg-green-100 p-2 rounded-full" />
                          </div>
                        </CardContent>
                      </Card>
                      
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm text-gray-500">New This Month</p>
                              <p className="text-2xl font-bold">1,842</p>
                            </div>
                            <TrendingUp className="h-10 w-10 text-purple-500 bg-purple-100 p-2 rounded-full" />
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="transactions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Transaction Analytics</CardTitle>
                  <CardDescription>Detailed breakdown of transaction volume and patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={transactionData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis 
                          yAxisId="left" 
                          tickFormatter={(value) => `₦${(value/1000000).toFixed(0)}M`}
                        />
                        <YAxis 
                          yAxisId="right" 
                          orientation="right" 
                          tickFormatter={(value) => `${(value/1000).toFixed(1)}K`}
                        />
                        <Tooltip 
                          formatter={(value, name) => {
                            if (name === 'volume') return [`₦${(Number(value)/1000000).toFixed(1)}M`, 'Volume'];
                            return [`${(Number(value)/1000).toFixed(1)}K`, 'Count'];
                          }}
                        />
                        <Legend />
                        <Line
                          yAxisId="left"
                          type="monotone"
                          dataKey="volume"
                          name="Transaction Volume"
                          stroke="#10b981"
                          strokeWidth={2}
                        />
                        <Line
                          yAxisId="right"
                          type="monotone"
                          dataKey="count"
                          name="Transaction Count"
                          stroke="#f59e0b"
                          strokeWidth={2}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="text-lg font-medium mb-4">Recent Transactions</h3>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Transaction ID</TableHead>
                            <TableHead>User</TableHead>
                            <TableHead>Type</TableHead>
                            <TableHead>Amount</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Date</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell className="font-medium">TRX-89023</TableCell>
                            <TableCell>John Doe</TableCell>
                            <TableCell>Transfer</TableCell>
                            <TableCell>₦25,000</TableCell>
                            <TableCell>
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                                Completed
                              </span>
                            </TableCell>
                            <TableCell>2023-04-23 14:35</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">TRX-89022</TableCell>
                            <TableCell>Sarah Johnson</TableCell>
                            <TableCell>Withdrawal</TableCell>
                            <TableCell>₦150,000</TableCell>
                            <TableCell>
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                                Completed
                              </span>
                            </TableCell>
                            <TableCell>2023-04-23 13:42</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">TRX-89021</TableCell>
                            <TableCell>Tech Solutions Ltd</TableCell>
                            <TableCell>Bill Payment</TableCell>
                            <TableCell>₦45,350</TableCell>
                            <TableCell>
                              <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">
                                Pending
                              </span>
                            </TableCell>
                            <TableCell>2023-04-23 12:18</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell className="font-medium">TRX-89020</TableCell>
                            <TableCell>Michael Brown</TableCell>
                            <TableCell>Deposit</TableCell>
                            <TableCell>₦200,000</TableCell>
                            <TableCell>
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                                Completed
                              </span>
                            </TableCell>
                            <TableCell>2023-04-23 11:05</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="kyc" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>KYC Verification Analytics</CardTitle>
                  <CardDescription>Overview of KYC verification metrics and approval rates</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={kycData}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="count"
                            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(1)}%`}
                          >
                            {kycData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    
                    <div className="space-y-6">
                      <div className="bg-white p-6 rounded-lg border">
                        <h3 className="font-medium text-lg mb-4">KYC Verification Stats</h3>
                        
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Approved</span>
                              <span className="text-sm font-medium text-green-600">8,450</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-green-500 h-2 rounded-full" style={{width: '85%'}}></div>
                            </div>
                          </div>
                          
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Pending</span>
                              <span className="text-sm font-medium text-yellow-600">142</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-yellow-500 h-2 rounded-full" style={{width: '5%'}}></div>
                            </div>
                          </div>
                          
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Rejected</span>
                              <span className="text-sm font-medium text-red-600">340</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-red-500 h-2 rounded-full" style={{width: '10%'}}></div>
                            </div>
                          </div>
                          
                          <div>
                            <div className="flex justify-between mb-1">
                              <span className="text-sm font-medium">Incomplete</span>
                              <span className="text-sm font-medium text-gray-600">567</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div className="bg-gray-500 h-2 rounded-full" style={{width: '15%'}}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-white p-4 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm text-gray-500">Avg. Approval Time</h4>
                            <p className="text-xl font-bold">5.3 hours</p>
                          </div>
                          <Clock className="h-8 w-8 text-indigo-500 bg-indigo-100 p-1 rounded-full" />
                        </div>
                      </div>
                      
                      <div className="bg-white p-4 rounded-lg border">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm text-gray-500">Verification Rate</h4>
                            <p className="text-xl font-bold">89.2%</p>
                          </div>
                          <Shield className="h-8 w-8 text-green-500 bg-green-100 p-1 rounded-full" />
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Security Analytics</CardTitle>
                  <CardDescription>System security and fraud prevention metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-500">Failed Logins</p>
                            <p className="text-2xl font-bold">1,243</p>
                            <p className="text-xs text-red-500">+12% from last week</p>
                          </div>
                          <div className="h-10 w-10 bg-red-100 rounded-full flex items-center justify-center">
                            <Shield className="h-5 w-5 text-red-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-500">Suspicious Activities</p>
                            <p className="text-2xl font-bold">57</p>
                            <p className="text-xs text-green-500">-8% from last week</p>
                          </div>
                          <div className="h-10 w-10 bg-yellow-100 rounded-full flex items-center justify-center">
                            <AlertTriangle className="h-5 w-5 text-yellow-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-500">Blocked Transactions</p>
                            <p className="text-2xl font-bold">35</p>
                            <p className="text-xs text-green-500">-15% from last week</p>
                          </div>
                          <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <FileText className="h-5 w-5 text-purple-500" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={[
                          { day: 'Mon', attacks: 32, blocked: 32 },
                          { day: 'Tue', attacks: 45, blocked: 43 },
                          { day: 'Wed', attacks: 28, blocked: 28 },
                          { day: 'Thu', attacks: 37, blocked: 36 },
                          { day: 'Fri', attacks: 68, blocked: 65 },
                          { day: 'Sat', attacks: 51, blocked: 49 },
                          { day: 'Sun', attacks: 39, blocked: 39 },
                        ]}
                        margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="day" />
                        <YAxis />
                        <Tooltip />
                        <Legend />
                        <Line type="monotone" dataKey="attacks" name="Attack Attempts" stroke="#ef4444" />
                        <Line type="monotone" dataKey="blocked" name="Blocked Attacks" stroke="#22c55e" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                  
                  <div className="mt-8">
                    <h3 className="text-lg font-medium mb-4">Recent Security Alerts</h3>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg bg-red-50 border-red-200">
                        <div className="flex items-start">
                          <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-red-800">Multiple Failed Login Attempts</h4>
                            <p className="text-sm text-red-600">User ID: 86743 | IP: ************ | Time: 2023-04-23 15:42:18</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
                        <div className="flex items-start">
                          <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-yellow-800">Unusual Transaction Pattern</h4>
                            <p className="text-sm text-yellow-600">User ID: 43265 | Amount: ₦2,500,000 | Time: 2023-04-23 14:21:05</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="p-4 border rounded-lg bg-green-50 border-green-200">
                        <div className="flex items-start">
                          <Shield className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-green-800">Security Patch Applied</h4>
                            <p className="text-sm text-green-600">System: Payment Gateway | Version: 2.4.1 | Time: 2023-04-23 09:15:30</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminLayout>
    </>
  );
};

export default AdminAnalytics;
