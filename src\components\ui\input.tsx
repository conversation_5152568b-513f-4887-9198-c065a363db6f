
import * as React from "react"
import { cn } from "@/lib/utils"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-12 w-full rounded-[30px] border border-gray-300/40 bg-white/60 backdrop-blur-sm px-4 py-2 text-base ring-offset-background transition-all duration-300 focus:border-kojaPrimary/50 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-kojaPrimary/20 focus-visible:ring-offset-2 focus:shadow-[0_0_0_3px_rgba(18,49,184,0.1)] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
