
import React, { useState } from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { 
  Home, 
  CreditCard, 
  BarChart3, 
  ShoppingBag, 
  FileText, 
  Settings, 
  Shield, 
  User, 
  Lock, 
  Receipt, 
  BellRing, 
  ChevronRight, 
  ChevronLeft, 
  Wallet, 
  Landmark, 
  Award,
  HelpCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import LogoutButton from '@/components/LogoutButton';

interface SidebarProps {
  logoUrl?: string;
}

interface NavItem {
  path: string;
  name: string;
  icon: React.ReactNode;
  badge?: string;
  onClick?: () => void;
}

const PersonalSidebar: React.FC<SidebarProps> = ({ logoUrl }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const isMobile = useIsMobile();

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };
  
  const showHelp = () => {
    toast({
      title: "Personal Support",
      description: "Our team is available to assist you",
      variant: "success"
    });
  };

  const personalNavItems: NavItem[] = [
    { path: '/wallet', name: 'Wallet', icon: <Wallet size={18} /> },
    { path: '/transactions', name: 'Transactions', icon: <FileText size={18} /> },
    { path: '/analysis', name: 'Analysis', icon: <BarChart3 size={18} /> },
    { path: '/cards', name: 'Cards', icon: <CreditCard size={18} /> },
    { path: '/bill-payment', name: 'Bill Payment', icon: <Receipt size={18} /> },
    { path: '/ecommerce', name: 'E-Commerce', icon: <ShoppingBag size={18} /> },
    { path: '/escrow', name: 'Escrow', icon: <Lock size={18} /> },
    { path: '/kyc', name: 'KYC', icon: <Shield size={18} /> },
    { path: '/loans', name: 'Loans', icon: <Landmark size={18} /> },
    { path: '/savings', name: 'Savings', icon: <Home size={18} /> },
    { path: '/reward-points', name: 'Rewards', icon: <Award size={18} /> },
    { path: '/notifications', name: 'Notifications', icon: <BellRing size={18} /> }
  ];

  const accountNavItems: NavItem[] = [
    { path: '/profile', name: 'Profile', icon: <User size={18} /> },
    { path: '/settings', name: 'Settings', icon: <Settings size={18} /> },
    { path: '/security', name: 'Security', icon: <Shield size={18} /> },
    { name: 'Help & Support', icon: <HelpCircle size={18} />, path: '#', onClick: showHelp }
  ];

  return (
    <div className={cn(
      "bg-gradient-to-b from-[#09125a] to-[#1231B8] backdrop-blur-xl text-white flex flex-col h-screen relative z-20 shadow-2xl font-unica border-r border-white/10 no-scrollbar", 
      isCollapsed ? "w-20" : "w-64", 
      isMobile && "w-full h-screen"
    )}>
      <div className="flex items-center justify-end mb-3 px-4">
        {!isMobile && (
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-white hover:bg-white/10 h-8 w-8" 
            onClick={toggleSidebar} 
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <ChevronRight size={15} /> : <ChevronLeft size={15} />}
          </Button>
        )}
      </div>

      {(!isCollapsed || isMobile) && (
        <div 
          className="p-3 bg-white/5 flex items-center gap-3 cursor-pointer hover:bg-white/10 transition-colors mx-4" 
          onClick={() => navigate('/profile')}
        >
          <Avatar className="w-10 h-10 border-2 border-white/30">
            <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-indigo-600 text-white">
              {user?.fullName?.charAt(0) || 'U'}
            </div>
          </Avatar>
          <div className="flex flex-col">
            <p className="text-white font-medium text-sm font-unica">{user?.fullName || 'User'}</p>
            <p className="text-white/70 text-xs font-unica">Personal Account</p>
          </div>
        </div>
      )}
      
      {isCollapsed && (
        <div className="flex justify-center mb-4">
          <Avatar className="w-10 h-10 border-2 border-white/30">
            <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-indigo-600 text-white">
              {user?.fullName?.charAt(0) || 'U'}
            </div>
          </Avatar>
        </div>
      )}

      <nav className="flex-1 flex flex-col justify-between overflow-y-auto no-scrollbar mt-4 px-2">
        <div className="space-y-1">
          <div className="mb-4 grid grid-cols-1 gap-1">
            {personalNavItems.map(item => {
              const isActive = location.pathname === item.path;
              if (item.onClick) {
                return (
                  <button
                    key={item.name}
                    onClick={item.onClick}
                    className={cn(
                      "nav-item", 
                      isCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className={cn(
                        "flex-shrink-0 p-1 flex items-center justify-center",
                        isActive && "bg-white/10"
                      )}>
                        {item.icon}
                      </div>
                      {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                    </div>
                  </button>
                );
              }
              return (
                <NavLink 
                  key={item.path} 
                  to={item.path} 
                  className={({isActive}) => cn(
                    "nav-item", 
                    isActive ? "nav-item-active bg-[#FDE314] text-[#000000]" : "", 
                    isCollapsed ? "justify-center" : ""
                  )}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={cn(
                      "flex-shrink-0 p-1 flex items-center justify-center",
                      isActive && "bg-white/10"
                    )}>
                      {item.icon}
                    </div>
                    {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                  </div>
                </NavLink>
              );
            })}
          </div>

          <div className="border-t border-white/10 my-2"></div>
          
          <div className="grid grid-cols-1 gap-1">
            {accountNavItems.map(item => {
              const isActive = location.pathname === item.path;
              if (item.onClick) {
                return (
                  <button
                    key={item.name}
                    onClick={item.onClick}
                    className={cn(
                      "nav-item", 
                      isCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className={cn(
                        "flex-shrink-0 p-1 flex items-center justify-center"
                      )}>
                        {item.icon}
                      </div>
                      {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                    </div>
                  </button>
                );
              }
              return (
                <NavLink 
                  key={item.path} 
                  to={item.path} 
                  className={({isActive}) => cn(
                    "nav-item", 
                    isActive ? "nav-item-active bg-[#FDE314] text-[#000000]" : "", 
                    isCollapsed ? "justify-center" : ""
                  )}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={cn(
                      "flex-shrink-0 p-1 flex items-center justify-center",
                      isActive && "bg-white/10"
                    )}>
                      {item.icon}
                    </div>
                    {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                  </div>
                </NavLink>
              );
            })}
          </div>
        </div>
        
        <div className="p-2 mb-4">
          <LogoutButton 
            asAuthButton 
            variant="destructive" 
            className={cn(
              "w-full border border-red-400/20 hover:border-red-400/40 rounded-xl bg-red-500/10", 
              isCollapsed ? "px-0" : ""
            )}
            fullWidth={true}
          />
        </div>
      </nav>
    </div>
  );
};

export default PersonalSidebar;
