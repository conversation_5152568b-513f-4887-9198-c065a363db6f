
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Wallet,
  Lightbulb,
  Wifi,
  Tv,
  Phone,
  Globe,
  Droplet,
  Home,
  FileText,
  CheckCircle,
  CreditCard
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { GoBackButton } from '@/components/ui/go-back-button';

const BillPayment = () => {
  const { toast } = useToast();
  const [selectedBill, setSelectedBill] = useState<string | null>(null);
  const [amount, setAmount] = useState("");
  const [reference, setReference] = useState("");
  const [processing, setProcessing] = useState(false);

  const billCategories = [
    { id: 'electricity', name: 'Electricity', icon: <Lightbulb size={24} />, providers: ['EKEDC', 'IKEDC', 'AEDC', 'PHEDC'], suggestedAmounts: [] },
    { id: 'internet', name: 'Internet', icon: <Wifi size={24} />, providers: ['Spectranet', 'Swift', 'Smile', 'IPNX'], suggestedAmounts: [5000, 8000, 10000, 15000] },
    { id: 'tv', name: 'Cable TV', icon: <Tv size={24} />, providers: ['DSTV', 'GoTV', 'StarTimes', 'ShowMax'], suggestedAmounts: [] },
    { id: 'mobile', name: 'Mobile Data', icon: <Phone size={24} />, providers: ['MTN', 'Airtel', 'Glo', '9mobile'], suggestedAmounts: [1000, 2000, 3000, 5000] },
    { id: 'water', name: 'Water', icon: <Droplet size={24} />, providers: ['Lagos Water', 'FCT Water', 'Kaduna Water'], suggestedAmounts: [] },
    { id: 'betting', name: 'Betting', icon: <Globe size={24} />, providers: ['Bet9ja', 'SportyBet', 'BetKing', '1xBet'], suggestedAmounts: [] },
    { id: 'airtime', name: 'Airtime', icon: <Phone size={24} />, providers: ['MTN', 'Airtel', 'Glo', '9mobile'], suggestedAmounts: [100, 200, 500, 1000, 2000, 5000] },
  ];

  const handlePayBill = () => {
    if (!selectedBill || !amount || !reference) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    setProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setProcessing(false);
      toast({
        title: "Payment Successful",
        description: `Your ${selectedBill} bill payment of ₦${amount} was successful`,
        variant: "default",
      });
      
      // Reset form
      setAmount("");
      setReference("");
    }, 2000);
  };

  // Determine the label based on selected bill type
  const getReferenceLabel = () => {
    if (!selectedBill) return "Reference Number";
    
    if (['internet', 'mobile', 'airtime'].includes(selectedBill)) {
      return "Phone Number";
    } else if (selectedBill === 'electricity') {
      return "Meter Number";
    } else {
      return "Customer ID / Reference Number";
    }
  };

  // Get suggested amounts for the selected bill type
  const getSuggestedAmounts = () => {
    if (!selectedBill) return [];
    const category = billCategories.find(cat => cat.id === selectedBill);
    return category?.suggestedAmounts || [];
  };

  return (
    <DashboardLayout pageTitle="Bill Payment">
      <div className="space-y-6">
        <GoBackButton />
        
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-roboto-condensed text-[#1231b8]">Bill Payment</h2>
          <p className="text-muted-foreground font-roboto">Pay your bills quickly and securely</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card className="bg-white shadow-lg border border-gray-100 transition-all duration-500 hover:shadow-xl">
              <CardHeader>
                <CardTitle className="font-roboto-condensed text-[#1231b8]">Pay a Bill</CardTitle>
                <CardDescription className="font-roboto">Select a service to continue</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="categories" className="w-full">
                  <TabsList className="mb-4 w-full bg-gray-100">
                    <TabsTrigger value="categories" className="flex-1 font-roboto data-[state=active]:bg-[#1231b8] data-[state=active]:text-white">Categories</TabsTrigger>
                    <TabsTrigger value="recent" className="flex-1 font-roboto data-[state=active]:bg-[#1231b8] data-[state=active]:text-white">Recent</TabsTrigger>
                    <TabsTrigger value="saved" className="flex-1 font-roboto data-[state=active]:bg-[#1231b8] data-[state=active]:text-white">Saved Bills</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="categories">
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                      {billCategories.map((category) => (
                        <div 
                          key={category.id}
                          className={`p-4 rounded-lg border flex flex-col items-center justify-center text-center cursor-pointer transition-all duration-300 shadow-sm hover:shadow-md ${
                            selectedBill === category.id 
                              ? 'bg-[#1231b8] text-white border-[#1231b8]' 
                              : 'hover:bg-[#fde314]/5 hover:border-[#fde314] border-gray-200'
                          }`}
                          onClick={() => setSelectedBill(category.id)}
                        >
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center mb-2 ${
                            selectedBill === category.id 
                              ? 'bg-white/20 text-white' 
                              : 'bg-[#fde314]/10 text-[#1231b8]'
                          }`}>
                            {category.icon}
                          </div>
                          <h3 className="font-medium font-roboto-condensed">{category.name}</h3>
                        </div>
                      ))}
                    </div>
                    
                    {selectedBill && (
                      <div className="mt-6 space-y-4 p-4 border border-gray-200 rounded-lg bg-gray-50 shadow-md">
                        <div className="space-y-2">
                          <Label htmlFor="provider" className="font-roboto">Select Provider</Label>
                          <select 
                            id="provider"
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 font-roboto"
                          >
                            <option value="">Select a provider</option>
                            {billCategories.find(c => c.id === selectedBill)?.providers.map(provider => (
                              <option key={provider} value={provider}>{provider}</option>
                            ))}
                          </select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="reference" className="font-roboto">{getReferenceLabel()}</Label>
                          <Input 
                            id="reference" 
                            placeholder={`Enter ${getReferenceLabel().toLowerCase()}`}
                            value={reference}
                            onChange={(e) => setReference(e.target.value)}
                            className="font-roboto"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor="amount" className="font-roboto">Amount (₦)</Label>
                          
                          {getSuggestedAmounts().length > 0 && (
                            <div className="flex flex-wrap gap-2 mb-2">
                              {getSuggestedAmounts().map(suggestedAmount => (
                                <Button 
                                  key={suggestedAmount}
                                  type="button" 
                                  variant="outline" 
                                  size="sm"
                                  className="rounded-full shadow-sm hover:shadow-md"
                                  onClick={() => setAmount(suggestedAmount.toString())}
                                >
                                  ₦{suggestedAmount.toLocaleString()}
                                </Button>
                              ))}
                            </div>
                          )}
                          
                          <Input 
                            id="amount" 
                            placeholder="Enter amount"
                            type="number"
                            value={amount}
                            onChange={(e) => setAmount(e.target.value)}
                            className="font-roboto"
                          />
                        </div>
                        
                        <Button 
                          className="w-full bg-[#1231b8] hover:bg-[#1231b8]/90 text-white font-roboto shadow-md hover:shadow-lg"
                          onClick={handlePayBill}
                          disabled={processing}
                        >
                          {processing ? "Processing..." : "Pay Bill"}
                        </Button>
                      </div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="recent">
                    <div className="space-y-4">
                      {[
                        { provider: 'EKEDC', reference: '**********', date: '10 May 2023', amount: '₦15,000' },
                        { provider: 'DSTV', reference: 'DSTV123456', date: '5 May 2023', amount: '₦8,900' },
                        { provider: 'MTN', reference: '08012345678', date: '1 May 2023', amount: '₦5,000' },
                      ].map((bill, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 shadow-sm hover:shadow-md">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-[#fde314]/10 flex items-center justify-center">
                              <FileText className="h-5 w-5 text-[#1231b8]" />
                            </div>
                            <div>
                              <p className="font-medium font-roboto">{bill.provider}</p>
                              <p className="text-sm text-gray-500 font-roboto">{bill.reference} • {bill.date}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium font-roboto">{bill.amount}</p>
                            <Button variant="ghost" size="sm" className="h-8 text-[#1231b8] font-roboto">Pay Again</Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="saved">
                    <div className="space-y-4">
                      {[
                        { name: 'Home Electricity', provider: 'IKEDC', reference: '**********', category: 'Electricity' },
                        { name: 'Family DSTV', provider: 'DSTV', reference: 'DSTV123456', category: 'Cable TV' },
                        { name: 'Personal Line', provider: 'MTN', reference: '08012345678', category: 'Mobile Data' },
                      ].map((bill, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 shadow-sm hover:shadow-md">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-[#fde314]/10 flex items-center justify-center">
                              <Home className="h-5 w-5 text-[#1231b8]" />
                            </div>
                            <div>
                              <p className="font-medium font-roboto">{bill.name}</p>
                              <p className="text-sm text-gray-500 font-roboto">{bill.provider} • {bill.reference}</p>
                            </div>
                          </div>
                          <Button variant="outline" size="sm" className="h-8 font-roboto border-[#1231b8] text-[#1231b8] hover:bg-[#1231b8] hover:text-white shadow-sm hover:shadow-md">Pay Now</Button>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card className="bg-[#1231b8] text-white shadow-lg border border-[#1231b8]/50 transition-all duration-500 hover:shadow-xl">
              <CardHeader>
                <CardTitle className="font-roboto-condensed text-white">Bill Payment Tips</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="mt-1 text-[#fde314]">
                    <CheckCircle size={18} />
                  </div>
                  <p className="text-sm font-roboto text-white">Keep your customer ID or reference number handy for faster payments.</p>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 text-[#fde314]">
                    <CheckCircle size={18} />
                  </div>
                  <p className="text-sm font-roboto text-white">Save frequently paid bills to make future payments easier.</p>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 text-[#fde314]">
                    <CheckCircle size={18} />
                  </div>
                  <p className="text-sm font-roboto text-white">You can set up automatic payments for recurring bills like electricity or TV subscriptions.</p>
                </div>
                
                <div className="flex items-start gap-3">
                  <div className="mt-1 text-[#fde314]">
                    <CheckCircle size={18} />
                  </div>
                  <p className="text-sm font-roboto text-white">All bill payments are processed instantly with real-time confirmation.</p>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-[#1231b8] text-white shadow-lg border border-[#1231b8]/50 transition-all duration-500 hover:shadow-xl">
              <CardHeader>
                <CardTitle className="font-roboto-condensed text-white">Payment Methods</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 rounded-lg border border-white/20 bg-white/10 shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-[#fde314] rounded-full flex items-center justify-center text-[#1231b8]">
                        <Wallet size={16} />
                      </div>
                      <div>
                        <p className="font-medium font-roboto-condensed text-white">KojaPay Balance</p>
                        <p className="text-sm text-white/80 font-roboto">Available: ₦350,000</p>
                      </div>
                    </div>
                    <div className="w-4 h-4 rounded-full border-2 border-[#fde314] flex items-center justify-center">
                      <div className="w-2 h-2 bg-[#fde314] rounded-full"></div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 rounded-lg border border-white/20 shadow-sm">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center text-white">
                        <CreditCard size={16} />
                      </div>
                      <p className="font-medium font-roboto-condensed text-white">Card Payment</p>
                    </div>
                    <div className="w-4 h-4 rounded-full border-2 border-white/50"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default BillPayment;
