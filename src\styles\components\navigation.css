
/* Navigation and sidebar components */
.nav-item {
  @apply flex items-center gap-2 px-4 py-2 rounded-[20px] transition-all duration-300 hover:bg-white/10 backdrop-blur-sm shadow-md hover:shadow-lg font-futura;
}
  
.nav-item-active {
  @apply bg-[#FDE314] text-[#000000] backdrop-blur-sm font-medium shadow-lg font-futura;
}

.business-section-title {
  @apply text-kojaYellow font-unica text-xl font-semibold mb-4;
}

.animated-underline {
  @apply relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-0 after:bg-kojaPrimary after:transition-all hover:after:w-full;
}

.profile-sidebar-nav {
  @apply flex flex-col w-full max-w-[240px] gap-2 py-4;
}

.profile-nav-item {
  @apply flex items-center gap-3 px-4 py-3 hover:bg-kojaPrimary/5 rounded-[20px] text-sm font-medium transition-all cursor-pointer shadow-md hover:shadow-lg font-futura;
}

.profile-nav-item-active {
  @apply bg-[#FDE314] text-[#000000] shadow-inner font-futura;
}

.profile-nav-icon {
  @apply w-6 h-6 text-kojaGray;
}

.profile-nav-text {
  @apply flex-1 font-futura;
}

.progress-circle {
  @apply relative flex items-center justify-center w-16 h-16 rounded-full shadow-md;
}

.progress-circle-text {
  @apply absolute text-sm font-bold font-futura;
}

/* Admin portal navigation */
.admin-nav-item {
  @apply flex items-center gap-3 px-4 py-3 rounded-[20px] transition-all duration-300 hover:bg-kojaPrimary/5 shadow-md hover:shadow-lg font-futura;
}

.admin-nav-item-active {
  @apply bg-[#FDE314] text-[#000000] shadow-inner font-futura;
}

.admin-sidebar-section {
  @apply pt-4 pb-2 px-4 text-xs font-medium uppercase text-gray-500 font-futura;
}

/* Enhanced mobile navigation */
.mobile-nav-container {
  @apply fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-xl border-t border-gray-200 z-40 py-2 rounded-t-[20px] shadow-lg;
}

.mobile-nav-grid {
  @apply grid grid-cols-5 gap-1;
}

.mobile-nav-item {
  @apply flex flex-col items-center justify-center p-2 rounded-[20px] text-kojaDark shadow-sm hover:shadow-md transition-all;
}

.mobile-nav-item-active {
  @apply text-[#000000] bg-[#FDE314] shadow-inner;
}

.mobile-nav-icon {
  @apply w-5 h-5 mb-1;
}

.mobile-nav-text {
  @apply text-xs;
}

/* Admin specific navigation */
.admin-main-nav {
  @apply flex items-center gap-6 px-6 py-4 border-b border-[#D3E4FD]/30;
}

.admin-nav-link {
  @apply text-sm font-medium text-gray-600 hover:text-[#1231B8] transition-colors relative px-2 py-1 rounded-[20px] shadow-sm hover:shadow-md;
}

.admin-nav-link-active {
  @apply text-[#000000] bg-[#FDE314] font-semibold shadow-inner;
}

/* No scrollbar utility */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Custom card styles */
.modern-card {
  @apply bg-white text-[#000000] border border-gray-200 shadow-md rounded-[20px];
}

.modern-card .card-title {
  @apply text-[#1231B8] font-semibold;
}

/* Navbar responsive styles */
.navbar-container {
  @apply grid grid-cols-3 md:grid-cols-12 items-center w-full gap-2 px-2;
}

.navbar-logo-container {
  @apply col-span-1 md:col-span-3 flex items-center justify-start h-full;
}

/* Updated logo styles to be smaller and better aligned */
.navbar-logo {
  @apply w-6 h-6 md:w-8 md:h-8 object-contain;
}

.navbar-search {
  @apply col-span-1 md:col-span-5 flex items-center justify-center;
}

.navbar-actions {
  @apply col-span-1 md:col-span-4 flex items-center justify-end gap-2;
}

/* Logo layout for consistent positioning */
.sidebar-logo {
  @apply fixed top-3 left-4 z-50 w-9 h-9;
}
