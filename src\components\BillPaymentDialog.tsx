
import React, { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Receipt, Smartphone, Zap, Wifi, Tv, CreditCard, Search, AlertCircle, CheckCircle2, Loader2, ArrowLeft, Printer, Download } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { generateTransactionReceipt, printPOSReceipt } from "@/utils/receipt-generator";

interface BillPaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountNumber: string;
}

const BillPaymentDialog = ({ open, onOpenChange, accountNumber }: BillPaymentDialogProps) => {
  const { toast } = useToast();
  const [billType, setBillType] = useState<string>("");
  const [provider, setProvider] = useState<string>("");
  const [amount, setAmount] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [customerId, setCustomerId] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [customerName, setCustomerName] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [step, setStep] = useState<'categories' | 'form' | 'receipt'>('categories');
  const [completedTransaction, setCompletedTransaction] = useState<any>(null);

  const billCategories = [
    { id: "airtime", name: "Airtime", icon: <Smartphone className="h-5 w-5" />, color: "from-blue-300/30 to-blue-500/20" },
    { id: "data", name: "Internet Data", icon: <Wifi className="h-5 w-5" />, color: "from-purple-300/30 to-purple-500/20" },
    { id: "electricity", name: "Electricity", icon: <Zap className="h-5 w-5" />, color: "from-yellow-300/30 to-yellow-500/20" },
    { id: "tv", name: "Cable TV", icon: <Tv className="h-5 w-5" />, color: "from-green-300/30 to-green-500/20" },
    { id: "education", name: "Education", icon: <CreditCard className="h-5 w-5" />, color: "from-red-300/30 to-red-500/20" }
  ];

  const providers = {
    airtime: [
      { id: "mtn", name: "MTN" },
      { id: "airtel", name: "Airtel" },
      { id: "glo", name: "Glo" },
      { id: "9mobile", name: "9Mobile" }
    ],
    data: [
      { id: "mtn", name: "MTN" },
      { id: "airtel", name: "Airtel" },
      { id: "glo", name: "Glo" },
      { id: "9mobile", name: "9Mobile" }
    ],
    electricity: [
      { id: "ikedc", name: "Ikeja Electric" },
      { id: "ekedc", name: "Eko Electric" },
      { id: "aedc", name: "Abuja Electric" },
      { id: "phedc", name: "Port Harcourt Electric" }
    ],
    tv: [
      { id: "dstv", name: "DSTV" },
      { id: "gotv", name: "GOTV" },
      { id: "startimes", name: "StarTimes" }
    ],
    education: [
      { id: "waec", name: "WAEC" },
      { id: "jamb", name: "JAMB" },
      { id: "neco", name: "NECO" }
    ]
  };

  const resetForm = () => {
    setProvider("");
    setAmount("");
    setPhoneNumber("");
    setCustomerId("");
    setIsVerified(false);
    setCustomerName("");
  };

  const handleBillTypeChange = (value: string) => {
    setBillType(value);
    setStep('form');
    resetForm();
  };

  const goBackToCategories = () => {
    setStep('categories');
    setBillType("");
    resetForm();
  };

  const verifyCustomer = () => {
    if (!customerId) {
      toast({
        title: "Error",
        description: "Please enter a valid customer ID",
        variant: "destructive"
      });
      return;
    }

    setIsVerifying(true);
    
    // Simulate API call to verify customer
    setTimeout(() => {
      setIsVerifying(false);
      setIsVerified(true);
      setCustomerName("John Doe");
      
      toast({
        title: "Customer Verified",
        description: "Customer details retrieved successfully",
        variant: "default"
      });
    }, 2000);
  };

  const handlePayment = () => {
    if (billType === "airtime" || billType === "data") {
      if (!provider || !amount || !phoneNumber) {
        toast({
          title: "Error",
          description: "Please fill all required fields",
          variant: "destructive"
        });
        return;
      }
    } else {
      if (!provider || !amount || !customerId || !isVerified) {
        toast({
          title: "Error",
          description: billType === "airtime" ? "Please fill all required fields" : "Please verify your customer ID",
          variant: "destructive"
        });
        return;
      }
    }

    setIsProcessing(true);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      
      // Create a transaction object for receipt
      const transaction = {
        id: `BP-${Math.random().toString(36).substring(2, 7).toUpperCase()}`,
        date: new Date().toISOString(),
        recipient: providers[billType as keyof typeof providers]?.find(p => p.id === provider)?.name || 'Service Provider',
        amount: parseFloat(amount) || 0,
        status: 'success',
        description: `${billCategories.find(c => c.id === billType)?.name} Payment`,
        reference: `REF-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
        type: 'outgoing',
        customerName: customerName || (billType === "airtime" || billType === "data" ? phoneNumber : customerId),
        paymentMethod: 'Wallet'
      };
      
      setCompletedTransaction(transaction);
      setStep('receipt');
      
      toast({
        title: "Payment Successful",
        description: `Your ${billType} payment was successful`,
        variant: "default"
      });
      
      // Don't close the dialog, show receipt instead
      // onOpenChange(false);
    }, 2000);
  };

  const handleDownloadReceipt = () => {
    if (!completedTransaction) return;
    
    generateTransactionReceipt(completedTransaction, true);
    
    toast({
      title: "Receipt Downloaded",
      description: "The receipt has been downloaded to your device",
      variant: "success"
    });
  };
  
  const handlePrintReceipt = () => {
    if (!completedTransaction) return;
    
    const receipt = generateTransactionReceipt(completedTransaction);
    printPOSReceipt(receipt);
    
    toast({
      title: "Printing Receipt",
      description: "Receipt sent to printer",
      variant: "success"
    });
  };
  
  const handleNewPayment = () => {
    setStep('categories');
    setBillType("");
    resetForm();
    setCompletedTransaction(null);
  };

  const getBillIcon = (id: string) => {
    const category = billCategories.find(cat => cat.id === id);
    return category ? category.icon : <Receipt className="h-5 w-5" />;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white/95 backdrop-blur-xl border border-[#D3E4FD] shadow-lg rounded-[20px] w-[95vw] max-w-[550px] p-4 sm:p-6 overflow-y-auto max-h-[90vh]">
        <DialogHeader className="space-y-2">
          <DialogTitle className="text-lg sm:text-xl font-poppins text-gray-900 flex items-center">
            <Receipt className="mr-2 text-kojaPrimary" size={18} /> 
            {step === 'form' && billType && (
              <>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="mr-2 p-0 h-8 w-8" 
                  onClick={goBackToCategories}
                >
                  <ArrowLeft className="h-4 w-4 text-kojaPrimary" />
                </Button>
                {billCategories.find(c => c.id === billType)?.name || 'Pay Bills'}
              </>
            )}
            {step === 'receipt' && (
              <>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="mr-2 p-0 h-8 w-8" 
                  onClick={handleNewPayment}
                >
                  <ArrowLeft className="h-4 w-4 text-kojaPrimary" />
                </Button>
                Payment Receipt
              </>
            )}
            {(step === 'categories' || !billType) && 'Pay Bills'}
          </DialogTitle>
          <DialogDescription className="text-sm font-poppins text-gray-600">
            {step === 'categories' ? 'Select a service to continue' : 
             step === 'receipt' ? 'Your transaction was successful' : 
             'Pay for utilities, subscriptions and services'}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 mt-2">
          {step === 'categories' && (
            <div className="grid grid-cols-2 gap-3 sm:grid-cols-3 sm:gap-4">
              {billCategories.map((category) => (
                <EnhancedCard
                  key={category.id}
                  variant="future"
                  animation="all"
                  className={`overflow-hidden cursor-pointer bg-gradient-to-br ${category.color} relative`}
                  onClick={() => handleBillTypeChange(category.id)}
                >
                  <div className="absolute -top-3 -right-3 w-12 h-12 bg-[#FDE314]/20 rounded-full blur-xl opacity-60 z-0"></div>
                  <div className="p-4 flex flex-col items-center justify-center text-center relative z-10">
                    <div className="p-2.5 rounded-full bg-white/80 backdrop-blur-md shadow-md border border-white/30 mb-3">
                      <div className="text-kojaPrimary">{category.icon}</div>
                    </div>
                    <span className="text-sm font-poppins font-medium text-gray-800">{category.name}</span>
                  </div>
                </EnhancedCard>
              ))}
            </div>
          )}

          {step === 'form' && billType && (
            <div className="space-y-3 sm:space-y-4">
              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="provider" className="text-sm font-poppins text-gray-700">Provider</Label>
                <Select value={provider} onValueChange={setProvider}>
                  <SelectTrigger id="provider" className="border-gray-200 font-poppins text-sm h-9 sm:h-10">
                    <SelectValue placeholder="Select provider" className="font-poppins" />
                  </SelectTrigger>
                  <SelectContent className="font-poppins">
                    {providers[billType as keyof typeof providers]?.map((p) => (
                      <SelectItem key={p.id} value={p.id} className="font-poppins">{p.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {(billType !== "airtime" && billType !== "data") && (
                <div className="space-y-1.5 sm:space-y-2">
                  <Label htmlFor="customerId" className="text-sm font-poppins text-gray-700">Customer ID / Meter Number</Label>
                  <div className="flex space-x-2">
                    <Input
                      id="customerId"
                      placeholder="Enter ID number"
                      value={customerId}
                      onChange={(e) => setCustomerId(e.target.value)}
                      className="border-gray-200 font-poppins text-sm h-9 sm:h-10"
                    />
                    <Button 
                      type="button" 
                      variant="outline"
                      className="shrink-0 h-9 sm:h-10 font-poppins"
                      onClick={verifyCustomer}
                      disabled={!customerId || isVerifying}
                    >
                      {isVerifying ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              )}

              {isVerified && customerName && (
                <Card className="bg-green-50 border-green-100">
                  <CardContent className="p-2 sm:p-3 flex items-center gap-2">
                    <CheckCircle2 className="text-green-600 flex-shrink-0" size={16} />
                    <div>
                      <p className="text-xs sm:text-sm font-medium font-poppins text-green-800">Customer Verified</p>
                      <p className="text-xs font-poppins text-green-700">{customerName}</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {(billType === "airtime" || billType === "data") && (
                <div className="space-y-1.5 sm:space-y-2">
                  <Label htmlFor="phoneNumber" className="text-sm font-poppins text-gray-700">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    placeholder="Enter phone number"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="border-gray-200 font-poppins text-sm h-9 sm:h-10"
                  />
                </div>
              )}

              <div className="space-y-1.5 sm:space-y-2">
                <Label htmlFor="amount" className="text-sm font-poppins text-gray-700">Amount (₦)</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="Enter amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="border-gray-200 font-poppins text-sm h-9 sm:h-10"
                />
              </div>

              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90 h-9 sm:h-10 font-poppins text-sm"
                onClick={handlePayment}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...</>
                ) : (
                  <>{getBillIcon(billType)} Pay Now</>
                )}
              </Button>
            </div>
          )}
          
          {step === 'receipt' && completedTransaction && (
            <div className="space-y-6">
              <div className="bg-green-50 p-4 rounded-[20px] border border-green-100 flex items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mb-2">
                    <CheckCircle2 className="h-8 w-8 text-green-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-green-800">Payment Successful</h3>
                  <p className="text-sm text-green-700">Transaction completed successfully</p>
                </div>
              </div>
              
              <Card className="border border-gray-100">
                <CardContent className="p-4 space-y-3">
                  <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                    <span className="text-sm text-gray-500">Service</span>
                    <span className="text-sm font-medium">{completedTransaction.description}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Amount</span>
                    <span className="text-lg font-bold">₦{completedTransaction.amount.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Reference</span>
                    <span className="text-sm font-mono">{completedTransaction.reference}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Date</span>
                    <span className="text-sm">{new Date(completedTransaction.date).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Time</span>
                    <span className="text-sm">{new Date(completedTransaction.date).toLocaleTimeString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Recipient</span>
                    <span className="text-sm font-medium">{completedTransaction.recipient}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-500">Status</span>
                    <span className="text-sm font-medium text-green-600">SUCCESS</span>
                  </div>
                </CardContent>
              </Card>
              
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={handlePrintReceipt}
                >
                  <Printer className="h-4 w-4 mr-2" /> Print
                </Button>
                
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={handleDownloadReceipt}
                >
                  <Download className="h-4 w-4 mr-2" /> Download
                </Button>
                
                <Button 
                  className="flex-1 bg-kojaPrimary hover:bg-kojaPrimary/90"
                  onClick={handleNewPayment}
                >
                  <Receipt className="h-4 w-4 mr-2" /> New Payment
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BillPaymentDialog;
