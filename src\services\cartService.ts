import { Product } from './ecommerceService';
import { useToast } from '@/hooks/use-toast';

export interface CartItem {
  productId: number | string;
  product: Product;
  quantity: number;
}

export interface Cart {
  items: CartItem[];
  totalItems: number;
  subtotal: number;
  lastUpdated: string;
}

// Error handling interfaces
export interface CartError {
  success: boolean;
  message: string;
  errorCode?: string;
  error?: any;
}

// Helper function for standardized error handling
const handleCartError = (error: any, defaultMessage: string, errorCode: string): CartError => {
  console.error(`Cart error (${errorCode}):`, error);
  
  // Log for security monitoring
  const logDetails = {
    timestamp: new Date().toISOString(),
    errorCode,
    message: error.message || defaultMessage,
    stack: error.stack,
  };
  
  console.log('Security log:', JSON.stringify(logDetails));
  
  return {
    success: false,
    message: error.message || defaultMessage,
    errorCode,
    error: process.env.NODE_ENV === 'production' ? undefined : error
  };
};

/**
 * CartService
 * 
 * This service handles shopping cart operations including adding, updating, and removing items,
 * calculating totals, and persisting the cart state to localStorage.
 */
class CartService {
  private readonly STORAGE_KEY = 'koja_pay_cart';
  
  /**
   * Get the current cart from localStorage
   * @returns The current cart or a new empty cart
   */
  getCart(): Cart {
    try {
      if (typeof window === 'undefined') {
        return this.createEmptyCart();
      }
      
      const cartData = localStorage.getItem(this.STORAGE_KEY);
      if (!cartData) {
        return this.createEmptyCart();
      }
      
      const cart: Cart = JSON.parse(cartData);
      return this.recalculateCart(cart);
    } catch (error) {
      console.error('Error getting cart:', error);
      return this.createEmptyCart();
    }
  }
  
  /**
   * Add a product to the cart
   * @param product The product to add
   * @param quantity The quantity to add (default: 1)
   * @returns The updated cart or an error
   */
  addToCart(product: Product, quantity: number = 1): Cart | CartError {
    try {
      // Validate inputs
      if (!product) {
        throw new Error('Product is required');
      }
      if (!product.id) {
        throw new Error('Product ID is required');
      }
      if (quantity <= 0) {
        throw new Error('Quantity must be greater than zero');
      }
      if (product.status === 'Out of Stock') {
        throw new Error('Product is out of stock');
      }
      if (product.inventory < quantity) {
        throw new Error(`Only ${product.inventory} items available`);
      }
      
      // Get current cart
      const cart = this.getCart();
      
      // Check if product already exists in cart
      const existingItemIndex = cart.items.findIndex(item => item.productId.toString() === product.id.toString());
      
      if (existingItemIndex >= 0) {
        // Update quantity if product already exists
        const newQuantity = cart.items[existingItemIndex].quantity + quantity;
        
        // Check if new quantity exceeds inventory
        if (newQuantity > product.inventory) {
          throw new Error(`Cannot add more than ${product.inventory} items`);
        }
        
        cart.items[existingItemIndex].quantity = newQuantity;
      } else {
        // Add new item to cart
        cart.items.push({
          productId: product.id,
          product,
          quantity
        });
      }
      
      // Update cart metadata
      const updatedCart = this.recalculateCart(cart);
      
      // Save to localStorage
      this.saveCart(updatedCart);
      
      // Implement fraud detection checks for suspicious cart activity
      this.performFraudDetectionChecks(updatedCart);
      
      return updatedCart;
    } catch (error) {
      return handleCartError(
        error, 
        'Failed to add product to cart', 
        'ADD_TO_CART_FAILED'
      );
    }
  }
  
  /**
   * Update the quantity of an item in the cart
   * @param productId The ID of the product to update
   * @param quantity The new quantity
   * @returns The updated cart or an error
   */
  updateCartItem(productId: number | string, quantity: number): Cart | CartError {
    try {
      // Validate inputs
      if (!productId) {
        throw new Error('Product ID is required');
      }
      if (quantity < 0) {
        throw new Error('Quantity cannot be negative');
      }
      
      // Get current cart
      const cart = this.getCart();
      
      // Find the item to update
      const itemIndex = cart.items.findIndex(item => item.productId.toString() === productId.toString());
      
      if (itemIndex === -1) {
        throw new Error(`Product with ID ${productId} not found in cart`);
      }
      
      const product = cart.items[itemIndex].product;
      
      // Check inventory if increasing quantity
      if (quantity > cart.items[itemIndex].quantity && quantity > product.inventory) {
        throw new Error(`Only ${product.inventory} items available`);
      }
      
      if (quantity === 0) {
        // Remove item if quantity is 0
        cart.items.splice(itemIndex, 1);
      } else {
        // Update quantity
        cart.items[itemIndex].quantity = quantity;
      }
      
      // Update cart metadata
      const updatedCart = this.recalculateCart(cart);
      
      // Save to localStorage
      this.saveCart(updatedCart);
      
      return updatedCart;
    } catch (error) {
      return handleCartError(
        error, 
        'Failed to update cart item', 
        'UPDATE_CART_ITEM_FAILED'
      );
    }
  }
  
  /**
   * Remove an item from the cart
   * @param productId The ID of the product to remove
   * @returns The updated cart or an error
   */
  removeFromCart(productId: number | string): Cart | CartError {
    try {
      // Validate inputs
      if (!productId) {
        throw new Error('Product ID is required');
      }
      
      // Get current cart
      const cart = this.getCart();
      
      // Find the item to remove
      const itemIndex = cart.items.findIndex(item => item.productId.toString() === productId.toString());
      
      if (itemIndex === -1) {
        throw new Error(`Product with ID ${productId} not found in cart`);
      }
      
      // Remove item
      cart.items.splice(itemIndex, 1);
      
      // Update cart metadata
      const updatedCart = this.recalculateCart(cart);
      
      // Save to localStorage
      this.saveCart(updatedCart);
      
      return updatedCart;
    } catch (error) {
      return handleCartError(
        error, 
        'Failed to remove product from cart', 
        'REMOVE_FROM_CART_FAILED'
      );
    }
  }
  
  /**
   * Clear all items from the cart
   * @returns An empty cart
   */
  clearCart(): Cart {
    const emptyCart = this.createEmptyCart();
    this.saveCart(emptyCart);
    return emptyCart;
  }
  
  /**
   * Create an empty cart object
   * @returns An empty cart
   */
  private createEmptyCart(): Cart {
    return {
      items: [],
      totalItems: 0,
      subtotal: 0,
      lastUpdated: new Date().toISOString()
    };
  }
  
  /**
   * Recalculate cart totals
   * @param cart The cart to recalculate
   * @returns The updated cart with recalculated totals
   */
  private recalculateCart(cart: Cart): Cart {
    const totalItems = cart.items.reduce((sum, item) => sum + item.quantity, 0);
    const subtotal = cart.items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
    
    return {
      ...cart,
      totalItems,
      subtotal,
      lastUpdated: new Date().toISOString()
    };
  }
  
  /**
   * Save the cart to localStorage
   * @param cart The cart to save
   */
  private saveCart(cart: Cart): void {
    if (typeof window === 'undefined') {
      return;
    }
    
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(cart));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
    }
  }
  
  /**
   * Perform fraud detection checks for suspicious cart activity
   * @param cart The cart to check
   */
  private performFraudDetectionChecks(cart: Cart): void {
    try {
      // In a real app, this would call the fraud detection service
      // For now, we'll just log some information
      
      const riskFactors = [];
      
      // Check for high-value cart
      if (cart.subtotal > 500000) {
        riskFactors.push('high_value_cart');
      }
      
      // Check for large quantities of the same item
      const suspiciousItems = cart.items.filter(item => item.quantity > 10);
      if (suspiciousItems.length > 0) {
        riskFactors.push('bulk_purchase');
      }
      
      // Log risk assessment if there are risk factors
      if (riskFactors.length > 0) {
        console.log('Cart risk assessment:', {
          cartItems: cart.totalItems,
          cartValue: cart.subtotal,
          riskFactors,
          riskLevel: riskFactors.length > 1 ? 'elevated' : 'moderate',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      // Don't let fraud detection errors block the cart process
      console.error('Error in cart fraud detection:', error);
    }
  }
}

// Export a singleton instance
export const cartService = new CartService();

/**
 * React hook for using the cart service in components
 */
export const useCart = () => {
  const { toast } = useToast();
  
  const getCart = (): Cart => {
    return cartService.getCart();
  };
  
  const addToCart = (product: Product, quantity: number = 1): Cart => {
    const result = cartService.addToCart(product, quantity);
    
    if ('success' in result && !result.success) {
      toast({
        title: 'Error',
        description: result.message,
        variant: 'destructive',
      });
      return cartService.getCart();
    } else {
      toast({
        title: 'Added to Cart',
        description: `${product.name} has been added to your cart`,
      });
      return result as Cart;
    }
  };
  
  const updateCartItem = (productId: number | string, quantity: number): Cart => {
    const result = cartService.updateCartItem(productId, quantity);
    
    if ('success' in result && !result.success) {
      toast({
        title: 'Error',
        description: result.message,
        variant: 'destructive',
      });
      return cartService.getCart();
    }
    
    return result as Cart;
  };
  
  const removeFromCart = (productId: number | string, productName?: string): Cart => {
    const result = cartService.removeFromCart(productId);
    
    if ('success' in result && !result.success) {
      toast({
        title: 'Error',
        description: result.message,
        variant: 'destructive',
      });
      return cartService.getCart();
    } else if (productName) {
      toast({
        title: 'Removed from Cart',
        description: `${productName} has been removed from your cart`,
      });
    }
    
    return result as Cart;
  };
  
  const clearCart = (): Cart => {
    return cartService.clearCart();
  };
  
  return {
    getCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart
  };
};
