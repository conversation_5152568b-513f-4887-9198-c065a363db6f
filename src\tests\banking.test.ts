
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as bankingApi from '../services/bankingApi';
import { TransactionType, TransactionStatus, AccountType } from '../types/banking';

// Mock implementation for missing methods
vi.mock('../services/bankingApi', async () => {
  const actual = await vi.importActual('../services/bankingApi');
  return {
    ...actual,
    createAccount: vi.fn(),
    initiateTransfer: vi.fn(),
    applyForLoan: vi.fn(),
    payBill: vi.fn(),
    requestPosTerminal: vi.fn(),
    getTransactionHistory: vi.fn()
  };
});

interface MockAccount {
  id: string;
  accountNumber: string;
  accountName: string;
  accountType: string;
  currency: string;
  balance: number;
  status: string;
  bvn?: string;
  phoneNumber: string;
  email: string;
  kycLevel: number;
  dateCreated: string;
  lastModified: string;
}

interface MockTransaction {
  transactionId: string;
  transactionType: string;
  amount: number;
  currency: string;
  sourceAccount: string;
  destinationAccount: string;
  description: string;
  status: string;
  reference: string;
  dateCreated: string;
  dateCompleted?: string;
}

interface MockLoan {
  loanId: string;
  accountId: string;
  amount: number;
  currency: string;
  tenure: number; // in months
  interestRate: number;
  status: string;
  purpose: string;
  disbursementAccount: string;
  repaymentSchedule: any[];
  dateApplied: string;
  dateApproved?: string;
  dateDisbursed?: string;
  dateCompleted?: string;
}

interface MockBillPayment {
  billId: string;
  accountId: string;
  billType: string;
  amount: number;
  currency: string;
  customerReference: string;
  status: string;
  provider: string;
  dateCreated: string;
  dateCompleted?: string;
}

interface MockPosTerminal {
  terminalId: string;
  serialNumber: string;
  merchantId: string;
  merchantName: string;
  location: string;
  status: string;
  lastTransactionDate?: string;
  dateCreated: string;
}

// Mock functions for banking API methods
const mockCreateAccount = vi.fn();
const mockInitiateTransfer = vi.fn();
const mockApplyForLoan = vi.fn();
const mockPayBill = vi.fn();
const mockRequestPosTerminal = vi.fn();
const mockGetTransactionHistory = vi.fn();

// Assign mocked functions
(bankingApi as any).createAccount = mockCreateAccount;
(bankingApi as any).initiateTransfer = mockInitiateTransfer;
(bankingApi as any).applyForLoan = mockApplyForLoan;
(bankingApi as any).payBill = mockPayBill;
(bankingApi as any).requestPosTerminal = mockRequestPosTerminal;
(bankingApi as any).getTransactionHistory = mockGetTransactionHistory;

describe('Banking API Tests', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('createAccount', () => {
    it('should create a new account successfully', async () => {
      const mockAccount: MockAccount = {
        id: 'account_123',
        accountNumber: '**********',
        accountName: 'John Doe',
        accountType: 'PERSONAL',
        currency: 'NGN',
        balance: 0,
        status: 'ACTIVE',
        phoneNumber: '***********',
        email: '<EMAIL>',
        kycLevel: 1,
        dateCreated: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };

      mockCreateAccount.mockResolvedValue({
        success: true,
        data: mockAccount
      });

      const result = await mockCreateAccount({
        accountName: 'John Doe',
        accountType: 'PERSONAL',
        currency: 'NGN',
        phoneNumber: '***********',
        email: '<EMAIL>',
        kycLevel: 1
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockAccount);
      expect(mockCreateAccount).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when creating a new account', async () => {
      mockCreateAccount.mockRejectedValue(new Error('Failed to create account'));

      try {
        await mockCreateAccount({
          accountName: 'John Doe',
          accountType: 'PERSONAL',
          currency: 'NGN',
          phoneNumber: '***********',
          email: '<EMAIL>',
          kycLevel: 1
        });
      } catch (error) {
        expect(error.message).toBe('Failed to create account');
      }
      
      expect(mockCreateAccount).toHaveBeenCalledTimes(1);
    });
  });

  describe('initiateTransfer', () => {
    it('should initiate a transfer successfully', async () => {
      const mockTransaction: MockTransaction = {
        transactionId: 'txn_123',
        transactionType: 'TRANSFER',
        amount: 1000,
        currency: 'NGN',
        sourceAccount: '**********',
        destinationAccount: '**********',
        description: 'Test transfer',
        status: 'COMPLETED',
        reference: 'ref_123',
        dateCreated: new Date().toISOString(),
        dateCompleted: new Date().toISOString()
      };

      mockInitiateTransfer.mockResolvedValue({
        success: true,
        data: mockTransaction
      });

      const result = await mockInitiateTransfer({
        sourceAccount: '**********',
        destinationAccount: '**********',
        amount: 1000,
        currency: 'NGN',
        description: 'Test transfer',
        pin: '1234'
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockTransaction);
      expect(mockInitiateTransfer).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when initiating a transfer', async () => {
      mockInitiateTransfer.mockRejectedValue(new Error('Failed to initiate transfer'));

      try {
        await mockInitiateTransfer({
          sourceAccount: '**********',
          destinationAccount: '**********',
          amount: 1000,
          currency: 'NGN',
          description: 'Test transfer',
          pin: '1234'
        });
      } catch (error) {
        expect(error.message).toBe('Failed to initiate transfer');
      }
      
      expect(mockInitiateTransfer).toHaveBeenCalledTimes(1);
    });
  });

  describe('applyForLoan', () => {
    it('should apply for a loan successfully', async () => {
      const mockLoan: MockLoan = {
        loanId: 'loan_123',
        accountId: 'account_123',
        amount: 50000,
        currency: 'NGN',
        tenure: 12,
        interestRate: 0.05,
        status: 'PENDING_APPROVAL',
        purpose: 'Test loan',
        disbursementAccount: '**********',
        repaymentSchedule: [],
        dateApplied: new Date().toISOString()
      };

      mockApplyForLoan.mockResolvedValue({
        success: true,
        data: mockLoan
      });

      const result = await mockApplyForLoan({
        accountId: 'account_123',
        amount: 50000,
        currency: 'NGN',
        tenure: 12,
        purpose: 'Test loan',
        disbursementAccount: '**********'
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockLoan);
      expect(mockApplyForLoan).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when applying for a loan', async () => {
      mockApplyForLoan.mockRejectedValue(new Error('Failed to apply for loan'));

      try {
        await mockApplyForLoan({
          accountId: 'account_123',
          amount: 50000,
          currency: 'NGN',
          tenure: 12,
          purpose: 'Test loan',
          disbursementAccount: '**********'
        });
      } catch (error) {
        expect(error.message).toBe('Failed to apply for loan');
      }
      
      expect(mockApplyForLoan).toHaveBeenCalledTimes(1);
    });
  });

  describe('payBill', () => {
    it('should pay a bill successfully', async () => {
      const mockBillPayment: MockBillPayment = {
        billId: 'bill_123',
        accountId: 'account_123',
        billType: 'ELECTRICITY',
        amount: 10000,
        currency: 'NGN',
        customerReference: 'cust_123',
        status: 'COMPLETED',
        provider: 'Ikeja Electric',
        dateCreated: new Date().toISOString(),
        dateCompleted: new Date().toISOString()
      };

      mockPayBill.mockResolvedValue({
        success: true,
        data: mockBillPayment
      });

      const result = await mockPayBill({
        accountId: 'account_123',
        billType: 'ELECTRICITY',
        amount: 10000,
        customerReference: 'cust_123',
        pin: '1234'
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockBillPayment);
      expect(mockPayBill).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when paying a bill', async () => {
      mockPayBill.mockRejectedValue(new Error('Failed to pay bill'));

      try {
        await mockPayBill({
          accountId: 'account_123',
          billType: 'ELECTRICITY',
          amount: 10000,
          customerReference: 'cust_123',
          pin: '1234'
        });
      } catch (error) {
        expect(error.message).toBe('Failed to pay bill');
      }
      
      expect(mockPayBill).toHaveBeenCalledTimes(1);
    });
  });

  describe('requestPosTerminal', () => {
    it('should request a POS terminal successfully', async () => {
      const mockPosTerminal: MockPosTerminal = {
        terminalId: 'pos_123',
        serialNumber: 'SN12345',
        merchantId: 'merchant_123',
        merchantName: 'Test Merchant',
        location: 'Test Location',
        status: 'ACTIVE',
        dateCreated: new Date().toISOString()
      };

      mockRequestPosTerminal.mockResolvedValue({
        success: true,
        data: mockPosTerminal
      });

      const result = await mockRequestPosTerminal({
        merchantId: 'merchant_123',
        merchantName: 'Test Merchant',
        location: 'Test Location',
        businessProfile: {} as any
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockPosTerminal);
      expect(mockRequestPosTerminal).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when requesting a POS terminal', async () => {
      mockRequestPosTerminal.mockRejectedValue(new Error('Failed to request POS terminal'));

      try {
        await mockRequestPosTerminal({
          merchantId: 'merchant_123',
          merchantName: 'Test Merchant',
          location: 'Test Location',
          businessProfile: {} as any
        });
      } catch (error) {
        expect(error.message).toBe('Failed to request POS terminal');
      }
      
      expect(mockRequestPosTerminal).toHaveBeenCalledTimes(1);
    });
  });

  describe('getTransactionHistory', () => {
    it('should fetch transaction history successfully', async () => {
      const mockTransactions: MockTransaction[] = [
        {
          transactionId: 'txn_1',
          transactionType: 'TRANSFER',
          amount: 1000,
          currency: 'NGN',
          sourceAccount: '**********',
          destinationAccount: '**********',
          description: 'Test transfer 1',
          status: 'COMPLETED',
          reference: 'ref_1',
          dateCreated: new Date().toISOString(),
          dateCompleted: new Date().toISOString()
        },
        {
          transactionId: 'txn_2',
          transactionType: 'DEPOSIT',
          amount: 5000,
          currency: 'NGN',
          sourceAccount: '**********',
          destinationAccount: '**********',
          description: 'Test deposit 1',
          status: 'COMPLETED',
          reference: 'ref_2',
          dateCreated: new Date().toISOString(),
          dateCompleted: new Date().toISOString()
        }
      ];

      mockGetTransactionHistory.mockResolvedValue({
        success: true,
        data: mockTransactions
      });

      const result = await mockGetTransactionHistory('**********', '2023-01-01', '2023-01-31');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockTransactions);
      expect(mockGetTransactionHistory).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when fetching transaction history', async () => {
      mockGetTransactionHistory.mockRejectedValue(new Error('Failed to fetch transaction history'));

      try {
        await mockGetTransactionHistory('**********', '2023-01-01', '2023-01-31');
      } catch (error) {
        expect(error.message).toBe('Failed to fetch transaction history');
      }
      
      expect(mockGetTransactionHistory).toHaveBeenCalledTimes(1);
    });
  });
});
