
import React from 'react';
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

const data = [
  { name: 'Jan', income: 1100, expenses: 900 },
  { name: 'Feb', income: 1300, expenses: 1000 },
  { name: 'Mar', income: 1400, expenses: 1100 },
  { name: 'Apr', income: 1700, expenses: 1300 },
  { name: 'May', income: 1900, expenses: 1400 },
  { name: 'Jun', income: 1400, expenses: 1500 },
  { name: 'Jul', income: 1500, expenses: 1800 },
  { name: 'Aug', income: 1800, expenses: 1700 },
  { name: 'Sep', income: 2000, expenses: 1800 },
  { name: 'Oct', income: 2200, expenses: 1900 },
  { name: 'Nov', income: 2100, expenses: 1700 },
  { name: 'Dec', income: 2300, expenses: 1800 },
];

const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-black p-2 rounded-md shadow-lg text-white text-xs">
        <p className="font-semibold mb-1">{label}</p>
        <p className="text-green-400">{`Income: ${payload[0].value.toLocaleString()}`}</p>
        <p className="text-gray-300">{`Expenses: ${payload[1].value.toLocaleString()}`}</p>
      </div>
    );
  }

  return null;
};

const FinancialChart = () => {
  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
        >
          <defs>
            <linearGradient id="colorIncome" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#059669" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#059669" stopOpacity={0} />
            </linearGradient>
            <linearGradient id="colorExpenses" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#D1D5DB" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#D1D5DB" stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
          <XAxis 
            dataKey="name" 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#9CA3AF' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: '#9CA3AF' }}
            tickFormatter={(value) => `${value/1000}k$`}
          />
          <Tooltip content={<CustomTooltip />} />
          <Area 
            type="monotone" 
            dataKey="expenses" 
            stroke="#D1D5DB" 
            fillOpacity={1} 
            fill="url(#colorExpenses)" 
            strokeWidth={2}
          />
          <Area 
            type="monotone" 
            dataKey="income" 
            stroke="#059669" 
            fillOpacity={1} 
            fill="url(#colorIncome)" 
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default FinancialChart;
