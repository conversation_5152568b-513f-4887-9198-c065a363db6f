
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useAuth } from '@/contexts/AuthContext';
import { 
  User, 
  Building, 
  Users, 
  ArrowLeftRight, 
  ChevronDown,
  Check
} from 'lucide-react';
import { AccountType } from '@/types/account';

const AccountSwitcher = () => {
  const { user, accountType, userAccounts, switchAccount, switchToJointAccount } = useAuth();
  const [open, setOpen] = useState(false);

  const accountTypeIcons = {
    personal: User,
    business: Building,
    joint: Users
  };

  const availableAccounts = Object.keys(userAccounts || {});
  const jointAccounts = (user as any)?.jointAccounts || [];

  const handleAccountSwitch = (newAccountType: AccountType) => {
    if (switchAccount) {
      switchAccount(newAccountType);
      setOpen(false);
    }
  };

  const handleJointAccountSwitch = (jointAccountId: string) => {
    if (switchToJointAccount) {
      switchToJointAccount(jointAccountId);
      setOpen(false);
    }
  };

  const CurrentIcon = accountTypeIcons[accountType as keyof typeof accountTypeIcons] || User;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          className="flex items-center gap-2 border-kojaPrimary/20 hover:bg-kojaPrimary/5"
        >
          <CurrentIcon className="h-4 w-4" />
          <span className="capitalize">{accountType}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowLeftRight className="h-5 w-5" />
            Switch Account
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium mb-3">Account Types</h4>
            <div className="space-y-2">
              {availableAccounts.map((type) => {
                const Icon = accountTypeIcons[type as keyof typeof accountTypeIcons];
                const isActive = type === accountType;
                
                return (
                  <Card 
                    key={type}
                    className={`cursor-pointer transition-all duration-200 ${
                      isActive ? 'border-kojaPrimary bg-kojaPrimary/5' : 'hover:border-kojaPrimary/30'
                    }`}
                    onClick={() => handleAccountSwitch(type as AccountType)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-lg ${
                            isActive ? 'bg-kojaPrimary text-white' : 'bg-gray-100'
                          }`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="font-medium capitalize">{type} Account</p>
                            <p className="text-sm text-gray-600">
                              {userAccounts?.[type]?.fullName || userAccounts?.[type]?.name}
                            </p>
                          </div>
                        </div>
                        {isActive && (
                          <Badge variant="secondary" className="bg-kojaPrimary text-white">
                            <Check className="h-3 w-3 mr-1" />
                            Active
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {jointAccounts.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3">Joint Accounts</h4>
              <div className="space-y-2">
                {jointAccounts.map((account: any) => (
                  <Card 
                    key={account.id}
                    className="cursor-pointer hover:border-kojaPrimary/30 transition-all duration-200"
                    onClick={() => handleJointAccountSwitch(account.id)}
                  >
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="bg-gradient-to-r from-kojaPrimary to-blue-600 p-2 rounded-lg">
                          <Users className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium">{account.name}</p>
                          <p className="text-sm text-gray-600">
                            ₦{account.balance.toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AccountSwitcher;
