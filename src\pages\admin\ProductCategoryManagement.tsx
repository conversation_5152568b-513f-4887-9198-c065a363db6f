
import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { Helmet } from 'react-helmet-async';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  Edit, Eye, MoreHorizontal, Package, Pencil, Plus, Search, ShoppingBag, Tag, Trash, Box, 
  Loader2, ClipboardList, ShoppingCart, Store, Truck, ChevronUp, ChevronDown, Archive, Filter
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

const ProductCategoryManagement = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddCategoryDialog, setShowAddCategoryDialog] = useState(false);
  const [editCategoryId, setEditCategoryId] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  
  // Mock product categories data
  const [categories, setCategories] = useState([
    { id: 'cat1', name: 'Electronics', slug: 'electronics', productsCount: 1245, active: true, icon: 'laptop', description: 'Electronic devices and gadgets', parent: null, level: 0, position: 1 },
    { id: 'cat2', name: 'Smartphones', slug: 'smartphones', productsCount: 458, active: true, icon: 'smartphone', description: 'Mobile phones and accessories', parent: 'cat1', level: 1, position: 2 },
    { id: 'cat3', name: 'Laptops', slug: 'laptops', productsCount: 326, active: true, icon: 'laptop', description: 'Laptops and notebooks', parent: 'cat1', level: 1, position: 3 },
    { id: 'cat4', name: 'Fashion', slug: 'fashion', productsCount: 890, active: true, icon: 'shirt', description: 'Clothing and fashion items', parent: null, level: 0, position: 4 },
    { id: 'cat5', name: "Men's Wear", slug: 'mens-wear', productsCount: 356, active: true, icon: 'user', description: 'Clothing for men', parent: 'cat4', level: 1, position: 5 },
    { id: 'cat6', name: "Women's Wear", slug: 'womens-wear', productsCount: 534, active: true, icon: 'user', description: 'Clothing for women', parent: 'cat4', level: 1, position: 6 },
    { id: 'cat7', name: 'Groceries', slug: 'groceries', productsCount: 652, active: true, icon: 'shopping-basket', description: 'Food and grocery items', parent: null, level: 0, position: 7 },
    { id: 'cat8', name: 'Home & Kitchen', slug: 'home-kitchen', productsCount: 724, active: true, icon: 'home', description: 'Home and kitchen appliances', parent: null, level: 0, position: 8 },
    { id: 'cat9', name: 'Sports & Outdoors', slug: 'sports-outdoors', productsCount: 412, active: false, icon: 'activity', description: 'Sports equipment and outdoor gear', parent: null, level: 0, position: 9 },
  ]);
  
  const filteredCategories = categories
    .filter(category => 
      category.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      category.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  
  const getParentName = (parentId: string | null) => {
    if (!parentId) return '-';
    const parent = categories.find(cat => cat.id === parentId);
    return parent ? parent.name : '-';
  };
  
  const handleAddCategory = (e: React.FormEvent) => {
    e.preventDefault();
    // Logic to add new category
    toast.success('New product category added successfully');
    setShowAddCategoryDialog(false);
  };
  
  const handleEditCategory = (id: string) => {
    setEditCategoryId(id);
    setShowAddCategoryDialog(true);
  };
  
  const handleDeleteCategory = (id: string) => {
    setSelectedCategoryId(id);
    setShowConfirmDialog(true);
  };
  
  const confirmDelete = () => {
    if (selectedCategoryId) {
      // Logic to delete category
      toast.success('Category deleted successfully');
      setShowConfirmDialog(false);
      setSelectedCategoryId(null);
    }
  };
  
  const toggleCategoryStatus = (id: string) => {
    setCategories(prev => 
      prev.map(cat => 
        cat.id === id ? { ...cat, active: !cat.active } : cat
      )
    );
    
    const category = categories.find(c => c.id === id);
    if (category) {
      toast.success(`${category.name} ${category.active ? 'deactivated' : 'activated'} successfully`);
    }
  };
  
  const moveCategoryUp = (id: string) => {
    // Logic to move category up in order
    toast.success('Category order updated');
  };
  
  const moveCategoryDown = (id: string) => {
    // Logic to move category down in order
    toast.success('Category order updated');
  };
  
  return (
    <AdminLayout pageTitle="Product Category Management">
      <Helmet>
        <title>Product Categories | KojaPay Admin</title>
      </Helmet>
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Product Category Management</h1>
        <p className="text-gray-500">Manage product categories for the marketplace</p>
      </div>
      
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <CardTitle>Product Categories</CardTitle>
              <CardDescription>Organize products with categories and subcategories</CardDescription>
            </div>
            
            <Dialog open={showAddCategoryDialog} onOpenChange={setShowAddCategoryDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Category
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>{editCategoryId ? 'Edit Category' : 'Add New Category'}</DialogTitle>
                  <DialogDescription>
                    {editCategoryId ? 'Update category details.' : 'Create a new product category for your marketplace.'}
                  </DialogDescription>
                </DialogHeader>
                
                <form onSubmit={handleAddCategory}>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category-name" className="text-right">Name</Label>
                      <Input id="category-name" className="col-span-3" required defaultValue={editCategoryId ? categories.find(c => c.id === editCategoryId)?.name : ''} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category-slug" className="text-right">Slug</Label>
                      <Input id="category-slug" className="col-span-3" required defaultValue={editCategoryId ? categories.find(c => c.id === editCategoryId)?.slug : ''} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category-description" className="text-right">Description</Label>
                      <Textarea id="category-description" className="col-span-3" defaultValue={editCategoryId ? categories.find(c => c.id === editCategoryId)?.description : ''} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category-parent" className="text-right">Parent Category</Label>
                      <select 
                        id="category-parent" 
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 col-span-3"
                        defaultValue={editCategoryId ? categories.find(c => c.id === editCategoryId)?.parent || "" : ""}
                      >
                        <option value="">-- None (Top Level) --</option>
                        {categories.filter(c => c.level === 0).map(cat => (
                          <option key={cat.id} value={cat.id}>{cat.name}</option>
                        ))}
                      </select>
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category-icon" className="text-right">Icon</Label>
                      <Input id="category-icon" className="col-span-3" defaultValue={editCategoryId ? categories.find(c => c.id === editCategoryId)?.icon : ''} />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="category-active" className="text-right">Status</Label>
                      <div className="flex items-center space-x-2 col-span-3">
                        <Switch 
                          id="category-active" 
                          defaultChecked={editCategoryId ? categories.find(c => c.id === editCategoryId)?.active : true} 
                        />
                        <Label htmlFor="category-active">Active</Label>
                      </div>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setShowAddCategoryDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">Save Category</Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
            
            {/* Delete Confirmation Dialog */}
            <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Confirm Delete</DialogTitle>
                  <DialogDescription>
                    Are you sure you want to delete this category? This action cannot be undone and may affect associated products.
                  </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowConfirmDialog(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="button"
                    variant="destructive"
                    onClick={confirmDelete}
                  >
                    Delete
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Button variant="outline" className="w-full sm:w-auto">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Parent</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Position</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCategories.length > 0 ? (
                  filteredCategories.map((category) => (
                    <TableRow key={category.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="bg-gray-100 p-1 rounded">
                            <Package className="h-4 w-4 text-gray-500" />
                          </div>
                          <div>
                            <p className="font-medium">{category.name}</p>
                            <p className="text-xs text-gray-500">{category.slug}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getParentName(category.parent)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <ShoppingBag className="h-3.5 w-3.5 mr-1 text-gray-500" />
                          {category.productsCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={category.active ? "bg-green-100 text-green-800 border-green-200" : "bg-gray-100 text-gray-800 border-gray-200"}>
                          {category.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => moveCategoryUp(category.id)}
                          >
                            <ChevronUp className="h-3 w-3" />
                          </Button>
                          <span className="text-sm">{category.position}</span>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => moveCategoryDown(category.id)}
                          >
                            <ChevronDown className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => toast.success(`Viewing ${category.name}`)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Products
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditCategory(category.id)}>
                              <Pencil className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => toggleCategoryStatus(category.id)}>
                              {category.active ? (
                                <>
                                  <Archive className="h-4 w-4 mr-2" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <Tag className="h-4 w-4 mr-2" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteCategory(category.id)}
                              className="text-red-600"
                            >
                              <Trash className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4">
                      No categories found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-gray-500">
            Showing {filteredCategories.length} of {categories.length} categories
          </div>
          {/* Pagination would go here */}
        </CardFooter>
      </Card>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Category Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Total Categories</span>
                <span className="font-semibold">{categories.length}</span>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Active Categories</span>
                <span className="font-semibold">{categories.filter(c => c.active).length}</span>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Top-level Categories</span>
                <span className="font-semibold">{categories.filter(c => c.level === 0).length}</span>
              </div>
              <Separator />
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Sub-categories</span>
                <span className="font-semibold">{categories.filter(c => c.level > 0).length}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Top Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {categories
                .sort((a, b) => b.productsCount - a.productsCount)
                .slice(0, 5)
                .map((category, index) => (
                  <div key={category.id} className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <span className="text-gray-500">{index + 1}.</span>
                      <span>{category.name}</span>
                    </div>
                    <span className="font-semibold">{category.productsCount} products</span>
                  </div>
                ))
              }
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" onClick={() => toast.success("Generating report")}>
              <ClipboardList className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </CardFooter>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => setShowAddCategoryDialog(true)}>
                <Tag className="h-5 w-5 mb-2" />
                <span className="text-xs">New Category</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => toast.success("Bulk import started")}>
                <Truck className="h-5 w-5 mb-2" />
                <span className="text-xs">Bulk Import</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => toast.success("Export started")}>
                <Box className="h-5 w-5 mb-2" />
                <span className="text-xs">Export Categories</span>
              </Button>
              
              <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => toast.success("Reordering mode activated")}>
                <Loader2 className="h-5 w-5 mb-2" />
                <span className="text-xs">Reorder All</span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default ProductCategoryManagement;
