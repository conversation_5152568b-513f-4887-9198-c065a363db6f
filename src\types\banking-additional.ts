// Define additional banking types that weren't in the original banking.d.ts file

// Basic enums
export enum AccountType {
  SAVINGS = 'SAVINGS',
  CURRENT = 'CURRENT',
  FIXED = 'FIXED',
  LOAN = 'LOAN'
}

export enum TransactionType {
  CREDIT = 'credit',
  DEBIT = 'debit',
  TRANSFER = 'transfer',
  WITHDRAWAL = 'withdrawal',
  DEPOSIT = 'deposit'
}

// Order and Product types
export interface OrderItem {
  id?: string | number;
  productId: string | number;
  productName?: string;
  name?: string;
  quantity: number;
  unitPrice?: number;
  price?: number;
  totalPrice?: number;
  image?: string;
}

export interface Product {
  id: string | number;
  name: string;
  description: string;
  price: number;
  image?: string;
  category?: string;
  stock?: number;
  inventory?: number;
  rating?: number;
  reviews?: number;
  storeName?: string;
  storeId?: string;
  status?: string;
  tags?: string[];
  specifications?: Record<string, string>;
  discountPercentage?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface Order {
  id: string;
  customerId: string;
  customerName: string;
  items: OrderItem[];
  products?: OrderItem[];
  totalAmount: number;
  status: string;
  paymentStatus: string;
  shippingAddress: string;
  trackingNumber?: string;
  createdAt: string;
  updatedAt: string;
  estimatedDelivery?: string;
  notes?: string;
  escrowId?: string;
  sellerId?: string;
}

// Transaction type
export interface Transaction {
  id: string;
  amount: number;
  type: string;
  status: string;
  reference: string;
  narration?: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
  metadata?: Record<string, any>;
  recipientId?: string;
  recipientName?: string;
  sourceAccount?: string;
  destinationAccount?: string;
  balance?: number;
  fee?: number;
  timestamp?: string;
}

export interface TransactionRequest {
  amount: number;
  recipientId: string;
  recipientName?: string;
  narration?: string;
  description?: string;
  reference?: string;
  metadata?: Record<string, any>;
  sourceAccountId?: string;
  destinationAccountId?: string;
  destinationAccountNumber?: string;
}

// Bank and account types
export interface BankAccount {
  id: string;
  accountNumber: string;
  accountName: string;
  accountType: string;
  balance: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

export interface Bank {
  code: string;
  name: string;
  country: string;
  currency: string;
}

export interface PaymentLink {
  id: string;
  name: string;
  description?: string;
  amount: number;
  currency: string;
  reference: string;
  status: string;
  redirectUrl?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface VirtualAccount {
  id: string;
  accountNumber: string;
  accountName: string;
  bankName: string;
  bankCode: string;
  reference: string;
  status: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface TransferRecipient {
  id: string;
  accountNumber: string;
  name: string;
  bankCode: string;
  bankName: string;
  createdAt: string;
}

export interface Card {
  id: string;
  last4: string;
  expMonth: number;
  expYear: number;
  brand: string;
  cardholderName: string;
  funding: string;
  country: string;
  accountId: string;
  createdAt: string;
  cardNumber?: string;
  expiryDate?: string;
  cardName?: string;
}

export interface CardRequest {
  accountId: string;
  cardName?: string;
  cardType?: string;
  address?: string;
  deliveryMethod?: string;
}

export interface Bill {
  id: string;
  amount: number;
  dueDate: string;
  accountId: string;
  createdAt: string;
  billName: string;
}

export interface TransactionAnalytics {
  totalIncome: number;
  totalExpenses: number;
  incomeByCategory: Record<string, number>;
  expensesByCategory: Record<string, number>;
}

export interface BankStatement {
  startDate: string;
  endDate: string;
  transactions: Transaction[];
  startBalance: number;
  endBalance: number;
}

export interface StandingOrder {
  id?: string;
  accountId: string;
  recipientId: string;
  amount: number;
  frequency: string;
  nextRunDate: string;
  endDate?: string;
  status?: string;
  description?: string;
}

export interface ApiErrorResponse {
  success: boolean;
  message: string;
  code?: string;
  error?: string;
}
