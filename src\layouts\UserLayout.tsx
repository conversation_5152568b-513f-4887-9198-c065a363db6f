
import React from 'react';
import PersonalSidebar from '@/components/PersonalSidebar';
import BusinessSidebar from '@/components/BusinessSidebar';
import { useAuth } from '@/contexts/AuthContext';

interface UserLayoutProps {
  children: React.ReactNode;
}

const UserLayout: React.FC<UserLayoutProps> = ({ children }) => {
  const { accountType } = useAuth();

  return (
    <div className="flex h-screen bg-gray-50">
      {accountType === 'business' ? <BusinessSidebar /> : <PersonalSidebar />}
      
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50">
          <div className="container mx-auto px-6 py-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default UserLayout;
