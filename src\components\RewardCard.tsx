
import React from 'react';
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Clock, Gift } from "lucide-react";

interface RewardCardProps {
  title: string;
  points: number;
  category: string;
  image: string;
  expiryDate: string;
  onRedeem: () => void;
  userPoints: number;
}

const RewardCard: React.FC<RewardCardProps> = ({
  title,
  points,
  category,
  image,
  expiryDate,
  onRedeem,
  userPoints
}) => {
  const canRedeem = userPoints >= points;
  
  return (
    <Card className="modern-card hover:shadow-md transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          <div className="h-16 w-16 rounded-lg overflow-hidden">
            <img src={image} alt={title} className="h-full w-full object-cover" />
          </div>
          <div>
            <h3 className="font-medium mb-1">{title}</h3>
            <Badge className="bg-kojaYellow/10 text-kojaYellow hover:bg-kojaYellow/20 border border-kojaYellow/20">
              {category}
            </Badge>
          </div>
        </div>
        
        <div className="flex justify-between items-center mt-4">
          <div className="flex items-center gap-1 text-kojaGray text-sm">
            <Clock size={16} />
            <span>Expires: {expiryDate}</span>
          </div>
          <div className="font-semibold text-kojaDark flex items-center gap-1">
            <Gift size={16} className="text-kojaYellow" />
            {points} points
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="border-t p-4">
        <Button 
          onClick={onRedeem}
          disabled={!canRedeem}
          className={`w-full ${canRedeem ? 'bg-kojaYellow text-kojaDark hover:bg-kojaYellow/90' : 'bg-gray-100 text-kojaGray'}`}
        >
          {canRedeem ? 'Redeem Reward' : `Need ${points - userPoints} more points`}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default RewardCard;
