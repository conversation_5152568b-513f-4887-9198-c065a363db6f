@keyframes toast-slide-in {
  from {
    transform: translateY(calc(100% + 1rem));
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes toast-slide-out {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(calc(100% + 1rem));
    opacity: 0;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes subtle-float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

@keyframes pulse-shadow {
  0%, 100% { box-shadow: 0 0 0 rgba(18, 49, 184, 0.4); }
  50% { box-shadow: 0 0 20px rgba(18, 49, 184, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

@keyframes scale-bounce {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes fade-in-out {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

@keyframes ripple {
  0% { box-shadow: 0 0 0 0 rgba(18, 49, 184, 0.3); }
  100% { box-shadow: 0 0 0 20px rgba(18, 49, 184, 0); }
}

@keyframes slide-up {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slide-in-right {
  0% { transform: translateX(20px); opacity: 0; }
  100% { transform: translateX(0); opacity: 1; }
}

@keyframes rotate-in {
  0% { transform: rotateY(90deg); opacity: 0; }
  100% { transform: rotateY(0); opacity: 1; }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-subtle-float {
  animation: subtle-float 3s ease-in-out infinite;
}

.animate-pulse-shadow {
  animation: pulse-shadow 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite linear;
}

.animate-scale-bounce {
  animation: scale-bounce 2s infinite ease-in-out;
}

.animate-fade-in-out {
  animation: fade-in-out 2s infinite ease-in-out;
}

.animate-ripple {
  position: relative;
  overflow: hidden;
}

.animate-ripple:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%);
  transform-origin: 50% 50%;
}

.animate-ripple:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-rotate-in {
  animation: rotate-in 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.animate-blink {
  animation: blink 1.5s ease-in-out infinite;
}

.fade-in {
  opacity: 0;
  animation: fade-in 0.5s forwards;
}

.stagger-item {
  opacity: 0;
  transform: translateY(10px);
}

.stagger-active .stagger-item {
  animation: stagger-fade-in 0.3s ease forwards;
}

.stagger-active .stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-active .stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-active .stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-active .stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-active .stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-active .stagger-item:nth-child(6) { animation-delay: 0.6s; }
.stagger-active .stagger-item:nth-child(7) { animation-delay: 0.7s; }

@keyframes stagger-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}
