
import React, { ReactNode } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface StatCardProps {
  title: string;
  value: string;
  icon: ReactNode;
  className?: string;
  trend?: {
    value: string;
    positive?: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  className,
  trend
}) => {
  return (
    <Card 
      variant="frosted" 
      className={cn(
        "wallet-balance-card overflow-hidden backdrop-blur-2xl border border-[#D3E4FD] shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl rounded-2xl sm:rounded-3xl", 
        className
      )}
    >
      <CardContent className="p-3 sm:p-4 md:p-5">
        <div className="relative z-10">
          {/* Enhanced yellow blur effect */}
          <div className="absolute -top-3 -right-3 w-12 sm:w-16 h-12 sm:h-16 bg-gradient-to-br from-[#FDE314]/30 to-transparent rounded-full blur-xl opacity-60"></div>
        </div>
        
        <div className="flex justify-between items-start relative z-20">
          <div>
            <p className="text-xs sm:text-sm text-gray-800 mb-1 sm:mb-1.5 font-poppins">{title}</p>
            <p className="text-sm sm:text-base font-semibold text-[#1231B8] font-poppins">{value}</p>
            {trend && (
              <p className={cn(
                "text-xs mt-1 sm:mt-2 flex items-center font-poppins", 
                trend.positive ? "text-green-600" : "text-red-600"
              )}>
                <span className="mr-1">{trend.positive ? '↑' : '↓'}</span>
                {trend.value}
              </p>
            )}
          </div>
          <div className="p-2 sm:p-2.5 md:p-3 rounded-full backdrop-blur-md transform hover:scale-105 transition-all duration-300 active:scale-95 bg-white/90 shadow-md border border-[#fde314]/20">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StatCard;
