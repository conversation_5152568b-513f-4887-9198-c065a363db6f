
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  MoreHorizontal, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  Lock,
  RefreshCcw,
  Clock,
  Eye,
  UserX,
  XCircle,
  ShieldAlert
} from 'lucide-react';

const SecurityManagement = () => {
  const securityAlerts = [
    { 
      id: 'SA001', 
      type: 'Login Attempt', 
      status: 'High Risk',
      user: '<EMAIL>',
      location: 'Lagos, Nigeria',
      ipAddress: '************',
      device: 'iPhone (iOS 16.5)',
      timestamp: '2023-06-15 08:24:36',
      action: 'Blocked'
    },
    { 
      id: 'SA002', 
      type: 'Password Reset', 
      status: 'Medium Risk',
      user: '<EMAIL>',
      location: 'Abuja, Nigeria',
      ipAddress: '*************',
      device: 'Chrome on Windows',
      timestamp: '2023-06-15 10:12:45',
      action: 'Approved'
    },
    { 
      id: 'SA003', 
      type: 'Large Transaction', 
      status: 'High Risk',
      user: '<EMAIL>',
      location: 'Port Harcourt, Nigeria',
      ipAddress: '*************',
      device: 'Safari on Mac',
      timestamp: '2023-06-15 11:35:22',
      action: 'Under Review'
    },
    { 
      id: 'SA004', 
      type: 'Multiple Failed Logins', 
      status: 'Critical',
      user: '<EMAIL>',
      location: 'Kano, Nigeria',
      ipAddress: '************',
      device: 'Firefox on Linux',
      timestamp: '2023-06-15 13:47:09',
      action: 'Blocked'
    },
    { 
      id: 'SA005', 
      type: 'New Device Login', 
      status: 'Low Risk',
      user: '<EMAIL>',
      location: 'Ibadan, Nigeria',
      ipAddress: '*************',
      device: 'Android 13',
      timestamp: '2023-06-15 15:22:58',
      action: 'Approved'
    },
  ];

  return (
    <AdminLayout pageTitle="Security Management">
      <Helmet>
        <title>Security Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Critical Alerts</CardTitle>
              <CardDescription>Require immediate action</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">5</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Failed Logins</CardTitle>
              <CardDescription>Last 24 hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">43</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Blocked Attempts</CardTitle>
              <CardDescription>Last 24 hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">18</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Security Score</CardTitle>
              <CardDescription>Platform health</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">93%</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="alerts" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="alerts">Security Alerts</TabsTrigger>
            <TabsTrigger value="access">Access Controls</TabsTrigger>
            <TabsTrigger value="settings">Security Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="alerts">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Security Alerts</CardTitle>
                  <CardDescription>Monitor and respond to security incidents</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <RefreshCcw size={16} />
                    <span className="hidden sm:inline">Refresh</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Export Log
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search alerts..." 
                      className="pl-9"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="cursor-pointer hover:bg-slate-100">
                      High Risk
                    </Badge>
                    <Badge variant="outline" className="cursor-pointer hover:bg-slate-100">
                      Blocked
                    </Badge>
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>Security alerts and incidents</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Alert ID</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Risk Level</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Action Taken</TableHead>
                        <TableHead className="text-right">Options</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {securityAlerts.map((alert) => (
                        <TableRow key={alert.id}>
                          <TableCell className="font-medium">{alert.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {alert.type === 'Login Attempt' && <Lock size={16} className="text-amber-600" />}
                              {alert.type === 'Password Reset' && <RefreshCcw size={16} className="text-blue-600" />}
                              {alert.type === 'Large Transaction' && <AlertTriangle size={16} className="text-red-600" />}
                              {alert.type === 'Multiple Failed Logins' && <ShieldAlert size={16} className="text-red-600" />}
                              {alert.type === 'New Device Login' && <Eye size={16} className="text-purple-600" />}
                              {alert.type}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              alert.status === 'Low Risk' ? 'outline' : 
                              alert.status === 'Medium Risk' ? 'secondary' : 
                              alert.status === 'High Risk' ? 'destructive' : 
                              'destructive'
                            }>
                              {alert.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{alert.user}</TableCell>
                          <TableCell>{alert.location}</TableCell>
                          <TableCell>{alert.timestamp}</TableCell>
                          <TableCell>
                            <Badge variant={
                              alert.action === 'Approved' ? 'outline' : 
                              alert.action === 'Under Review' ? 'secondary' : 
                              'destructive'
                            }>
                              {alert.action === 'Approved' && <CheckCircle size={12} className="mr-1 text-green-600" />}
                              {alert.action === 'Under Review' && <Clock size={12} className="mr-1" />}
                              {alert.action === 'Blocked' && <XCircle size={12} className="mr-1" />}
                              {alert.action}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                {alert.action !== 'Approved' && (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                    <span className="text-green-600">Approve</span>
                                  </DropdownMenuItem>
                                )}
                                {alert.action !== 'Blocked' && (
                                  <DropdownMenuItem>
                                    <UserX className="mr-2 h-4 w-4 text-red-600" />
                                    <span className="text-red-600">Block</span>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="access">
            <Card>
              <CardHeader>
                <CardTitle>Access Controls</CardTitle>
                <CardDescription>Manage user access and permissions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <Shield size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Access Control Management</h3>
                  <p className="text-gray-500 mb-6">Configure roles, permissions, and user access levels</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Configure Access Controls</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>Manage global security configurations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <Lock size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Platform Security Configuration</h3>
                  <p className="text-gray-500 mb-6">Adjust security policies, authentication requirements, and more</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Edit Security Settings</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default SecurityManagement;
