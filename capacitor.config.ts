
import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'app.lovable.b84755f4d5e44f4bb0c205bf5f1c38b4',
  appName: 'netbankingkojapay-98',
  webDir: 'dist',
  server: {
    url: 'https://b84755f4-d5e4-4f4b-b0c2-05bf5f1c38b4.lovableproject.com?forceHideBadge=true',
    cleartext: true
  },
  ios: {
    scheme: 'KojaPay'
  },
  android: {
    buildOptions: {
      keystorePath: 'release.keystore',
      keystoreAlias: 'kojapay',
      keystoreAliasPassword: 'kojapay',
      keystorePassword: 'kojapay'
    }
  }
};

export default config;
