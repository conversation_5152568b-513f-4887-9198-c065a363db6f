import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as cardApi from '../services/cardApi';
import { ApiError } from '../types/errorHandling';

// Mock implementation for missing methods
vi.mock('../services/cardApi', async () => {
  const actual = await vi.importActual('../services/cardApi');
  return {
    ...actual,
    requestDebitCard: vi.fn(),
    requestCardPrinting: vi.fn(),
    activateCard: vi.fn()
  };
});

interface Card {
  id: string;
  cardNumber: string;
  expiryDate: string;
  cardType: string;
}

describe('Card API Tests', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('requestDebitCard', () => {
    it('should request a debit card successfully', async () => {
      // Use the mock implementation
      const mockCard = {
        id: 'card_123',
        cardNumber: '**** **** **** 1234',
        expiryDate: '12/25',
        cardType: 'visa'
      };

      vi.mocked(cardApi.requestDebitCard).mockResolvedValue({
        success: true,
        data: mockCard
      });

      const result = await cardApi.requestDebitCard({
        userId: 'user_123',
        accountId: 'account_123',
        cardType: 'debit'
      });

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockCard);
      expect(cardApi.requestDebitCard).toHaveBeenCalledTimes(1);
    });

    it('should handle errors when requesting a debit card', async () => {
      vi.mocked(cardApi.requestDebitCard).mockRejectedValue(new Error('Failed to request card'));

      try {
        await cardApi.requestDebitCard({
          userId: 'user_123',
          accountId: 'account_123',
          cardType: 'debit'
        });
      } catch (error: any) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toBe('Failed to request card');
      }

      expect(cardApi.requestDebitCard).toHaveBeenCalledTimes(1);
    });
  });

  describe('requestCardPrinting', () => {
    it('should request card printing successfully', async () => {
      const mockCard = {
        id: 'card_123',
        cardNumber: '**** **** **** 1234',
        expiryDate: '12/25',
        cardType: 'visa'
      };

      vi.mocked(cardApi.requestCardPrinting).mockResolvedValue({
        success: true,
        data: mockCard
      });

      const result = await cardApi.requestCardPrinting('card_123');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockCard);
      expect(cardApi.requestCardPrinting).toHaveBeenCalledTimes(1);
      expect(cardApi.requestCardPrinting).toHaveBeenCalledWith('card_123');
    });

    it('should handle errors when requesting card printing', async () => {
      vi.mocked(cardApi.requestCardPrinting).mockRejectedValue(new Error('Failed to request card printing'));

      try {
        await cardApi.requestCardPrinting('card_123');
      } catch (error: any) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toBe('Failed to request card printing');
      }

      expect(cardApi.requestCardPrinting).toHaveBeenCalledTimes(1);
      expect(cardApi.requestCardPrinting).toHaveBeenCalledWith('card_123');
    });
  });

  describe('activateCard', () => {
    it('should activate a card successfully', async () => {
      vi.mocked(cardApi.activateCard).mockResolvedValue({
        success: true,
        message: 'Card activated successfully'
      });

      const result = await cardApi.activateCard('card_123');

      expect(result.success).toBe(true);
      expect(result.message).toBe('Card activated successfully');
      expect(cardApi.activateCard).toHaveBeenCalledTimes(1);
      expect(cardApi.activateCard).toHaveBeenCalledWith('card_123');
    });

    it('should handle errors when activating a card', async () => {
      vi.mocked(cardApi.activateCard).mockRejectedValue(new Error('Failed to activate card'));

      try {
        await cardApi.activateCard('card_123');
      } catch (error: any) {
        expect(error).toBeInstanceOf(Error);
        expect(error.message).toBe('Failed to activate card');
      }

      expect(cardApi.activateCard).toHaveBeenCalledTimes(1);
      expect(cardApi.activateCard).toHaveBeenCalledWith('card_123');
    });
  });
});
