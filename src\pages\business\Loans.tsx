
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { formatDate } from '@/lib/utils';
import TransactionStatus from '@/components/TransactionStatus';

const BusinessLoans = () => {
  const [loanAmount, setLoanAmount] = useState(100000);
  const [duration, setDuration] = useState(6);
  const [purpose, setPurpose] = useState('');
  const [applicationStatus, setApplicationStatus] = useState<'idle' | 'success' | 'failed' | 'pending'>('idle');
  
  const interestRate = 15; // 15% annual interest rate
  const monthlyPayment = (loanAmount * (1 + interestRate/100 * duration/12)) / duration;
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setApplicationStatus('pending');
    
    // Simulate API call
    setTimeout(() => {
      // Randomly succeed or fail for demo purposes
      const success = Math.random() > 0.3;
      setApplicationStatus(success ? 'success' : 'failed');
    }, 1500);
  };
  
  return (
    <BusinessLayout pageTitle="Business Loans">
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Loan Application</CardTitle>
                <CardDescription>Apply for a loan to grow your business</CardDescription>
              </CardHeader>
              <CardContent>
                {applicationStatus !== 'idle' && (
                  <div className="mb-6">
                    <TransactionStatus 
                      status={applicationStatus === 'success' ? 'success' : applicationStatus === 'failed' ? 'failed' : 'pending'} 
                      message={applicationStatus === 'success' 
                        ? "Your loan application has been submitted successfully. We'll review it shortly." 
                        : applicationStatus === 'failed'
                        ? "There was an issue with your loan application. Please try again."
                        : "Processing your loan application..."}
                    />
                  </div>
                )}
                
                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="amount">Loan Amount (₦)</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <Input 
                          id="amount" 
                          type="number" 
                          value={loanAmount} 
                          onChange={(e) => setLoanAmount(Number(e.target.value))}
                          min={50000}
                          max={10000000}
                          className="flex-1"
                        />
                        <div className="w-1/2">
                          <Slider 
                            value={[loanAmount]} 
                            onValueChange={(value) => setLoanAmount(value[0])} 
                            min={50000} 
                            max={10000000}
                            step={50000}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="duration">Loan Duration (months)</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <Input 
                          id="duration" 
                          type="number" 
                          value={duration} 
                          onChange={(e) => setDuration(Number(e.target.value))}
                          min={3}
                          max={36}
                          className="flex-1"
                        />
                        <div className="w-1/2">
                          <Slider 
                            value={[duration]} 
                            onValueChange={(value) => setDuration(value[0])} 
                            min={3} 
                            max={36}
                            step={1}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="purpose">Purpose of Loan</Label>
                      <Input 
                        id="purpose" 
                        value={purpose} 
                        onChange={(e) => setPurpose(e.target.value)}
                        placeholder="E.g., Equipment purchase, Expansion, etc."
                        className="mt-2"
                      />
                    </div>
                    
                    <Button type="submit" className="w-full" disabled={applicationStatus === 'pending'}>
                      {applicationStatus === 'pending' ? 'Processing...' : 'Apply for Loan'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Loan Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-kojaGray">Loan Amount:</span>
                    <span className="font-medium">₦{loanAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-kojaGray">Interest Rate:</span>
                    <span className="font-medium">{interestRate}% p.a.</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-kojaGray">Duration:</span>
                    <span className="font-medium">{duration} months</span>
                  </div>
                  <div className="border-t pt-4">
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Total Repayment:</span>
                      <span className="font-medium">₦{(monthlyPayment * duration).toLocaleString(undefined, {maximumFractionDigits: 2})}</span>
                    </div>
                    <div className="flex justify-between mt-2">
                      <span className="text-kojaGray">Monthly Payment:</span>
                      <span className="font-semibold text-lg">₦{monthlyPayment.toLocaleString(undefined, {maximumFractionDigits: 2})}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <Tabs defaultValue="active">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="active">Active Loans</TabsTrigger>
            <TabsTrigger value="history">Loan History</TabsTrigger>
            <TabsTrigger value="available">Available Products</TabsTrigger>
          </TabsList>
          
          <TabsContent value="active" className="mt-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <p className="text-kojaGray">You don't have any active loans at the moment.</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="history" className="mt-4">
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {[
                    { id: 1, amount: '₦500,000', date: new Date(2023, 5, 10), status: 'Repaid', duration: '6 months' },
                    { id: 2, amount: '₦1,200,000', date: new Date(2022, 11, 15), status: 'Repaid', duration: '12 months' },
                  ].map(loan => (
                    <div key={loan.id} className="flex flex-col sm:flex-row justify-between items-start sm:items-center p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{loan.amount}</p>
                        <p className="text-sm text-kojaGray">{formatDate(loan.date)}</p>
                      </div>
                      <div className="mt-2 sm:mt-0 flex flex-col sm:items-end">
                        <span className="text-green-500 font-medium">{loan.status}</span>
                        <span className="text-sm text-kojaGray">{loan.duration}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="available" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Working Capital Loan</CardTitle>
                  <CardDescription>For day-to-day operations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Interest Rate:</span>
                      <span>15% p.a.</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Max Amount:</span>
                      <span>₦5,000,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Max Duration:</span>
                      <span>12 months</span>
                    </div>
                    <Button className="w-full mt-4">Apply Now</Button>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Equipment Financing</CardTitle>
                  <CardDescription>For purchasing business assets</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Interest Rate:</span>
                      <span>18% p.a.</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Max Amount:</span>
                      <span>₦10,000,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-kojaGray">Max Duration:</span>
                      <span>36 months</span>
                    </div>
                    <Button className="w-full mt-4">Apply Now</Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>
  );
};

export default BusinessLoans;
