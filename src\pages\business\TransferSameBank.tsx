
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, User, Building, ArrowRight, CheckCircle2 } from 'lucide-react';
import TransactionStatus, { TransactionStatusType, normalizeTransactionStatus } from '@/components/TransactionStatus';
import { formatDate } from '@/lib/utils';
import { 
  Dialog, 
  DialogContent, 
  DialogTitle, 
  DialogHeader,
  DialogFooter,
  DialogDescription 
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';

const TransferSameBank = () => {
  const [accountNumber, setAccountNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [narration, setNarration] = useState('');
  const [accountName, setAccountName] = useState('');
  const [transferStatus, setTransferStatus] = useState<TransactionStatusType>('idle');
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const { toast } = useToast();
  
  const handleAccountLookup = () => {
    if (accountNumber.length === 10) {
      // Simulate account lookup
      setTimeout(() => {
        setAccountName('John Doe');
      }, 500);
    } else {
      setAccountName('');
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setConfirmDialogOpen(true);
  };
  
  const handleTransfer = () => {
    setConfirmDialogOpen(false);
    setIsProcessing(true);
    setTransferStatus('pending');
    
    // Simulate progress updates
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + 20;
      });
    }, 500);
    
    // Simulate API call
    setTimeout(() => {
      // Randomly succeed or fail for demo purposes
      const success = Math.random() > 0.2;
      setTransferStatus(success ? 'success' : 'failed');
      setProgress(100);
      setIsProcessing(false);
      
      toast({
        title: success ? "Transfer Successful" : "Transfer Failed",
        description: success 
          ? `Successfully transferred ₦${amount} to ${accountName}` 
          : "There was an issue with your transfer. Please try again.",
        variant: success ? "default" : "destructive",
      });
      
      // Reset form on success
      if (success) {
        setTimeout(() => {
          setAccountNumber('');
          setAccountName('');
          setAmount('');
          setNarration('');
          setTransferStatus('idle');
          setProgress(0);
        }, 3000);
      }
    }, 3000);
  };
  
  const recentTransactions = [
    { id: 1, receiver: 'Alice Johnson', accountNumber: '**********', amount: '₦150,000', date: new Date(2023, 7, 15), status: 'success' as TransactionStatusType },
    { id: 2, receiver: 'Bob Smith', accountNumber: '**********', amount: '₦75,000', date: new Date(2023, 7, 13), status: 'success' as TransactionStatusType },
    { id: 3, receiver: 'Carol Williams', accountNumber: '**********', amount: '₦120,000', date: new Date(2023, 7, 10), status: 'success' as TransactionStatusType },
  ];
  
  const savedBeneficiaries = [
    { id: 1, name: 'Alice Johnson', accountNumber: '**********', bankCode: '000', bankName: 'KojaPay' },
    { id: 2, name: 'Bob Smith', accountNumber: '**********', bankCode: '000', bankName: 'KojaPay' },
    { id: 3, name: 'Carol Williams', accountNumber: '**********', bankCode: '000', bankName: 'KojaPay' },
  ];
  
  return (
    <BusinessLayout pageTitle="Transfer (KojaPay)">
      <div className="space-y-6 max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card className="shadow-sm">
              <CardHeader className="border-b bg-gray-50/80">
                <CardTitle className="text-xl">Transfer to KojaPay Account</CardTitle>
                <CardDescription>Send money to other KojaPay accounts</CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                {isProcessing && (
                  <div className="mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg mb-4">
                      <div className="flex justify-between mb-2">
                        <span className="text-sm text-gray-500">Transaction Processing</span>
                        <span className="text-sm font-medium">{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>
                    <TransactionStatus 
                      status={transferStatus}
                      message={transferStatus === 'success' 
                        ? "Your transfer has been processed successfully." 
                        : transferStatus === 'failed'
                        ? "There was an issue with your transfer. Please try again."
                        : "Processing your transfer..."}
                    />
                  </div>
                )}
                
                {transferStatus === 'success' && !isProcessing && (
                  <div className="mb-6 text-center py-8 bg-green-50 rounded-lg">
                    <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Transfer Successful!</h3>
                    <p className="text-gray-600 mb-4">
                      Your transfer of ₦{amount} to {accountName} was successful.
                    </p>
                    <Button onClick={() => setTransferStatus('idle')}>
                      Make Another Transfer
                    </Button>
                  </div>
                )}
                
                {(transferStatus === 'idle' || (transferStatus === 'failed' && !isProcessing)) && (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="accountNumber">Account Number</Label>
                        <div className="flex mt-1">
                          <Input 
                            id="accountNumber" 
                            value={accountNumber}
                            onChange={(e) => {
                              setAccountNumber(e.target.value);
                              setAccountName('');
                            }}
                            placeholder="Enter 10-digit account number"
                            className="flex-1"
                          />
                          <Button 
                            type="button" 
                            variant="outline" 
                            className="ml-2"
                            onClick={handleAccountLookup}
                          >
                            <Search className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      {accountName && (
                        <div className="bg-gray-50 p-3 rounded-md">
                          <p className="text-sm font-medium">Account Name</p>
                          <p className="text-base">{accountName}</p>
                        </div>
                      )}
                      
                      <div>
                        <Label htmlFor="amount">Amount (₦)</Label>
                        <Input 
                          id="amount" 
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          type="number" 
                          placeholder="0.00"
                          className="mt-1"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="narration">Narration (Optional)</Label>
                        <Input 
                          id="narration" 
                          value={narration}
                          onChange={(e) => setNarration(e.target.value)}
                          placeholder="What's this transfer for?"
                          className="mt-1"
                        />
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="w-full" 
                        disabled={!accountName || !amount}
                      >
                        Send Money
                      </Button>
                    </div>
                  </form>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card className="shadow-sm h-full">
              <CardHeader className="border-b bg-gray-50/80">
                <CardTitle className="text-xl">Saved Beneficiaries</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {savedBeneficiaries.length > 0 ? (
                  <div className="space-y-3">
                    {savedBeneficiaries.map(beneficiary => (
                      <div 
                        key={beneficiary.id} 
                        className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                        onClick={() => {
                          setAccountNumber(beneficiary.accountNumber);
                          setAccountName(beneficiary.name);
                        }}
                      >
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full bg-[#1231b8] flex items-center justify-center text-white">
                            {beneficiary.name.charAt(0)}
                          </div>
                          <div className="ml-3">
                            <p className="font-medium">{beneficiary.name}</p>
                            <p className="text-sm text-kojaGray">{beneficiary.accountNumber}</p>
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-kojaGray" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <User className="h-10 w-10 mx-auto text-kojaGray opacity-50 mb-2" />
                    <p className="text-kojaGray">No saved beneficiaries</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
        
        <Tabs defaultValue="recent" className="w-full">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="recent" className="flex-1">Recent Transfers</TabsTrigger>
            <TabsTrigger value="recurring" className="flex-1">Recurring Transfers</TabsTrigger>
          </TabsList>
          
          <TabsContent value="recent">
            <Card className="shadow-sm">
              <CardContent className="p-6">
                {recentTransactions.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Recipient</th>
                          <th className="pb-3 font-medium hidden md:table-cell">Account</th>
                          <th className="pb-3 font-medium">Amount</th>
                          <th className="pb-3 font-medium hidden md:table-cell">Date</th>
                          <th className="pb-3 font-medium">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentTransactions.map(transaction => (
                          <tr key={transaction.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{transaction.receiver}</td>
                            <td className="py-4 hidden md:table-cell">{transaction.accountNumber}</td>
                            <td className="py-4">{transaction.amount}</td>
                            <td className="py-4 hidden md:table-cell">{formatDate(transaction.date)}</td>
                            <td className="py-4">
                              <TransactionStatus 
                                status={transaction.status}
                                compact={true}
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    
                    {/* Mobile view cards - only show on small screens */}
                    <div className="md:hidden space-y-4 mt-4">
                      {recentTransactions.map(transaction => (
                        <div key={transaction.id} className="bg-white p-4 rounded-lg border shadow-sm">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium">{transaction.receiver}</span>
                            <TransactionStatus status={transaction.status} compact={true} />
                          </div>
                          <div className="text-sm text-gray-500 space-y-1">
                            <div className="flex justify-between">
                              <span>Amount:</span>
                              <span className="font-medium text-black">{transaction.amount}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Account:</span>
                              <span>{transaction.accountNumber}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Date:</span>
                              <span>{formatDate(transaction.date)}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Building className="h-10 w-10 mx-auto text-kojaGray opacity-50 mb-2" />
                    <p className="text-kojaGray">No recent transfers</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="recurring">
            <Card className="shadow-sm">
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Building className="h-10 w-10 mx-auto text-kojaGray opacity-50 mb-2" />
                  <p className="text-kojaGray">No recurring transfers set up</p>
                  <Button className="mt-4">Set Up Recurring Transfer</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Transaction Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Transfer</DialogTitle>
            <DialogDescription>
              Please review the transaction details below before proceeding.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Account Number</p>
                <p className="text-base font-semibold">{accountNumber}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Account Name</p>
                <p className="text-base font-semibold">{accountName}</p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Amount</p>
              <p className="text-lg font-semibold">₦{amount}</p>
            </div>
            
            {narration && (
              <div>
                <p className="text-sm font-medium text-gray-500">Narration</p>
                <p className="text-base">{narration}</p>
              </div>
            )}
            
            <div className="bg-amber-50 p-3 rounded-md mt-4">
              <p className="text-sm text-amber-700">
                Please confirm that the details above are correct. This transaction cannot be reversed once completed.
              </p>
            </div>
          </div>
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleTransfer}
              className="sm:flex-1"
            >
              Confirm and Transfer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </BusinessLayout>
  );
};

export default TransferSameBank;
