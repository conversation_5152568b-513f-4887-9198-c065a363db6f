
import React, { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { 
  Eye, 
  Download,
  ArrowDownLeft,
  ArrowUpRight,
  Printer
} from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';
import { useNavigate } from 'react-router-dom';
import { generateTransactionReceipt, printPOSReceipt } from '@/utils/receipt-generator';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import EnhancedTransactionReceipt from './EnhancedTransactionReceipt';
import OTPVerification from './OTPVerification';
import { useAuth } from '@/contexts/AuthContext';

export interface Transaction {
  id: string;
  recipient: string;
  date: string;
  amount: number;
  status: 'pending' | 'success' | 'failed';
  initials?: string;
  image?: string;
  type?: 'incoming' | 'outgoing';
  description?: string;
  reference?: string;
  fee?: string;
  items?: Array<{name: string; price: number; quantity: number}>;
  paymentMethod?: string;
  tax?: number;
  subtotal?: number;
  customerName?: string;
}

interface TransactionTableProps {
  transactions: Transaction[];
  onViewReceipt?: (transaction: Transaction) => void;
  showPrintButton?: boolean;
  requireOTP?: boolean;
}

const TransactionTable: React.FC<TransactionTableProps> = ({ 
  transactions,
  onViewReceipt,
  showPrintButton = true,
  requireOTP = false
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { accountType } = useAuth();
  const [receiptDialogOpen, setReceiptDialogOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [otpDialogOpen, setOtpDialogOpen] = useState(false);
  
  const handleViewReceipt = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    
    if (requireOTP && transaction.status === 'pending') {
      setOtpDialogOpen(true);
    } else {
      setReceiptDialogOpen(true);
    }
  };
  
  const handleVerifyOTP = (otp: string) => {
    if (otp === '123456' || otp.length === 6) {
      toast({
        title: "Transaction Verified",
        description: "Your transaction has been authorized successfully",
        variant: "success"
      });
      
      setOtpDialogOpen(false);
      
      if (selectedTransaction) {
        const updatedTransaction = {
          ...selectedTransaction,
          status: 'success' as const
        };
        setSelectedTransaction(updatedTransaction);
        
        setTimeout(() => {
          setReceiptDialogOpen(true);
        }, 500);
      }
    } else {
      toast({
        title: "Invalid OTP",
        description: "Please enter a valid OTP code",
        variant: "destructive"
      });
    }
  };
  
  const handleCancelOTP = () => {
    setOtpDialogOpen(false);
    setSelectedTransaction(null);
  };
  
  const handleCloseReceipt = () => {
    setReceiptDialogOpen(false);
    setSelectedTransaction(null);
  };
  
  const handlePrintReceipt = () => {
    if (!selectedTransaction) return;
    
    const receipt = generateTransactionReceipt(selectedTransaction);
    printPOSReceipt(receipt);
    
    toast({
      title: "Printing Receipt",
      description: "Receipt has been sent to your printer",
      variant: "success",
    });
  };
  
  const handleDownloadReceipt = () => {
    if (!selectedTransaction) return;
    
    generateTransactionReceipt(selectedTransaction, true);
    
    toast({
      title: "Receipt Downloaded",
      description: "Receipt has been downloaded as an image",
      variant: "default",
    });
  };

  return (
    <>
      <div className="w-full overflow-auto">
        <Table>
          <TableHeader>
            <TableRow className="hover:bg-transparent">
              <TableHead className="w-[50px] text-xs font-unica"></TableHead>
              <TableHead className="text-xs font-unica">Transaction</TableHead>
              <TableHead className="hidden md:table-cell text-xs font-unica">Date</TableHead>
              <TableHead className="text-right text-xs font-unica">Amount</TableHead>
              <TableHead className="hidden md:table-cell text-center text-xs font-unica">Status</TableHead>
              <TableHead className="text-right text-xs font-unica">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.length > 0 ? (
              transactions.map((transaction) => (
                <TableRow key={transaction.id} className="group">
                  <TableCell>
                    {transaction.type === 'incoming' ? (
                      <ArrowDownLeft className="text-[#8B5CF6]" size={16} />
                    ) : (
                      <ArrowUpRight className="text-[#F97316]" size={16} />
                    )}
                  </TableCell>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-3">
                      {transaction.image ? (
                        <Avatar className="h-7 w-7">
                          <img src={transaction.image} alt={transaction.recipient} />
                        </Avatar>
                      ) : (
                        <Avatar className="h-7 w-7 bg-kojaPrimary/10 text-kojaYellow">
                          <span className="text-xs font-medium">{transaction.initials}</span>
                        </Avatar>
                      )}
                      <div>
                        <div className="font-medium text-sm">{transaction.recipient}</div>
                        <div className="text-xs text-muted-foreground md:hidden">{transaction.date}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-muted-foreground text-xs">
                    {transaction.date}
                  </TableCell>
                  <TableCell className="text-right font-medium text-xs">
                    ₦{typeof transaction.amount === 'number' 
                        ? transaction.amount.toLocaleString() 
                        : transaction.amount}
                  </TableCell>
                  <TableCell className="hidden md:table-cell text-center">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      transaction.status === 'success' 
                        ? 'bg-[#F2FCE2] text-[#10B981] border border-[#10B981]/20' 
                        : transaction.status === 'pending'
                          ? 'bg-[#FEF7CD] text-[#FBBF24] border border-[#FBBF24]/20'
                          : 'bg-[#FEE2E2] text-[#EF4444] border border-[#EF4444]/20'
                    }`}>
                      {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleViewReceipt(transaction)}
                        className="hover:bg-kojaPrimary/10 h-7 w-7 rounded-[20px]"
                      >
                        <Eye className="h-3 w-3 text-[#D946EF]" />
                      </Button>
                      {showPrintButton && (
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={handlePrintReceipt}
                          className="hover:bg-kojaPrimary/10 h-7 w-7 rounded-[20px]"
                        >
                          <Printer className="h-3 w-3 text-[#D946EF]" />
                        </Button>
                      )}
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={handleDownloadReceipt}
                        className="hover:bg-kojaPrimary/10 h-7 w-7 rounded-[20px]"
                      >
                        <Download className="h-3 w-3 text-[#D946EF]" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-6 text-muted-foreground text-xs">
                  No transactions found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      <Dialog open={receiptDialogOpen} onOpenChange={setReceiptDialogOpen}>
        <DialogContent className="sm:max-w-md p-0 overflow-hidden">
          {selectedTransaction && (
            <EnhancedTransactionReceipt 
              transaction={selectedTransaction}
              onClose={handleCloseReceipt}
              onPrint={handlePrintReceipt}
              onDownload={handleDownloadReceipt}
            />
          )}
        </DialogContent>
      </Dialog>
      
      <Dialog open={otpDialogOpen} onOpenChange={setOtpDialogOpen}>
        <DialogContent className="sm:max-w-md p-0 overflow-hidden">
          <OTPVerification 
            onVerify={handleVerifyOTP} 
            onCancel={handleCancelOTP}
            isBusinessAccount={accountType === 'business'}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default TransactionTable;
