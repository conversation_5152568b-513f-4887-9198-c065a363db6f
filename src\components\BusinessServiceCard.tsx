
import React from 'react';
import { MicroButton } from '@/components/ui/micro-button';
import { EnhancedCard, EnhancedCardContent } from '@/components/ui/enhanced-card';
import { useNavigate } from 'react-router-dom';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface BusinessServiceCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  route: string;
  color?: 'blue' | 'yellow' | 'default';
}

const BusinessServiceCard: React.FC<BusinessServiceCardProps> = ({
  title,
  description,
  icon: Icon,
  route,
  color = 'default'
}) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    navigate(route);
  };

  const getCardStyle = () => {
    switch (color) {
      case 'blue':
        return "bg-[#1231b8]/30 backdrop-blur-xl border-[#D3E4FD] text-white shadow-none";
      case 'yellow':
        return "bg-[#fde314]/30 backdrop-blur-xl border-[#D3E4FD] text-kojaDark shadow-none";
      default:
        return "bg-kojaPrimary/40 backdrop-blur-xl border-[#D3E4FD] text-kojaDark shadow-none";
    }
  };

  const getIconStyle = () => {
    switch (color) {
      case 'blue':
        return "bg-kojaPrimary/20 text-kojaYellow transform hover:scale-105 transition-all duration-300 active:scale-95";
      case 'yellow':
        return "bg-kojaPrimary/20 text-kojaYellow transform hover:scale-105 transition-all duration-300 active:scale-95";
      default:
        return "bg-kojaPrimary/20 text-kojaYellow transform hover:scale-105 transition-all duration-300 active:scale-95";
    }
  };

  const getButtonStyle = () => {
    switch (color) {
      case 'blue':
        return "bg-kojaYellow hover:bg-kojaYellow/90 text-kojaDark";
      case 'yellow':
        return "bg-kojaPrimary hover:bg-kojaPrimary/90 text-white";
      default:
        return "bg-kojaYellow hover:bg-kojaYellow/90 text-kojaDark";
    }
  };

  return <EnhancedCard 
    className={cn("transition-all duration-300 transform hover:-translate-y-1 cursor-pointer h-full overflow-hidden glass-morph", getCardStyle())} 
    onClick={handleClick}
  >
      <EnhancedCardContent className="p-2.5 sm:p-3.5 flex flex-col h-full">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-xs sm:text-sm text-zinc-900">{title}</h3>
          <div className={`rounded-full p-1 sm:p-1.5 ${getIconStyle()}`}>
            <Icon size={14} />
          </div>
        </div>
        <p className="text-xs mb-3 flex-grow line-clamp-2 text-zinc-900">{description}</p>
        <MicroButton className={`w-full mt-auto text-xs rounded-xl ${getButtonStyle()}`} variant="glass">
          Access
        </MicroButton>
      </EnhancedCardContent>
    </EnhancedCard>;
};

export default BusinessServiceCard;
