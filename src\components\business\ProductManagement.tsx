
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { Package, Plus, Edit, Trash2, Image as ImageIcon, Save, X, Info, UploadCloud } from 'lucide-react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  inventory: number;
  category: string;
  sku: string;
  status: 'active' | 'draft' | 'archived';
  images: string[];
  featured: boolean;
  variations?: ProductVariation[];
  dateAdded: string;
  lastUpdated: string;
}

interface ProductVariation {
  id: string;
  name: string;
  options: string[];
  priceModifier: number;
}

// Mock data for testing
const initialProducts: Product[] = [
  {
    id: '1',
    name: 'Wireless Earbuds',
    description: 'High-quality wireless earbuds with noise cancellation.',
    price: 15000,
    inventory: 23,
    category: 'Electronics',
    sku: 'EARB-001',
    status: 'active',
    images: ['/lovable-uploads/c6ef850a-0def-4097-aff9-b8dfc8bb22e6.png'],
    featured: true,
    dateAdded: '2023-05-10',
    lastUpdated: '2023-05-15'
  },
  {
    id: '2',
    name: 'Smart Watch',
    description: 'Track your fitness and stay connected with this smart watch.',
    price: 25000,
    inventory: 12,
    category: 'Electronics',
    sku: 'SWTC-002',
    status: 'active',
    images: ['/lovable-uploads/f9c0ea5d-a465-49fb-8b2f-ccac2b858bad.png'],
    featured: false,
    dateAdded: '2023-05-08',
    lastUpdated: '2023-05-14'
  },
  {
    id: '3',
    name: 'Leather Wallet',
    description: 'Premium leather wallet with multiple card slots.',
    price: 8500,
    inventory: 45,
    category: 'Fashion',
    sku: 'WLLT-003',
    status: 'active',
    images: ['/lovable-uploads/7efdf088-98ad-45d6-b51d-a15cdcbef2c6.png'],
    featured: false,
    dateAdded: '2023-05-05',
    lastUpdated: '2023-05-12'
  },
  {
    id: '4',
    name: 'Portable Blender',
    description: 'Blend your favorite smoothies on the go.',
    price: 12000,
    inventory: 18,
    category: 'Home & Kitchen',
    sku: 'BLND-004',
    status: 'active',
    images: ['/lovable-uploads/2abc36d0-01c9-4395-b926-976d7adb4088.png'],
    featured: false,
    dateAdded: '2023-05-01',
    lastUpdated: '2023-05-11'
  }
];

const ProductManagement: React.FC = () => {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const { toast } = useToast();
  
  // Form state
  const [formData, setFormData] = useState<Partial<Product>>({
    name: '',
    description: '',
    price: 0,
    inventory: 0,
    category: '',
    sku: '',
    status: 'draft',
    images: [],
    featured: false
  });
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData({
      ...formData,
      [name]: checked
    });
  };
  
  const addProduct = () => {
    setSelectedProduct(null);
    setFormData({
      name: '',
      description: '',
      price: 0,
      inventory: 0,
      category: '',
      sku: '',
      status: 'draft',
      images: [],
      featured: false
    });
    setIsProductDialogOpen(true);
  };
  
  const editProduct = (product: Product) => {
    setSelectedProduct(product);
    setFormData({
      ...product
    });
    setIsProductDialogOpen(true);
  };
  
  const confirmDelete = (product: Product) => {
    setSelectedProduct(product);
    setIsDeleteDialogOpen(true);
  };
  
  const deleteProduct = () => {
    if (!selectedProduct) return;
    
    setProducts(products.filter(p => p.id !== selectedProduct.id));
    setIsDeleteDialogOpen(false);
    
    toast({
      title: "Product Deleted",
      description: `${selectedProduct.name} has been removed`,
      variant: "default"
    });
  };
  
  const saveProduct = () => {
    if (!formData.name || !formData.price) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    const now = new Date().toISOString().split('T')[0];
    
    if (selectedProduct) {
      // Update existing product
      setProducts(products.map(p => 
        p.id === selectedProduct.id 
          ? { ...formData, lastUpdated: now } as Product 
          : p
      ));
      
      toast({
        title: "Product Updated",
        description: `${formData.name} has been updated successfully`,
      });
    } else {
      // Add new product
      const newProduct: Product = {
        id: Date.now().toString(),
        name: formData.name!,
        description: formData.description || '',
        price: formData.price || 0,
        inventory: formData.inventory || 0,
        category: formData.category || 'Uncategorized',
        sku: formData.sku || `SKU-${Date.now().toString().slice(-6)}`,
        status: formData.status as 'active' | 'draft' | 'archived' || 'draft',
        images: formData.images || [],
        featured: formData.featured || false,
        dateAdded: now,
        lastUpdated: now
      };
      
      setProducts([...products, newProduct]);
      
      toast({
        title: "Product Added",
        description: `${newProduct.name} has been added successfully`,
      });
    }
    
    setIsProductDialogOpen(false);
  };
  
  const handleImageUpload = () => {
    // In a real app, this would open file picker and handle upload
    // For now, we'll just simulate adding a random image URL
    const demoImages = [
      '/lovable-uploads/c6ef850a-0def-4097-aff9-b8dfc8bb22e6.png',
      '/lovable-uploads/f9c0ea5d-a465-49fb-8b2f-ccac2b858bad.png',
      '/lovable-uploads/7efdf088-98ad-45d6-b51d-a15cdcbef2c6.png',
      '/lovable-uploads/2abc36d0-01c9-4395-b926-976d7adb4088.png'
    ];
    
    const randomImage = demoImages[Math.floor(Math.random() * demoImages.length)];
    
    setFormData({
      ...formData,
      images: [...(formData.images || []), randomImage]
    });
    
    toast({
      title: "Image Added",
      description: "Demo image has been added to the product",
    });
  };
  
  const removeImage = (index: number) => {
    const updatedImages = [...(formData.images || [])];
    updatedImages.splice(index, 1);
    
    setFormData({
      ...formData,
      images: updatedImages
    });
  };
  
  // Filter products based on search term and status filter
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase());
                         
    const matchesFilter = activeFilter === 'all' || product.status === activeFilter;
    
    return matchesSearch && matchesFilter;
  });
  
  return (
    <div className="space-y-4">
      {/* Header with search and filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
        <div className="relative flex-1 w-full sm:max-w-md">
          <Input
            type="search"
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
          <div className="absolute left-3 top-2.5">
            <Package size={16} className="text-muted-foreground" />
          </div>
        </div>
        
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Select value={activeFilter} onValueChange={(value) => setActiveFilter(value)}>
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Products</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="draft">Drafts</SelectItem>
              <SelectItem value="archived">Archived</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={addProduct}>
            <Plus size={16} className="mr-2" />
            Add Product
          </Button>
        </div>
      </div>
      
      {/* Product Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {filteredProducts.map(product => (
          <Card key={product.id} className="overflow-hidden flex flex-col">
            <div className="relative aspect-square bg-gray-50">
              {product.images.length > 0 ? (
                <img 
                  src={product.images[0]} 
                  alt={product.name} 
                  className="w-full h-full object-contain p-2"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-gray-400">
                  <ImageIcon size={64} />
                </div>
              )}
              {product.featured && (
                <div className="absolute top-2 left-2 bg-amber-500 text-white text-xs px-2 py-1 rounded-full">
                  Featured
                </div>
              )}
              <div className={`absolute top-2 right-2 text-white text-xs px-2 py-1 rounded-full ${
                product.status === 'active' ? 'bg-green-500' : 
                product.status === 'draft' ? 'bg-gray-500' : 'bg-red-500'
              }`}>
                {product.status.charAt(0).toUpperCase() + product.status.slice(1)}
              </div>
            </div>
            
            <CardContent className="flex flex-col flex-1 p-3">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-medium text-sm">{product.name}</h3>
                  <p className="text-xs text-muted-foreground line-clamp-2 mt-1">{product.description}</p>
                </div>
              </div>
              
              <div className="mt-2 text-sm font-semibold">₦{product.price.toLocaleString()}</div>
              
              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span>SKU: {product.sku}</span>
                <span>{product.inventory} in stock</span>
              </div>
              
              <div className="mt-auto pt-3 flex justify-between items-center border-t border-gray-100 mt-3">
                <Button variant="outline" size="sm" onClick={() => editProduct(product)}>
                  <Edit size={14} className="mr-1" />
                  Edit
                </Button>
                <Button variant="ghost" size="sm" onClick={() => confirmDelete(product)}>
                  <Trash2 size={14} className="mr-1" />
                  Delete
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {/* Product Form Dialog */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>{selectedProduct ? 'Edit Product' : 'Add New Product'}</DialogTitle>
            <DialogDescription>
              {selectedProduct 
                ? 'Update the details of your existing product.' 
                : 'Fill in the details to add a new product to your store.'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name || ''}
                  onChange={handleInputChange}
                  placeholder="Enter product name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description || ''}
                  onChange={handleInputChange}
                  placeholder="Describe your product"
                  className="min-h-[120px]"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">Price (₦) <span className="text-red-500">*</span></Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    value={formData.price || ''}
                    onChange={handleInputChange}
                    placeholder="0.00"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="inventory">Inventory</Label>
                  <Input
                    id="inventory"
                    name="inventory"
                    type="number"
                    value={formData.inventory || ''}
                    onChange={handleInputChange}
                    placeholder="0"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select 
                    value={formData.category || ''} 
                    onValueChange={(value) => handleSelectChange('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Electronics">Electronics</SelectItem>
                      <SelectItem value="Fashion">Fashion</SelectItem>
                      <SelectItem value="Home & Kitchen">Home & Kitchen</SelectItem>
                      <SelectItem value="Health & Beauty">Health & Beauty</SelectItem>
                      <SelectItem value="Food & Beverages">Food & Beverages</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="sku">SKU</Label>
                  <Input
                    id="sku"
                    name="sku"
                    value={formData.sku || ''}
                    onChange={handleInputChange}
                    placeholder="SKU-123456"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  value={formData.status || 'draft'} 
                  onValueChange={(value) => handleSelectChange('status', value as 'active' | 'draft' | 'archived')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch 
                  id="featured" 
                  checked={formData.featured || false}
                  onCheckedChange={(checked) => handleSwitchChange('featured', checked)}
                />
                <Label htmlFor="featured">Featured Product</Label>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Product Images</Label>
                <div className="border rounded-md p-4 min-h-[250px] bg-gray-50">
                  {(formData.images || []).length > 0 ? (
                    <div className="grid grid-cols-2 gap-2">
                      {formData.images?.map((image, index) => (
                        <div key={index} className="relative aspect-square bg-white rounded border">
                          <img 
                            src={image} 
                            alt={`Product image ${index + 1}`} 
                            className="w-full h-full object-contain p-2"
                          />
                          <Button 
                            variant="destructive" 
                            size="icon" 
                            className="absolute top-1 right-1 h-6 w-6"
                            onClick={() => removeImage(index)}
                          >
                            <X size={12} />
                          </Button>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-gray-400 space-y-2">
                      <ImageIcon size={48} />
                      <p className="text-sm">No images added yet</p>
                    </div>
                  )}
                </div>
                
                <Button 
                  variant="outline" 
                  type="button" 
                  onClick={handleImageUpload} 
                  className="w-full"
                >
                  <UploadCloud size={16} className="mr-2" />
                  Add Image
                </Button>
                
                <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-2">
                  <Info size={12} />
                  <p>For demo purposes, clicking Add Image will add a random demo image</p>
                </div>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsProductDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={saveProduct}>
              <Save size={16} className="mr-2" />
              {selectedProduct ? 'Update Product' : 'Save Product'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Product</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedProduct?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={deleteProduct}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductManagement;
