
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Bell, AlertTriangle, CreditCard, BadgeCheck, Info, Shield, Smartphone, Mail, Globe, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import notificationService, { NotificationType } from '@/services/notificationService';
import { useToast } from '@/hooks/use-toast';

interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
}

const NotificationPreferencesPage = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [settings, setSettings] = useState<NotificationSettings>({
    email: true,
    push: true,
    sms: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isSending, setIsSending] = useState(false);
  
  useEffect(() => {
    const loadSettings = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setSettings({
          email: true,
          push: true,
          sms: false,
        });
      } catch (error) {
        console.error('Error loading settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load notification preferences',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadSettings();
  }, []);
  
  const handleSettingsChange = (channel: string, value: boolean) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      [channel]: value,
    }));
  };
  
  const handleSaveSettings = async () => {
    setIsSaving(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Settings Saved',
        description: 'Your notification preferences have been updated',
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save notification preferences',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSendTestNotification = async (channel: string) => {
    setIsSending(true);
    try {
      await notificationService.createNotification({
        userId: user?.id || '',
        title: 'Test Notification',
        message: `This is a test ${channel} notification from KojaPay`,
        type: NotificationType.INFO,
        isRead: false
      });
      toast({
        title: 'Test Notification Sent',
        description: `A test notification has been sent to your ${channel}`,
      });
    } catch (error) {
      console.error('Error sending test notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to send test notification',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="container py-8">
        <Button variant="ghost" onClick={() => navigate('/notifications')} className="mb-4">
          &larr; Back to Notifications
        </Button>
        
        <h1 className="text-2xl font-bold mb-4">Notification Preferences</h1>
        <p className="text-muted-foreground mb-6">
          Manage how you receive updates and alerts from KojaPay.
        </p>
        
        <Tabs defaultValue="general" className="space-y-4">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="promotions">Promotions</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Transaction Notifications</CardTitle>
                <CardDescription>
                  Receive updates on your account transactions.
                </CardDescription>
              </CardHeader>
              <CardContent className="grid gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="email-transaction">Email</Label>
                  <Switch 
                    id="email-transaction"
                    checked={settings.email}
                    onCheckedChange={(checked) => handleSettingsChange('email', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="push-transaction">Push Notification</Label>
                  <Switch 
                    id="push-transaction"
                    checked={settings.push}
                    onCheckedChange={(checked) => handleSettingsChange('push', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sms-transaction">SMS</Label>
                  <Switch 
                    id="sms-transaction"
                    checked={settings.sms}
                    onCheckedChange={(checked) => handleSettingsChange('sms', checked)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={() => handleSendTestNotification('transaction')}>
                  Send Test Notification
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="security" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Security Alerts</CardTitle>
                <CardDescription>
                  Get notified about suspicious activity and security updates.
                </CardDescription>
              </CardHeader>
              <CardContent className="grid gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="email-security">Email</Label>
                  <Switch 
                    id="email-security"
                    checked={settings.email}
                    onCheckedChange={(checked) => handleSettingsChange('email', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="push-security">Push Notification</Label>
                  <Switch 
                    id="push-security"
                    checked={settings.push}
                    onCheckedChange={(checked) => handleSettingsChange('push', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sms-security">SMS</Label>
                  <Switch 
                    id="sms-security"
                    checked={settings.sms}
                    onCheckedChange={(checked) => handleSettingsChange('sms', checked)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={() => handleSendTestNotification('security')}>
                  Send Test Notification
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="promotions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Promotional Offers</CardTitle>
                <CardDescription>
                  Receive exclusive deals and offers from KojaPay.
                </CardDescription>
              </CardHeader>
              <CardContent className="grid gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="email-promotions">Email</Label>
                  <Switch 
                    id="email-promotions"
                    checked={settings.email}
                    onCheckedChange={(checked) => handleSettingsChange('email', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="push-promotions">Push Notification</Label>
                  <Switch 
                    id="push-promotions"
                    checked={settings.push}
                    onCheckedChange={(checked) => handleSettingsChange('push', checked)}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="sms-promotions">SMS</Label>
                  <Switch 
                    id="sms-promotions"
                    checked={settings.sms}
                    onCheckedChange={(checked) => handleSettingsChange('sms', checked)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={() => handleSendTestNotification('promotions')}>
                  Send Test Notification
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
        
        <Button 
          variant="primary" 
          onClick={handleSaveSettings} 
          disabled={isSaving}
        >
          {isSaving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </DashboardLayout>
  );
};

export default NotificationPreferencesPage;
