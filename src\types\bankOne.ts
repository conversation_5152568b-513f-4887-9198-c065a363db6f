// BankOne API Response Types

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  error?: any;
  transactionReference?: string;
}

export interface AccountVerificationResponse {
  accountNumber: string;
  accountName: string;
  accountType?: string;
  accountStatus?: string;
  currencyCode?: string;
  responseCode?: string;
  responseMessage?: string;
}

export interface BalanceEnquiryResponse {
  availableBalance: number;
  bookBalance: number;
  ledgerBalance?: number;
  accountNumber: string;
  accountName?: string;
  currencyCode?: string;
  responseCode?: string;
  responseMessage?: string;
}

export interface TransactionResponse {
  transactionReference: string;
  sessionId?: string;
  responseCode?: string;
  responseMessage?: string;
  transactionStatus?: string;
  narration?: string;
  amount?: number;
  fee?: number;
  transactionDate?: string;
}

// Alias for backward compatibility
export type TransferResponse = TransactionResponse;

export interface TransactionStatusResponse {
  transactionReference: string;
  transactionStatus: 'Successful' | 'Failed' | 'Pending' | 'Unknown';
  responseCode?: string;
  responseMessage?: string;
  amount?: number;
  senderName?: string;
  receiverName?: string;
  transactionDate?: string;
}

export interface TransactionHistoryItem {
  transactionReference: string;
  transactionDate: string;
  narration: string;
  amount: number;
  transactionType: 'Credit' | 'Debit';
  balance?: number;
  senderAccountNumber?: string;
  senderName?: string;
  receiverAccountNumber?: string;
  receiverName?: string;
  status: 'Successful' | 'Failed' | 'Pending';
  channel?: string;
}

export interface TransactionHistoryResponse {
  transactions: TransactionHistoryItem[];
  totalCount?: number;
  responseCode?: string;
  responseMessage?: string;
}

export interface Bank {
  bankCode: string;
  bankName: string;
  bankLogoUrl?: string;
}

export interface BankListItem {
  bankCode: string;
  bankName: string;
  bankLogoUrl?: string;
  sortCode?: string;
  isActive?: boolean;
}

export interface BanksListResponse {
  banks: BankListItem[];
  responseCode?: string;
  responseMessage?: string;
}

// Transfer Request Types
export interface SameBankTransferRequest {
  senderAccountNumber: string;
  receiverAccountNumber: string;
  amount: number;
  narration: string;
}

export interface OtherBankTransferRequest {
  senderAccountNumber: string;
  receiverAccountNumber: string;
  receiverBankCode: string;
  receiverName: string;
  amount: number;
  narration: string;
}
