
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, Download, Eye, Trash2, Send, FileText } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import TransactionStatus from '@/components/TransactionStatus';

const BusinessInvoices = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [showInvoiceForm, setShowInvoiceForm] = useState(false);
  const [formStatus, setFormStatus] = useState<'idle' | 'success' | 'failed'>('idle');
  
  const handleCreateInvoice = (e: React.FormEvent) => {
    e.preventDefault();
    setFormStatus('success');
    setTimeout(() => {
      setShowInvoiceForm(false);
      setFormStatus('idle');
    }, 2000);
  };
  
  const invoices = [
    { id: 'INV-001', customer: 'Acme Corporation', amount: '₦125,000', date: new Date(2023, 6, 15), status: 'Paid' },
    { id: 'INV-002', customer: 'TechStartup Inc.', amount: '₦85,000', date: new Date(2023, 7, 3), status: 'Pending' },
    { id: 'INV-003', customer: 'Global Enterprises', amount: '₦230,500', date: new Date(2023, 7, 20), status: 'Overdue' },
    { id: 'INV-004', customer: 'Local Business Ltd.', amount: '₦45,000', date: new Date(2023, 8, 1), status: 'Pending' },
  ];
  
  const filteredInvoices = activeTab === 'all' 
    ? invoices 
    : invoices.filter(inv => inv.status.toLowerCase() === activeTab);
  
  return (
    <BusinessLayout pageTitle="Invoice Management">
      <div className="space-y-6">
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Invoices</CardTitle>
              <CardDescription>Create and manage invoices for your customers</CardDescription>
            </div>
            <Button className="mt-4 sm:mt-0" onClick={() => setShowInvoiceForm(true)}>
              <PlusCircle className="mr-2 h-4 w-4" /> Create Invoice
            </Button>
          </CardHeader>
          <CardContent>
            {formStatus === 'success' && (
              <div className="mb-6">
                <TransactionStatus 
                  status="success" 
                  message="Invoice created successfully and sent to the customer."
                />
              </div>
            )}
            
            {formStatus === 'failed' && (
              <div className="mb-6">
                <TransactionStatus 
                  status="failed" 
                  message="Failed to create invoice. Please try again."
                />
              </div>
            )}
            
            {showInvoiceForm ? (
              <form onSubmit={handleCreateInvoice} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="customerName">Customer Name</Label>
                      <Input id="customerName" placeholder="Enter customer name" className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="customerEmail">Customer Email</Label>
                      <Input id="customerEmail" type="email" placeholder="<EMAIL>" className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="invoiceNumber">Invoice Number</Label>
                      <Input id="invoiceNumber" defaultValue="INV-005" className="mt-1" />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="amount">Amount (₦)</Label>
                      <Input id="amount" type="number" placeholder="0.00" className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="dueDate">Due Date</Label>
                      <Input id="dueDate" type="date" className="mt-1" />
                    </div>
                    <div>
                      <Label htmlFor="description">Description</Label>
                      <Input id="description" placeholder="Invoice for services rendered" className="mt-1" />
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 mt-6">
                  <Button variant="outline" type="button" onClick={() => setShowInvoiceForm(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    Create Invoice
                  </Button>
                </div>
              </form>
            ) : (
              <>
                <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid grid-cols-4 w-full mb-6">
                    <TabsTrigger value="all">All</TabsTrigger>
                    <TabsTrigger value="paid">Paid</TabsTrigger>
                    <TabsTrigger value="pending">Pending</TabsTrigger>
                    <TabsTrigger value="overdue">Overdue</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value={activeTab} className="space-y-4">
                    {filteredInvoices.length === 0 ? (
                      <div className="text-center py-8">
                        <FileText className="mx-auto h-10 w-10 text-kojaGray opacity-50 mb-3" />
                        <p className="text-kojaGray">No invoices found in this category</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="text-left border-b">
                              <th className="pb-3 font-medium">Invoice #</th>
                              <th className="pb-3 font-medium">Customer</th>
                              <th className="pb-3 font-medium">Date</th>
                              <th className="pb-3 font-medium">Amount</th>
                              <th className="pb-3 font-medium">Status</th>
                              <th className="pb-3 font-medium">Actions</th>
                            </tr>
                          </thead>
                          <tbody>
                            {filteredInvoices.map(invoice => (
                              <tr key={invoice.id} className="border-b hover:bg-gray-50">
                                <td className="py-4">{invoice.id}</td>
                                <td className="py-4">{invoice.customer}</td>
                                <td className="py-4">{formatDate(invoice.date)}</td>
                                <td className="py-4">{invoice.amount}</td>
                                <td className="py-4">
                                  <span className={`px-2 py-1 rounded-full text-xs ${
                                    invoice.status === 'Paid' ? 'bg-green-100 text-green-800' : 
                                    invoice.status === 'Pending' ? 'bg-yellow-100 text-yellow-800' : 
                                    'bg-red-100 text-red-800'
                                  }`}>
                                    {invoice.status}
                                  </span>
                                </td>
                                <td className="py-4">
                                  <div className="flex space-x-2">
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <Eye className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <Download className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <Send className="h-4 w-4" />
                                    </Button>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              </>
            )}
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="text-4xl font-bold mb-2">₦485,500</div>
              <p className="text-kojaGray">Total Invoiced</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-4xl font-bold mb-2 text-green-500">₦125,000</div>
              <p className="text-kojaGray">Paid Invoices</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-4xl font-bold mb-2 text-yellow-500">₦130,000</div>
              <p className="text-kojaGray">Pending Invoices</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-4xl font-bold mb-2 text-red-500">₦230,500</div>
              <p className="text-kojaGray">Overdue Invoices</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </BusinessLayout>
  );
};

export default BusinessInvoices;
