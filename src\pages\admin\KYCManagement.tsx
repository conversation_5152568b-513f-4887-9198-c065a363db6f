import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  CheckCircle, 
  Clock, 
  X,
  User,
  FileText,
  Mail,
  Phone,
  Calendar
} from 'lucide-react';

const KYCManagement = () => {
  const kycRequests = [
    { 
      id: 'KYC001', 
      user: '<EMAIL>', 
      submissionDate: '2023-06-15',
      type: 'Individual',
      documents: 3,
      status: 'Verified'
    },
    { 
      id: 'KYC002', 
      user: '<EMAIL>', 
      submissionDate: '2023-06-14',
      type: 'Individual',
      documents: 2,
      status: 'Pending'
    },
    { 
      id: 'KYC003', 
      user: '<EMAIL>', 
      submissionDate: '2023-06-13',
      type: 'Business',
      documents: 5,
      status: 'Verified'
    },
    { 
      id: 'KYC004', 
      user: '<EMAIL>', 
      submissionDate: '2023-06-12',
      type: 'Individual',
      documents: 2,
      status: 'Rejected'
    },
    { 
      id: 'KYC005', 
      user: '<EMAIL>', 
      submissionDate: '2023-06-11',
      type: 'Individual',
      documents: 3,
      status: 'Verified'
    },
  ];

  return (
    <AdminLayout pageTitle="KYC Management">
      <Helmet>
        <title>KYC Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Submissions</CardTitle>
              <CardDescription>KYC requests received</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">125</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Verified Accounts</CardTitle>
              <CardDescription>KYC approved</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">98</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Pending Review</CardTitle>
              <CardDescription>Awaiting verification</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">18</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Rejected Submissions</CardTitle>
              <CardDescription>KYC denied</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">9</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">KYC Management</CardTitle>
              <CardDescription>Manage user KYC submissions and verification</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Export Data
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search users..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <User size={12} />
                  <span>Individual</span>
                  <X size={12} className="ml-1" />
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <CheckCircle size={12} />
                  <span>Verified</span>
                  <X size={12} className="ml-1" />
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>Recent KYC verifications and submissions</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Request ID</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Submission Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {kycRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell className="font-medium">{request.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User size={16} className="text-blue-600" />
                          {request.user}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar size={14} className="text-purple-600" />
                          {request.submissionDate}
                        </div>
                      </TableCell>
                      <TableCell>{request.type}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <FileText size={14} className="text-amber-600" />
                          {request.documents}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          request.status === 'Verified' ? 'outline' : 
                          request.status === 'Rejected' ? 'destructive' : 
                          'secondary'
                        }>
                          {request.status === 'Verified' && <CheckCircle size={12} className="mr-1" />}
                          {request.status === 'Pending' && <Clock size={12} className="mr-1" />}
                          {request.status === 'Rejected' && <X size={12} className="mr-1" />}
                          {request.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                              <span>Approve</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600">
                              <X className="mr-2 h-4 w-4" />
                              <span>Reject</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Mail className="mr-2 h-4 w-4" />
                              <span>Contact User</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Phone className="mr-2 h-4 w-4" />
                              <span>Call User</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default KYCManagement;
