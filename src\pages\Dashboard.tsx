import React, { useEffect, useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import StatCard from '@/components/StatCard';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar } from "@/components/ui/avatar";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from "@/components/ui/drawer";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { MoreVertical, Plus, Wallet, ArrowUp, ArrowDown, LayoutList, ChevronRight, CreditCard, ArrowUpRight, PiggyBank, Landmark, Clock, LineChart, BadgeDollarSign, BarChart3, AlertCircle, Receipt, Settings, Shield, Users } from "lucide-react";
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import EnhancedVirtualCard from '@/components/EnhancedVirtualCard';
import BusinessServicesSection from '@/components/BusinessServicesSection';
import TransferDialog from '@/components/TransferDialog';
import { useIsMobile } from '@/hooks/use-mobile';
import TransactionTable, { Transaction } from '@/components/TransactionTable';
import { useToast } from '@/hooks/use-toast';
import JointAccountDashboard from '@/components/JointAccountDashboard';
import AccountSwitcher from '@/components/AccountSwitcher';
import { fetchUserInfo } from '@/services/userApi';

const Dashboard = () => {
  const { user, accountType } = useAuth();
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [transferOpen, setTransferOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Assume user has a token property or get from localStorage
        const token = localStorage.getItem('token');
        if (token) {
          const data = await fetchUserInfo(token);
          setUserInfo(data);
        }
      } catch (e) {
        toast({ title: 'Error', description: 'Failed to load user info' });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) return <div>Loading...</div>;

  const firstName = userInfo?.firstName || 'User';
  const accountNumber = userInfo?.accountNumber || '**********';
  const hasJointAccounts = userInfo?.hasJointAccounts || false;

  const accountData = {
    name: `${userInfo?.firstName || ''} ${userInfo?.lastName || ''}`.trim() || 'User',
    balance: `₦${userInfo?.balance?.toLocaleString() || '0'}`,
    accountNumber: accountNumber,
    savings: `₦${userInfo?.savings?.toLocaleString() || '0'}`,
    income: `₦${userInfo?.income?.toLocaleString() || '0'}`,
    expenses: `₦${userInfo?.expenses?.toLocaleString() || '0'}`,
  };
  
  const activeCard = {
    type: 'virtual',
    cardNumber: "**** **** **** 8741",
    name: (user as any)?.fullName?.toUpperCase() || "DARAMOLA OLALEKAN",
    expiryDate: "05/25",
    cardType: "personal",
    balance: "₦45,000",
    limit: "₦100,000"
  };

  const savingsGoals = [
    { name: "Vacation", target: "₦500,000", current: "₦150,000", percentage: 30 },
    { name: "New Car", target: "₦3,000,000", current: "₦900,000", percentage: 30 },
    { name: "Education", target: "₦1,000,000", current: "₦400,000", percentage: 40 }
  ];

  const recentTransactions: Transaction[] = [
    {
      id: '1',
      recipient: 'Netflix Subscription',
      date: '2022-01-12',
      amount: 1200,
      status: 'pending',
      initials: 'NF',
      image: 'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/227_Netflix_logo-512.png',
      type: 'outgoing'
    }, 
    {
      id: '2',
      recipient: 'Figma Subscription',
      date: '2022-01-12',
      amount: 1200,
      status: 'success',
      initials: 'FG',
      image: 'https://cdn.icon-icons.com/icons2/2699/PNG/512/figma_logo_icon_170157.png',
      type: 'outgoing'
    }, 
    {
      id: '3',
      recipient: 'Sent to Alex',
      date: '2022-01-12',
      amount: 1200,
      status: 'success',
      initials: 'AL',
      type: 'outgoing'
    }
  ];
  
  const fadeInUp = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };
  
  const handleViewAllTransactions = () => {
    navigate('/transactions');
  };

  const handleViewReceipt = (transaction: Transaction) => {
    navigate(`/transactions/${transaction.id}`);
  };

  const handleCreateSavingsGoal = () => {
    toast({
      title: "Coming Soon!",
      description: "This feature will be available in the next update.",
    });
  };

  const handleQuickActions = (action: string) => {
    switch(action) {
      case 'bills':
        navigate('/bill-payment');
        break;
      case 'loans':
        navigate('/loans');
        break;
      case 'cards':
        navigate('/cards');
        break;
      case 'invest':
        toast({
          title: "Coming Soon!",
          description: "Investment features will be available in the next update.",
        });
        break;
      default:
        break;
    }
  };
  
  return (
    <DashboardLayout pageTitle="Dashboard">
      <div className="py-6 space-y-8 mb-6">
        <motion.div 
          className="glass-morph p-6 rounded-3xl border border-[#fde314]/20" 
          initial="hidden" 
          animate="visible" 
          variants={fadeInUp}
        >
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-neutral-800">Welcome back, {firstName}!</h1>
              <p className="text-neutral-600">Your financial summary at a glance</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <AccountSwitcher />
              <Dialog open={transferOpen} onOpenChange={setTransferOpen}>
                <Button 
                  onClick={() => setTransferOpen(true)}
                  className="bg-kojaPrimary hover:bg-kojaPrimary/90 border border-[#fde314]/50 shadow-[0_0_15px_rgba(253,227,20,0.3)]"
                >
                  <ArrowUpRight className="mr-2 h-4 w-4" />
                  Send Money
                </Button>
                <DialogContent className="sm:max-w-[550px]">
                  <DialogHeader>
                    <DialogTitle>Send Money</DialogTitle>
                  </DialogHeader>
                  <div className="p-4">
                    <TransferDialog open={true} onOpenChange={() => {}} isDrawer={false} />
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </motion.div>

        {hasJointAccounts && (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 lg:w-[400px]">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <LayoutList className="h-4 w-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="joint" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Joint Accounts
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-8">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <motion.div 
                  className="col-span-1 sm:col-span-2 lg:col-span-2"
                  variants={fadeInUp} 
                  initial="hidden" 
                  animate="visible" 
                  transition={{ delay: 0.1 }}
                >
                  <Card className="modern-card h-full border-[#fde314]/10">
                    <CardHeader className="flex flex-row items-center justify-between pb-2">
                      <CardTitle className="text-lg font-medium">Balance Details</CardTitle>
                      <Button variant="ghost" size="sm" className="text-kojaGray hover:text-kojaPrimary">
                        <MoreVertical size={16} />
                      </Button>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <p className="text-sm text-kojaGray">Total Balance</p>
                        <h3 className="text-3xl font-bold text-kojaDark">
                          {accountData.balance}
                        </h3>
                        <p className="text-sm text-green-600 flex items-center gap-1">
                          <ArrowUp size={14} />
                          <span>+₦15,000 this month</span>
                        </p>
                      </div>
                      
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div className="bg-gray-50/80 p-3 rounded-xl">
                          <div className="flex items-center gap-2">
                            <div className="bg-kojaPrimary/10 p-2 rounded-lg">
                              <ArrowUp className="text-kojaPrimary" size={18} />
                            </div>
                            <span className="text-sm font-medium">Income</span>
                          </div>
                          <p className="mt-2 text-xl font-semibold">{accountData.income}</p>
                        </div>
                        
                        <div className="bg-gray-50/80 p-3 rounded-xl">
                          <div className="flex items-center gap-2">
                            <div className="bg-red-100 p-2 rounded-lg">
                              <ArrowDown className="text-red-500" size={18} />
                            </div>
                            <span className="text-sm font-medium">Expenses</span>
                          </div>
                          <p className="mt-2 text-xl font-semibold">{accountData.expenses}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
                
                <motion.div 
                  className="col-span-1 sm:col-span-2 lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-6"
                  variants={fadeInUp} 
                  initial="hidden" 
                  animate="visible" 
                  transition={{ delay: 0.2 }}
                >
                  <Card className="glass-morph border-kojaPrimary/20 hover:border-kojaPrimary/40">
                    <CardContent className="p-4 flex flex-col justify-between h-full">
                      <div className="flex items-center gap-2">
                        <div className="bg-kojaPrimary/10 p-2 rounded-lg">
                          <PiggyBank className="text-kojaPrimary" size={20} />
                        </div>
                        <span className="text-sm font-medium">Savings</span>
                      </div>
                      <div className="mt-4">
                        <p className="text-xl font-semibold">{accountData.savings}</p>
                        <Button 
                          variant="ghost" 
                          className="text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5 flex items-center gap-1 px-0 mt-1"
                          onClick={() => navigate('/savings')}
                        >
                          <span className="text-xs">View details</span>
                          <ChevronRight size={14} />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card className="glass-morph border-purple-300/20 hover:border-purple-300/40">
                    <CardContent className="p-4 flex flex-col justify-between h-full">
                      <div className="flex items-center gap-2">
                        <div className="bg-purple-100 p-2 rounded-lg">
                          <Landmark className="text-purple-600" size={20} />
                        </div>
                        <span className="text-sm font-medium">Loans</span>
                      </div>
                      <div className="mt-4">
                        <p className="text-xl font-semibold">₦0.00</p>
                        <Button 
                          variant="ghost" 
                          className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 flex items-center gap-1 px-0 mt-1"
                          onClick={() => navigate('/loans')}
                        >
                          <span className="text-xs">Get a loan</span>
                          <ChevronRight size={14} />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>

              <motion.div 
                className="space-y-4"
                variants={fadeInUp} 
                initial="hidden" 
                animate="visible" 
                transition={{ delay: 0.3 }}
              >
                <h2 className="text-lg font-medium">Quick Actions</h2>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  <Button 
                    variant="outline" 
                    className="h-24 flex flex-col items-center justify-center gap-2 border-kojaPrimary/20 hover:bg-[#fde314]/5 hover:border-[#fde314]/50"
                    onClick={() => handleQuickActions('bills')}
                  >
                    <div className="bg-[#fde314]/10 p-2 rounded-full">
                      <Receipt className="text-[#fde314] h-5 w-5" />
                    </div>
                    <span>Pay Bills</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-24 flex flex-col items-center justify-center gap-2 border-purple-300/20"
                    onClick={() => handleQuickActions('loans')}
                  >
                    <div className="bg-purple-100 p-2 rounded-full">
                      <Landmark className="text-purple-600 h-5 w-5" />
                    </div>
                    <span>Loans</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-24 flex flex-col items-center justify-center gap-2 border-blue-300/20"
                    onClick={() => handleQuickActions('cards')}
                  >
                    <div className="bg-blue-100 p-2 rounded-full">
                      <CreditCard className="text-blue-600 h-5 w-5" />
                    </div>
                    <span>Cards</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="h-24 flex flex-col items-center justify-center gap-2 border-green-300/20"
                    onClick={() => handleQuickActions('invest')}
                  >
                    <div className="bg-green-100 p-2 rounded-full">
                      <LineChart className="text-green-600 h-5 w-5" />
                    </div>
                    <span>Invest</span>
                  </Button>
                </div>
              </motion.div>

              <motion.div 
                className="space-y-4"
                variants={fadeInUp} 
                initial="hidden" 
                animate="visible" 
                transition={{ delay: 0.3 }}
              >
                <h2 className="text-lg font-medium">My Virtual Card</h2>
                <div className="w-full bg-white/60 backdrop-blur-xl p-4 rounded-[20px] border border-[#D3E4FD] shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                    <div className="w-full md:w-auto">
                      <EnhancedVirtualCard
                        cardNumber="**** **** **** 8741"
                        name={(user as any)?.fullName?.toUpperCase() || "DARAMOLA OLALEKAN"}
                        expiryDate="05/25"
                        cardType="personal"
                        variant="virtual"
                        className="mx-auto"
                      />
                    </div>
                    <div className="w-full md:w-auto space-y-4">
                      <div className="space-y-2">
                        <p className="text-kojaGray text-sm">Card Balance</p>
                        <h3 className="text-2xl font-bold">₦45,000</h3>
                        <p className="text-sm text-kojaGray">Limit: ₦100,000</p>
                      </div>
                      <div className="flex flex-col sm:flex-row gap-3">
                        <Button 
                          variant="outline" 
                          className="flex items-center gap-2 border-kojaPrimary/20 text-kojaPrimary shadow-sm hover:shadow-md"
                          onClick={() => navigate('/cards')}
                        >
                          <Shield size={16} />
                          <span>Card Details</span>
                        </Button>
                        <Button 
                          className="flex items-center gap-2 bg-[#9b87f5] hover:bg-[#9b87f5]/90 text-white shadow-sm hover:shadow-md"
                          onClick={() => navigate('/cards')}
                        >
                          <Settings size={16} />
                          <span>Manage Card</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div 
                className="lg:col-span-2 space-y-6 mb-8"
                variants={fadeInUp} 
                initial="hidden" 
                animate="visible" 
                transition={{ delay: 0.4 }}
              >
                <Card className="modern-card">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-lg font-medium">Recent Transactions</CardTitle>
                    <Button 
                      variant="ghost" 
                      className="text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5 flex items-center gap-1" 
                      onClick={handleViewAllTransactions}
                    >
                      View all
                      <ChevronRight size={16} />
                    </Button>
                  </CardHeader>
                  <CardContent>
                    <TransactionTable 
                      transactions={recentTransactions} 
                      onViewReceipt={handleViewReceipt}
                    />
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div 
                className="space-y-6 mb-8"
                variants={fadeInUp} 
                initial="hidden" 
                animate="visible" 
                transition={{ delay: 0.5 }}
              >
                <Card className="modern-card">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-lg font-medium">Savings Goals</CardTitle>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-kojaPrimary"
                      onClick={handleCreateSavingsGoal}
                    >
                      <Plus size={16} className="mr-1" />
                      New Goal
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {savingsGoals.map((goal, index) => (
                      <div key={index} className="space-y-2 p-3 bg-gray-50/80 rounded-xl">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{goal.name}</span>
                          <span className="text-sm text-kojaGray">{goal.current} / {goal.target}</span>
                        </div>
                        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-kojaPrimary"
                            style={{ width: `${goal.percentage}%` }}
                          ></div>
                        </div>
                        <div className="flex justify-between items-center text-xs text-kojaGray">
                          <span>{goal.percentage}% completed</span>
                          <Button variant="ghost" size="sm" className="h-6 text-xs text-kojaPrimary p-0">
                            Add funds
                          </Button>
                        </div>
                      </div>
                    ))}
                    
                    {savingsGoals.length === 0 && (
                      <div className="flex flex-col items-center justify-center py-6 text-center">
                        <PiggyBank className="text-kojaGray mb-2" size={40} />
                        <p className="text-kojaGray">No savings goals yet</p>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="mt-2 text-kojaPrimary border-kojaPrimary/20"
                          onClick={handleCreateSavingsGoal}
                        >
                          Create a goal
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card className="modern-card">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-lg font-medium">Your Card</CardTitle>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-kojaGray hover:text-kojaPrimary" 
                      onClick={() => navigate('/cards')}
                    >
                      Manage
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-2 bg-gray-50/50 rounded-xl">
                      <EnhancedVirtualCard
                        cardNumber={activeCard.cardNumber}
                        name={activeCard.name}
                        expiryDate={activeCard.expiryDate}
                        cardType={activeCard.cardType as 'personal' | 'business'}
                        variant={activeCard.type as 'physical' | 'virtual'}
                      />
                    </div>
                    
                    <Button 
                      variant="outline" 
                      className="w-full flex items-center justify-center gap-2 text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5" 
                      onClick={() => navigate('/cards')}
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add new card</span>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            </TabsContent>

            <TabsContent value="joint" className="space-y-6">
              <JointAccountDashboard />
            </TabsContent>
          </Tabs>
        )}

        {!hasJointAccounts && (
          <>
            <motion.div 
              className="glass-morph p-6 rounded-3xl border border-[#fde314]/20" 
              initial="hidden" 
              animate="visible" 
              variants={fadeInUp}
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="space-y-2">
                  <h1 className="text-2xl font-bold text-neutral-800">Welcome back, {firstName}!</h1>
                  <p className="text-neutral-600">Your financial summary at a glance</p>
                </div>
                <div className="mt-4 md:mt-0 flex space-x-3">
                  <Dialog open={transferOpen} onOpenChange={setTransferOpen}>
                    <Button 
                      onClick={() => setTransferOpen(true)}
                      className="bg-kojaPrimary hover:bg-kojaPrimary/90 border border-[#fde314]/50 shadow-[0_0_15px_rgba(253,227,20,0.3)]"
                    >
                      <ArrowUpRight className="mr-2 h-4 w-4" />
                      Send Money
                    </Button>
                    <DialogContent className="sm:max-w-[550px]">
                      <DialogHeader>
                        <DialogTitle>Send Money</DialogTitle>
                      </DialogHeader>
                      <div className="p-4">
                        <TransferDialog open={true} onOpenChange={() => {}} isDrawer={false} />
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            </motion.div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <motion.div 
                className="col-span-1 sm:col-span-2 lg:col-span-2"
                variants={fadeInUp} 
                initial="hidden" 
                animate="visible" 
                transition={{ delay: 0.1 }}
              >
                <Card className="modern-card h-full border-[#fde314]/10">
                  <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-lg font-medium">Balance Details</CardTitle>
                    <Button variant="ghost" size="sm" className="text-kojaGray hover:text-kojaPrimary">
                      <MoreVertical size={16} />
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <p className="text-sm text-kojaGray">Total Balance</p>
                      <h3 className="text-3xl font-bold text-kojaDark">
                        {accountData.balance}
                      </h3>
                      <p className="text-sm text-green-600 flex items-center gap-1">
                        <ArrowUp size={14} />
                        <span>+₦15,000 this month</span>
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div className="bg-gray-50/80 p-3 rounded-xl">
                        <div className="flex items-center gap-2">
                          <div className="bg-kojaPrimary/10 p-2 rounded-lg">
                            <ArrowUp className="text-kojaPrimary" size={18} />
                          </div>
                          <span className="text-sm font-medium">Income</span>
                        </div>
                        <p className="mt-2 text-xl font-semibold">{accountData.income}</p>
                      </div>
                      
                      <div className="bg-gray-50/80 p-3 rounded-xl">
                        <div className="flex items-center gap-2">
                          <div className="bg-red-100 p-2 rounded-lg">
                            <ArrowDown className="text-red-500" size={18} />
                          </div>
                          <span className="text-sm font-medium">Expenses</span>
                        </div>
                        <p className="mt-2 text-xl font-semibold">{accountData.expenses}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
              
              <motion.div 
                className="col-span-1 sm:col-span-2 lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-6"
                variants={fadeInUp} 
                initial="hidden" 
                animate="visible" 
                transition={{ delay: 0.2 }}
              >
                <Card className="glass-morph border-kojaPrimary/20 hover:border-kojaPrimary/40">
                  <CardContent className="p-4 flex flex-col justify-between h-full">
                    <div className="flex items-center gap-2">
                      <div className="bg-kojaPrimary/10 p-2 rounded-lg">
                        <PiggyBank className="text-kojaPrimary" size={20} />
                      </div>
                      <span className="text-sm font-medium">Savings</span>
                    </div>
                    <div className="mt-4">
                      <p className="text-xl font-semibold">{accountData.savings}</p>
                      <Button 
                        variant="ghost" 
                        className="text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5 flex items-center gap-1 px-0 mt-1"
                        onClick={() => navigate('/savings')}
                      >
                        <span className="text-xs">View details</span>
                        <ChevronRight size={14} />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="glass-morph border-purple-300/20 hover:border-purple-300/40">
                  <CardContent className="p-4 flex flex-col justify-between h-full">
                    <div className="flex items-center gap-2">
                      <div className="bg-purple-100 p-2 rounded-lg">
                        <Landmark className="text-purple-600" size={20} />
                      </div>
                      <span className="text-sm font-medium">Loans</span>
                    </div>
                    <div className="mt-4">
                      <p className="text-xl font-semibold">₦0.00</p>
                      <Button 
                        variant="ghost" 
                        className="text-purple-600 hover:text-purple-700 hover:bg-purple-50 flex items-center gap-1 px-0 mt-1"
                        onClick={() => navigate('/loans')}
                      >
                        <span className="text-xs">Get a loan</span>
                        <ChevronRight size={14} />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            <motion.div 
              className="space-y-4"
              variants={fadeInUp} 
              initial="hidden" 
              animate="visible" 
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-lg font-medium">Quick Actions</h2>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                <Button 
                  variant="outline" 
                  className="h-24 flex flex-col items-center justify-center gap-2 border-kojaPrimary/20 hover:bg-[#fde314]/5 hover:border-[#fde314]/50"
                  onClick={() => handleQuickActions('bills')}
                >
                  <div className="bg-[#fde314]/10 p-2 rounded-full">
                    <Receipt className="text-[#fde314] h-5 w-5" />
                  </div>
                  <span>Pay Bills</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  className="h-24 flex flex-col items-center justify-center gap-2 border-purple-300/20"
                  onClick={() => handleQuickActions('loans')}
                >
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Landmark className="text-purple-600 h-5 w-5" />
                  </div>
                  <span>Loans</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  className="h-24 flex flex-col items-center justify-center gap-2 border-blue-300/20"
                  onClick={() => handleQuickActions('cards')}
                >
                  <div className="bg-blue-100 p-2 rounded-full">
                    <CreditCard className="text-blue-600 h-5 w-5" />
                  </div>
                  <span>Cards</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  className="h-24 flex flex-col items-center justify-center gap-2 border-green-300/20"
                  onClick={() => handleQuickActions('invest')}
                >
                  <div className="bg-green-100 p-2 rounded-full">
                    <LineChart className="text-green-600 h-5 w-5" />
                  </div>
                  <span>Invest</span>
                </Button>
              </div>
            </motion.div>

            <motion.div 
              className="space-y-4"
              variants={fadeInUp} 
              initial="hidden" 
              animate="visible" 
              transition={{ delay: 0.3 }}
            >
              <h2 className="text-lg font-medium">My Virtual Card</h2>
              <div className="w-full bg-white/60 backdrop-blur-xl p-4 rounded-[20px] border border-[#D3E4FD] shadow-lg hover:shadow-xl transition-all duration-300">
                <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                  <div className="w-full md:w-auto">
                    <EnhancedVirtualCard
                      cardNumber="**** **** **** 8741"
                      name={(user as any)?.fullName?.toUpperCase() || "DARAMOLA OLALEKAN"}
                      expiryDate="05/25"
                      cardType="personal"
                      variant="virtual"
                      className="mx-auto"
                    />
                  </div>
                  <div className="w-full md:w-auto space-y-4">
                    <div className="space-y-2">
                      <p className="text-kojaGray text-sm">Card Balance</p>
                      <h3 className="text-2xl font-bold">₦45,000</h3>
                      <p className="text-sm text-kojaGray">Limit: ₦100,000</p>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <Button 
                        variant="outline" 
                        className="flex items-center gap-2 border-kojaPrimary/20 text-kojaPrimary shadow-sm hover:shadow-md"
                        onClick={() => navigate('/cards')}
                      >
                        <Shield size={16} />
                        <span>Card Details</span>
                      </Button>
                      <Button 
                        className="flex items-center gap-2 bg-[#9b87f5] hover:bg-[#9b87f5]/90 text-white shadow-sm hover:shadow-md"
                        onClick={() => navigate('/cards')}
                      >
                        <Settings size={16} />
                        <span>Manage Card</span>
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            <motion.div 
              className="lg:col-span-2 space-y-6 mb-8"
              variants={fadeInUp} 
              initial="hidden" 
              animate="visible" 
              transition={{ delay: 0.4 }}
            >
              <Card className="modern-card">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium">Recent Transactions</CardTitle>
                  <Button 
                    variant="ghost" 
                    className="text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5 flex items-center gap-1" 
                    onClick={handleViewAllTransactions}
                  >
                    View all
                    <ChevronRight size={16} />
                  </Button>
                </CardHeader>
                <CardContent>
                  <TransactionTable 
                    transactions={recentTransactions} 
                    onViewReceipt={handleViewReceipt}
                  />
                </CardContent>
              </Card>
            </motion.div>

            <motion.div 
              className="space-y-6 mb-8"
              variants={fadeInUp} 
              initial="hidden" 
              animate="visible" 
              transition={{ delay: 0.5 }}
            >
              <Card className="modern-card">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium">Savings Goals</CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-kojaPrimary"
                    onClick={handleCreateSavingsGoal}
                  >
                    <Plus size={16} className="mr-1" />
                    New Goal
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  {savingsGoals.map((goal, index) => (
                    <div key={index} className="space-y-2 p-3 bg-gray-50/80 rounded-xl">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{goal.name}</span>
                        <span className="text-sm text-kojaGray">{goal.current} / {goal.target}</span>
                      </div>
                      <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-kojaPrimary"
                          style={{ width: `${goal.percentage}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between items-center text-xs text-kojaGray">
                        <span>{goal.percentage}% completed</span>
                        <Button variant="ghost" size="sm" className="h-6 text-xs text-kojaPrimary p-0">
                          Add funds
                        </Button>
                      </div>
                    </div>
                  ))}
                  
                  {savingsGoals.length === 0 && (
                    <div className="flex flex-col items-center justify-center py-6 text-center">
                      <PiggyBank className="text-kojaGray mb-2" size={40} />
                      <p className="text-kojaGray">No savings goals yet</p>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="mt-2 text-kojaPrimary border-kojaPrimary/20"
                        onClick={handleCreateSavingsGoal}
                      >
                        Create a goal
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card className="modern-card">
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <CardTitle className="text-lg font-medium">Your Card</CardTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-kojaGray hover:text-kojaPrimary" 
                    onClick={() => navigate('/cards')}
                  >
                    Manage
                  </Button>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-2 bg-gray-50/50 rounded-xl">
                    <EnhancedVirtualCard
                      cardNumber={activeCard.cardNumber}
                      name={activeCard.name}
                      expiryDate={activeCard.expiryDate}
                      cardType={activeCard.cardType as 'personal' | 'business'}
                      variant={activeCard.type as 'physical' | 'virtual'}
                    />
                  </div>
                  
                  <Button 
                    variant="outline" 
                    className="w-full flex items-center justify-center gap-2 text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5" 
                    onClick={() => navigate('/cards')}
                  >
                    <Plus className="h-4 w-4" />
                    <span>Add new card</span>
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
