
/* Navigation Item Styles */
.nav-item {
  @apply flex items-center px-3 py-2 rounded-lg transition-colors duration-200 text-white/90 hover:bg-white/10 hover:text-white font-futura shadow-md;
}

.nav-item-active {
  @apply bg-[#FDE314] text-[#000000] font-futura shadow-lg;
}

.nav-badge {
  @apply px-2 py-0.5 text-xs rounded-full bg-red-500 text-white font-futura;
}

.sidebar-profile {
  @apply p-3 bg-white/5 flex items-center gap-3 cursor-pointer hover:bg-white/10 transition-colors mx-4 font-futura;
}

.sidebar-profile-name {
  @apply text-white font-medium text-sm font-futura;
}

.sidebar-profile-role {
  @apply text-white/70 text-xs font-futura;
}

.sidebar-section-title {
  @apply text-white/70 px-3 py-1 text-xs uppercase tracking-wider font-futura;
}

.sidebar-button {
  @apply w-full flex items-center justify-center gap-2 px-3 py-2 rounded-lg transition-colors font-futura;
}

/* Business UI components */
.business-nav-item {
  @apply flex items-center px-3 py-2 rounded-lg transition-colors duration-200 text-white/90 hover:bg-white/10 hover:text-white font-futura shadow-md;
}

.business-card-title {
  @apply font-futura font-bold;
}

.business-stat-value {
  @apply text-2xl font-bold font-futura;
}

/* Admin UI components */
.admin-nav-item {
  @apply flex items-center px-3 py-2 rounded-lg transition-colors duration-200 text-white/90 hover:bg-white/10 hover:text-white font-futura shadow-md;
}

.admin-card-title {
  @apply font-futura font-bold;
}

.admin-page-title {
  @apply text-2xl font-bold tracking-tight font-futura;
}

/* Virtual Card components */
.virtual-card {
  @apply rounded-xl shadow-lg transition-all duration-300 overflow-hidden p-4 font-futura;
}

.virtual-card-number {
  @apply font-mono text-sm tracking-widest font-futura;
}

.virtual-card-name {
  @apply text-sm font-semibold tracking-wide uppercase font-futura;
}

.virtual-card-expiry {
  @apply text-sm font-semibold tracking-wide font-futura;
}

.virtual-card-balance {
  @apply text-2xl font-bold font-futura;
}

.virtual-card-label {
  @apply text-sm text-gray-600 font-futura;
}

.virtual-card-button {
  @apply flex items-center gap-2 text-sm font-futura;
}

/* Profile page specific styles */
.profile-nav-item {
  @apply flex items-center justify-between p-3 rounded-lg transition-all duration-200 mb-1 hover:bg-white/10 text-white/90 font-futura;
}

.profile-nav-item-active {
  @apply bg-kojaYellow text-kojaDark font-futura font-medium;
}

.profile-nav-icon {
  @apply h-4 w-4 mr-3;
}

.profile-nav-text {
  @apply flex-1 text-sm font-futura;
}

.progress-circle {
  @apply w-14 h-14 rounded-full flex items-center justify-center mx-auto;
}

.progress-circle-text {
  @apply text-sm font-medium font-futura;
}
