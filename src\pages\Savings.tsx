
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Ta<PERSON>, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Switch 
} from '@/components/ui/switch';
import { 
  PiggyBank, 
  Target, 
  Plus, 
  Calendar, 
  Clock, 
  TrendingUp,
  ArrowRight,
  ArrowUpRight,
  DollarSign
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Savings = () => {
  const { toast } = useToast();
  const [showCreateTarget, setShowCreateTarget] = useState(false);
  const [showCreateAutoSave, setShowCreateAutoSave] = useState(false);
  
  const targetSavings = [
    { 
      id: 1, 
      name: 'Dream Vacation', 
      target: 500000, 
      saved: 350000, 
      progress: 70, 
      deadline: 'December 2023',
      icon: <Target size={20} className="text-purple-600" />
    },
    { 
      id: 2, 
      name: 'New Laptop', 
      target: 300000, 
      saved: 120000, 
      progress: 40, 
      deadline: 'August 2023',
      icon: <Target size={20} className="text-blue-600" />
    },
    { 
      id: 3, 
      name: 'Emergency Fund', 
      target: 1000000, 
      saved: 600000, 
      progress: 60, 
      deadline: 'Ongoing',
      icon: <Target size={20} className="text-green-600" />
    },
  ];
  
  const autoSavings = [
    { 
      id: 1, 
      name: 'Daily Savings', 
      amount: 2000, 
      frequency: 'Daily', 
      total: 168000,
      nextDate: 'Tomorrow',
      icon: <Clock size={20} className="text-banklyBlue" />
    },
    { 
      id: 2, 
      name: 'Salary Savings', 
      amount: 50000, 
      frequency: 'Monthly', 
      total: 300000,
      nextDate: 'July 28, 2023',
      icon: <Calendar size={20} className="text-green-600" />
    },
  ];

  const handleCreateTarget = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Target Savings Created",
      description: "Your new savings goal has been created successfully",
    });
    setShowCreateTarget(false);
  };

  const handleCreateAutoSave = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Auto-Save Rule Created",
      description: "Your new auto-save rule has been created successfully",
    });
    setShowCreateAutoSave(false);
  };

  return (
    <DashboardLayout pageTitle="Savings">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-katina">Savings</h2>
          <p className="text-muted-foreground">Manage your savings goals and auto-save rules</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-banklyBlue/10 flex items-center justify-center">
                  <PiggyBank size={24} className="text-banklyBlue" />
                </div>
                <TrendingUp size={20} className="text-green-500" />
              </div>
              <h3 className="text-lg font-semibold">Total Savings</h3>
              <p className="text-3xl font-bold mt-2">₦1,070,000</p>
              <p className="text-sm text-green-600 mt-1">+8.5% from last month</p>
              <Button className="w-full mt-4" variant="outline">
                View Details
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <Target size={24} className="text-green-600" />
                </div>
                <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Active</div>
              </div>
              <h3 className="text-lg font-semibold">Target Savings</h3>
              <p className="text-3xl font-bold mt-2">3</p>
              <p className="text-sm text-muted-foreground mt-1">Goals in progress</p>
              <Button className="w-full mt-4" variant="outline">
                View Targets
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  <Clock size={24} className="text-blue-600" />
                </div>
                <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Automated</div>
              </div>
              <h3 className="text-lg font-semibold">Auto Savings</h3>
              <p className="text-3xl font-bold mt-2">₦52,000</p>
              <p className="text-sm text-muted-foreground mt-1">Saved this month</p>
              <Button className="w-full mt-4" variant="outline">
                View Rules
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <Tabs defaultValue="target" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="target">Target Savings</TabsTrigger>
            <TabsTrigger value="auto">Auto Savings</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="target">
            {!showCreateTarget ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Your Savings Goals</h3>
                  <Button onClick={() => setShowCreateTarget(true)}>
                    <Plus size={16} className="mr-2" />
                    New Target
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {targetSavings.map((target) => (
                    <Card key={target.id} className="backdrop-blur-sm bg-white/90 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                              {target.icon}
                            </div>
                            <h4 className="font-semibold">{target.name}</h4>
                          </div>
                          <div className="text-sm text-gray-500">{target.deadline}</div>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Target</span>
                            <span className="font-medium">₦{target.target.toLocaleString()}</span>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Saved</span>
                            <span className="font-medium text-green-600">₦{target.saved.toLocaleString()}</span>
                          </div>
                          
                          <div className="space-y-1">
                            <div className="flex justify-between items-center text-xs">
                              <span>{target.progress}%</span>
                              <span>₦{(target.target - target.saved).toLocaleString()} to go</span>
                            </div>
                            <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                              <div 
                                className="h-full bg-banklyBlue rounded-full" 
                                style={{ width: `${target.progress}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="mt-4 flex gap-2">
                          <Button variant="outline" size="sm" className="flex-1">Add Funds</Button>
                          <Button variant="outline" size="sm" className="flex-1">Details</Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            ) : (
              <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
                <CardHeader>
                  <CardTitle>Create New Savings Target</CardTitle>
                  <CardDescription>Set a new savings goal and track your progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreateTarget} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="targetName">Goal Name</Label>
                      <Input id="targetName" placeholder="e.g., Dream Vacation, New Car, etc." required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="targetAmount">Target Amount (₦)</Label>
                      <Input id="targetAmount" type="number" placeholder="Enter amount" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="targetDeadline">Deadline</Label>
                      <Input id="targetDeadline" type="date" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="initialDeposit">Initial Deposit (₦)</Label>
                      <Input id="initialDeposit" type="number" placeholder="0" />
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Switch id="enableReminders" />
                      <Label htmlFor="enableReminders">Enable reminders</Label>
                    </div>
                  </form>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateTarget(false)}>Cancel</Button>
                  <Button onClick={handleCreateTarget}>Create Target</Button>
                </CardFooter>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="auto">
            {!showCreateAutoSave ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Your Auto-Save Rules</h3>
                  <Button onClick={() => setShowCreateAutoSave(true)}>
                    <Plus size={16} className="mr-2" />
                    New Rule
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {autoSavings.map((rule) => (
                    <Card key={rule.id} className="backdrop-blur-sm bg-white/90 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center">
                              {rule.icon}
                            </div>
                            <div>
                              <h4 className="font-semibold">{rule.name}</h4>
                              <p className="text-xs text-gray-500">{rule.frequency} savings</p>
                            </div>
                          </div>
                          <Switch defaultChecked />
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Amount</span>
                            <span className="font-medium">₦{rule.amount.toLocaleString()}</span>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Total Saved</span>
                            <span className="font-medium text-green-600">₦{rule.total.toLocaleString()}</span>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Next Deduction</span>
                            <span className="font-medium">{rule.nextDate}</span>
                          </div>
                        </div>
                        
                        <div className="mt-4 flex gap-2">
                          <Button variant="outline" size="sm" className="flex-1">Edit</Button>
                          <Button variant="outline" size="sm" className="flex-1">Details</Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                
                <Card className="backdrop-blur-sm bg-white/90 shadow-md border border-gray-100 mt-6">
                  <CardHeader>
                    <CardTitle>Smart Auto-Save</CardTitle>
                    <CardDescription>Let AI help you save money automatically based on your spending patterns</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                        <ArrowUpRight size={24} className="text-purple-600" />
                      </div>
                      <div>
                        <h4 className="font-semibold">Smart Saving Algorithm</h4>
                        <p className="text-sm text-gray-600">Our AI analyzes your transactions and automatically saves small amounts you won't miss</p>
                      </div>
                    </div>
                    
                    <div className="border rounded-lg p-4 bg-gray-50 mb-4">
                      <p className="text-sm">Based on your spending patterns, we can save approximately <span className="font-semibold text-green-600">₦3,000 - ₦7,000</span> weekly for you without affecting your lifestyle.</p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Switch id="enableSmartSave" />
                        <Label htmlFor="enableSmartSave">Enable Smart Auto-Save</Label>
                      </div>
                      <Button variant="outline" size="sm">Learn More</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
                <CardHeader>
                  <CardTitle>Create Auto-Save Rule</CardTitle>
                  <CardDescription>Set up automatic savings at your preferred frequency</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreateAutoSave} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="ruleName">Rule Name</Label>
                      <Input id="ruleName" placeholder="e.g., Daily Savings, Salary Save, etc." required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="frequency">Frequency</Label>
                      <select 
                        id="frequency"
                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        required
                      >
                        <option value="">Select frequency</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                        <option value="biweekly">Bi-weekly</option>
                        <option value="monthly">Monthly</option>
                      </select>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="saveAmount">Amount to Save (₦)</Label>
                      <Input id="saveAmount" type="number" placeholder="Enter amount" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="startDate">Start Date</Label>
                      <Input id="startDate" type="date" required />
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Switch id="saveOnWeekends" />
                      <Label htmlFor="saveOnWeekends">Save on weekends (for daily savings)</Label>
                    </div>
                  </form>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateAutoSave(false)}>Cancel</Button>
                  <Button onClick={handleCreateAutoSave}>Create Rule</Button>
                </CardFooter>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="history">
            <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
              <CardHeader>
                <CardTitle>Savings History</CardTitle>
                <CardDescription>View your recent savings transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { date: 'July 10, 2023', type: 'Auto Save', amount: '₦2,000', target: 'Daily Savings' },
                    { date: 'July 9, 2023', type: 'Auto Save', amount: '₦2,000', target: 'Daily Savings' },
                    { date: 'July 8, 2023', type: 'Manual Deposit', amount: '₦50,000', target: 'Dream Vacation' },
                    { date: 'July 8, 2023', type: 'Auto Save', amount: '₦2,000', target: 'Daily Savings' },
                    { date: 'July 7, 2023', type: 'Auto Save', amount: '₦2,000', target: 'Daily Savings' },
                    { date: 'July 6, 2023', type: 'Auto Save', amount: '₦2,000', target: 'Daily Savings' },
                    { date: 'July 5, 2023', type: 'Manual Deposit', amount: '₦20,000', target: 'New Laptop' },
                  ].map((transaction, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border-b last:border-0">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                          <DollarSign className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium">{transaction.type}</div>
                          <div className="text-sm text-muted-foreground">{transaction.date} • {transaction.target}</div>
                        </div>
                      </div>
                      <div className="font-semibold text-green-600">{transaction.amount}</div>
                    </div>
                  ))}
                </div>
                
                <Button variant="outline" className="w-full mt-4">
                  View All Transactions
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Savings;
