
import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Switch
} from '@/components/ui/switch';
import {
  PiggyBank,
  Target,
  Plus,
  TrendingUp,
  ArrowRight,
  Users,
  Heart,
  Share2
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { personalSavingsApi, groupSavingsApi, savingsApi } from '@/services/savingsApi';
import { PersonalSavingsPlan, GroupSavingsMember, SavingsSummary } from '@/types/savings';

const Savings = () => {
  const { toast } = useToast();
  const [showCreatePersonalPlan, setShowCreatePersonalPlan] = useState(false);
  const [showContribute, setShowContribute] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<PersonalSavingsPlan | null>(null);

  // State for data
  const [personalPlans, setPersonalPlans] = useState<PersonalSavingsPlan[]>([]);
  const [groupMemberships, setGroupMemberships] = useState<GroupSavingsMember[]>([]);
  const [publicPlans, setPublicPlans] = useState<PersonalSavingsPlan[]>([]);
  const [savingsSummary, setSavingsSummary] = useState<SavingsSummary | null>(null);
  const [loading, setLoading] = useState(true);

  // Form states
  const [personalPlanForm, setPersonalPlanForm] = useState({
    title: '',
    description: '',
    targetAmount: '',
    targetDate: '',
    isPublic: true,
    allowContributions: true
  });

  const [contributionForm, setContributionForm] = useState({
    amount: '',
    message: '',
    isAnonymous: false
  });

  // Load data on component mount
  useEffect(() => {
    loadSavingsData();
  }, []);

  const loadSavingsData = async () => {
    try {
      setLoading(true);
      const [personalPlansData, groupMembershipsData, publicPlansData, summaryData] = await Promise.all([
        personalSavingsApi.getUserPlans(),
        groupSavingsApi.getUserGroups(),
        personalSavingsApi.getPublicPlans(),
        savingsApi.getSavingsSummary()
      ]);

      setPersonalPlans(personalPlansData);
      setGroupMemberships(groupMembershipsData);
      setPublicPlans(publicPlansData);
      setSavingsSummary(summaryData);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to load savings data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreatePersonalPlan = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const planData = {
        title: personalPlanForm.title,
        description: personalPlanForm.description || undefined,
        targetAmount: parseFloat(personalPlanForm.targetAmount),
        targetDate: personalPlanForm.targetDate ? new Date(personalPlanForm.targetDate) : undefined,
        isPublic: personalPlanForm.isPublic,
        allowContributions: personalPlanForm.allowContributions
      };

      await personalSavingsApi.createPlan(planData);
      toast({
        title: "Personal Savings Plan Created",
        description: "Your new savings plan has been created successfully",
      });

      setShowCreatePersonalPlan(false);
      setPersonalPlanForm({
        title: '',
        description: '',
        targetAmount: '',
        targetDate: '',
        isPublic: true,
        allowContributions: true
      });

      loadSavingsData(); // Reload data
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create savings plan",
        variant: "destructive"
      });
    }
  };

  const handleContributeToPlan = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPlan) return;

    try {
      const contributionData = {
        planId: selectedPlan.id,
        amount: parseFloat(contributionForm.amount),
        message: contributionForm.message || undefined,
        isAnonymous: contributionForm.isAnonymous
      };

      await personalSavingsApi.contributeToPlan(contributionData);
      toast({
        title: "Contribution Successful",
        description: `You contributed ₦${parseFloat(contributionForm.amount).toLocaleString()} to ${selectedPlan.title}`,
      });

      setShowContribute(false);
      setSelectedPlan(null);
      setContributionForm({
        amount: '',
        message: '',
        isAnonymous: false
      });

      loadSavingsData(); // Reload data
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to contribute to savings plan",
        variant: "destructive"
      });
    }
  };

  return (
    <DashboardLayout pageTitle="Savings">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-katina text-slate-800">Savings</h2>
          <p className="text-slate-600">Manage your savings goals and contribute to others' plans</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-white/95 shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-banklyBlue/10 flex items-center justify-center">
                  <PiggyBank size={24} className="text-banklyBlue" />
                </div>
                <TrendingUp size={20} className="text-green-500" />
              </div>
              <h3 className="text-lg font-semibold">Personal Savings</h3>
              <p className="text-3xl font-bold mt-2">
                ₦{savingsSummary?.totalPersonalSavings?.toLocaleString() || '0'}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {personalPlans.length} active plan{personalPlans.length !== 1 ? 's' : ''}
              </p>
              <Button className="w-full mt-4" variant="outline">
                View Plans
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-white/95 shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                  <Users size={24} className="text-green-600" />
                </div>
                <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Group</div>
              </div>
              <h3 className="text-lg font-semibold">Group Savings</h3>
              <p className="text-3xl font-bold mt-2">
                ₦{savingsSummary?.totalGroupSavings?.toLocaleString() || '0'}
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                {groupMemberships.length} group{groupMemberships.length !== 1 ? 's' : ''} joined
              </p>
              <Button className="w-full mt-4" variant="outline">
                View Groups
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>

          <Card className="bg-white/95 shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                  <Heart size={24} className="text-purple-600" />
                </div>
                <div className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">Contributions</div>
              </div>
              <h3 className="text-lg font-semibold">Contributions</h3>
              <p className="text-3xl font-bold mt-2">
                ₦{savingsSummary?.totalContributions?.toLocaleString() || '0'}
              </p>
              <p className="text-sm text-muted-foreground mt-1">Helped others save</p>
              <Button className="w-full mt-4" variant="outline">
                Contribute More
                <ArrowRight size={16} className="ml-2" />
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <Tabs defaultValue="personal" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="personal">Personal Plans</TabsTrigger>
            <TabsTrigger value="group">Group Savings</TabsTrigger>
            <TabsTrigger value="contribute">Contribute</TabsTrigger>
          </TabsList>
          
          <TabsContent value="personal">
            {!showCreatePersonalPlan ? (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Your Personal Savings Plans</h3>
                  <Button onClick={() => setShowCreatePersonalPlan(true)}>
                    <Plus size={16} className="mr-2" />
                    Create Plan
                  </Button>
                </div>

                {loading ? (
                  <div className="text-center py-8">Loading your savings plans...</div>
                ) : personalPlans.length === 0 ? (
                  <div className="text-center py-8">
                    <PiggyBank size={48} className="mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500">No personal savings plans yet</p>
                    <Button onClick={() => setShowCreatePersonalPlan(true)} className="mt-4">
                      Create Your First Plan
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {personalPlans.map((plan) => (
                      <Card key={plan.id} className="bg-white/95 shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
                        <CardContent className="p-6">
                          <div className="flex items-center justify-between mb-4">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
                                <Target size={20} className="text-purple-600" />
                              </div>
                              <h4 className="font-semibold">{plan.title}</h4>
                            </div>
                            <div className="flex items-center gap-2">
                              {plan.isPublic && (
                                <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Public</div>
                              )}
                              <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">{plan.status}</div>
                            </div>
                          </div>

                          {plan.description && (
                            <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                          )}

                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-500">Target:</span>
                              <span className="font-semibold">₦{plan.targetAmount.toLocaleString()}</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm text-gray-500">Current:</span>
                              <span className="font-semibold text-green-600">₦{plan.currentAmount.toLocaleString()}</span>
                            </div>

                            <div className="space-y-1">
                              <div className="flex justify-between items-center text-xs">
                                <span>{Math.round((plan.currentAmount / plan.targetAmount) * 100)}%</span>
                                <span>₦{(plan.targetAmount - plan.currentAmount).toLocaleString()} to go</span>
                              </div>
                              <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-purple-500 rounded-full"
                                  style={{ width: `${Math.min((plan.currentAmount / plan.targetAmount) * 100, 100)}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>

                          <div className="mt-4 flex gap-2">
                            <Button variant="outline" size="sm" className="flex-1">Add Funds</Button>
                            <Button variant="outline" size="sm" className="flex-1">
                              <Share2 size={14} className="mr-1" />
                              Share
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              <Card className="max-w-md mx-auto">
                <CardHeader>
                  <CardTitle>Create Personal Savings Plan</CardTitle>
                  <CardDescription>Create a savings plan that others can contribute to</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleCreatePersonalPlan} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="planTitle">Plan Title</Label>
                      <Input
                        id="planTitle"
                        placeholder="e.g., Dream Vacation, New Car, etc."
                        value={personalPlanForm.title}
                        onChange={(e) => setPersonalPlanForm({...personalPlanForm, title: e.target.value})}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="planAmount">Target Amount (₦)</Label>
                      <Input
                        id="planAmount"
                        type="number"
                        placeholder="Enter target amount"
                        value={personalPlanForm.targetAmount}
                        onChange={(e) => setPersonalPlanForm({...personalPlanForm, targetAmount: e.target.value})}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="planDate">Target Date (Optional)</Label>
                      <Input
                        id="planDate"
                        type="date"
                        value={personalPlanForm.targetDate}
                        onChange={(e) => setPersonalPlanForm({...personalPlanForm, targetDate: e.target.value})}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="planDescription">Description (Optional)</Label>
                      <Textarea
                        id="planDescription"
                        placeholder="Brief description of your savings goal"
                        value={personalPlanForm.description}
                        onChange={(e) => setPersonalPlanForm({...personalPlanForm, description: e.target.value})}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="isPublic"
                        checked={personalPlanForm.isPublic}
                        onCheckedChange={(checked) => setPersonalPlanForm({...personalPlanForm, isPublic: checked})}
                      />
                      <Label htmlFor="isPublic">Make plan public (others can see and contribute)</Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="allowContributions"
                        checked={personalPlanForm.allowContributions}
                        onCheckedChange={(checked) => setPersonalPlanForm({...personalPlanForm, allowContributions: checked})}
                      />
                      <Label htmlFor="allowContributions">Allow others to contribute</Label>
                    </div>
                  </form>
                </CardContent>
                <CardFooter className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreatePersonalPlan(false)}>Cancel</Button>
                  <Button onClick={handleCreatePersonalPlan}>Create Plan</Button>
                </CardFooter>
              </Card>
            )}
          </TabsContent>
          
          <TabsContent value="group">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Group Savings</h3>
                <Button onClick={() => toast({
                  title: "Coming Soon",
                  description: "Group savings creation will be available soon"
                })}>
                  <Plus size={16} className="mr-2" />
                  Create Group
                </Button>
              </div>

              {loading ? (
                <div className="text-center py-8">Loading group savings...</div>
              ) : groupMemberships.length === 0 ? (
                <div className="text-center py-8">
                  <Users size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">No group savings yet</p>
                  <Button onClick={() => toast({
                    title: "Coming Soon",
                    description: "Group joining will be available soon"
                  })} className="mt-4">
                    Join a Group
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {groupMemberships.map((membership) => (
                    <Card key={membership.id} className="bg-white/95 shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <Users size={20} className="text-blue-600" />
                            </div>
                            <div>
                              <h4 className="font-semibold">{membership.group?.name}</h4>
                              <p className="text-xs text-gray-500">Individual target</p>
                            </div>
                          </div>
                          <div className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {membership.isActive ? 'Active' : 'Inactive'}
                          </div>
                        </div>

                        {membership.group?.description && (
                          <p className="text-sm text-gray-600 mb-3">{membership.group.description}</p>
                        )}

                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">My Target:</span>
                            <span className="font-semibold">₦{membership.individualTarget.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">My Savings:</span>
                            <span className="font-semibold text-green-600">₦{membership.currentAmount.toLocaleString()}</span>
                          </div>

                          <div className="space-y-1">
                            <div className="flex justify-between items-center text-xs">
                              <span>{Math.round((membership.currentAmount / membership.individualTarget) * 100)}%</span>
                              <span>₦{(membership.individualTarget - membership.currentAmount).toLocaleString()} to go</span>
                            </div>
                            <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-blue-500 rounded-full"
                                style={{ width: `${Math.min((membership.currentAmount / membership.individualTarget) * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        <div className="mt-4 flex gap-2">
                          <Button variant="outline" size="sm" className="flex-1">Add Funds</Button>
                          <Button variant="outline" size="sm" className="flex-1">View Group</Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="contribute">
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Contribute to Others' Plans</h3>
              </div>

              {loading ? (
                <div className="text-center py-8">Loading public savings plans...</div>
              ) : publicPlans.length === 0 ? (
                <div className="text-center py-8">
                  <Heart size={48} className="mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">No public savings plans available</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {publicPlans.map((plan) => (
                    <Card key={plan.id} className="bg-white/95 shadow-sm border border-slate-200/60 hover:shadow-md transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center">
                              <Heart size={20} className="text-pink-600" />
                            </div>
                            <div>
                              <h4 className="font-semibold">{plan.title}</h4>
                              <p className="text-xs text-gray-500">by {plan.creator?.fullName}</p>
                            </div>
                          </div>
                          <div className="bg-pink-100 text-pink-800 text-xs px-2 py-1 rounded-full">{plan.status}</div>
                        </div>

                        {plan.description && (
                          <p className="text-sm text-gray-600 mb-3">{plan.description}</p>
                        )}

                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Target:</span>
                            <span className="font-semibold">₦{plan.targetAmount.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-500">Raised:</span>
                            <span className="font-semibold text-green-600">₦{plan.currentAmount.toLocaleString()}</span>
                          </div>

                          <div className="space-y-1">
                            <div className="flex justify-between items-center text-xs">
                              <span>{Math.round((plan.currentAmount / plan.targetAmount) * 100)}%</span>
                              <span>₦{(plan.targetAmount - plan.currentAmount).toLocaleString()} needed</span>
                            </div>
                            <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                              <div
                                className="h-full bg-pink-500 rounded-full"
                                style={{ width: `${Math.min((plan.currentAmount / plan.targetAmount) * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>

                        <div className="mt-4">
                          <Button
                            className="w-full"
                            size="sm"
                            onClick={() => {
                              setSelectedPlan(plan);
                              setShowContribute(true);
                            }}
                          >
                            <Heart size={14} className="mr-1" />
                            Contribute
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Savings;
