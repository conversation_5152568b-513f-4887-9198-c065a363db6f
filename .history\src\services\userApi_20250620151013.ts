import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL;

export const fetchUserInfo = async (token: string) => {
  console.log('fetchUserInfo: using token', token);
  const url = `${API_BASE_URL}/api/v1/user`;
  console.log('fetchUserInfo: url', url);
  try {
    const response = await axios.get(url, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('fetchUserInfo: response', response);
    // Accept both 'success' and 'status' as true for compatibility
    const isSuccess = response.data.success === true || response.data.status === true;
    if (!isSuccess) {
      console.error('fetchUserInfo: not success', response.data);
      throw new Error(response.data.message || 'Failed to fetch user info');
    }
    let data = response.data.data;
    if (data && data.data) data = data.data;
    console.log('fetchUserInfo: final data', data);
    return data;
  } catch (err) {
    console.error('fetchUserInfo: error', err);
    throw err;
  }
};
