// E-commerce service for handling products and orders
import { Escrow } from '@/types/escrow';

// Custom error class for ecommerce operations
export class EcommerceError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'EcommerceError';
  }
}

// Re-export Product and Order types to avoid conflicts
export interface Product {
  id: string | number;
  name: string;
  description: string;
  price: number;
  category: string;
  image: string;
  storeName: string;
  status: string;
  inventory?: number;
  stock?: number;
  rating?: number;
}

export interface Order {
  id: string;
  customerId: string;
  customerName: string;
  products: OrderItem[];
  totalAmount: number;
  status: string;
  paymentStatus: string;
  shippingAddress: string;
  createdAt: string;
  updatedAt: string;
  escrowId?: string;
}

export interface OrderItem {
  productId: string;
  productName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

export interface EcommerceProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  imageUrl: string;
  stock: number;
  sellerId: string;
}

export interface EcommerceOrder {
  id: string;
  buyerId: string;
  sellerId: string;
  products: EcommerceOrderItem[];
  totalAmount: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: string;
  shippingAddress: string;
}

export interface EcommerceOrderItem {
  productId: string;
  quantity: number;
  price: number;
}

export interface EcommerceEscrow {
  id: string;
  orderId: string;
  amount: number;
  status: 'held' | 'released' | 'refunded';
  createdAt: string;
}

export interface CreateOrderParams {
  customerId: string;
  customerName: string;
  products: { productId: string; quantity: number }[];
  shippingAddress: string;
}

export class EcommerceService {
  private mockProducts: EcommerceProduct[] = [
    {
      id: '1',
      name: 'Sample Product',
      description: 'A sample product for testing',
      price: 10000,
      category: 'Electronics',
      imageUrl: 'https://via.placeholder.com/300',
      stock: 50,
      sellerId: 'seller1'
    }
  ];

  private mockOrders: EcommerceOrder[] = [];
  private mockEscrows: EcommerceEscrow[] = [];

  // Convert EcommerceProduct to Product format
  private convertToProduct(eProduct: EcommerceProduct): Product {
    return {
      id: eProduct.id,
      name: eProduct.name,
      description: eProduct.description,
      price: eProduct.price,
      category: eProduct.category,
      image: eProduct.imageUrl,
      storeName: `Store ${eProduct.sellerId}`,
      status: eProduct.stock > 0 ? 'In Stock' : 'Out of Stock',
      inventory: eProduct.stock,
      stock: eProduct.stock,
      rating: 4.5
    };
  }

  // Convert EcommerceOrder to Order format
  private convertToOrder(eOrder: EcommerceOrder): Order {
    return {
      id: eOrder.id,
      customerId: eOrder.buyerId,
      customerName: `Customer ${eOrder.buyerId}`,
      products: eOrder.products.map(item => ({
        productId: item.productId,
        productName: `Product ${item.productId}`,
        quantity: item.quantity,
        price: item.price,
        totalPrice: item.price * item.quantity
      })),
      totalAmount: eOrder.totalAmount,
      status: eOrder.status,
      paymentStatus: 'Completed',
      shippingAddress: eOrder.shippingAddress,
      createdAt: eOrder.createdAt,
      updatedAt: eOrder.createdAt,
      escrowId: `esc_${eOrder.id}`
    };
  }

  async getProducts(): Promise<Product[]> {
    return this.mockProducts.map(p => this.convertToProduct(p));
  }

  async getProductById(productId: string): Promise<Product | { message: string }> {
    const product = this.mockProducts.find(p => p.id === productId);
    if (!product) {
      return { message: 'Product not found' };
    }
    return this.convertToProduct(product);
  }

  async createProduct(product: Omit<EcommerceProduct, 'id'>): Promise<EcommerceProduct> {
    const newProduct: EcommerceProduct = {
      id: Math.random().toString(36).substr(2, 9),
      ...product
    };
    this.mockProducts.push(newProduct);
    return newProduct;
  }

  async createOrder(params: CreateOrderParams): Promise<{ order: Order; escrow: Escrow }> {
    const orderData: Omit<EcommerceOrder, 'id' | 'createdAt'> = {
      buyerId: params.customerId,
      sellerId: 'seller1',
      products: params.products.map(p => ({
        productId: p.productId,
        quantity: p.quantity,
        price: 10000 // Mock price
      })),
      totalAmount: params.products.length * 10000,
      status: 'pending',
      shippingAddress: params.shippingAddress
    };

    const newOrder: EcommerceOrder = {
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      ...orderData
    };
    
    this.mockOrders.push(newOrder);
    
    const convertedOrder = this.convertToOrder(newOrder);
    
    const escrow: Escrow = {
      id: `esc_${newOrder.id}`,
      orderId: newOrder.id, // Add the missing orderId property
      title: `Order ${newOrder.id}`,
      description: 'Escrow for order payment',
      amount: newOrder.totalAmount,
      currency: 'NGN',
      buyerId: newOrder.buyerId,
      sellerId: newOrder.sellerId,
      buyer: { name: params.customerName, email: '<EMAIL>' },
      seller: { name: 'Seller', email: '<EMAIL>' },
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    return { order: convertedOrder, escrow };
  }

  async getCustomerOrders(customerId: string): Promise<Order[]> {
    const customerOrders = this.mockOrders.filter(o => o.buyerId === customerId);
    return customerOrders.map(o => this.convertToOrder(o));
  }

  async completeOrder(orderId: string): Promise<{ order: Order; escrowStatus: string }> {
    const orderIndex = this.mockOrders.findIndex(o => o.id === orderId);
    if (orderIndex === -1) {
      throw new Error('Order not found');
    }
    
    this.mockOrders[orderIndex].status = 'delivered';
    const updatedOrder = this.convertToOrder(this.mockOrders[orderIndex]);
    
    return { order: updatedOrder, escrowStatus: 'completed' };
  }

  async updatePaymentStatus(orderId: string, status: string): Promise<void> {
    const orderIndex = this.mockOrders.findIndex(o => o.id === orderId);
    if (orderIndex !== -1) {
      // Update payment status logic here
      console.log(`Payment status updated for order ${orderId}: ${status}`);
    }
  }

  async createEscrow(escrowData: Omit<EcommerceEscrow, 'id' | 'createdAt'>): Promise<EcommerceEscrow> {
    const newEscrow: EcommerceEscrow = {
      id: Math.random().toString(36).substr(2, 9),
      createdAt: new Date().toISOString(),
      ...escrowData
    };
    this.mockEscrows.push(newEscrow);
    return newEscrow;
  }
}

export const ecommerceService = new EcommerceService();
