
/**
 * Fraud Detection Service
 * Provides utilities for detecting and preventing fraudulent transactions
 */

export interface FraudDetectionResult {
  isFraudulent: boolean;
  riskScore: number;
  riskFactors: string[];
  action: 'allow' | 'review' | 'block';
  timestamp: string;
}

export interface TransactionContext {
  amount: number;
  recipientId: string;
  recipientName: string;
  isNewRecipient?: boolean;
  isInternational?: boolean;
  deviceFingerprint?: string;
  ipAddress?: string;
  timestamp?: string;
  description?: string;
  userLocation?: {
    latitude: number;
    longitude: number;
    accuracy: number;
  };
}

export class FraudDetectionService {
  private static instance: FraudDetectionService | null = null;
  
  /**
   * Gets the singleton instance
   */
  public static getInstance(): FraudDetectionService {
    if (!FraudDetectionService.instance) {
      FraudDetectionService.instance = new FraudDetectionService();
    }
    return FraudDetectionService.instance;
  }
  
  /**
   * Checks a transaction for potential fraud
   */
  async detectFraudulentTransaction(transaction: TransactionContext): Promise<FraudDetectionResult> {
    // This would be a real API call in production
    console.log('Analyzing transaction for fraud:', transaction);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // For demo purposes, calculate a risk score
    let riskScore = 0;
    const riskFactors: string[] = [];
    
    // Check amount thresholds
    if (transaction.amount > 1000000) {
      riskScore += 30;
      riskFactors.push('high_value_transaction');
    } else if (transaction.amount > 500000) {
      riskScore += 15;
      riskFactors.push('medium_value_transaction');
    }
    
    // Check new recipient
    if (transaction.isNewRecipient) {
      riskScore += 20;
      riskFactors.push('new_recipient');
    }
    
    // Check international transaction
    if (transaction.isInternational) {
      riskScore += 25;
      riskFactors.push('international_transfer');
    }
    
    // Determine action based on risk score
    let action: 'allow' | 'review' | 'block';
    if (riskScore >= 50) {
      action = 'block';
    } else if (riskScore >= 30) {
      action = 'review';
    } else {
      action = 'allow';
    }
    
    return {
      isFraudulent: riskScore >= 50,
      riskScore,
      riskFactors,
      action,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Reports a transaction as fraudulent
   */
  async reportFraud(transactionId: string, reason: string): Promise<void> {
    // This would be a real API call in production
    console.log(`Reporting transaction ${transactionId} as fraud. Reason: ${reason}`);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  /**
   * Verifies a user's identity with additional checks
   */
  async verifyIdentity(userId: string, verificationMethod: string): Promise<boolean> {
    // This would be a real API call in production
    console.log(`Verifying identity for user ${userId} using ${verificationMethod}`);
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // For demo purposes, assume verification passes
    return true;
  }
}

export default FraudDetectionService;
