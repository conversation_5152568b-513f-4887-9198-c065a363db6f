
import React from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Toast } from '@/hooks/use-toast';

interface ToastProps {
  toast: Toast;
  onDismiss: (id: string) => void;
}

const ToastComponent: React.FC<ToastProps> = ({ toast, onDismiss }) => {
  return (
    <div
      className={cn(
        'fixed top-4 right-4 z-50 w-full max-w-sm p-4 mb-4 rounded-lg shadow-lg',
        'bg-white border border-gray-200',
        toast.variant === 'destructive' && 'bg-red-50 border-red-200',
        toast.variant === 'success' && 'bg-green-50 border-green-200'
      )}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          {toast.title && (
            <h4 className={cn(
              'font-medium text-sm',
              toast.variant === 'destructive' && 'text-red-800',
              toast.variant === 'success' && 'text-green-800'
            )}>
              {toast.title}
            </h4>
          )}
          {toast.description && (
            <p className={cn(
              'text-sm mt-1',
              toast.variant === 'destructive' && 'text-red-600',
              toast.variant === 'success' && 'text-green-600',
              !toast.variant && 'text-gray-600'
            )}>
              {toast.description}
            </p>
          )}
          {toast.action && (
            <div className="mt-2">
              {toast.action}
            </div>
          )}
        </div>
        <button
          onClick={() => onDismiss(toast.id)}
          className="ml-2 text-gray-400 hover:text-gray-600"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export default ToastComponent;
