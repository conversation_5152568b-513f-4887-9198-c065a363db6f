
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, Link, Copy, ExternalLink, MoreHorizontal, Eye } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import TransactionStatus from '@/components/TransactionStatus';

const PaymentLinks = () => {
  const { toast } = useToast();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formStatus, setFormStatus] = useState<'idle' | 'success' | 'failed'>('idle');
  
  const handleCopyLink = (link: string) => {
    navigator.clipboard.writeText(link).then(() => {
      toast({
        title: "Link copied",
        description: "Payment link copied to clipboard"
      });
    });
  };
  
  const handleCreateLink = (e: React.FormEvent) => {
    e.preventDefault();
    setFormStatus('success');
    setTimeout(() => {
      setShowCreateForm(false);
      setFormStatus('idle');
    }, 2000);
  };
  
  const paymentLinks = [
    { id: 1, name: 'Website Payment', amount: '₦15,000', fixedAmount: true, link: 'https://kojapay.com/pay/website15k', visits: 145, conversions: 89 },
    { id: 2, name: 'Consultation Fee', amount: 'Variable', fixedAmount: false, link: 'https://kojapay.com/pay/consultation', visits: 78, conversions: 32 },
    { id: 3, name: 'Product A', amount: '₦8,500', fixedAmount: true, link: 'https://kojapay.com/pay/productA', visits: 210, conversions: 124 },
    { id: 4, name: 'Service Package', amount: 'Variable', fixedAmount: false, link: 'https://kojapay.com/pay/servicepackage', visits: 95, conversions: 41 },
  ];
  
  return (
    <BusinessLayout pageTitle="Payment Links">
      <div className="space-y-6">
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Payment Links</CardTitle>
              <CardDescription>Create sharable payment links for your customers</CardDescription>
            </div>
            <Button 
              className="mt-4 sm:mt-0" 
              onClick={() => setShowCreateForm(true)}
              disabled={showCreateForm}
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Create Link
            </Button>
          </CardHeader>
          <CardContent>
            {formStatus === 'success' && (
              <div className="mb-6">
                <TransactionStatus 
                  status="success" 
                  message="Payment link created successfully"
                />
              </div>
            )}
            
            {formStatus === 'failed' && (
              <div className="mb-6">
                <TransactionStatus 
                  status="failed" 
                  message="Failed to create payment link. Please try again."
                />
              </div>
            )}
            
            {showCreateForm ? (
              <form onSubmit={handleCreateLink} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="linkName">Link Name</Label>
                      <Input id="linkName" placeholder="E.g., Product Payment" className="mt-1" />
                    </div>
                    
                    <div>
                      <Label htmlFor="description">Description (Optional)</Label>
                      <Input id="description" placeholder="What is this payment for?" className="mt-1" />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch id="fixedAmount" />
                      <Label htmlFor="fixedAmount">Fixed Amount</Label>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="amount">Amount (₦)</Label>
                      <Input id="amount" type="number" placeholder="0.00" className="mt-1" />
                    </div>
                    
                    <div>
                      <Label htmlFor="redirectUrl">Redirect URL (Optional)</Label>
                      <Input id="redirectUrl" placeholder="https://yourbusiness.com/thank-you" className="mt-1" />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch id="collectCustomerInfo" defaultChecked />
                      <Label htmlFor="collectCustomerInfo">Collect Customer Information</Label>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 mt-6">
                  <Button variant="outline" type="button" onClick={() => setShowCreateForm(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    Create Payment Link
                  </Button>
                </div>
              </form>
            ) : (
              <Tabs defaultValue="active">
                <TabsList className="w-full grid grid-cols-2 mb-6">
                  <TabsTrigger value="active">Active Links</TabsTrigger>
                  <TabsTrigger value="analytics">Analytics</TabsTrigger>
                </TabsList>
                
                <TabsContent value="active" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Name</th>
                          <th className="pb-3 font-medium">Amount</th>
                          <th className="pb-3 font-medium">Link</th>
                          <th className="pb-3 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {paymentLinks.map(link => (
                          <tr key={link.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{link.name}</td>
                            <td className="py-4">{link.amount}</td>
                            <td className="py-4 max-w-xs truncate">
                              <div className="flex items-center">
                                <span className="text-kojaGray truncate">{link.link}</span>
                              </div>
                            </td>
                            <td className="py-4">
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={() => handleCopyLink(link.link)}>
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <ExternalLink className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
                
                <TabsContent value="analytics" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardContent className="p-6">
                        <div className="text-4xl font-bold mb-2">528</div>
                        <p className="text-kojaGray">Total Link Visits</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-6">
                        <div className="text-4xl font-bold mb-2">286</div>
                        <p className="text-kojaGray">Successful Payments</p>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-6">
                        <div className="text-4xl font-bold mb-2">54.2%</div>
                        <p className="text-kojaGray">Conversion Rate</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Link Name</th>
                          <th className="pb-3 font-medium">Visits</th>
                          <th className="pb-3 font-medium">Conversions</th>
                          <th className="pb-3 font-medium">Rate</th>
                          <th className="pb-3 font-medium">Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {paymentLinks.map(link => (
                          <tr key={link.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{link.name}</td>
                            <td className="py-4">{link.visits}</td>
                            <td className="py-4">{link.conversions}</td>
                            <td className="py-4">{((link.conversions / link.visits) * 100).toFixed(1)}%</td>
                            <td className="py-4">
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <Eye className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Embed Payment Button</CardTitle>
            <CardDescription>Use this code to add a payment button to your website</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-100 rounded-md p-4 overflow-x-auto">
              <code className="text-sm">
                &lt;script src="https://kojapay.com/js/payment-button.js" data-business-id="YOUR_ID"&gt;&lt;/script&gt;<br />
                &lt;button class="kojapay-button" data-amount="5000" data-name="Product Payment"&gt;Pay with KojaPay&lt;/button&gt;
              </code>
            </div>
            <Button className="mt-4" variant="outline" onClick={() => handleCopyLink('<script src="https://kojapay.com/js/payment-button.js" data-business-id="YOUR_ID"></script>\n<button class="kojapay-button" data-amount="5000" data-name="Product Payment">Pay with KojaPay</button>')}>
              <Copy className="mr-2 h-4 w-4" /> Copy Code
            </Button>
          </CardContent>
        </Card>
      </div>
    </BusinessLayout>
  );
};

export default PaymentLinks;
