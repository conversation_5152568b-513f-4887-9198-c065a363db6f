
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  CreditCard, 
  Plus, 
  Lock, 
  ChevronRight,
  ChevronLeft,
  Settings,
  DollarSign,
  ShieldCheck
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import EnhancedVirtualCard from '@/components/EnhancedVirtualCard';
import { Slider } from '@/components/ui/slider';
import { useIsMobile } from '@/hooks/use-mobile';

const BusinessCards = () => {
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [activeCardIndex, setActiveCardIndex] = useState(0);
  
  // Sample business cards
  const businessCards = [
    {
      cardNumber: "**** **** **** 4567",
      name: "KOJA ENTERPRISES",
      expiryDate: "05/26",
      cardType: "business",
      variant: "physical",
    },
    {
      cardNumber: "**** **** **** 7890",
      name: "KOJA ENTERPRISES",
      expiryDate: "09/25",
      cardType: "business",
      variant: "virtual",
    }
  ];

  const handleCardSliderChange = (values: number[]) => {
    setActiveCardIndex(values[0]);
  };

  const handlePrevCard = () => {
    setActiveCardIndex(prev => (prev === 0 ? businessCards.length - 1 : prev - 1));
  };

  const handleNextCard = () => {
    setActiveCardIndex(prev => (prev === businessCards.length - 1 ? 0 : prev + 1));
  };
  
  return (
    <BusinessLayout pageTitle="Business Cards">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Your Cards</h2>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add New Card
            </Button>
          </div>
          
          {/* Cards Display - Adaptive for Mobile and Desktop */}
          <div className="space-y-6">
            {isMobile ? (
              <div className="space-y-4">
                <div className="relative overflow-hidden">
                  <div className="flex items-center justify-between mb-2">
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 rounded-full bg-white/80 shadow-sm z-10"
                      onClick={handlePrevCard}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <div className="text-xs font-medium text-kojaGray">
                      {activeCardIndex + 1}/{businessCards.length}
                    </div>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-8 w-8 rounded-full bg-white/80 shadow-sm z-10"
                      onClick={handleNextCard}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-2 bg-gray-50/50 rounded-xl">
                    <EnhancedVirtualCard
                      cardNumber={businessCards[activeCardIndex].cardNumber}
                      name={businessCards[activeCardIndex].name}
                      expiryDate={businessCards[activeCardIndex].expiryDate}
                      cardType={businessCards[activeCardIndex].cardType as 'personal' | 'business'}
                      variant={businessCards[activeCardIndex].variant as 'physical' | 'virtual'}
                    />
                  </div>
                </div>
                
                <div className="py-2">
                  <Slider
                    defaultValue={[activeCardIndex]}
                    value={[activeCardIndex]}
                    max={businessCards.length - 1}
                    step={1}
                    onValueChange={handleCardSliderChange}
                    className="w-full"
                  />
                  <div className="flex justify-between mt-2 text-xs text-kojaGray">
                    <span>Physical Card</span>
                    <span>Virtual Card</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {businessCards.map((card, index) => (
                  <div key={index} className="bg-gray-50/50 p-2 rounded-xl">
                    <EnhancedVirtualCard
                      cardNumber={card.cardNumber}
                      name={card.name}
                      expiryDate={card.expiryDate}
                      cardType={card.cardType as 'personal' | 'business'}
                      variant={card.variant as 'physical' | 'virtual'}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
          
          {/* Card Transactions */}
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>Card Transactions</CardTitle>
              <CardDescription>Recent transactions from your business cards</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 overflow-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="text-left text-kojaGray border-b">
                      <th className="pb-2 font-medium">Merchant</th>
                      <th className="pb-2 font-medium hidden sm:table-cell">Date</th>
                      <th className="pb-2 font-medium hidden md:table-cell">Card</th>
                      <th className="pb-2 font-medium text-right">Amount</th>
                      <th className="pb-2 font-medium text-right">Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {[
                      { merchant: 'Office Supplies Inc', date: 'May 12, 2023', amount: '₦35,400', card: '****4567', status: 'Completed' },
                      { merchant: 'Business Conference', date: 'May 10, 2023', amount: '₦120,000', card: '****7890', status: 'Completed' },
                      { merchant: 'Digital Marketing', date: 'May 08, 2023', amount: '₦75,000', card: '****4567', status: 'Pending' },
                      { merchant: 'Office Rent', date: 'May 01, 2023', amount: '₦250,000', card: '****7890', status: 'Completed' },
                    ].map((transaction, index) => (
                      <tr key={index} className="border-b last:border-b-0 hover:bg-gray-50/50">
                        <td className="py-3">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                              <CreditCard className="h-4 w-4 text-gray-500" />
                            </div>
                            <div>
                              <div className="font-medium text-sm">{transaction.merchant}</div>
                              <div className="text-xs text-gray-500 sm:hidden">{transaction.date}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 hidden sm:table-cell">{transaction.date}</td>
                        <td className="py-3 hidden md:table-cell">{transaction.card}</td>
                        <td className="py-3 text-right font-medium">{transaction.amount}</td>
                        <td className="py-3 text-right">
                          <span className={`inline-flex px-2 py-1 rounded-full text-xs ${
                            transaction.status === 'Completed' 
                              ? 'bg-green-100 text-green-700' 
                              : 'bg-yellow-100 text-yellow-700'
                          }`}>
                            {transaction.status}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <Button variant="outline" className="w-full mt-4 text-sm">
                View All Transactions
                <ChevronRight className="ml-2 h-3 w-3" />
              </Button>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>Card Controls</CardTitle>
              <CardDescription>Manage your business card settings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Lock className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="font-medium text-sm">Freeze Card</div>
                      <div className="text-xs text-gray-500">Temporarily lock your card</div>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigate('/business/card-services')}
                  >
                    Freeze
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="font-medium text-sm">Spending Limits</div>
                      <div className="text-xs text-gray-500">Set transaction limits</div>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigate('/business/card-services')}
                  >
                    Adjust
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <ShieldCheck className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="font-medium text-sm">Security Settings</div>
                      <div className="text-xs text-gray-500">Change PIN & security options</div>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigate('/business/card-services')}
                  >
                    Manage
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Settings className="h-4 w-4 text-gray-500" />
                    <div>
                      <div className="font-medium text-sm">Card Preferences</div>
                      <div className="text-xs text-gray-500">Update card settings</div>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => navigate('/business/card-services')}
                  >
                    Edit
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>Card Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 text-sm">Active Cards</span>
                  <span className="font-semibold text-sm">2</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 text-sm">Total Card Limit</span>
                  <span className="font-semibold text-sm">₦500,000</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 text-sm">Available Credit</span>
                  <span className="font-semibold text-sm text-green-600">₦325,000</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 text-sm">Current Utilization</span>
                  <span className="font-semibold text-sm">35%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 text-sm">Next Statement Date</span>
                  <span className="font-semibold text-sm">May 31, 2023</span>
                </div>
              </div>
              
              <div className="h-1 w-full bg-gray-100 mt-4 rounded-full overflow-hidden">
                <div className="h-full w-[35%] bg-banklyBlue rounded-full"></div>
              </div>
              <div className="text-xs text-gray-500 mt-1">Credit utilization: 35%</div>
              
              <Button className="w-full mt-6 text-sm">
                <Plus className="mr-2 h-3 w-3" />
                Apply for New Card
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </BusinessLayout>
  );
};

export default BusinessCards;
