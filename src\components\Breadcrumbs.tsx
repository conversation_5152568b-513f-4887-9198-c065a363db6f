
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';

interface BreadcrumbsProps {
  customPaths?: { name: string; path: string }[];
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ customPaths }) => {
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter(x => x);

  const routeMap: Record<string, string> = {
    'dashboard': 'Dashboard',
    'transactions': 'Transactions',
    'analysis': 'Analysis',
    'profile': 'Profile',
    'settings': 'Settings',
    'cards': 'Cards',
    'card-services': 'Card Services',
    'bill-payment': 'Bill Payment',
    'kyc': 'KYC',
    'savings': 'Savings',
    'loans': 'Loans',
    'ecommerce': 'Ecommerce',
    'insights': 'Insights',
    'notifications': 'Notifications',
    'escrow': 'Escrow',
    'security': 'Security',
    'legal': 'Legal',
    'privacy-policy': 'Privacy Policy',
    'terms-of-service': 'Terms of Service',
    'business-dashboard': 'Business Dashboard',
    'business': 'Business',
    'api-docs': 'API Docs',
    'safety-tips': 'Safety Tips',
    'reward-points': 'Reward Points',
    'personal-login': 'Personal Login',
    'business-login': 'Business Login',
    'create-account': 'Create Account',
  };

  const breadcrumbs = customPaths || pathnames.map((path, index) => {
    const routeTo = `/${pathnames.slice(0, index + 1).join('/')}`;
    return {
      name: routeMap[path] || path.charAt(0).toUpperCase() + path.slice(1),
      path: routeTo
    };
  });

  return (
    <div className="flex items-center text-sm mb-4 text-gray-500">
      <Link to="/" className="hover:text-kojaPrimary">
        Home
      </Link>
      
      {breadcrumbs.map((breadcrumb, index) => (
        <React.Fragment key={breadcrumb.path}>
          <ChevronRight className="mx-2 h-4 w-4" />
          {index === breadcrumbs.length - 1 ? (
            <span className="text-kojaPrimary font-medium">{breadcrumb.name}</span>
          ) : (
            <Link to={breadcrumb.path} className="hover:text-kojaPrimary">
              {breadcrumb.name}
            </Link>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};

export default Breadcrumbs;
