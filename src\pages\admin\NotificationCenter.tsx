
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash,
  Bell,
  Users,
  Send,
  Calendar,
  CheckCircle,
  Clock,
  Copy,
  XCircle,
  BellRing,
  UserCircle,
  Building,
  BellPlus
} from 'lucide-react';

const NotificationCenter = () => {
  const notifications = [
    { 
      id: 'NOT001', 
      title: 'System Maintenance Notification', 
      type: 'System',
      audience: 'All Users',
      sentDate: '2023-06-15 08:00',
      status: 'Sent',
      deliveryRate: '98%',
      openRate: '72%'
    },
    { 
      id: 'NOT002', 
      title: 'New Feature Announcement', 
      type: 'Marketing',
      audience: 'All Users',
      sentDate: '2023-06-14 10:30',
      status: 'Sent',
      deliveryRate: '99%',
      openRate: '68%'
    },
    { 
      id: 'NOT003', 
      title: 'Important Security Update', 
      type: 'Security',
      audience: 'All Users',
      sentDate: '2023-06-12 14:15',
      status: 'Sent',
      deliveryRate: '100%',
      openRate: '85%'
    },
    { 
      id: 'NOT004', 
      title: 'Business Account Fee Update', 
      type: 'Billing',
      audience: 'Business Users',
      sentDate: '2023-06-10 09:45',
      status: 'Sent',
      deliveryRate: '97%',
      openRate: '76%'
    },
    { 
      id: 'NOT005', 
      title: 'Quarterly Newsletter', 
      type: 'Marketing',
      audience: 'Subscribed Users',
      sentDate: 'Scheduled - 2023-06-20 09:00',
      status: 'Scheduled',
      deliveryRate: 'N/A',
      openRate: 'N/A'
    },
  ];

  const templates = [
    {
      id: 'TEMP001',
      name: 'System Maintenance',
      type: 'System',
      lastModified: '2023-06-01',
      usageCount: 8
    },
    {
      id: 'TEMP002',
      name: 'Monthly Newsletter',
      type: 'Marketing',
      lastModified: '2023-05-15',
      usageCount: 5
    },
    {
      id: 'TEMP003',
      name: 'Security Alert',
      type: 'Security',
      lastModified: '2023-05-10',
      usageCount: 3
    },
    {
      id: 'TEMP004',
      name: 'Welcome Message',
      type: 'Onboarding',
      lastModified: '2023-04-28',
      usageCount: 120
    }
  ];

  return (
    <AdminLayout pageTitle="Notification Center">
      <Helmet>
        <title>Notification Center | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Notifications Sent</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">243</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Average Open Rate</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">76%</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Scheduled</CardTitle>
              <CardDescription>Pending notifications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">4</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Templates</CardTitle>
              <CardDescription>Available templates</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">16</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="history" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="history">Notification History</TabsTrigger>
            <TabsTrigger value="compose">Compose Notification</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>
          
          <TabsContent value="history">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Notification History</CardTitle>
                  <CardDescription>Track all sent and scheduled notifications</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a] flex items-center gap-2">
                    <BellPlus size={16} />
                    <span>New Notification</span>
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search notifications..." 
                      className="pl-9"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <BellRing size={12} />
                      <span>System</span>
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <CheckCircle size={12} />
                      <span>Sent</span>
                    </Badge>
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>Notification history</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Audience</TableHead>
                        <TableHead>Sent Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Delivery Rate</TableHead>
                        <TableHead>Open Rate</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {notifications.map((notification) => (
                        <TableRow key={notification.id}>
                          <TableCell className="font-medium">{notification.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {notification.type === 'System' && <Bell size={16} className="text-blue-600" />}
                              {notification.type === 'Marketing' && <Users size={16} className="text-green-600" />}
                              {notification.type === 'Security' && <Bell size={16} className="text-red-600" />}
                              {notification.type === 'Billing' && <Bell size={16} className="text-purple-600" />}
                              {notification.title}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {notification.type}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {notification.audience === 'All Users' && <Users size={14} />}
                              {notification.audience === 'Business Users' && <Building size={14} />}
                              {notification.audience === 'Subscribed Users' && <UserCircle size={14} />}
                              {notification.audience}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {notification.status === 'Scheduled' ? (
                                <Calendar size={14} className="text-amber-600" />
                              ) : (
                                <Send size={14} className="text-blue-600" />
                              )}
                              {notification.sentDate}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={notification.status === 'Sent' ? 'outline' : 'secondary'}>
                              {notification.status === 'Sent' && <CheckCircle size={12} className="mr-1 text-green-600" />}
                              {notification.status === 'Scheduled' && <Clock size={12} className="mr-1" />}
                              {notification.status}
                            </Badge>
                          </TableCell>
                          <TableCell>{notification.deliveryRate}</TableCell>
                          <TableCell>{notification.openRate}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Copy className="mr-2 h-4 w-4" />
                                  <span>Duplicate</span>
                                </DropdownMenuItem>
                                {notification.status === 'Scheduled' && (
                                  <DropdownMenuItem>
                                    <XCircle className="mr-2 h-4 w-4 text-red-600" />
                                    <span className="text-red-600">Cancel</span>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="compose">
            <Card>
              <CardHeader>
                <CardTitle>Compose New Notification</CardTitle>
                <CardDescription>Create and send a notification to your users</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Notification Type</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      <Button variant="outline" className="justify-start">
                        <Bell className="mr-2 h-4 w-4 text-blue-600" />
                        System
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Users className="mr-2 h-4 w-4 text-green-600" />
                        Marketing
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Bell className="mr-2 h-4 w-4 text-red-600" />
                        Security
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Bell className="mr-2 h-4 w-4 text-purple-600" />
                        Billing
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Target Audience</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                      <Button variant="outline" className="justify-start">
                        <Users className="mr-2 h-4 w-4" />
                        All Users
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Building className="mr-2 h-4 w-4" />
                        Business Users
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <UserCircle className="mr-2 h-4 w-4" />
                        Personal Users
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Notification Title</h3>
                    <Input placeholder="Enter notification title..." />
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Message</h3>
                    <Textarea placeholder="Type your notification message here..." rows={5} />
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Delivery Options</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <Button variant="outline" className="justify-start">
                        <Send className="mr-2 h-4 w-4 text-blue-600" />
                        Send Now
                      </Button>
                      <Button variant="outline" className="justify-start">
                        <Calendar className="mr-2 h-4 w-4 text-amber-600" />
                        Schedule
                      </Button>
                    </div>
                  </div>
                  
                  <div className="pt-4 flex justify-end gap-2">
                    <Button variant="outline">Preview</Button>
                    <Button className="bg-[#1231B8] hover:bg-[#09125a]">Send Notification</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="templates">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Notification Templates</CardTitle>
                  <CardDescription>Manage reusable notification templates</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Create New Template
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>List of notification templates</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Template Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Last Modified</TableHead>
                        <TableHead>Usage Count</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {templates.map((template) => (
                        <TableRow key={template.id}>
                          <TableCell className="font-medium">{template.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {template.type === 'System' && <Bell size={16} className="text-blue-600" />}
                              {template.type === 'Marketing' && <Users size={16} className="text-green-600" />}
                              {template.type === 'Security' && <Bell size={16} className="text-red-600" />}
                              {template.type === 'Onboarding' && <UserCircle size={16} className="text-purple-600" />}
                              {template.name}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {template.type}
                            </Badge>
                          </TableCell>
                          <TableCell>{template.lastModified}</TableCell>
                          <TableCell>{template.usageCount}</TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Edit</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Send className="mr-2 h-4 w-4 text-blue-600" />
                                  <span className="text-blue-600">Use Template</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Trash className="mr-2 h-4 w-4 text-red-600" />
                                  <span className="text-red-600">Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default NotificationCenter;
