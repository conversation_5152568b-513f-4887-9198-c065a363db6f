
import { ReactNode, useState } from "react";
import { Menu } from "lucide-react";
import { Helmet } from "react-helmet-async";
import PersonalSidebar from "./PersonalSidebar";
import { useIsMobile } from "@/hooks/use-mobile";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import NotificationBell from "./NotificationBell";
import { Separator } from "@/components/ui/separator";

interface DashboardLayoutProps {
  children: ReactNode;
  pageTitle?: string;
}

const DashboardLayout = ({
  children,
  pageTitle,
}: DashboardLayoutProps) => {
  const isMobile = useIsMobile();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <>
      {pageTitle && <Helmet>
          <title>{pageTitle} | KojaPay</title>
        </Helmet>}
      
      <div className="flex h-screen bg-gray-100">
        {/* Desktop sidebar */}
        {!isMobile && (
          <PersonalSidebar logoUrl="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" />
        )}

        {/* Mobile sidebar */}
        {isMobile && (
          <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
            <SheetTrigger asChild>
              <button onClick={() => setIsSidebarOpen(true)} className="lg:hidden p-2 rounded-md hover:bg-gray-100 absolute top-3 left-4 z-30">
                <Menu size={24} />
              </button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0 w-full max-w-[280px] bg-gradient-to-b from-[#09125a] to-[#1231B8]">
              <PersonalSidebar logoUrl="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" />
            </SheetContent>
          </Sheet>
        )}

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          {/* Header */}
          <header className="bg-white border-b h-16 flex items-center justify-between px-4">
            <div className="flex items-center">
              {isMobile ? (
                <h1 className="ml-10 text-lg font-semibold">{pageTitle || "KojaPay"}</h1>
              ) : (
                <h1 className="text-lg font-semibold">{pageTitle || "KojaPay"}</h1>
              )}
            </div>
            <div className="flex items-center gap-4">
              <NotificationBell />
            </div>
          </header>

          {/* Content */}
          <main className="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50">
            {children}
          </main>
        </div>
      </div>
    </>
  );
};

export default DashboardLayout;
