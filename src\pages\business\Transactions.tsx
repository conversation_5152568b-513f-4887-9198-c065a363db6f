
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  ArrowUpRight, 
  ArrowDownLeft, 
  FileText, 
  Download, 
  Filter, 
  Search,
  ChevronDown
} from 'lucide-react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import TransactionTable, { Transaction } from '@/components/TransactionTable';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import EnhancedTransactionReceipt from '@/components/EnhancedTransactionReceipt';

const BusinessTransactions = () => {
  const [receiptDialog, setReceiptDialog] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [filterType, setFilterType] = useState("all");
  
  const transactions: Transaction[] = [
    { 
      id: '1', 
      type: 'incoming', 
      recipient: 'Payment from Customer', 
      description: 'Invoice #INV-2023-001', 
      amount: 120000, 
      date: 'Jun 15, 2023',
      status: 'success',
      initials: 'PC'
    },
    { 
      id: '2', 
      type: 'outgoing', 
      recipient: 'Vendor Payment', 
      description: 'Office Supplies', 
      amount: 45000, 
      date: 'Jun 14, 2023',
      status: 'success',
      initials: 'VP'
    },
    { 
      id: '3', 
      type: 'incoming', 
      recipient: 'Store Purchase', 
      description: 'Online Store Order #12345', 
      amount: 78500, 
      date: 'Jun 12, 2023',
      status: 'success',
      initials: 'SP'
    },
    { 
      id: '4', 
      type: 'outgoing', 
      recipient: 'Staff Salary', 
      description: 'Monthly Payroll', 
      amount: 250000, 
      date: 'Jun 10, 2023',
      status: 'pending',
      initials: 'SS'
    },
    { 
      id: '5', 
      type: 'incoming', 
      recipient: 'Service Payment', 
      description: 'Consulting Fee', 
      amount: 350000, 
      date: 'Jun 5, 2023',
      status: 'success',
      initials: 'CF'
    },
    { 
      id: '6', 
      type: 'outgoing', 
      recipient: 'Rent Payment', 
      description: 'Office Space', 
      amount: 500000, 
      date: 'Jun 1, 2023',
      status: 'success',
      initials: 'RP'
    },
  ];

  const filteredTransactions = filterType === "all" 
    ? transactions 
    : transactions.filter(t => t.type === filterType);
    
  const handleViewReceipt = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setReceiptDialog(true);
  };

  const handleCloseReceipt = () => {
    setReceiptDialog(false);
    setSelectedTransaction(null);
  };

  const handlePrintReceipt = () => {
    if (!selectedTransaction) return;
    
    // Use the enhanced receipt generator
    import('@/utils/receipt-generator').then(module => {
      const receipt = module.generateTransactionReceipt(selectedTransaction);
      module.printPOSReceipt(receipt);
    });
  };
  
  const handleDownloadReceipt = () => {
    if (!selectedTransaction) return;
    
    import('@/utils/receipt-generator').then(module => {
      module.generateTransactionReceipt(selectedTransaction, true);
    });
  };

  return (
    <BusinessLayout pageTitle="Business Transactions">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Transactions</h2>
            <p className="text-muted-foreground">View and manage your business transactions</p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" className="rounded-[20px]">
              <FileText className="mr-2 h-4 w-4" />
              Generate Statement
            </Button>
            <Button variant="outline" className="rounded-[20px]">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>View all your business transactions</CardDescription>
              </div>
              <div className="flex flex-wrap gap-2">
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search transactions..." className="pl-8 w-[180px] md:w-[200px] rounded-[20px]" />
                  </div>
                  <Button variant="outline" size="icon" className="rounded-[20px]">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
                <Select 
                  defaultValue="all" 
                  value={filterType}
                  onValueChange={setFilterType}
                >
                  <SelectTrigger className="w-[130px] rounded-[20px]">
                    <SelectValue placeholder="Filter by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Transactions</SelectItem>
                    <SelectItem value="incoming">Credits Only</SelectItem>
                    <SelectItem value="outgoing">Debits Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <TransactionTable 
              transactions={filteredTransactions}
              requireOTP={true}
            />
            
            <div className="flex items-center justify-center mt-6">
              <Button variant="outline" className="rounded-[20px]">
                Load More
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Dialog open={receiptDialog} onOpenChange={handleCloseReceipt}>
        <DialogContent className="sm:max-w-md p-0 overflow-hidden">
          {selectedTransaction && (
            <EnhancedTransactionReceipt 
              transaction={selectedTransaction}
              onClose={handleCloseReceipt}
              onPrint={handlePrintReceipt}
              onDownload={handleDownloadReceipt}
            />
          )}
        </DialogContent>
      </Dialog>
    </BusinessLayout>
  );
};

export default BusinessTransactions;
