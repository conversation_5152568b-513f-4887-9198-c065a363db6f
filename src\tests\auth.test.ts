
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { login } from '@/services/authApi';

// Mock the authApi
vi.mock('@/services/authApi');

interface UserRole {
  id: string;
  name: string;
}

interface LoginCredentials {
  email: string;
  pin: string;
}

describe('Authentication Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should login successfully with valid credentials', async () => {
    const mockResponse = {
      user: { 
        id: '1', 
        fullName: 'Test User',
        role: 'ADMIN' as const,
        department: 'IT',
        permissions: ['view_transactions', 'manage_users']
      },
      tokens: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token'
      }
    };

    vi.mocked(login).mockResolvedValue(mockResponse);

    const credentials = {
      identifier: '<EMAIL>',
      pin: '1234'
    };

    const result = await login(credentials);

    expect(result).toEqual(mockResponse);
    expect(login).toHaveBeenCalledWith(credentials);
  });

  it('should handle login failure', async () => {
    const mockError = new Error('Invalid credentials');
    vi.mocked(login).mockRejectedValue(mockError);

    const credentials = {
      identifier: '<EMAIL>',
      pin: 'wrong-pin'
    };

    await expect(login(credentials)).rejects.toThrow('Invalid credentials');
  });

  it('should validate email format', async () => {
    const credentials = {
      identifier: 'invalid-email',
      pin: '1234'
    };

    const mockError = new Error('Invalid email format');
    vi.mocked(login).mockRejectedValue(mockError);

    await expect(login(credentials)).rejects.toThrow('Invalid email format');
  });
});
