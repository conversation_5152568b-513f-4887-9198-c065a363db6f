
import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import NotificationBadge from '../NotificationBadge/NotificationBadge';
import NotificationCenter from '../NotificationCenter/NotificationCenter';
import {
  Menu as MenuIcon,
  UserCircle,
  LayoutDashboard,
  Receipt,
  CreditCard,
  Settings,
  LogOut,
  Shield,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from '@/components/ui/sheet';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  // State for mobile drawer
  const [drawerOpen, setDrawerOpen] = useState(false);
  
  // State for notification popover
  const [notificationAnchorEl, setNotificationAnchorEl] = useState<HTMLButtonElement | null>(null);
  const notificationPopoverOpen = Boolean(notificationAnchorEl);

  // Handle notification badge click
  const handleNotificationClick = () => {
    setNotificationAnchorEl(notificationAnchorEl ? null : document.querySelector('.notification-trigger') as HTMLButtonElement);
  };

  // Handle notification popover close
  const handleNotificationClose = () => {
    setNotificationAnchorEl(null);
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Navigation items
  const navItems = [
    { text: 'Dashboard', path: '/dashboard', icon: <LayoutDashboard size={18} /> },
    { text: 'Transactions', path: '/transactions', icon: <Receipt size={18} /> },
    { text: 'Cards', path: '/cards', icon: <CreditCard size={18} /> },
    { text: 'Settings', path: '/settings', icon: <Settings size={18} /> },
    { text: 'Security', path: '/security', icon: <Shield size={18} /> },
  ];

  return (
    <>
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-4">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <MenuIcon className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left">
                <div className="flex flex-col space-y-4 py-4">
                  <div className="flex items-center gap-2 px-2">
                    <Avatar>
                      <span>{user?.name?.charAt(0) || 'U'}</span>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{user?.name || 'User'}</p>
                      <p className="text-xs text-muted-foreground">{user?.email || ''}</p>
                    </div>
                  </div>
                  
                  <nav className="flex flex-col space-y-1">
                    {navItems.map((item) => (
                      <Link 
                        key={item.text} 
                        to={item.path}
                        className={`
                          flex items-center gap-2 rounded-md px-2 py-1.5 text-sm font-medium
                          ${location.pathname === item.path 
                            ? 'bg-muted text-foreground' 
                            : 'text-muted-foreground hover:bg-muted hover:text-foreground'}
                        `}
                      >
                        {item.icon}
                        {item.text}
                      </Link>
                    ))}
                  </nav>
                  
                  <div className="mt-auto">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="w-full justify-start gap-2"
                      onClick={handleLogout}
                    >
                      <LogOut size={16} />
                      Logout
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            
            <Link to="/" className="flex items-center gap-2 font-bold text-xl">
              KojaPay
            </Link>
          </div>
          
          {user ? (
            <>
              <nav className="hidden md:flex items-center gap-4">
                {navItems.map((item) => (
                  <Link
                    key={item.text}
                    to={item.path}
                    className={`
                      flex items-center gap-2 text-sm font-medium px-3 py-2 rounded-md
                      ${location.pathname === item.path 
                        ? 'bg-muted text-foreground' 
                        : 'text-muted-foreground hover:bg-muted hover:text-foreground'}
                    `}
                  >
                    {item.icon}
                    {item.text}
                  </Link>
                ))}
              </nav>
              
              <div className="flex items-center gap-2">
                <div className="notification-trigger">
                  <NotificationBadge onClick={handleNotificationClick} />
                </div>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="rounded-full">
                      <Avatar>
                        <span>{user?.name?.charAt(0) || 'U'}</span>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => navigate('/profile')}>
                      Profile
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/settings')}>
                      Settings
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => navigate('/notifications/preferences')}>
                      Notification Settings
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          ) : (
            <div className="flex items-center gap-2">
              <Button variant="ghost" onClick={() => navigate('/login')}>
                Login
              </Button>
              <Button variant="default" onClick={() => navigate('/register')}>
                Register
              </Button>
            </div>
          )}
        </div>
      </header>
      
      {/* Notification Popover */}
      <Popover open={notificationPopoverOpen} onOpenChange={() => setNotificationAnchorEl(null)}>
        <PopoverTrigger className="hidden" />
        <PopoverContent className="w-80 p-0" align="end">
          <NotificationCenter userId={user?.id} />
        </PopoverContent>
      </Popover>
    </>
  );
};

export default Header;
