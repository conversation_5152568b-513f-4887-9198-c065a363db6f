import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { ShoppingCart as CartIcon, Trash2, Plus, Minus, ShoppingBag, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { CartItem } from '@/services/cartService';
import { Product } from '@/services/ecommerceService';
import EcommerceCheckout from './EcommerceCheckout';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useCart } from '@/contexts/CartContext';

const useSession = () => {
  return {
    user: { id: 'user-1', name: 'Test User', email: '<EMAIL>' }
  };
};

interface ShoppingCartProps {
  isDrawer?: boolean;
  onClose?: () => void;
}

const ShoppingCart: React.FC<ShoppingCartProps> = ({ isDrawer = false, onClose }) => {
  const { cart, updateCartItem, removeFromCart, clearCart } = useCart();
  const navigate = useNavigate();
  const { toast } = useToast();
  const session = useSession();
  const [isUpdating, setIsUpdating] = useState<string | null>(null);
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  const handleQuantityChange = async (item: CartItem, newQuantity: number) => {
    if (newQuantity < 1) newQuantity = 1;
    if (newQuantity > (item.product.stock || item.product.inventory || 10)) {
      newQuantity = (item.product.stock || item.product.inventory || 10);
    }

    setIsUpdating(item.productId.toString());
    try {
      updateCartItem(item.productId, newQuantity);
    } catch (error) {
      console.error('Error updating quantity:', error);
      toast({
        title: 'Error',
        description: 'Failed to update item quantity',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(null);
    }
  };

  const handleRemoveItem = (item: CartItem) => {
    removeFromCart(item.productId, item.product.name);
    toast({
      title: 'Item Removed',
      description: `${item.product.name} has been removed from your cart`,
    });
  };

  const handleClearCart = () => {
    clearCart();
    toast({
      title: 'Cart Cleared',
      description: 'All items have been removed from your cart',
    });
  };

  const handleCheckoutComplete = (order: Record<string, any>) => {
    clearCart();

    toast({
      title: 'Order Placed',
      description: `Your order #${order.id} has been placed successfully`,
    });

    if (isDrawer && onClose) {
      onClose();
    }

    navigate(`/account/orders/${order.id}`);
  };

  const handleContinueShopping = () => {
    if (isDrawer && onClose) {
      onClose();
    } else {
      navigate('/ecommerce');
    }
  };

  const startCheckout = () => {
    if (!session) {
      toast({
        title: 'Sign in required',
        description: 'Please sign in to proceed with checkout',
        variant: 'destructive',
      });
      return;
    }
    setIsCheckingOut(true);
  };

  if (cart.items.length === 0) {
    return (
      <div className={`flex flex-col items-center justify-center ${isDrawer ? 'h-full' : 'min-h-[400px]'} p-4`}>
        <ShoppingBag className="h-16 w-16 text-muted-foreground mb-4" />
        <h2 className="text-xl font-semibold mb-2">Your cart is empty</h2>
        <p className="text-muted-foreground text-center mb-6">Looks like you haven't added any products to your cart yet.</p>
        <Button onClick={handleContinueShopping}>
          Continue Shopping
        </Button>
      </div>
    );
  }

  if (isCheckingOut) {
    return (
      <div className={isDrawer ? 'h-full' : ''}>
        <Button variant="ghost" onClick={() => setIsCheckingOut(false)} className="mb-4">
          &larr; Back to Cart
        </Button>
        <EcommerceCheckout 
          products={cart.items.map(item => item.product)}
          customerId={session?.user.id || 'guest'}
          customerName={session?.user.name || 'Guest User'}
          onCheckoutComplete={handleCheckoutComplete}
        >
          <></>
        </EcommerceCheckout>
      </div>
    );
  }

  return (
    <div className={`${isDrawer ? 'h-full flex flex-col' : ''}`}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold flex items-center">
          <CartIcon className="mr-2 h-5 w-5" />
          Your Cart
        </h2>
        <Button variant="ghost" size="sm" onClick={handleClearCart}>
          <Trash2 className="h-4 w-4 mr-1" />
          Clear
        </Button>
      </div>

      {!session && (
        <Alert className="mb-4" variant="warning">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Not signed in</AlertTitle>
          <AlertDescription>
            You need to sign in to complete your purchase.
          </AlertDescription>
        </Alert>
      )}

      <div className={`space-y-4 ${isDrawer ? 'flex-grow overflow-y-auto pr-2' : ''}`}>
        {cart.items.map((item) => (
          <Card key={item.productId.toString()} className="border border-kojaPrimary/20">
            <CardContent className="p-3 flex items-center space-x-3">
              <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                <img src={item.product.image} alt={item.product.name} className="w-full h-full object-cover" />
              </div>
              <div className="flex-grow">
                <div className="font-medium">{item.product.name}</div>
                <div className="text-sm text-muted-foreground">{item.product.storeName}</div>
                <div className="font-bold mt-1">₦{item.product.price.toLocaleString()}</div>
              </div>
              <div className="flex flex-col items-end space-y-2">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-6 w-6" 
                  onClick={() => handleRemoveItem(item)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
                <div className="flex items-center border rounded-md">
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8 rounded-none" 
                    onClick={() => handleQuantityChange(item, item.quantity - 1)}
                    disabled={item.quantity <= 1 || isUpdating === item.productId.toString()}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  <Input 
                    type="number" 
                    value={item.quantity} 
                    onChange={(e) => handleQuantityChange(item, parseInt(e.target.value) || 1)}
                    className="h-8 w-12 text-center p-0 border-0 focus-visible:ring-0" 
                    min={1} 
                    max={(item.product.stock || item.product.inventory || 10)}
                    disabled={isUpdating === item.productId.toString()}
                  />
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8 rounded-none" 
                    onClick={() => handleQuantityChange(item, item.quantity + 1)}
                    disabled={item.quantity >= (item.product.stock || item.product.inventory || 10) || isUpdating === item.productId.toString()}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className={`mt-6 ${isDrawer ? 'mt-auto pt-4' : ''}`}>
        <div className="space-y-2 mb-4">
          <div className="flex justify-between">
            <span>Subtotal</span>
            <span className="font-medium">₦{cart.subtotal.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Shipping</span>
            <span>Calculated at checkout</span>
          </div>
          <Separator className="my-2" />
          <div className="flex justify-between font-bold">
            <span>Total</span>
            <span>₦{cart.subtotal.toLocaleString()}</span>
          </div>
        </div>

        <div className="flex flex-col space-y-2">
          <Button onClick={startCheckout} disabled={!cart.items.length}>
            Proceed to Checkout
          </Button>
          <Button variant="outline" onClick={handleContinueShopping}>
            Continue Shopping
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ShoppingCart;
