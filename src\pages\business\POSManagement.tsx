
import React, { useState, useEffect } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Terminal, 
  Plus, 
  RotateCw, 
  AlertTriangle, 
  Check, 
  Smartphone,
  MapPin,
  User,
  MoreVertical,
  Settings,
  HelpCircle,
  X,
  CreditCard,
  Wifi,
  BatteryFull,
  Signal,
  Printer,
  DollarSign,
  CircleCheck,
  Keyboard,
  Scroll
} from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import TransactionStatus from '@/components/TransactionStatus';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useIsMobile } from '@/hooks/use-mobile';

interface POSTerminal {
  id: string;
  serialNumber: string;
  location: string;
  assignedTo: string;
  status: 'active' | 'inactive' | 'maintenance';
  lastActive: string;
  transactions: number;
  model: string;
  batteryLevel: number;
  signalStrength: number;
  connected: boolean;
}

const BusinessPOSManagement = () => {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [selectedTerminal, setSelectedTerminal] = useState<POSTerminal | null>(null);
  const [showCardReaderDialog, setShowCardReaderDialog] = useState(false);
  const [cardReaderConnected, setCardReaderConnected] = useState(false);
  const [cardReaderSession, setCardReaderSession] = useState<Date | null>(null);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [paymentSuccessful, setPaymentSuccessful] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [virtualPinpad, setVirtualPinpad] = useState(false);
  const [pinEntered, setPinEntered] = useState('');
  
  const terminals: POSTerminal[] = [
    {
      id: 'POS-001',
      serialNumber: 'KP-278321',
      location: 'Main Store',
      assignedTo: 'Sarah Johnson',
      status: 'active',
      lastActive: '2 mins ago',
      transactions: 324,
      model: 'PAX S920',
      batteryLevel: 85,
      signalStrength: 90,
      connected: true
    },
    {
      id: 'POS-002',
      serialNumber: 'KP-278322',
      location: 'Branch Office',
      assignedTo: 'Michael Smith',
      status: 'inactive',
      lastActive: '3 days ago',
      transactions: 156,
      model: 'PAX A920',
      batteryLevel: 20,
      signalStrength: 40,
      connected: false
    },
    {
      id: 'POS-003',
      serialNumber: 'KP-278323',
      location: 'Exhibition Stand',
      assignedTo: 'David Wilson',
      status: 'maintenance',
      lastActive: '1 week ago',
      transactions: 78,
      model: 'Nexgo N5',
      batteryLevel: 0,
      signalStrength: 0,
      connected: false
    }
  ];
  
  // Auto-detect card reader periodically
  useEffect(() => {
    if (selectedTerminal && selectedTerminal.status === 'active' && !cardReaderConnected) {
      // Auto-detect card reader after terminal is selected
      const detectTimer = setTimeout(() => {
        autoDetectCardReader();
      }, 1500);
      
      return () => clearTimeout(detectTimer);
    }
  }, [selectedTerminal]);
  
  // Check if card reader session has expired
  useEffect(() => {
    if (cardReaderConnected && cardReaderSession) {
      const sessionTimer = setInterval(() => {
        const now = new Date();
        // Session expires after 30 minutes of inactivity
        const sessionExpiryTime = 30 * 60 * 1000; // 30 minutes in milliseconds
        
        if (now.getTime() - cardReaderSession.getTime() > sessionExpiryTime) {
          setCardReaderConnected(false);
          setCardReaderSession(null);
          toast({
            title: "Card Reader Session Expired",
            description: "Your card reader session has expired due to inactivity",
            variant: "destructive",
          });
        }
      }, 60000); // Check every minute
      
      return () => clearInterval(sessionTimer);
    }
  }, [cardReaderConnected, cardReaderSession, toast]);
  
  const handleRequestNew = () => {
    setShowApplicationForm(true);
  };
  
  const handleSubmitApplication = (e: React.FormEvent) => {
    e.preventDefault();
    
    toast({
      title: "Application Submitted",
      description: "Your POS terminal application has been received",
      variant: "success"
    });
    
    setFormSubmitted(true);
  };
  
  const getStatusBadge = (status: string) => {
    switch(status) {
      case 'active':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"><Check size={12} className="mr-1" /> Active</span>;
      case 'inactive':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Inactive</span>;
      case 'maintenance':
        return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800"><AlertTriangle size={12} className="mr-1" /> Maintenance</span>;
      default:
        return null;
    }
  };
  
  const resetForm = () => {
    setShowApplicationForm(true);
    setFormSubmitted(false);
  };
  
  const openTerminalInterface = (terminal: POSTerminal) => {
    setSelectedTerminal(terminal);
    if (terminal.status === 'active') {
      toast({
        title: "Terminal Interface",
        description: "Initializing POS terminal interface...",
      });
    } else {
      toast({
        title: "Terminal Unavailable",
        description: "This terminal is currently unavailable or in maintenance",
        variant: "destructive"
      });
    }
  };
  
  const autoDetectCardReader = () => {
    // Simulate auto-detection of card reader
    toast({
      title: "Scanning for Card Readers",
      description: "Automatically detecting available card readers...",
    });
    
    setTimeout(() => {
      setCardReaderConnected(true);
      setCardReaderSession(new Date());
      toast({
        title: "Card Reader Connected",
        description: "Card reader automatically detected and connected",
        variant: "success",
      });
    }, 2000);
  };
  
  const connectCardReader = () => {
    if (!cardReaderConnected) {
      setShowCardReaderDialog(true);
    } else {
      toast({
        title: "Card Reader Active",
        description: "Your card reader is already connected and active",
        variant: "default",
      });
    }
  };
  
  const simulateCardReaderConnection = () => {
    toast({
      title: "Connecting Card Reader",
      description: "Please wait while we connect to your card reader...",
    });
    
    setTimeout(() => {
      setCardReaderConnected(true);
      setCardReaderSession(new Date());
      toast({
        title: "Card Reader Connected",
        description: "Your card reader is now ready to use",
        variant: "success",
      });
      setShowCardReaderDialog(false);
    }, 2000);
  };
  
  const processPayment = () => {
    if (!paymentAmount || isNaN(parseFloat(paymentAmount))) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid payment amount",
        variant: "destructive",
      });
      return;
    }
    
    // Update card reader session time
    setCardReaderSession(new Date());
    setProcessingPayment(true);
    setVirtualPinpad(true);
  };
  
  const handlePinInput = (digit: string) => {
    if (pinEntered.length < 4) {
      setPinEntered(prev => prev + digit);
    }
    
    if (pinEntered.length === 3) {
      setTimeout(() => {
        setVirtualPinpad(false);
        toast({
          title: "Processing Payment",
          description: "Please wait while we process your transaction...",
        });
        
        setTimeout(() => {
          setPaymentSuccessful(true);
          setProcessingPayment(false);
          
          toast({
            title: "Payment Successful",
            description: `Transaction of ₦${paymentAmount} has been completed successfully.`,
            variant: "success",
          });
        }, 2000);
      }, 1000);
    }
  };
  
  const clearPin = () => {
    setPinEntered('');
  };
  
  const resetTerminal = () => {
    // Don't reset the card reader connection, just the payment
    setProcessingPayment(false);
    setPaymentSuccessful(false);
    setPaymentAmount('');
    setPinEntered('');
    setVirtualPinpad(false);
  };
  
  // Add PWA support with manifest
  useEffect(() => {
    // This would normally be in the index.html, but we're simulating PWA setup
    const linkElement = document.createElement('link');
    linkElement.rel = 'manifest';
    linkElement.href = '/manifest.json';
    document.head.appendChild(linkElement);
    
    // This is a simulation. In a real app, we'd create the manifest.json file
  }, []);

  return (
    <BusinessLayout pageTitle="POS Terminal Management">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-semibold mb-1">POS Terminal Management</h2>
            <p className="text-muted-foreground">Manage your point of sale terminals</p>
          </div>
          <Button 
            className="bg-[#1231b8] hover:bg-[#1231b8]/90"
            onClick={handleRequestNew}
          >
            <Plus size={16} className="mr-1" /> Request New Terminal
          </Button>
        </div>
        
        {showApplicationForm ? (
          <Card>
            <CardHeader>
              <CardTitle>POS Terminal Application</CardTitle>
              <CardDescription>
                {formSubmitted 
                  ? "Your application has been submitted successfully" 
                  : "Complete the form below to request a new POS terminal"}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {formSubmitted ? (
                <div className="py-6">
                  <TransactionStatus 
                    status="success" 
                    message="Your POS terminal application has been submitted. We'll process your request within 48 hours and contact you with further details." 
                  />
                  
                  <div className="flex justify-center mt-6">
                    <Button onClick={resetForm} variant="outline" className="mr-4">
                      Submit Another Application
                    </Button>
                    <Button onClick={() => setShowApplicationForm(false)}>
                      Back to Terminal Management
                    </Button>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmitApplication} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="businessName">Business Name</Label>
                      <Input id="businessName" placeholder="Your registered business name" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="contactPerson">Contact Person</Label>
                      <Input id="contactPerson" placeholder="Full name" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input id="phone" placeholder="Contact phone number" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input id="email" type="email" placeholder="Contact email" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="location">Terminal Location</Label>
                      <Input id="location" placeholder="Where will this terminal be used?" required />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="businessType">Business Type</Label>
                      <Input id="businessType" placeholder="e.g. Retail, Restaurant, etc." required />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address">Business Address</Label>
                    <Input id="address" placeholder="Full address where terminal will be used" required />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="additionalInfo">Additional Information</Label>
                    <Input id="additionalInfo" placeholder="Any specific requirements or questions" />
                  </div>
                  
                  <div className="pt-4 flex justify-end gap-3">
                    <Button type="button" variant="outline" onClick={() => setShowApplicationForm(false)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="bg-[#1231b8] hover:bg-[#1231b8]/90">
                      Submit Application
                    </Button>
                  </div>
                </form>
              )}
            </CardContent>
          </Card>
        ) : selectedTerminal ? (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setSelectedTerminal(null)}
                >
                  <X size={16} className="mr-1" /> Close Interface
                </Button>
                <h3 className="text-lg font-medium">{selectedTerminal.model} Terminal Interface</h3>
              </div>
              <Badge variant="outline" className="flex items-center gap-1">
                <span className={`w-2 h-2 rounded-full ${selectedTerminal.connected ? 'bg-green-500' : 'bg-red-500'}`}></span>
                {selectedTerminal.connected ? 'Connected' : 'Disconnected'}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 gap-6">
              {/* Main Terminal Interface - Now a phone-sized UI even on larger screens */}
              <div className="mx-auto w-full max-w-[375px]">
                <Card className="terminal-container overflow-hidden border-2 border-gray-300 shadow-xl">
                  {/* Terminal Status Bar */}
                  <CardHeader className="bg-[#1231b8] text-white py-2 px-4">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-sm flex items-center">
                        <Terminal size={14} className="mr-2" /> KojaPay POS
                      </CardTitle>
                      <div className="flex items-center space-x-2">
                        <Wifi size={12} />
                        <Signal size={12} />
                        <BatteryFull size={12} />
                        <span className="text-xs">{new Date().toLocaleTimeString('en-US', {hour: '2-digit', minute:'2-digit'})}</span>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-4">
                    {/* Terminal Status Overview */}
                    <div className="flex justify-between items-center mb-4 text-xs bg-gray-50 p-2 rounded-lg">
                      <span className="flex items-center">
                        <BatteryFull size={12} className="mr-1" />
                        {selectedTerminal.batteryLevel}%
                      </span>
                      <span className="flex items-center">
                        <Signal size={12} className="mr-1" />
                        {selectedTerminal.signalStrength}%
                      </span>
                      <span className={`flex items-center ${cardReaderConnected ? 'text-green-600' : 'text-red-600'}`}>
                        <CreditCard size={12} className="mr-1" />
                        {cardReaderConnected ? 'Reader Connected' : 'No Reader'}
                      </span>
                    </div>
                    
                    {paymentSuccessful ? (
                      <div className="text-center py-8 space-y-5">
                        <div className="w-16 h-16 rounded-full bg-green-100 text-green-600 flex items-center justify-center mx-auto">
                          <CircleCheck size={32} />
                        </div>
                        <div>
                          <h3 className="text-base font-bold mb-1">Payment Successful</h3>
                          <p className="text-gray-500 text-sm mb-3">Amount: ₦{paymentAmount}</p>
                          <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg border border-gray-100 mb-4">
                            <p className="font-medium mb-1">Transaction Details:</p>
                            <p>Terminal ID: {selectedTerminal.id}</p>
                            <p>Date: {new Date().toLocaleString()}</p>
                            <p>Reference: KP-{Math.floor(Math.random() * 1000000)}</p>
                          </div>
                        </div>
                        <Button onClick={resetTerminal} className="bg-[#1231b8] text-sm">
                          New Transaction
                        </Button>
                      </div>
                    ) : processingPayment ? (
                      virtualPinpad ? (
                        <div className="space-y-4 py-3">
                          <div className="text-center mb-4">
                            <h3 className="text-base font-medium mb-1">Enter PIN</h3>
                            <p className="text-xs text-gray-500">Amount: ₦{paymentAmount}</p>
                            
                            <div className="flex justify-center space-x-2 my-4">
                              {[...Array(4)].map((_, i) => (
                                <div
                                  key={i}
                                  className="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center"
                                >
                                  {pinEntered.length > i ? '•' : ''}
                                </div>
                              ))}
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-3 gap-2">
                            {[1, 2, 3, 4, 5, 6, 7, 8, 9, '', 0, 'X'].map((key, index) => (
                              <Button
                                key={index}
                                variant={key === 'X' ? "destructive" : "outline"}
                                className="h-10 text-sm font-medium"
                                onClick={() => key === 'X' ? clearPin() : handlePinInput(key.toString())}
                                disabled={key === ''}
                              >
                                {key === 'X' ? <X size={16} /> : key}
                              </Button>
                            ))}
                          </div>
                          
                          <p className="text-xs text-center text-gray-500 mt-3">
                            Enter your 4-digit PIN. For security, PIN is not stored.
                          </p>
                        </div>
                      ) : (
                        <div className="text-center py-10 space-y-4">
                          <div className="animate-spin w-10 h-10 border-4 border-[#1231b8] border-t-transparent rounded-full mx-auto"></div>
                          <div>
                            <h3 className="text-base font-medium mb-1">Processing Payment</h3>
                            <p className="text-xs text-gray-500">Please wait while we process your transaction</p>
                          </div>
                        </div>
                      )
                    ) : cardReaderConnected ? (
                      <div className="space-y-4">
                        <div className="text-center mb-3">
                          <h3 className="text-base font-medium mb-1">New Payment</h3>
                          <p className="text-xs text-gray-500">Enter transaction amount to proceed</p>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="space-y-1">
                            <Label htmlFor="amount" className="text-sm">Amount (₦)</Label>
                            <div className="relative">
                              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                                <DollarSign size={14} />
                              </span>
                              <Input 
                                id="amount"
                                value={paymentAmount}
                                onChange={(e) => setPaymentAmount(e.target.value)}
                                className="pl-9 text-base font-medium"
                                placeholder="0.00"
                              />
                            </div>
                          </div>
                          
                          <div className="flex justify-between">
                            <Button variant="outline" size="sm" onClick={resetTerminal}>
                              Cancel
                            </Button>
                            <Button 
                              className="bg-[#1231b8] hover:bg-[#1231b8]/90"
                              size="sm"
                              onClick={processPayment}
                            >
                              Process Payment
                            </Button>
                          </div>
                        </div>
                        
                        <div className="border-t pt-3 mt-3">
                          <h4 className="text-xs font-medium mb-2">Quick Amount</h4>
                          <div className="grid grid-cols-4 gap-1">
                            {['1,000', '2,000', '5,000', '10,000'].map((amount) => (
                              <Button 
                                key={amount} 
                                variant="outline" 
                                size="sm"
                                className="text-xs py-1 h-8"
                                onClick={() => setPaymentAmount(amount.replace(/,/g, ''))}
                              >
                                ₦{amount}
                              </Button>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-5 space-y-4">
                        <div className="w-16 h-16 rounded-full bg-amber-100 text-amber-600 flex items-center justify-center mx-auto">
                          <AlertTriangle size={28} />
                        </div>
                        <div>
                          <h3 className="text-base font-medium mb-1">Card Reader Required</h3>
                          <p className="text-xs text-gray-500 mb-4">Connect a card reader to process payments</p>
                          
                          <Button 
                            onClick={autoDetectCardReader} 
                            className="bg-[#1231b8] hover:bg-[#1231b8]/90 w-full"
                            size="sm"
                          >
                            Scan For Card Reader
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                  
                  <CardFooter className="bg-gray-50 flex justify-between border-t text-xs p-2">
                    <Button variant="ghost" size="sm" className="text-gray-500 p-1 h-8">
                      <HelpCircle size={14} className="mr-1" /> Help
                    </Button>
                    <Button variant="ghost" size="sm" className="text-gray-500 p-1 h-8">
                      <Settings size={14} className="mr-1" /> Settings
                    </Button>
                    <Button variant="ghost" size="sm" className="text-gray-500 p-1 h-8">
                      <Scroll size={14} className="mr-1" /> Reports
                    </Button>
                  </CardFooter>
                </Card>
                
                {/* Card Reader Connection Status */}
                <div className="mt-4 p-3 bg-white rounded-lg border text-xs">
                  <div className="flex justify-between items-center">
                    <span className="flex items-center text-gray-600">
                      <CreditCard size={14} className="mr-1" /> Card Reader Status
                    </span>
                    <span className={`font-medium ${cardReaderConnected ? 'text-green-600' : 'text-red-600'}`}>
                      {cardReaderConnected ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                  
                  {cardReaderConnected && cardReaderSession && (
                    <div className="mt-2 text-gray-500">
                      <p>Connected since: {cardReaderSession.toLocaleTimeString()}</p>
                      <p>Session expires after 30 minutes of inactivity</p>
                    </div>
                  )}
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-3 text-xs h-8"
                    onClick={connectCardReader}
                  >
                    {cardReaderConnected ? 'Reader Connected' : 'Connect Reader Manually'}
                  </Button>
                </div>
              </div>
              
              {/* Safety and Usage Guide */}
              <Card className="bg-white/80 backdrop-blur-2xl border-kojaYellow/20 mx-auto w-full max-w-[375px]">
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center text-amber-600">
                    <AlertTriangle size={16} className="mr-2" /> Safety Guidelines
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-xs">
                  <ul className="space-y-2">
                    <li className="flex items-start">
                      <span className="text-amber-500 mr-2">•</span>
                      <span>Only use KojaPay-approved card readers for all transactions.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-amber-500 mr-2">•</span>
                      <span>Keep your terminal and card reader in a secure location when not in use.</span>
                    </li>
                    <li className="flex items-start">
                      <span className="text-amber-500 mr-2">•</span>
                      <span>For any technical issues, contact KojaPay Support at 0800-KOJAPAY.</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="bg-[#1231b8] text-white">
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-white/70">Total Terminals</p>
                      <h3 className="text-3xl font-bold mt-1">{terminals.length}</h3>
                    </div>
                    <div className="h-12 w-12 bg-white/20 rounded-full flex items-center justify-center">
                      <Terminal size={24} />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="bg-[#fde314] text-kojaDark">
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-kojaDark/70">Active Terminals</p>
                      <h3 className="text-3xl font-bold mt-1">
                        {terminals.filter(t => t.status === 'active').length}
                      </h3>
                    </div>
                    <div className="h-12 w-12 bg-kojaDark/10 rounded-full flex items-center justify-center">
                      <Check size={24} />
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm text-muted-foreground">Maintenance</p>
                      <h3 className="text-3xl font-bold mt-1">
                        {terminals.filter(t => t.status === 'maintenance').length}
                      </h3>
                    </div>
                    <div className="h-12 w-12 bg-amber-100 rounded-full flex items-center justify-center">
                      <AlertTriangle size={24} className="text-amber-600" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Tabs defaultValue="active">
              <TabsList className="mb-4">
                <TabsTrigger value="active">Active Terminals</TabsTrigger>
                <TabsTrigger value="all">All Terminals</TabsTrigger>
                <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
              </TabsList>
              
              <TabsContent value="active">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {terminals
                    .filter(terminal => terminal.status === 'active')
                    .map(terminal => (
                      <Card key={terminal.id} className="overflow-hidden hover:bg-gray-50/50 transition-colors">
                        <CardHeader className="pb-2 flex flex-row items-center justify-between">
                          <div>
                            <CardTitle className="text-base">{terminal.model}</CardTitle>
                            <CardDescription>{terminal.serialNumber}</CardDescription>
                          </div>
                          {getStatusBadge(terminal.status)}
                        </CardHeader>
                        <CardContent className="pb-3">
                          <div className="space-y-3 text-sm">
                            <div className="flex items-center text-muted-foreground">
                              <MapPin size={16} className="mr-2" /> {terminal.location}
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <User size={16} className="mr-2" /> {terminal.assignedTo}
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <RotateCw size={16} className="mr-2" /> Last active: {terminal.lastActive}
                            </div>
                            <div className="flex items-center font-medium">
                              <Smartphone size={16} className="mr-2" /> {terminal.transactions} transactions
                            </div>
                            <div className="space-y-1 pt-1">
                              <div className="flex justify-between items-center text-xs">
                                <span className="text-gray-500">Battery</span>
                                <span className={terminal.batteryLevel > 20 ? 'text-green-600' : 'text-red-600'}>
                                  {terminal.batteryLevel}%
                                </span>
                              </div>
                              <Progress value={terminal.batteryLevel} className="h-1" />
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter className="border-t pt-4 flex justify-between">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => openTerminalInterface(terminal)}
                          >
                            Open Terminal
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreVertical size={16} />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Terminal Actions</DropdownMenuLabel>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Settings size={16} className="mr-2" /> Configure
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <HelpCircle size={16} className="mr-2" /> Report Issue
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <AlertTriangle size={16} className="mr-2" /> Deactivate
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </CardFooter>
                      </Card>
                    ))}
                </div>
              </TabsContent>
              
              <TabsContent value="all">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {terminals.map(terminal => (
                    <Card key={terminal.id} className="overflow-hidden">
                      <CardHeader className="pb-2 flex flex-row items-center justify-between">
                        <div>
                          <CardTitle className="text-base">{terminal.model}</CardTitle>
                          <CardDescription>{terminal.serialNumber}</CardDescription>
                        </div>
                        {getStatusBadge(terminal.status)}
                      </CardHeader>
                      <CardContent className="pb-3">
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center text-muted-foreground">
                            <MapPin size={16} className="mr-2" /> {terminal.location}
                          </div>
                          <div className="flex items-center text-muted-foreground">
                            <User size={16} className="mr-2" /> {terminal.assignedTo}
                          </div>
                          <div className="flex items-center text-muted-foreground">
                            <RotateCw size={16} className="mr-2" /> Last active: {terminal.lastActive}
                          </div>
                          <div className="flex items-center font-medium">
                            <Smartphone size={16} className="mr-2" /> {terminal.transactions} transactions
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="border-t pt-4 flex justify-between">
                        <Button variant="outline" size="sm">
                          View Details
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreVertical size={16} />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Terminal Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Settings size={16} className="mr-2" /> Configure
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <HelpCircle size={16} className="mr-2" /> Report Issue
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <AlertTriangle size={16} className="mr-2" /> Deactivate
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="maintenance">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {terminals
                    .filter(terminal => terminal.status === 'maintenance')
                    .map(terminal => (
                      <Card key={terminal.id} className="overflow-hidden">
                        <CardHeader className="pb-2 flex flex-row items-center justify-between">
                          <div>
                            <CardTitle className="text-base">{terminal.model}</CardTitle>
                            <CardDescription>{terminal.serialNumber}</CardDescription>
                          </div>
                          {getStatusBadge(terminal.status)}
                        </CardHeader>
                        <CardContent className="pb-3">
                          <div className="space-y-2 text-sm">
                            <div className="flex items-center text-muted-foreground">
                              <MapPin size={16} className="mr-2" /> {terminal.location}
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <User size={16} className="mr-2" /> {terminal.assignedTo}
                            </div>
                            <div className="flex items-center text-muted-foreground">
                              <RotateCw size={16} className="mr-2" /> Last active: {terminal.lastActive}
                            </div>
                            <div className="flex items-center font-medium">
                              <Smartphone size={16} className="mr-2" /> {terminal.transactions} transactions
                            </div>
                          </div>
                        </CardContent>
                        <CardFooter className="border-t pt-4 flex justify-between">
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                          <Button size="sm" className="bg-[#1231b8] hover:bg-[#1231b8]/90">
                            Resolve Issue
                          </Button>
                        </CardFooter>
                      </Card>
                    ))}
                    
                    {terminals.filter(terminal => terminal.status === 'maintenance').length === 0 && (
                      <div className="col-span-full flex items-center justify-center py-16">
                        <div className="text-center">
                          <Check size={48} className="mx-auto text-green-500 mb-4" />
                          <h3 className="text-lg font-medium mb-1">All systems operational</h3>
                          <p className="text-muted-foreground">No terminals currently under maintenance</p>
                        </div>
                      </div>
                    )}
                </div>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
      
      <Dialog open={showCardReaderDialog} onOpenChange={setShowCardReaderDialog}>
        <DialogContent className="max-w-sm mx-auto">
          <DialogHeader>
            <DialogTitle>Connect Card Reader</DialogTitle>
            <DialogDescription>
              Only use KojaPay authorized card readers for secure transactions.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="border-2 border-red-200 bg-red-50 p-4 rounded-lg text-sm">
              <h4 className="font-semibold text-red-600 flex items-center mb-2">
                <AlertTriangle size={16} className="mr-2" /> Security Warning
              </h4>
              <p className="text-red-600">
                For your security, only use card readers provided by KojaPay. Unauthorized readers may compromise transaction security.
              </p>
            </div>
            
            <div className="border rounded-lg p-4">
              <h4 className="font-medium mb-2">Authorized Card Readers</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center">
                  <Check size={16} className="text-green-600 mr-2" />
                  <span>KojaPay Smart Card Reader (SCR-100)</span>
                </li>
                <li className="flex items-center">
                  <Check size={16} className="text-green-600 mr-2" />
                  <span>KojaPay Wireless Card Reader (WCR-200)</span>
                </li>
                <li className="flex items-center">
                  <Check size={16} className="text-green-600 mr-2" />
                  <span>KojaPay NFC Reader (NFCR-50)</span>
                </li>
              </ul>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCardReaderDialog(false)}>
              Cancel
            </Button>
            <Button onClick={simulateCardReaderConnection} className="bg-[#1231b8]">
              Connect Reader
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </BusinessLayout>
  );
};

export default BusinessPOSManagement;
