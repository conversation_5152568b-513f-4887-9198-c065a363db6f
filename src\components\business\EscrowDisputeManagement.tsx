
import React, { useState, useEffect } from 'react';
import { Dispute, DisputeMessage, DisputeSender } from '@/types/escrow';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

interface EscrowDisputeManagementProps {
  dispute?: Dispute;
  onResolve?: (resolution: string) => void;
  onAddMessage?: (message: string) => void;
  isBusinessAccount?: boolean;
}

// Helper function to convert legacy message format
export function convertLegacyMessage(legacyMessage: any): DisputeMessage {
  return {
    id: legacyMessage.id || `msg-${Date.now()}`,
    disputeId: legacyMessage.disputeId || 'unknown',
    message: legacyMessage.message || legacyMessage.content || '',
    sender: legacyMessage.sender || legacyMessage.createdBy,
    content: legacyMessage.content || legacyMessage.message || '',
    createdAt: legacyMessage.createdAt || legacyMessage.timestamp || new Date().toISOString(),
    timestamp: legacyMessage.timestamp || legacyMessage.createdAt || new Date().toISOString()
  };
}

const EscrowDisputeManagement: React.FC<EscrowDisputeManagementProps> = ({
  dispute,
  onResolve,
  onAddMessage,
  isBusinessAccount = true
}) => {
  const [message, setMessage] = useState('');
  const [resolution, setResolution] = useState('');
  const [isResolveDialogOpen, setIsResolveDialogOpen] = useState(false);
  const [messages, setMessages] = useState<DisputeMessage[]>([]);

  useEffect(() => {
    if (dispute?.messages) {
      // Convert any legacy message format to the current format
      const formattedMessages = dispute.messages.map(msg => {
        if (('text' in msg && !('content' in msg)) || 
            ('content' in msg && !('message' in msg)) || 
            (!('disputeId' in msg)) || 
            (!('createdAt' in msg))) {
          return convertLegacyMessage(msg);
        }
        return msg;
      });
      setMessages(formattedMessages);
    }
  }, [dispute]);

  const handleSendMessage = () => {
    if (!message.trim()) {
      toast.error('Please enter a message');
      return;
    }

    if (onAddMessage) {
      onAddMessage(message);
      
      // Determine the appropriate sender type based on isBusinessAccount
      const senderType: DisputeSender = isBusinessAccount 
        ? 'admin' // Business users are mapped to 'admin' type
        : 'buyer'; // Regular users are mapped to 'buyer' type
      
      // Optimistically add the message to the UI
      const newMessage: DisputeMessage = {
        id: `temp-${Date.now()}`,
        disputeId: dispute?.id || 'unknown',
        message: message,
        content: message,
        sender: senderType,
        createdAt: new Date().toISOString(),
        timestamp: new Date().toISOString()
      };
      
      setMessages([...messages, newMessage]);
      setMessage('');
      
      toast.success('Message sent');
    }
  };

  const handleResolve = () => {
    if (!resolution.trim()) {
      toast.error('Please enter a resolution');
      return;
    }

    if (onResolve) {
      onResolve(resolution);
      setIsResolveDialogOpen(false);
      toast.success('Dispute resolved successfully');
    }
  };

  if (!dispute) {
    return (
      <div className="p-4 text-center">
        <p>No dispute information available</p>
      </div>
    );
  }

  const mapSenderType = (sender: DisputeSender): string => {
    if (sender === 'admin') return 'Business';
    if (sender === 'buyer' || sender === 'seller') return 'Customer';
    if (sender === 'system') return 'System';
    return sender;
  };

  const isResolved = dispute.status === 'resolved' || 
    dispute.status === 'resolved_buyer' || 
    dispute.status === 'resolved_seller' || 
    dispute.status === 'closed';

  const isInReview = dispute.status === 'in_review' || 
    dispute.status === 'reviewing';

  const isEscalated = dispute.status === 'escalated';

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-4 border-b">
        <h2 className="text-xl font-semibold">Dispute Details</h2>
        <div className="mt-2">
          <div className="flex justify-between text-sm text-gray-500">
            <span>ID: {dispute.id}</span>
            <span>Created: {new Date(dispute.createdAt).toLocaleDateString()}</span>
          </div>
          <h3 className="mt-3 font-medium">{dispute.title || 'Untitled Dispute'}</h3>
          
          <div className="mt-2 p-3 bg-gray-50 rounded-md">
            <span className="text-sm text-gray-500">Reason:</span>
            <p className="font-medium">{dispute.reason}</p>
          </div>
          
          {dispute.description && (
            <div className="mt-3 p-3 bg-gray-50 rounded-md">
              <span className="text-sm text-gray-500">Description:</span>
              <p>{dispute.description}</p>
            </div>
          )}
          
          <div className="mt-3">
            <span className="text-sm font-medium">Status: </span>
            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
              isResolved
                ? 'bg-green-100 text-green-800' 
                : isInReview 
                  ? 'bg-blue-100 text-blue-800'
                  : isEscalated
                    ? 'bg-red-100 text-red-800'
                    : 'bg-yellow-100 text-yellow-800'
            }`}>
              {dispute.status === 'resolved_seller' 
                ? 'Resolved in favor of seller'
                : dispute.status === 'resolved_buyer'
                  ? 'Resolved in favor of buyer'
                  : dispute.status.charAt(0).toUpperCase() + dispute.status.slice(1).replace('_', ' ')}
            </span>
          </div>
        </div>
      </div>
      
      <div className="p-4 border-b">
        <h3 className="font-medium mb-3">Messages</h3>
        <div className="space-y-3 max-h-60 overflow-y-auto">
          {messages.length > 0 ? (
            messages.map((msg) => (
              <div 
                key={msg.id} 
                className={`p-3 rounded-lg ${
                  (isBusinessAccount && msg.sender === 'admin') ||
                  (!isBusinessAccount && (msg.sender === 'buyer' || msg.sender === 'seller'))
                    ? 'bg-blue-50 ml-8' 
                    : 'bg-gray-50 mr-8'
                }`}
              >
                <div className="flex justify-between items-start">
                  <span className="font-medium text-sm">
                    {mapSenderType(msg.sender)}
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(msg.timestamp).toLocaleString()}
                  </span>
                </div>
                <p className="mt-1">{msg.content}</p>
              </div>
            ))
          ) : (
            <p className="text-center text-gray-500">No messages yet</p>
          )}
        </div>
        
        {!isResolved && (
          <div className="mt-4">
            <Textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message here..."
              className="min-h-[100px]"
            />
            <div className="mt-2 flex justify-end">
              <Button onClick={handleSendMessage}>
                Send Message
              </Button>
            </div>
          </div>
        )}
      </div>
      
      {isBusinessAccount && !isResolved && (
        <div className="p-4 flex justify-end">
          <Button variant="outline" className="mr-2" onClick={() => setIsResolveDialogOpen(true)}>
            Resolve Dispute
          </Button>
        </div>
      )}
      
      <Dialog open={isResolveDialogOpen} onOpenChange={setIsResolveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Resolve Dispute</DialogTitle>
            <DialogDescription>
              Please provide a resolution for this dispute. This will be final and cannot be changed.
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            <Textarea
              value={resolution}
              onChange={(e) => setResolution(e.target.value)}
              placeholder="Enter your resolution details..."
              className="min-h-[150px]"
            />
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsResolveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleResolve}>
              Submit Resolution
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EscrowDisputeManagement;
