
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { AuthButton } from '@/components/ui/auth-button';

interface LogoutButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'primary' | 'yellow';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  asAuthButton?: boolean;
  fullWidth?: boolean;
}

const LogoutButton: React.FC<LogoutButtonProps> = ({ 
  variant = 'default', 
  size = 'default', 
  className = '',
  asAuthButton = false,
  fullWidth = false
}) => {
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    console.log('Logged Out Successfully: Your account has been securely logged out');
    navigate('/login');
  };

  if (asAuthButton) {
    return (
      <AuthButton 
        variant="destructive" 
        size={size} 
        className={`rounded-full shadow-sm hover:shadow-md transition-all duration-300 ${className}`}
        onClick={handleLogout}
        icon={<LogOut size={16} className="mr-1.5" />}
        fullWidth={fullWidth}
      >
        Logout
      </AuthButton>
    );
  }

  return (
    <Button 
      variant={variant} 
      size={size} 
      className={`rounded-full shadow-sm hover:shadow-md transition-all duration-300 ${className}`}
      onClick={handleLogout}
    >
      <LogOut className="h-4 w-4 mr-2" />
      Logout
    </Button>
  );
};

export default LogoutButton;
