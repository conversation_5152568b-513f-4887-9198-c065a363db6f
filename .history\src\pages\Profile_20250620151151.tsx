import React, { useEffect, useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Avatar } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { MicroButton } from "@/components/ui/micro-button";
import { 
  Camera, Mail, Phone, User, MapPin, Shield, LogOut, 
  CreditCard, Lock, Bell, Share2, HelpCircle, ChevronRight 
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import LogoutButton from '@/components/LogoutButton';
import { AuthButton } from '@/components/ui/auth-button';
import { fetchUserInfo } from '@/services/userApi';

const Profile = () => {
  const { logout } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("account");
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('token');
        console.log('Profile: token', token);
        if (token) {
          const data = await fetchUserInfo(token);
          console.log('Profile: fetched user info', data);
          setUserInfo(data);
        } else {
          console.warn('Profile: No token found in localStorage');
        }
      } catch (e) {
        console.error('Profile: error fetching user info', e);
        toast({ title: 'Error', description: 'Failed to load user info' });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  if (loading) return <div>Loading...</div>;
  console.log('Profile: userInfo state', userInfo);

  // Use userInfo for all fields
  const userData = {
    fullName: `${userInfo?.firstName || ''} ${userInfo?.lastName || ''}`.trim() || 'User',
    firstName: userInfo?.firstName || '',
    lastName: userInfo?.lastName || '',
    email: userInfo?.email || '',
    phone: userInfo?.phone || '',
    address: userInfo?.address || '',
    accountNumber: userInfo?.accountNumber || '',
    accountType: userInfo?.accountType || '',
    bvn: userInfo?.bvn || '',
    kycStatus: userInfo?.kyc?.status === 'verified' ? 'Verified' : 'Pending',
    dateJoined: userInfo?.createdAt || '',
    gender: userInfo?.gender || '',
    dateOfBirth: userInfo?.dateOfBirth || '',
    country: userInfo?.country || '',
    completionPercentage: userInfo?.profileCompletion || 0
  };
  
  const handleLogout = () => {
    logout();
    toast({
      title: 'Logged Out',
      description: 'You have been successfully logged out',
    });
    navigate('/login');
  };
  
  const toggleEdit = () => {
    setIsEditing(!isEditing);
    
    if (isEditing) {
      // Save changes (mock)
      toast({
        title: 'Profile Updated',
        description: 'Your profile has been updated successfully',
      });
    }
  };

  const handleSaveChanges = () => {
    toast({
      title: 'Changes Saved',
      description: 'Your profile information has been updated successfully',
    });
    setIsEditing(false);
  };

  const handleUploadProfilePicture = () => {
    // Mock upload functionality
    toast({
      title: 'Profile Picture Updated',
      description: 'Your profile picture has been updated successfully',
    });
  };

  const renderSidebar = () => (
    <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 h-full">
      <CardContent className="p-4">
        <div className="mb-6 flex flex-col items-center">
          <div className="relative mb-4 mt-4">
            <Avatar className="w-16 h-16 border-2 border-white shadow-sm">
              <img 
                src={`https://ui-avatars.com/api/?name=${userData.fullName.replace(/ /g, '+')}&background=random&size=200`} 
                alt={userData.fullName} 
              />
            </Avatar>
            <Button 
              size="icon" 
              variant="outline"
              className="absolute -bottom-2 -right-2 h-7 w-7 rounded-full bg-kojaPrimary hover:bg-kojaPrimary/90 text-white border-none shadow-sm"
              onClick={handleUploadProfilePicture}
            >
              <Camera className="h-3.5 w-3.5" />
            </Button>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-bold text-kojaDark font-futura">{userData.fullName}</h3>
            <p className="text-kojaGray text-xs font-futura">{userData.accountNumber}</p>
          </div>

          <div className="mt-5 w-full">
            <div className="progress-circle bg-kojaPrimary/10">
              <div className="progress-circle-text text-kojaPrimary">{userData.completionPercentage}%</div>
            </div>
            <p className="text-xs text-center mt-2 text-kojaGray font-futura">Profile Completion</p>
          </div>
        </div>
        
        <div className="profile-sidebar-nav">
          <div 
            className={`profile-nav-item ${activeTab === "account" ? "profile-nav-item-active bg-kojaPrimary" : ""}`}
            onClick={() => setActiveTab("account")}
          >
            <User className="profile-nav-icon" />
            <span className="profile-nav-text">Personal Account</span>
            <ChevronRight size={16} className={activeTab === "account" ? "text-white" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "payment" ? "profile-nav-item-active bg-kojaPrimary" : ""}`}
            onClick={() => setActiveTab("payment")}
          >
            <CreditCard className="profile-nav-icon" />
            <span className="profile-nav-text">Payment Limits</span>
            <ChevronRight size={16} className={activeTab === "payment" ? "text-white" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "security" ? "profile-nav-item-active bg-kojaPrimary" : ""}`}
            onClick={() => setActiveTab("security")}
          >
            <Lock className="profile-nav-icon" />
            <span className="profile-nav-text">Account & Security</span>
            <ChevronRight size={16} className={activeTab === "security" ? "text-white" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "social" ? "profile-nav-item-active bg-kojaPrimary" : ""}`}
            onClick={() => setActiveTab("social")}
          >
            <Share2 className="profile-nav-icon" />
            <span className="profile-nav-text">Social Networks</span>
            <ChevronRight size={16} className={activeTab === "social" ? "text-white" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "notifications" ? "profile-nav-item-active bg-kojaPrimary" : ""}`}
            onClick={() => setActiveTab("notifications")}
          >
            <Bell className="profile-nav-icon" />
            <span className="profile-nav-text">Notifications</span>
            <ChevronRight size={16} className={activeTab === "notifications" ? "text-white" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "help" ? "profile-nav-item-active bg-kojaPrimary" : ""}`}
            onClick={() => setActiveTab("help")}
          >
            <HelpCircle className="profile-nav-icon" />
            <span className="profile-nav-text">Get Help</span>
            <ChevronRight size={16} className={activeTab === "help" ? "text-white" : "text-kojaGray"} />
          </div>
        </div>
        
        <div className="mt-6">
          <LogoutButton 
            asAuthButton 
            variant="destructive" 
            className="w-full font-futura"
          />
        </div>
      </CardContent>
    </Card>
  );

  const renderPersonalInfo = () => (
    <Card variant="responsive" className="shadow-sm">
      <CardHeader className="border-b border-gray-100 pb-4">
        <CardTitle className="font-bold font-futura">Personal Information</CardTitle>
        <CardDescription className="font-futura">Your basic account details</CardDescription>
      </CardHeader>
      <CardContent className="pt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="firstName" className="text-xs text-kojaGray font-futura">First Name*</Label>
          <Input 
            id="firstName" 
            value={userData.firstName} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
        
        <div>
          <Label htmlFor="lastName" className="text-xs text-kojaGray font-futura">Last Name*</Label>
          <Input 
            id="lastName" 
            value={userData.lastName} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
        
        <div>
          <Label htmlFor="email" className="text-xs text-kojaGray font-futura">Email Address*</Label>
          <Input 
            id="email" 
            type="email" 
            value={userData.email} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
        
        <div>
          <Label htmlFor="dob" className="text-xs text-kojaGray font-futura">Date of Birth*</Label>
          <Input 
            id="dob" 
            value={userData.dateOfBirth} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
        
        <div>
          <Label htmlFor="gender" className="text-xs text-kojaGray font-futura">Gender*</Label>
          <Input 
            id="gender" 
            value={userData.gender} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
      </CardContent>
    </Card>
  );

  const renderContactInfo = () => (
    <Card variant="responsive" className="shadow-sm mt-6">
      <CardHeader className="border-b border-gray-100 pb-4">
        <CardTitle className="font-bold font-futura">Contact Information</CardTitle>
        <CardDescription className="font-futura">Your contact details</CardDescription>
      </CardHeader>
      <CardContent className="pt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="phoneNumber" className="text-xs text-kojaGray font-futura">Phone Number*</Label>
          <Input 
            id="phoneNumber" 
            value={userData.phone} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
        
        <div>
          <Label htmlFor="country" className="text-xs text-kojaGray font-futura">Country*</Label>
          <Input 
            id="country" 
            value={userData.country} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
        
        <div className="md:col-span-3">
          <Label htmlFor="address" className="text-xs text-kojaGray font-futura">Address*</Label>
          <Input 
            id="address" 
            value={userData.address} 
            disabled={!isEditing} 
            className="mt-1 rounded-[20px] font-futura"
          />
        </div>
      </CardContent>
      <CardFooter className="border-t border-gray-100 pt-4 flex justify-end">
        {isEditing && (
          <Button 
            onClick={handleSaveChanges}
            className="bg-kojaPrimary hover:bg-kojaPrimary/90 font-futura font-medium"
          >
            Save Changes
          </Button>
        )}
      </CardFooter>
    </Card>
  );

  const renderAccountInfo = () => (
    <Card variant="responsive" className="shadow-sm mt-6">
      <CardHeader className="border-b border-gray-100 pb-4">
        <CardTitle className="font-bold font-futura">Account Information</CardTitle>
        <CardDescription className="font-futura">Your account details and verification status</CardDescription>
      </CardHeader>
      <CardContent className="pt-6 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50/80 p-4 rounded-xl">
            <p className="text-xs text-kojaGray font-futura">Account Number</p>
            <p className="text-sm font-medium font-futura">{userData.accountNumber}</p>
          </div>
          
          <div className="bg-gray-50/80 p-4 rounded-xl">
            <p className="text-xs text-kojaGray font-futura">Account Type</p>
            <p className="text-sm font-medium font-futura">{userData.accountType}</p>
          </div>
          
          <div className="bg-gray-50/80 p-4 rounded-xl">
            <p className="text-xs text-kojaGray font-futura">BVN</p>
            <p className="text-sm font-medium font-futura">{userData.bvn}</p>
          </div>
          
          <div className="bg-gray-50/80 p-4 rounded-xl">
            <p className="text-xs text-kojaGray font-futura">KYC Status</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium font-futura">{userData.kycStatus}</p>
              {userData.kycStatus === "Verified" ? (
                <Shield className="h-4 w-4 text-green-500" />
              ) : (
                <Button variant="outline" size="sm" className="text-xs h-7 ml-2 font-futura">
                  Verify Now
                </Button>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <DashboardLayout pageTitle="Profile Settings">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar */}
        <div className="lg:col-span-1">
          {renderSidebar()}
        </div>
        
        {/* Right Content */}
        <div className="lg:col-span-3">
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader className="border-b border-gray-100 pb-4">
              <div className="flex justify-between items-center flex-wrap gap-2">
                <div>
                  <CardTitle className="text-xl font-bold font-futura">Account Information</CardTitle>
                  <CardDescription className="font-futura">Manage your personal details and settings</CardDescription>
                </div>
                <div className="flex items-center gap-2 flex-wrap">
                  <MicroButton 
                    variant="pill" 
                    size="sm"
                    className="text-kojaPrimary border border-kojaPrimary/30 bg-transparent hover:bg-kojaPrimary/5 font-futura font-medium"
                    onClick={toggleEdit}
                  >
                    {isEditing ? "Cancel Editing" : "Edit Profile"}
                  </MicroButton>
                  <MicroButton 
                    variant="pill" 
                    size="sm"
                    className="bg-kojaPrimary text-white font-futura font-medium"
                    onClick={() => navigate('/contact-us')}
                  >
                    Connect CS
                  </MicroButton>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-6">
              <div>
                <div className="flex items-center mb-6 flex-wrap gap-4">
                  <Avatar className="w-16 h-16 border-2 border-white shadow-sm">
                    <img 
                      src={`https://ui-avatars.com/api/?name=${userData.fullName.replace(/ /g, '+')}&background=random&size=200`} 
                      alt={userData.fullName} 
                    />
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex flex-wrap gap-2">
                      <MicroButton 
                        variant="outline" 
                        size="sm"
                        onClick={handleUploadProfilePicture}
                        className="font-futura"
                      >
                        <Camera className="h-3.5 w-3.5 mr-1" />
                        Upload New Profile
                      </MicroButton>
                      <MicroButton 
                        variant="outline" 
                        size="sm"
                        className="text-red-500 border-red-500/30 hover:bg-red-500/5 font-futura"
                      >
                        Delete
                      </MicroButton>
                    </div>
                  </div>
                </div>
                
                {renderPersonalInfo()}
                {renderContactInfo()}
                {renderAccountInfo()}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Profile;
