import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { CreditCard, Plus, Lock, Eye, EyeOff, Wallet, Percent } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { GoBackButton } from '@/components/ui/go-back-button';
import { cardService } from '@/services/cardApi';
import { useAuth } from '@/contexts/AuthContext';

const Cards = () => {
  const [showCardDetails, setShowCardDetails] = React.useState(false);
  const [cards, setCards] = React.useState([]);
  const [loading, setLoading] = React.useState(false);
  const [creating, setCreating] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const navigate = useNavigate();

  // Get token from localStorage (as per AuthContext)
  const token = localStorage.getItem('token') || '';

  // Fetch cards from backend
  const fetchCards = async () => {
    setLoading(true);
    setError(null);
    try {
      const res = await cardService.getCards(token);
      if (res.success && Array.isArray(res.data)) {
        setCards(res.data);
      } else {
        setError(res.message || 'Failed to fetch cards');
      }
    } catch (e: any) {
      setError(e.message || 'Failed to fetch cards');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchCards();
    // eslint-disable-next-line
  }, []);

  const handleAddCard = async () => {
    setCreating(true);
    setError(null);
    try {
      const res = await cardService.createCard(token);
      if (res.success) {
        await fetchCards();
      } else {
        setError(res.message || 'Failed to create card');
      }
    } catch (e: any) {
      setError(e.message || 'Failed to create card');
    } finally {
      setCreating(false);
    }
  };

  const toggleCardDetails = () => {
    setShowCardDetails(!showCardDetails);
  };

  return <DashboardLayout pageTitle="Cards">
      <div className="mb-6">
        <GoBackButton />
        <h1 className="text-2xl font-semibold text-kojaDark">Your Cards</h1>
        <p className="text-kojaGray mt-1">Manage your debit and virtual cards</p>
      </div>
      {error && <div className="text-red-500 mb-4">{error}</div>}
      <div className="mb-6">
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="w-full max-w-md">
            <TabsTrigger value="all" className="flex-1">All Cards</TabsTrigger>
            <TabsTrigger value="debit" className="flex-1">Debit Cards</TabsTrigger>
            <TabsTrigger value="credit" className="flex-1">Virtual Cards</TabsTrigger>
          </TabsList>
          <TabsContent value="all" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {loading ? <div>Loading cards...</div> : cards.map((card: any) => (
                <Card key={card.id} className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
                  <CardContent className="p-5">
                    <div className="mb-5 relative overflow-hidden rounded-xl">
                      <div className={`bg-gradient-to-r from-kojaPrimary to-blue-800 rounded-xl p-5 text-white relative overflow-hidden`}>
                        <div className="absolute top-0 left-0 w-full h-full bg-black opacity-5 pattern-dots"></div>
                        <div className="card-chip absolute top-5 right-5 bg-gray-950 rounded-full"></div>
                        <div className="flex flex-col h-32">
                          <div className="font-mono text-sm opacity-80 mb-4">
                            {showCardDetails ? card.cardNumber || card.last4 : `**** **** **** ${card.last4 || '0000'}`}
                          </div>
                          <div className="mt-auto">
                            <div className="text-sm opacity-80">Valid Thru</div>
                            <div className="flex justify-between items-center">
                              <div className="font-medium uppercase">{card.cardholderName || card.cardName || 'CARD HOLDER'}</div>
                              <div className="font-mono">{card.expiryDate || `${card.expMonth || '--'}/${card.expYear || '--'}`}</div>
                            </div>
                          </div>
                        </div>
                        <div className="absolute bottom-5 right-5">
                          <div className="flex gap-1"></div>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-kojaGray">Card balance</p>
                        <p className="text-lg font-semibold text-kojaDark">{card.balance ? `₦${card.balance}` : '--'}</p>
                      </div>
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-kojaGray">{card.type === 'debit' ? 'Daily limit' : 'Card limit'}</p>
                        <p className="text-sm text-kojaDark">{card.limit ? `₦${card.limit}` : '--'}</p>
                      </div>
                      <div className="flex gap-2 mt-4">
                        <Button variant="outline" size="sm" className="flex-1 text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5" onClick={toggleCardDetails}>
                          {showCardDetails ? <EyeOff size={16} /> : <Eye size={16} />}
                          <span>{showCardDetails ? 'Hide' : 'View'}</span>
                        </Button>
                        <Button variant="outline" size="sm" className="flex-1 text-kojaGray border-kojaGray/20 hover:bg-kojaGray/5">
                          <Lock size={16} />
                          <span>Block</span>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              {/* Add New Card */}
              <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow border-dashed">
                <CardContent className="p-5 flex flex-col items-center justify-center h-full min-h-[300px]">
                  <Button variant="ghost" className="w-16 h-16 rounded-full bg-gray-50" onClick={handleAddCard} disabled={creating}>
                    <Plus size={24} className="text-kojaPrimary" />
                  </Button>
                  <p className="mt-4 font-medium text-kojaDark">{creating ? 'Creating...' : 'Add New Card'}</p>
                  <p className="text-sm text-kojaGray mt-1 text-center">Apply for a new debit or virtual card</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="debit" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cards.filter(card => card.type === 'debit').map(card => <Card key={card.id} className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
                  {/* Card content - same as in the "all" tab */}
                  <CardContent className="p-5">
                    <div className="mb-5 relative overflow-hidden rounded-xl">
                      <div className={`bg-gradient-to-r from-kojaPrimary to-blue-800 rounded-xl p-5 text-white relative overflow-hidden`}>
                        {/* Card content */}
                        <div className="absolute top-0 left-0 w-full h-full bg-black opacity-5 pattern-dots"></div>
                        <div className="card-chip absolute top-5 right-5 bg-gray-950 rounded-full"></div>
                        
                        <div className="flex flex-col h-32">
                          <div className="font-mono text-sm opacity-80 mb-4">
                            {showCardDetails ? '1234 5678 9012 3456' : card.cardNumber}
                          </div>
                          
                          <div className="mt-auto">
                            <div className="text-sm opacity-80">Valid Thru</div>
                            <div className="flex justify-between items-center">
                              <div className="font-medium uppercase">{card.name}</div>
                              <div className="font-mono">{card.expiryDate}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="absolute bottom-5 right-5">
                          <div className="flex gap-1">
                            
                            
                          </div>
                        </div>
                        
                        
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-kojaGray">Card balance</p>
                        <p className="text-lg font-semibold text-kojaDark">{card.balance}</p>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-kojaGray">Daily limit</p>
                        <p className="text-sm text-kojaDark">{card.limit}</p>
                      </div>
                      
                      <div className="flex gap-2 mt-4">
                        <Button variant="outline" size="sm" className="flex-1 text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5" onClick={toggleCardDetails}>
                          {showCardDetails ? <EyeOff size={16} /> : <Eye size={16} />}
                          <span>{showCardDetails ? 'Hide' : 'View'}</span>
                        </Button>
                        
                        <Button variant="outline" size="sm" className="flex-1 text-kojaGray border-kojaGray/20 hover:bg-kojaGray/5">
                          <Lock size={16} />
                          <span>Block</span>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>)}
            </div>
          </TabsContent>
          
          <TabsContent value="credit" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cards.filter(card => card.type === 'virtual').map(card => <Card key={card.id} className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
                  {/* Card content - same as in the "all" tab */}
                  <CardContent className="p-5">
                    <div className="mb-5 relative overflow-hidden rounded-xl">
                      <div className={`bg-gradient-to-r from-kojaPrimary to-blue-800 rounded-xl p-5 text-white relative overflow-hidden`}>
                        {/* Card content */}
                        <div className="absolute top-0 left-0 w-full h-full bg-black opacity-5 pattern-dots"></div>
                        <div className="card-chip absolute top-5 right-5 bg-gray-950 rounded-full"></div>
                        
                        <div className="flex flex-col h-32">
                          <div className="font-mono text-sm opacity-80 mb-4">
                            {showCardDetails ? '1234 5678 9012 3456' : card.cardNumber}
                          </div>
                          
                          <div className="mt-auto">
                            <div className="text-sm opacity-80">Valid Thru</div>
                            <div className="flex justify-between items-center">
                              <div className="font-medium uppercase">{card.name}</div>
                              <div className="font-mono">{card.expiryDate}</div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="absolute bottom-5 right-5">
                          <div className="flex gap-1">
                            
                            
                          </div>
                        </div>
                        
                        
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-kojaGray">Card balance</p>
                        <p className="text-lg font-semibold text-kojaDark">{card.balance}</p>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <p className="text-sm text-kojaGray">Card limit</p>
                        <p className="text-sm text-kojaDark">{card.limit}</p>
                      </div>
                      
                      <div className="flex gap-2 mt-4">
                        <Button variant="outline" size="sm" className="flex-1 text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5" onClick={toggleCardDetails}>
                          {showCardDetails ? <EyeOff size={16} /> : <Eye size={16} />}
                          <span>{showCardDetails ? 'Hide' : 'View'}</span>
                        </Button>
                        
                        <Button variant="outline" size="sm" className="flex-1 text-kojaGray border-kojaGray/20 hover:bg-kojaGray/5">
                          <Lock size={16} />
                          <span>Block</span>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>)}
            </div>
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Card Management Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle>Card Services</CardTitle>
            <CardDescription>Manage your card settings and services</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="bg-kojaPrimary/10 p-2 rounded-md">
                  <Lock size={20} className="text-kojaPrimary" />
                </div>
                <div>
                  <p className="font-medium text-kojaDark">Change PIN</p>
                  <p className="text-xs text-kojaGray">Update your card PIN</p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/card-services')}
              >
                Manage
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="bg-kojaPrimary/10 p-2 rounded-md">
                  <Wallet size={20} className="text-kojaPrimary" />
                </div>
                <div>
                  <p className="font-medium text-kojaDark">Transaction Limits</p>
                  <p className="text-xs text-kojaGray">Adjust your spending limits</p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/card-services')}
              >
                Manage
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="bg-kojaPrimary/10 p-2 rounded-md">
                  <CreditCard size={20} className="text-kojaPrimary" />
                </div>
                <div>
                  <p className="font-medium text-kojaDark">Card Replacement</p>
                  <p className="text-xs text-kojaGray">Request a new card</p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/card-services')}
              >
                Request
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle>Special Offers</CardTitle>
            <CardDescription>Exclusive offers for KojaPay cardholders</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="bg-green-100 p-2 rounded-md">
                  <Percent size={20} className="text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-kojaDark">5% Cashback</p>
                  <p className="text-xs text-kojaGray">On all online purchases</p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="text-kojaPrimary">
                View
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="bg-red-100 p-2 rounded-md">
                  <Percent size={20} className="text-red-600" />
                </div>
                <div>
                  <p className="font-medium text-kojaDark">3% Discount</p>
                  <p className="text-xs text-kojaGray">On travel bookings</p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="text-kojaPrimary">
                View
              </Button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="bg-purple-100 p-2 rounded-md">
                  <Percent size={20} className="text-purple-600" />
                </div>
                <div>
                  <p className="font-medium text-kojaDark">2x Rewards</p>
                  <p className="text-xs text-kojaGray">On dining and entertainment</p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="text-kojaPrimary">
                View
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>;
};

export default Cards;
