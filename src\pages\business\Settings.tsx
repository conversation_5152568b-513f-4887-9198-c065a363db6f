
import React from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Switch 
} from '@/components/ui/switch';
import { 
  Lock, 
  Bell, 
  CreditCard, 
  Users, 
  Building2, 
  Share2, 
  Shield, 
  LogOut 
} from 'lucide-react';

const BusinessSettings = () => {
  return (
    <BusinessLayout pageTitle="Business Settings">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Business Settings</h2>
          <p className="text-muted-foreground">Manage your business account settings</p>
        </div>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="billing">Billing</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Update your business details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessName">Business Name</Label>
                    <Input id="businessName" defaultValue="Koja Enterprises" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessType">Business Type</Label>
                    <Input id="businessType" defaultValue="Technology" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessEmail">Email Address</Label>
                    <Input id="businessEmail" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessPhone">Phone Number</Label>
                    <Input id="businessPhone" defaultValue="+234 ************" />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="businessAddress">Business Address</Label>
                    <Input id="businessAddress" defaultValue="123 Marina Street, Lagos Island, Lagos" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input id="website" defaultValue="www.kojaenterprises.com" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="taxId">Tax ID</Label>
                    <Input id="taxId" defaultValue="1234567890" />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                <Button variant="outline">Cancel</Button>
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="notifications">
            <Card>
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>Choose how you want to be notified</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { id: 'txn-notif', title: 'Transaction Notifications', description: 'Receive notifications for all transactions' },
                    { id: 'login-notif', title: 'Login Alerts', description: 'Get notified about new login activities' },
                    { id: 'product-notif', title: 'Product Updates', description: 'Receive product and feature update information' },
                    { id: 'mkt-notif', title: 'Marketing Emails', description: 'Receive marketing and promotional emails' },
                    { id: 'app-notif', title: 'App Notifications', description: 'Enable push notifications in the mobile app' },
                  ].map((item) => (
                    <div key={item.id} className="flex items-center justify-between space-x-2">
                      <div className="flex-1 space-y-1">
                        <p className="font-medium">{item.title}</p>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                      <Switch id={item.id} defaultChecked={item.id !== 'mkt-notif'} />
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button>Save Preferences</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="security">
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>Manage your account security</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPin">Current PIN</Label>
                    <Input id="currentPin" type="password" placeholder="Enter current PIN" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPin">New PIN</Label>
                    <Input id="newPin" type="password" placeholder="Enter new PIN" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPin">Confirm New PIN</Label>
                    <Input id="confirmPin" type="password" placeholder="Confirm new PIN" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">Change PIN</Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Two-Factor Authentication</CardTitle>
                  <CardDescription>Add an extra layer of security</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between space-x-2">
                    <div className="flex-1 space-y-1">
                      <p className="font-medium">Enable Two-Factor Authentication</p>
                      <p className="text-sm text-muted-foreground">Protect your account with an additional security layer</p>
                    </div>
                    <Switch id="2fa" />
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Two-factor authentication adds an extra layer of security to your account by requiring more than just a password to log in.</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="flex items-center gap-3">
                      <div className="rounded-full p-2 bg-primary/10">
                        <Shield className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">Your account is protected</h4>
                        <p className="text-xs text-muted-foreground">Fraud protection and monitoring is enabled</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full" variant="outline">Setup 2FA</Button>
                </CardFooter>
              </Card>
              
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Login Sessions</CardTitle>
                  <CardDescription>Manage your active sessions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { device: 'Windows PC', browser: 'Chrome', location: 'Lagos, Nigeria', time: 'Current session' },
                      { device: 'iPhone 13', browser: 'Safari', location: 'Lagos, Nigeria', time: 'Last accessed 2 hours ago' },
                      { device: 'MacBook Pro', browser: 'Firefox', location: 'Abuja, Nigeria', time: 'Last accessed yesterday' },
                    ].map((session, index) => (
                      <div key={index} className="flex items-center justify-between py-3 border-b last:border-0">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                            <Lock className="h-5 w-5 text-muted-foreground" />
                          </div>
                          <div>
                            <div className="font-medium">{session.device} • {session.browser}</div>
                            <div className="text-sm text-muted-foreground">{session.location} • {session.time}</div>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm" className={index === 0 ? 'opacity-50 cursor-not-allowed' : ''}>
                          {index === 0 ? 'Current' : 'Logout'}
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full text-red-500 hover:text-red-500 hover:bg-red-50">
                    <LogOut className="mr-2 h-4 w-4" />
                    Logout from all devices
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="team">
            <Card>
              <CardHeader>
                <CardTitle>Team Management</CardTitle>
                <CardDescription>Manage users who have access to your business account</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: 'John Doe', email: '<EMAIL>', role: 'Admin', lastActive: 'Currently active' },
                    { name: 'Jane Smith', email: '<EMAIL>', role: 'Staff', lastActive: '2 hours ago' },
                    { name: 'Mike Johnson', email: '<EMAIL>', role: 'Accountant', lastActive: 'Yesterday' },
                  ].map((member, index) => (
                    <div key={index} className="flex items-center justify-between py-3 border-b last:border-0">
                      <div className="flex items-center gap-3">
                        <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center text-primary font-medium">
                          {member.name.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-sm text-muted-foreground">{member.email}</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-muted text-xs px-2 py-1 rounded">{member.role}</div>
                        <Button variant="ghost" size="sm">Edit</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">
                  <Users className="mr-2 h-4 w-4" />
                  Invite User
                </Button>
                <Button>Manage Roles</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="billing">
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Methods</CardTitle>
                  <CardDescription>Manage your payment methods</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { type: 'Credit Card', last4: '4242', expiryDate: '12/25' },
                      { type: 'Debit Card', last4: '8888', expiryDate: '06/24' },
                    ].map((card, index) => (
                      <div key={index} className="flex items-center justify-between py-3 border-b last:border-0">
                        <div className="flex items-center gap-3">
                          <div className="h-10 w-10 rounded-full bg-muted flex items-center justify-center">
                            <CreditCard className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <div className="font-medium">{card.type} ending in {card.last4}</div>
                            <div className="text-sm text-muted-foreground">Expires {card.expiryDate}</div>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">Remove</Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Add Payment Method
                  </Button>
                </CardFooter>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Billing Information</CardTitle>
                  <CardDescription>Manage your billing details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="billingName">Billing Name</Label>
                    <Input id="billingName" defaultValue="Koja Enterprises Ltd" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="billingEmail">Billing Email</Label>
                    <Input id="billingEmail" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="billingAddress">Billing Address</Label>
                    <Input id="billingAddress" defaultValue="123 Marina Street, Lagos Island, Lagos" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="billingCity">City</Label>
                      <Input id="billingCity" defaultValue="Lagos" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="billingZip">Postal Code</Label>
                      <Input id="billingZip" defaultValue="100001" />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button>Save Changes</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>
  );
};

export default BusinessSettings;
