
import * as React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, Shield, LockKeyhole, Eye, Smartphone, AlertCircle } from 'lucide-react';

const SafetyTips = () => {
  return (
    <DashboardLayout pageTitle="Safety Tips">
      <div className="max-w-4xl mx-auto">
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center gap-4">
            <Shield className="h-8 w-8 text-kojaPrimary" />
            <CardTitle>KojaPay Security Guidelines</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-kojaGray">
              At KojaPay, your security is our priority. Follow these important safety tips to protect your account and financial information.
            </p>
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <LockKeyhole className="h-5 w-5 text-kojaPrimary" />
                <CardTitle className="text-lg">Strong Password</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-kojaGray text-sm">
                Use a unique password with a mix of letters, numbers, and symbols. Never share your password with anyone, including KojaPay staff.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-kojaPrimary" />
                <CardTitle className="text-lg">Two-Factor Authentication</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-kojaGray text-sm">
                Enable two-factor authentication for an additional layer of security. This prevents unauthorized access even if your password is compromised.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-kojaPrimary" />
                <CardTitle className="text-lg">Beware of Phishing</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-kojaGray text-sm">
                Be vigilant about suspicious emails or messages asking for your login details. KojaPay will never ask for your password or PIN via email or SMS.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-kojaPrimary" />
                <CardTitle className="text-lg">Verify Transactions</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-kojaGray text-sm">
                Always verify transaction details before confirming. Check recipient details carefully and be wary of urgent requests for money transfers.
              </p>
            </CardContent>
          </Card>
        </div>
        
        <Card className="warning-box">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              <CardTitle className="text-lg">Report Suspicious Activity</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-kojaGray">
              If you notice any unusual activity on your account or suspect that your account has been compromised, contact our support team immediately.
            </p>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default SafetyTips;
