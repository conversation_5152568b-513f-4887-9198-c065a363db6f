
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import LegalContent from '@/components/LegalContent';
import { Shield, Lock, AlertTriangle, Eye, KeyRound, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';

const Security = () => {
  return (
    <DashboardLayout pageTitle="Security">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-katina">Account Security</h2>
          <p className="text-muted-foreground">Manage your security settings and preferences</p>
        </div>
        
        {/* Security Warning - Moved from Dashboard */}
        <Card className="backdrop-blur-sm bg-white/90 border border-orange-100 shadow-md">
          <CardContent className="p-4 md:p-6">
            <LegalContent type="security" />
          </CardContent>
        </Card>
        
        {/* Security Status Card */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-green-500" />
              Security Status
            </CardTitle>
            <CardDescription>Your account security level is good</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-green-500 h-2.5 rounded-full w-4/5"></div>
                  </div>
                  <p className="text-sm text-gray-500">Security Score: 80/100</p>
                </div>
                <Button variant="outline" size="sm">Improve</Button>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-100">
                  <div className="mt-0.5">
                    <Lock className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">Two-Factor Authentication</h4>
                    <p className="text-xs text-gray-600 mt-1">Your account is protected with 2FA</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-amber-50 rounded-lg border border-amber-100">
                  <div className="mt-0.5">
                    <KeyRound className="h-5 w-5 text-amber-500" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">Password Strength</h4>
                    <p className="text-xs text-gray-600 mt-1">Your password strength is moderate</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-100">
                  <div className="mt-0.5">
                    <Bell className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">Security Alerts</h4>
                    <p className="text-xs text-gray-600 mt-1">You will be notified of unusual activities</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Security Settings Card */}
        <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
          <CardHeader>
            <CardTitle>Security Settings</CardTitle>
            <CardDescription>Manage your security preferences</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { id: 'login-alert', title: 'Login Alerts', description: 'Get notified about new login activities' },
                { id: 'transaction-verify', title: 'Transaction Verification', description: 'Require PIN for all transactions' },
                { id: 'device-tracking', title: 'Device Tracking', description: 'Track devices used to access your account' },
                { id: 'biometric', title: 'Biometric Authentication', description: 'Use fingerprint or face recognition' },
              ].map((item) => (
                <div key={item.id} className="flex items-center justify-between space-x-2">
                  <div className="flex-1 space-y-1">
                    <p className="font-medium">{item.title}</p>
                    <p className="text-sm text-muted-foreground">{item.description}</p>
                  </div>
                  <Switch id={item.id} defaultChecked={true} />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Security;
