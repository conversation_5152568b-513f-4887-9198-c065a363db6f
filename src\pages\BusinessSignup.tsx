import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { BusinessAccount } from '@/types/account';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, ArrowRight, Eye, EyeOff, Check, Shield, User, Mail, Phone, Home, Calendar, Lock, Building, Briefcase } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

const BUSINESS_TYPES = [
  'Retail',
  'E-commerce',
  'Manufacturing',
  'Technology',
  'Healthcare',
  'Financial Services',
  'Education',
  'Food & Beverage',
  'Transportation',
  'Real Estate',
  'Construction',
  'Agriculture',
  'Entertainment',
  'Hospitality',
  'Consulting',
  'Other'
];

const BusinessSignup = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showPin, setShowPin] = useState(false);
  const [showConfirmPin, setShowConfirmPin] = useState(false);
  const { register, isLoading } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<Partial<BusinessAccount>>({
    fullName: '',
    businessName: '',
    address: '',
    businessAddress: '',
    email: '',
    phoneNumber: '',
    businessType: '',
    pin: '',
  });

  const [confirmPin, setConfirmPin] = useState('');
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [deviceFingerprint, setDeviceFingerprint] = useState('');

  React.useEffect(() => {
    const generateFingerprint = () => {
      const userAgent = navigator.userAgent;
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const language = navigator.language;
      
      const fingerprintString = `${userAgent}-${screenWidth}x${screenHeight}-${timeZone}-${language}`;
      const hash = Array.from(fingerprintString).reduce(
        (hash, char) => ((hash << 5) - hash) + char.charCodeAt(0), 0
      );
      
      return Math.abs(hash).toString(36);
    };
    
    setDeviceFingerprint(generateFingerprint());
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validateStep1 = () => {
    const { fullName, businessName, email, phoneNumber } = formData;
    
    if (!fullName || !businessName || !email || !phoneNumber) {
      toast.error('Please fill in all required fields');
      return false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast.error('Please enter a valid email address');
      return false;
    }
    
    const phoneRegex = /^(\+234|0)[0-9]{10}$/;
    if (!phoneRegex.test(phoneNumber)) {
      toast.error('Please enter a valid Nigerian phone number');
      return false;
    }
    
    return true;
  };

  const validateStep2 = () => {
    const { address, businessAddress, businessType } = formData;
    
    if (!address || !businessAddress || !businessType) {
      toast.error('Please fill in all required fields');
      return false;
    }
    
    return true;
  };

  const validateStep3 = () => {
    const { pin } = formData;
    
    if (!pin || pin.length !== 6 || !/^\d+$/.test(pin)) {
      toast.error('Please enter a valid 6-digit PIN');
      return false;
    }
    
    if (pin !== confirmPin) {
      toast.error('PINs do not match');
      return false;
    }
    
    if (!acceptedTerms) {
      toast.error('You must accept the terms and conditions');
      return false;
    }
    
    return true;
  };

  const handleNext = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
      console.log('Security event: Business signup step 1 completed', {
        email: formData.email,
        businessName: formData.businessName,
        deviceFingerprint,
        timestamp: new Date().toISOString(),
        ipAddress: '***********',
      });
    } else if (currentStep === 2 && validateStep2()) {
      setCurrentStep(3);
      console.log('Security event: Business signup step 2 completed', {
        email: formData.email,
        businessName: formData.businessName,
        deviceFingerprint,
        timestamp: new Date().toISOString(),
      });
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep3()) return;
    
    try {
      const completeData: BusinessAccount = {
        ...formData as BusinessAccount,
        bvnNumber: '',
        governmentId: '',
        cacNumber: '',
        accountNumber: '',
        balance: 0,
        isVerified: false,
        tier: 'tier1',
        transactionLimits: {
          daily: 100000,
          weekly: 500000,
          monthly: 2000000,
          perTransaction: 50000
        }
      };
      
      await register(completeData, 'business');
      navigate('/business-dashboard');
    } catch (error) {
      console.error('Registration error:', error);
    }
  };

  const renderProgressBar = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {[1, 2, 3].map((step) => (
          <div key={step} className="flex flex-col items-center">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium ${
              currentStep >= step
                ? 'bg-kojaPrimary text-white'
                : 'bg-gray-200 text-gray-500'
            }`}>
              {currentStep > step ? <Check size={18} /> : step}
            </div>
            <span className={`text-xs mt-2 ${currentStep >= step ? 'text-kojaPrimary font-medium' : 'text-gray-500'}`}>
              {step === 1 ? 'Business Info' : step === 2 ? 'Additional Info' : 'Security'}
            </span>
          </div>
        ))}
      </div>
      <div className="flex mt-2">
        <div className={`h-1 flex-1 ${currentStep >= 2 ? 'bg-kojaPrimary' : 'bg-gray-200'}`}></div>
        <div className={`h-1 flex-1 ${currentStep >= 3 ? 'bg-kojaPrimary' : 'bg-gray-200'}`}></div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-b from-kojaLight to-white p-4 py-8">
      <Helmet>
        <title>Create Business Account | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-xl">
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center justify-center mb-6">
            <div className="flex items-center justify-center w-14 h-14 rounded-full bg-kojaPrimary text-white font-bold text-2xl shadow-lg">
              K
            </div>
          </Link>
          <h1 className="text-3xl font-bold text-kojaDark">Create Your <span className="text-kojaPrimary">Koja</span><span className="text-kojaYellow">Pay</span> Business Account</h1>
          <p className="text-kojaGray mt-2">Empower your business with secure banking solutions</p>
        </div>

        <Card className="shadow-xl border-none rounded-2xl overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-kojaSecondary to-kojaYellow text-white pb-4">
            <CardTitle className="text-xl">Business Account Registration</CardTitle>
            <CardDescription className="text-white/80">Step {currentStep} of 3</CardDescription>
          </CardHeader>
          
          <CardContent className="p-6">
            {renderProgressBar()}
            
            <form onSubmit={handleSubmit} className="space-y-6">
              {currentStep === 1 && (
                <div className="space-y-4 animate-fadeIn">
                  <div>
                    <Label htmlFor="businessName" className="text-kojaDark font-medium flex items-center">
                      <Building size={16} className="mr-2 text-kojaPrimary" />
                      Business Name
                    </Label>
                    <Input
                      id="businessName"
                      name="businessName"
                      placeholder="Enter your business name"
                      value={formData.businessName}
                      onChange={handleInputChange}
                      className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="fullName" className="text-kojaDark font-medium flex items-center">
                      <User size={16} className="mr-2 text-kojaPrimary" />
                      Owner/Manager Full Name
                    </Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      placeholder="Enter your full name"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="text-kojaDark font-medium flex items-center">
                      <Mail size={16} className="mr-2 text-kojaPrimary" />
                      Business Email Address
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter your business email address"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="phoneNumber" className="text-kojaDark font-medium flex items-center">
                      <Phone size={16} className="mr-2 text-kojaPrimary" />
                      Business Phone Number
                    </Label>
                    <Input
                      id="phoneNumber"
                      name="phoneNumber"
                      placeholder="Enter your business phone number"
                      value={formData.phoneNumber}
                      onChange={handleInputChange}
                      className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Format: *********** or +2348012345678</p>
                  </div>
                </div>
              )}
              
              {currentStep === 2 && (
                <div className="space-y-4 animate-fadeIn">
                  <div>
                    <Label htmlFor="businessType" className="text-kojaDark font-medium flex items-center">
                      <Briefcase size={16} className="mr-2 text-kojaPrimary" />
                      Business Type
                    </Label>
                    <Select 
                      value={formData.businessType} 
                      onValueChange={(value) => handleSelectChange('businessType', value)}
                    >
                      <SelectTrigger className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary">
                        <SelectValue placeholder="Select business type" />
                      </SelectTrigger>
                      <SelectContent>
                        {BUSINESS_TYPES.map((type) => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="address" className="text-kojaDark font-medium flex items-center">
                      <Home size={16} className="mr-2 text-kojaPrimary" />
                      Owner/Manager Residential Address
                    </Label>
                    <Input
                      id="address"
                      name="address"
                      placeholder="Enter your residential address"
                      value={formData.address}
                      onChange={handleInputChange}
                      className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                      required
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="businessAddress" className="text-kojaDark font-medium flex items-center">
                      <Building size={16} className="mr-2 text-kojaPrimary" />
                      Business Address
                    </Label>
                    <Input
                      id="businessAddress"
                      name="businessAddress"
                      placeholder="Enter your business address"
                      value={formData.businessAddress}
                      onChange={handleInputChange}
                      className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                      required
                    />
                  </div>
                  
                  <div className="bg-amber-50 p-4 rounded-xl border border-amber-100">
                    <div className="flex items-start">
                      <Shield className="h-5 w-5 text-amber-600 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-amber-800">Business Verification</h3>
                        <p className="text-sm text-amber-700 mt-1">
                          After registration, you'll need to complete your business verification by providing your CAC registration number and other business documents to unlock higher transaction limits.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {currentStep === 3 && (
                <div className="space-y-4 animate-fadeIn">
                  <div>
                    <Label htmlFor="pin" className="text-kojaDark font-medium flex items-center">
                      <Lock size={16} className="mr-2 text-kojaPrimary" />
                      Create 6-Digit PIN
                    </Label>
                    <div className="relative">
                      <Input
                        id="pin"
                        name="pin"
                        type={showPin ? 'text' : 'password'}
                        placeholder="Enter 6-digit PIN"
                        value={formData.pin}
                        onChange={handleInputChange}
                        className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                        maxLength={6}
                        pattern="[0-9]{6}"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                        onClick={() => setShowPin(!showPin)}
                      >
                        {showPin ? <EyeOff size={18} /> : <Eye size={18} />}
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Your PIN will be used to authorize transactions</p>
                  </div>
                  
                  <div>
                    <Label htmlFor="confirmPin" className="text-kojaDark font-medium flex items-center">
                      <Lock size={16} className="mr-2 text-kojaPrimary" />
                      Confirm PIN
                    </Label>
                    <div className="relative">
                      <Input
                        id="confirmPin"
                        type={showConfirmPin ? 'text' : 'password'}
                        placeholder="Confirm your PIN"
                        value={confirmPin}
                        onChange={(e) => setConfirmPin(e.target.value)}
                        className="mt-1 rounded-xl border-gray-300 focus:border-kojaPrimary focus:ring-kojaPrimary"
                        maxLength={6}
                        pattern="[0-9]{6}"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                        onClick={() => setShowConfirmPin(!showConfirmPin)}
                      >
                        {showConfirmPin ? <EyeOff size={18} /> : <Eye size={18} />}
                      </button>
                    </div>
                  </div>
                  
                  <div className="bg-blue-50 p-4 rounded-xl border border-blue-100">
                    <div className="flex items-start">
                      <Shield className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-blue-800">Enhanced Security</h3>
                        <p className="text-sm text-blue-700 mt-1">
                          Your business account comes with enhanced security features including fraud detection, real-time transaction monitoring, and multi-factor authentication options.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3 pt-2">
                    <Checkbox 
                      id="terms" 
                      checked={acceptedTerms}
                      onCheckedChange={(checked) => setAcceptedTerms(checked as boolean)}
                    />
                    <Label htmlFor="terms" className="text-sm text-gray-600">
                      I agree to KojaPay's{' '}
                      <Link to="/terms" className="text-kojaPrimary hover:underline">Terms of Service</Link>,{' '}
                      <Link to="/privacy" className="text-kojaPrimary hover:underline">Privacy Policy</Link>, and{' '}
                      <Link to="/business-terms" className="text-kojaPrimary hover:underline">Business Account Agreement</Link>
                    </Label>
                  </div>
                </div>
              )}
            </form>
          </CardContent>
          
          <CardFooter className="flex justify-between p-6 bg-gray-50 border-t">
            {currentStep > 1 ? (
              <Button
                type="button"
                variant="outline"
                onClick={handleBack}
                className="flex items-center"
              >
                <ArrowLeft size={16} className="mr-2" />
                Back
              </Button>
            ) : (
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/')}
                className="flex items-center"
              >
                <ArrowLeft size={16} className="mr-2" />
                Home
              </Button>
            )}
            
            {currentStep < 3 ? (
              <Button
                type="button"
                onClick={handleNext}
                className="bg-kojaPrimary hover:bg-kojaSecondary flex items-center"
              >
                Next
                <ArrowRight size={16} className="ml-2" />
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleSubmit}
                className="bg-kojaPrimary hover:bg-kojaSecondary flex items-center"
                disabled={isLoading}
              >
                {isLoading ? 'Creating Account...' : 'Create Business Account'}
                {!isLoading && <Check size={16} className="ml-2" />}
              </Button>
            )}
          </CardFooter>
        </Card>
        
        <div className="text-center mt-6">
          <p className="text-gray-600">
            Already have a business account?{' '}
            <Link to="/business-login" className="text-kojaPrimary font-medium hover:underline">
              Log In
            </Link>
          </p>
          <p className="text-gray-600 mt-2">
            Need a personal account?{' '}
            <Link to="/personal-signup" className="text-kojaPrimary font-medium hover:underline">
              Create Personal Account
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default BusinessSignup;
