
import React from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, Lock, Key, RotateCw, Smartphone, FileWarning, Eye, EyeOff } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

const BusinessSecurity = () => {
  return (
    <BusinessLayout pageTitle="Business Security">
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Security Settings</h1>
        <p className="text-muted-foreground">Manage your business account security and verification settings</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Two-factor authentication */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone size={18} className="text-amber-500" />
                Two-factor Authentication
              </CardTitle>
              <CardDescription>
                Add an extra layer of security to your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">SMS Verification</Label>
                    <p className="text-sm text-muted-foreground">
                      Require an SMS code for all transactions
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Email Verification</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive email alerts for high-value transactions
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Authenticator App</Label>
                    <p className="text-sm text-muted-foreground">
                      Use an authenticator app for additional security
                    </p>
                  </div>
                  <Switch />
                </div>
              </div>
              
              <Button variant="outline" className="w-full mt-4 rounded-[20px]">
                <Key className="mr-2 h-4 w-4" />
                Set Up Authenticator
              </Button>
            </CardContent>
          </Card>
          
          {/* Security tips card */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield size={18} className="text-blue-600" />
                Security Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="bg-white/70 p-3 rounded-[20px] shadow-sm">
                <h3 className="font-medium text-sm text-blue-800">Use Strong Passwords</h3>
                <p className="text-xs text-slate-600 mt-1">
                  Create unique passwords with letters, numbers, and symbols.
                </p>
              </div>
              <div className="bg-white/70 p-3 rounded-[20px] shadow-sm">
                <h3 className="font-medium text-sm text-blue-800">Monitor Account Activity</h3>
                <p className="text-xs text-slate-600 mt-1">
                  Regularly review transaction history for suspicious activities.
                </p>
              </div>
              <div className="bg-white/70 p-3 rounded-[20px] shadow-sm">
                <h3 className="font-medium text-sm text-blue-800">Update Contact Info</h3>
                <p className="text-xs text-slate-600 mt-1">
                  Keep your email and phone number updated for security alerts.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Security settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock size={18} className="text-purple-500" />
              Account Security
            </CardTitle>
            <CardDescription>
              Configure your business account security preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Require OTP for All Transfers</Label>
                    <p className="text-sm text-muted-foreground">
                      Always require OTP verification for transfers
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Require OTP for High-Value Transactions</Label>
                    <p className="text-sm text-muted-foreground">
                      Verify transactions over ₦100,000
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Transaction Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Get real-time alerts for all transactions
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Login Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive alerts when someone logs into your account
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Hide Transaction Amounts</Label>
                    <p className="text-sm text-muted-foreground">
                      Hide amounts in notifications for privacy
                    </p>
                  </div>
                  <Switch />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Staff Access Logs</Label>
                    <p className="text-sm text-muted-foreground">
                      Keep detailed logs of staff account access
                    </p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex flex-wrap gap-2 justify-end">
              <Button variant="outline" className="rounded-[20px]">
                <RotateCw className="mr-2 h-4 w-4" />
                Reset Password
              </Button>
              <Button variant="outline" className="rounded-[20px]">
                <FileWarning className="mr-2 h-4 w-4" />
                View Access Logs
              </Button>
              <Button className="rounded-[20px]">
                <Shield className="mr-2 h-4 w-4" />
                Save Settings
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </BusinessLayout>
  );
};

export default BusinessSecurity;
