
import React, { useState, useEffect } from 'react';
import { <PERSON>ert<PERSON><PERSON>gle, Shield, CheckCircle, XCircle } from 'lucide-react';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useSecurity } from '@/contexts/SecurityContext';
import { toast } from 'sonner';

interface TransactionFraudCheckProps {
  transaction: {
    id?: string;
    amount: number;
    recipientId: string;
    recipientName: string;
    isNewRecipient?: boolean;
    isInternational?: boolean;
    description?: string;
  };
  onContinue: () => void;
  onCancel: () => void;
}

const TransactionFraudCheck: React.FC<TransactionFraudCheckProps> = ({
  transaction,
  onContinue,
  onCancel
}) => {
  const { detectFraud, settings: securitySettings } = useSecurity();
  const [isChecking, setIsChecking] = useState(true);
  const [checkResult, setCheckResult] = useState<{
    isFraudulent: boolean;
    riskScore: number;
    riskFactors: string[];
    action: 'allow' | 'review' | 'block';
  } | null>(null);
  
  useEffect(() => {
    const performFraudCheck = async () => {
      setIsChecking(true);
      try {
        // Prepare transaction with additional context
        const enrichedTransaction = {
          ...transaction,
          isNewRecipient: transaction.isNewRecipient ?? Math.random() > 0.7, // Mock for demo
          isInternational: transaction.isInternational ?? Math.random() > 0.8, // Mock for demo
          timestamp: new Date().toISOString(),
          deviceFingerprint: 'mock-device-fingerprint-' + Math.random().toString(36).substring(2, 15),
          ipAddress: '192.168.1.' + Math.floor(Math.random() * 255),
        };
        
        // Perform fraud detection
        const result = await detectFraud(enrichedTransaction);
        setCheckResult(result);
        
        // Show toast based on result
        if (result.action === 'block') {
          toast.error('This transaction has been blocked due to high fraud risk');
        } else if (result.action === 'review') {
          toast.warning('This transaction requires additional review');
        }
      } catch (error) {
        console.error('Error during fraud check:', error);
        toast.error('Unable to complete security check');
        // Default to review if check fails
        setCheckResult({
          isFraudulent: false,
          riskScore: 0,
          riskFactors: ['check_failed'],
          action: 'review'
        });
      } finally {
        setIsChecking(false);
      }
    };
    
    performFraudCheck();
  }, [transaction, detectFraud]);
  
  const getRiskBadge = (score: number) => {
    if (score >= 50) {
      return <Badge variant="destructive" className="text-white">High Risk</Badge>;
    } else if (score >= 25) {
      return <Badge variant="default" className="bg-amber-500 text-white">Medium Risk</Badge>;
    } else {
      return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Low Risk</Badge>;
    }
  };
  
  const formatRiskFactor = (factor: string) => {
    return factor
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };
  
  if (isChecking) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-blue-500 animate-pulse" />
            Performing Security Check
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="flex flex-col items-center justify-center">
            <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-blue-200 animate-spin mb-4"></div>
            <p className="text-gray-600">
              Verifying transaction safety...
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  if (!checkResult) {
    return null;
  }
  
  if (checkResult.action === 'allow') {
    // Auto-continue for low-risk transactions
    setTimeout(() => {
      onContinue();
    }, 500);
    
    return (
      <Card className="w-full max-w-md mx-auto border-green-200">
        <CardHeader className="bg-green-50 border-b border-green-100">
          <CardTitle className="flex items-center text-green-700">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Transaction Approved
          </CardTitle>
        </CardHeader>
        <CardContent className="py-4">
          <p className="text-gray-600">This transaction has passed all security checks.</p>
        </CardContent>
      </Card>
    );
  }
  
  if (checkResult.action === 'block') {
    return (
      <Card className="w-full max-w-md mx-auto border-red-200">
        <CardHeader className="bg-red-50 border-b border-red-100">
          <CardTitle className="flex items-center text-red-700">
            <XCircle className="h-5 w-5 mr-2 text-red-600" />
            Transaction Blocked
          </CardTitle>
        </CardHeader>
        <CardContent className="py-4">
          <div className="mb-4">
            <p className="text-gray-700 mb-2">
              This transaction has been blocked due to security concerns:
            </p>
            <ul className="list-disc pl-5 space-y-1 text-gray-600">
              {checkResult.riskFactors.map((factor, index) => (
                <li key={index}>{formatRiskFactor(factor)}</li>
              ))}
            </ul>
          </div>
          
          <div className="bg-red-50 p-3 rounded-lg border border-red-100 mt-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 mr-2" />
              <div>
                <p className="text-sm text-red-700">
                  For your security, you cannot proceed with this transaction. Please contact our support team for assistance.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t border-red-100 bg-red-50">
          <Button 
            variant="outline" 
            className="w-full"
            onClick={onCancel}
          >
            Return to Dashboard
          </Button>
        </CardFooter>
      </Card>
    );
  }
  
  // Review case
  return (
    <Card className="w-full max-w-md mx-auto border-amber-200">
      <CardHeader className="bg-amber-50 border-b border-amber-100">
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center text-amber-700">
            <AlertTriangle className="h-5 w-5 mr-2 text-amber-600" />
            Additional Verification Required
          </CardTitle>
          {getRiskBadge(checkResult.riskScore)}
        </div>
      </CardHeader>
      <CardContent className="py-4">
        <div className="mb-4">
          <p className="text-gray-700 mb-2">
            This transaction requires additional verification due to the following risk factors:
          </p>
          <ul className="list-disc pl-5 space-y-1 text-gray-600">
            {checkResult.riskFactors.map((factor, index) => (
              <li key={index}>{formatRiskFactor(factor)}</li>
            ))}
          </ul>
        </div>
        
        <div className="bg-amber-50 p-3 rounded-lg border border-amber-100 mt-4">
          <div className="flex items-start">
            <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 mr-2" />
            <div>
              <p className="text-sm text-amber-700">
                Please confirm that you want to proceed with this transaction. If you did not initiate this transaction, click "Cancel" immediately.
              </p>
            </div>
          </div>
        </div>
        
        <div className="mt-4">
          <p className="text-sm font-medium mb-1">Transaction Details:</p>
          <div className="bg-white p-3 rounded-lg border border-gray-200">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-gray-500">Amount:</div>
              <div className="font-medium">u20a6{transaction.amount.toLocaleString()}</div>
              
              <div className="text-gray-500">Recipient:</div>
              <div className="font-medium">{transaction.recipientName}</div>
              
              {transaction.description && (
                <>
                  <div className="text-gray-500">Description:</div>
                  <div className="font-medium">{transaction.description}</div>
                </>
              )}
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t border-amber-100 bg-amber-50 flex justify-between">
        <Button 
          variant="outline" 
          className="w-1/2 mr-2"
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button 
          variant="default" 
          className="w-1/2 bg-amber-600 hover:bg-amber-700"
          onClick={onContinue}
        >
          Proceed Anyway
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TransactionFraudCheck;
