
import * as React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  keywords?: string;
  ogImage?: string;
  ogType?: 'website' | 'article';
  twitterCard?: 'summary' | 'summary_large_image';
}

const SEO: React.FC<SEOProps> = ({
  title = 'KojaPay - Your Financial Partner',
  description = 'KojaPay Finance App - Secure Banking, Payments and Financial Management',
  canonical,
  keywords = 'banking, payments, financial management, money transfer, kojapay',
  ogImage = '/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png',
  ogType = 'website',
  twitterCard = 'summary_large_image'
}) => {
  // Safely get current URL and site URL
  const currentUrl = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      return canonical || window.location.href;
    }
    return canonical || '';
  }, [canonical]);

  const siteUrl = React.useMemo(() => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return '';
  }, []);

  const fullOgImage = React.useMemo(() => {
    if (ogImage.startsWith('http')) {
      return ogImage;
    }
    return siteUrl ? `${siteUrl}${ogImage}` : ogImage;
  }, [ogImage, siteUrl]);
  
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      {currentUrl && <link rel="canonical" href={currentUrl} />}
      
      {/* Open Graph Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      {currentUrl && <meta property="og:url" content={currentUrl} />}
      <meta property="og:image" content={fullOgImage} />
      <meta property="og:type" content={ogType} />
      <meta property="og:site_name" content="KojaPay" />
      
      {/* Twitter Tags */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullOgImage} />
      
      {/* Additional SEO tags */}
      <meta name="robots" content="index, follow" />
      <meta name="googlebot" content="index, follow" />
    </Helmet>
  );
};

export default SEO;
