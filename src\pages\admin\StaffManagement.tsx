
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  MoreHorizontal, 
  User, 
  Users, 
  Eye, 
  Edit, 
  Trash,
  Plus,
  Filter,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Building,
  FileText,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Avatar } from '@/components/ui/avatar';

const StaffManagement = () => {
  const staff = [
    { 
      id: 'STF001', 
      name: 'John Adeyemi', 
      email: '<EMAIL>',
      department: 'IT',
      role: 'System Administrator',
      location: 'Lagos Office',
      joinDate: '2022-03-15',
      status: 'Active'
    },
    { 
      id: 'STF002', 
      name: 'Sarah Ibrahim', 
      email: '<EMAIL>',
      department: 'Finance',
      role: 'Finance Manager',
      location: 'Abuja Office',
      joinDate: '2022-05-20',
      status: 'Active'
    },
    { 
      id: 'STF003', 
      name: 'Michael Okonkwo', 
      email: '<EMAIL>',
      department: 'Customer Support',
      role: 'Support Team Lead',
      location: 'Lagos Office',
      joinDate: '2022-07-08',
      status: 'Active'
    },
    { 
      id: 'STF004', 
      name: 'Elizabeth Nwosu', 
      email: '<EMAIL>',
      department: 'Marketing',
      role: 'Marketing Specialist',
      location: 'Port Harcourt Office',
      joinDate: '2022-09-14',
      status: 'On Leave'
    },
    { 
      id: 'STF005', 
      name: 'David Okoli', 
      email: '<EMAIL>',
      department: 'Security',
      role: 'Security Analyst',
      location: 'Lagos Office',
      joinDate: '2023-01-10',
      status: 'Active'
    },
  ];

  return (
    <AdminLayout pageTitle="Staff Management">
      <Helmet>
        <title>Staff Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Staff</CardTitle>
              <CardDescription>All employees</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">47</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">New Hires</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">5</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Departments</CardTitle>
              <CardDescription>Active departments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">8</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">On Leave</CardTitle>
              <CardDescription>Currently away</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">3</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="staff" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="staff">Staff Directory</TabsTrigger>
            <TabsTrigger value="departments">Departments</TabsTrigger>
            <TabsTrigger value="onboarding">Onboarding</TabsTrigger>
          </TabsList>
          
          <TabsContent value="staff">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Staff Management</CardTitle>
                  <CardDescription>Manage staff members and their information</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    <Plus size={16} className="mr-2" />
                    Add Staff Member
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search staff..." 
                      className="pl-9"
                    />
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>List of staff members</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Staff ID</TableHead>
                        <TableHead>Name</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Join Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {staff.map((member) => (
                        <TableRow key={member.id}>
                          <TableCell className="font-medium">{member.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8 rounded-full">
                                <img 
                                  src={`https://ui-avatars.com/api/?name=${member.name}&background=1231B8&color=fff&size=32`} 
                                  alt={member.name} 
                                />
                              </Avatar>
                              <div>
                                <div className="font-medium">{member.name}</div>
                                <div className="text-xs text-gray-500">{member.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{member.department}</TableCell>
                          <TableCell>{member.role}</TableCell>
                          <TableCell>{member.location}</TableCell>
                          <TableCell>{member.joinDate}</TableCell>
                          <TableCell>
                            <Badge variant={member.status === 'Active' ? 'outline' : 'secondary'}>
                              {member.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                              {member.status === 'On Leave' && <Calendar size={12} className="mr-1" />}
                              {member.status === 'Inactive' && <XCircle size={12} className="mr-1" />}
                              {member.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Profile</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Edit Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Mail className="mr-2 h-4 w-4" />
                                  <span>Send Email</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Deactivate</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="departments">
            <Card>
              <CardHeader>
                <CardTitle>Department Management</CardTitle>
                <CardDescription>Manage company departments and structures</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <Building size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Organizational Structure</h3>
                  <p className="text-gray-500 mb-6">Manage departments, teams and reporting structures</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Manage Departments</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="onboarding">
            <Card>
              <CardHeader>
                <CardTitle>Staff Onboarding</CardTitle>
                <CardDescription>Employee onboarding processes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <FileText size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Onboarding Processes</h3>
                  <p className="text-gray-500 mb-6">Manage new employee onboarding, training and setup</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Manage Onboarding</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default StaffManagement;
