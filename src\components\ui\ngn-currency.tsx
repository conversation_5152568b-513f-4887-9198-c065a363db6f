
import React from 'react';

interface NGNCurrencyProps {
  amount: number | string;
  className?: string;
  showSymbol?: boolean;
  showDecimal?: boolean;
  decimalPlaces?: number;
}

/**
 * NGNCurrency component for displaying Nigerian Naira currency
 * 
 * @param amount - The amount to display
 * @param className - Optional CSS class names
 * @param showSymbol - Whether to show the ₦ symbol (default: true)
 * @param showDecimal - Whether to show decimal places (default: true)
 * @param decimalPlaces - Number of decimal places to show (default: 2)
 */
const NGNCurrency: React.FC<NGNCurrencyProps> = ({
  amount,
  className = '',
  showSymbol = true,
  showDecimal = true,
  decimalPlaces = 2
}) => {
  // Convert string amount to number if needed
  const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  // Handle special cases
  if (isNaN(numericAmount)) {
    return <span className={className}>₦0.00</span>;
  }
  
  // Format the amount
  const formattedAmount = new Intl.NumberFormat('en-NG', {
    minimumFractionDigits: showDecimal ? decimalPlaces : 0,
    maximumFractionDigits: showDecimal ? decimalPlaces : 0,
  }).format(numericAmount);
  
  return (
    <span className={className}>
      {showSymbol ? '₦' : ''}{formattedAmount}
    </span>
  );
};

export default NGNCurrency;
