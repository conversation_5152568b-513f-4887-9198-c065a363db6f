
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Avatar } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus } from 'lucide-react';

// Define the QuickTransfer component data types
interface TransferRecipient {
  id: string;
  name: string;
  avatar?: string;
}

interface QuickTransferProps {
  recipients: TransferRecipient[];
  onAddRecipient?: () => void;
  onSelectRecipient?: (id: string) => void;
  onSendMoney?: (amount: number, recipientId: string) => void;
}

const QuickTransfer: React.FC<QuickTransferProps> = ({
  recipients = [],
  onAddRecipient,
  onSelectRecipient,
  onSendMoney
}) => {
  const [amount, setAmount] = React.useState<string>('');
  const [selectedRecipient, setSelectedRecipient] = React.useState<string | null>(null);

  const handleSelectRecipient = (id: string) => {
    setSelectedRecipient(id);
    if (onSelectRecipient) {
      onSelectRecipient(id);
    }
  };

  const handleSendMoney = () => {
    if (!amount || !selectedRecipient) return;

    if (onSendMoney) {
      onSendMoney(parseFloat(amount), selectedRecipient);
    }
    
    // Reset after sending
    setAmount('');
    setSelectedRecipient(null);
  };

  return (
    <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
      <CardHeader>
        <CardTitle className="text-lg font-medium">Quick Transfer</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-between">
          <div className="flex flex-col items-center gap-2">
            <Avatar 
              className="h-12 w-12 bg-gray-100 text-kojaPrimary hover:scale-105 transition-transform cursor-pointer"
              onClick={onAddRecipient}
            >
              <Plus className="h-6 w-6" />
            </Avatar>
            <span className="text-xs font-medium">Add</span>
          </div>
          
          {recipients.map((recipient) => (
            <div key={recipient.id} className="flex flex-col items-center gap-2">
              <Avatar 
                className={`h-12 w-12 hover:scale-105 transition-transform cursor-pointer ${
                  selectedRecipient === recipient.id ? 'ring-2 ring-kojaPrimary' : ''
                }`}
                onClick={() => handleSelectRecipient(recipient.id)}
              >
                {recipient.avatar ? (
                  <img src={recipient.avatar} alt={recipient.name} />
                ) : (
                  <img src={`https://ui-avatars.com/api/?name=${recipient.name}&background=random`} alt={recipient.name} />
                )}
              </Avatar>
              <span className="text-xs font-medium">{recipient.name}</span>
            </div>
          ))}
        </div>
        
        <div>
          <Input 
            type="text" 
            placeholder="Enter Amount" 
            className="border-gray-200 focus:border-kojaPrimary"
            value={amount}
            onChange={(e) => setAmount(e.target.value)}
          />
        </div>
        
        <Button 
          className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
          onClick={handleSendMoney}
          disabled={!amount || !selectedRecipient}
        >
          Send Money
        </Button>
      </CardContent>
    </Card>
  );
};

export default QuickTransfer;
