import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Dialog<PERSON>ontent, 
  Di<PERSON>Header, 
  Di<PERSON>Title,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { MicroButton } from '@/components/ui/micro-button';
import { Loader2, Send, BuildingIcon, Users, ArrowRight, X, AlertCircle, CheckCircle } from 'lucide-react';
import TransactionStatus from '@/components/TransactionStatus';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { BankOneApi } from '@/services/bankOneApi';
import TransferForm from './TransferForm';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose
} from '@/components/ui/drawer';

interface TransferDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isDrawer?: boolean;
}

type TransferStep = 'select-type' | 'enter-details' | 'processing' | 'complete';
type TransferType = 'same-bank' | 'other-bank';

const TransferDialog: React.FC<TransferDialogProps> = ({ open, onOpenChange, isDrawer }) => {
  const [step, setStep] = useState<TransferStep>('select-type');
  const [transferType, setTransferType] = useState<TransferType>('same-bank');
  const [transferStatus, setTransferStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [transactionReference, setTransactionReference] = useState('');
  const [transactionAmount, setTransactionAmount] = useState(0);
  
  const isMobile = useIsMobile();
  const { toast } = useToast();
  const { user } = useAuth();

  const resetForm = () => {
    setStep('select-type');
    setTransferType('same-bank');
    setTransferStatus('idle');
    setProgress(0);
    setError(null);
    setTransactionReference('');
    setTransactionAmount(0);
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const handleTransferStart = () => {
    setStep('processing');
    setTransferStatus('processing');
    setError(null);

    // Start progress animation
    let progress = 0;
    const interval = setInterval(() => {
      progress += 5;
      setProgress(progress > 70 ? 70 : progress); // Max at 70% until we get confirmation
      if (progress >= 70) {
        clearInterval(interval);
      }
    }, 200);
  };

  const handleTransferSuccess = (reference: string, amount: number) => {
    setTransactionReference(reference);
    setTransactionAmount(amount);
    
    // Complete the progress bar
    setProgress(100);
    setTimeout(() => {
      setTransferStatus('success');
      setStep('complete');
      
      // Attempt to check transaction status after a delay
      setTimeout(async () => {
        try {
          if (reference) {
            const statusResponse = await BankOneApi.getTransactionStatus(reference);
            console.log('Transaction status:', statusResponse);
          }
        } catch (error) {
          console.error('Error checking transaction status:', error);
        }
      }, 5000);
    }, 500);
  };

  const handleTransferError = (errorMessage: string) => {
    setError(errorMessage);
    setTransferStatus('failed');
    setProgress(100); // Complete the progress bar
    
    toast({
      title: "Transfer Failed",
      description: errorMessage,
      variant: "destructive"
    });
  };

  const renderContent = () => {
    switch (step) {
      case 'select-type':
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Select Transfer Type</h3>
            <div className="grid grid-cols-1 gap-4">
              <Card 
                className={`cursor-pointer transition-colors ${transferType === 'same-bank' ? 'bg-primary/5 border-primary/30' : ''}`}
                onClick={() => setTransferType('same-bank')}
              >
                <CardContent className="p-4 flex items-center space-x-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">KojaPay Transfer</p>
                    <p className="text-sm text-muted-foreground">Transfer to another KojaPay account</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card 
                className={`cursor-pointer transition-colors ${transferType === 'other-bank' ? 'bg-primary/5 border-primary/30' : ''}`}
                onClick={() => setTransferType('other-bank')}
              >
                <CardContent className="p-4 flex items-center space-x-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <BuildingIcon className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Other Bank Transfer</p>
                    <p className="text-sm text-muted-foreground">Transfer to any bank account in Nigeria</p>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <Button 
              className="w-full mt-4" 
              onClick={() => setStep('enter-details')}
            >
              Continue
            </Button>
          </div>
        );
      case 'enter-details':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                {transferType === 'same-bank' ? 'KojaPay Transfer' : 'Other Bank Transfer'}
              </h3>
              <MicroButton onClick={() => setStep('select-type')}>
                Change
              </MicroButton>
            </div>
            
            <TransferForm
              transferType={transferType}
              onTransferStart={handleTransferStart}
              onTransferSuccess={handleTransferSuccess}
              onTransferError={handleTransferError}
            />
          </div>
        );
      case 'processing':
        return (
          <div className="space-y-6 text-center">
            <div className="flex flex-col items-center justify-center space-y-2">
              <div className="w-16 h-16 flex items-center justify-center bg-primary/10 rounded-full">
                <Loader2 className="h-8 w-8 text-primary animate-spin" />
              </div>
              <h3 className="text-lg font-semibold">Processing Transfer</h3>
              <p className="text-sm text-muted-foreground">
                Please wait while we process your transfer
              </p>
            </div>
            
            <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
              <div 
                className="h-full bg-primary transition-all duration-300" 
                style={{ width: `${progress}%` }}
              />
            </div>
            
            {error && (
              <div className="flex items-center justify-center text-red-500 space-x-2">
                <AlertCircle className="h-5 w-5" />
                <p className="text-sm">{error}</p>
              </div>
            )}
          </div>
        );
      case 'complete':
        return (
          <div className="space-y-6 text-center">
            {transferStatus === 'success' ? (
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className="w-16 h-16 flex items-center justify-center bg-green-100 rounded-full">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-lg font-semibold">Transfer Successful</h3>
                <div className="bg-green-50 rounded-lg p-4 w-full">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Amount</span>
                    <span className="font-semibold">₦{transactionAmount.toLocaleString()}</span>
                  </div>
                  {transactionReference && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Reference</span>
                      <span className="font-mono text-xs">{transactionReference}</span>
                    </div>
                  )}
                </div>
                <Button
                  className="w-full mt-2"
                  onClick={handleClose}
                >
                  Done
                </Button>
              </div>
            ) : (
              <TransactionStatus 
                status={transferStatus} 
                onClose={handleClose} 
                errorMessage={error || 'An error occurred during the transfer'}
              />
            )}
          </div>
        );
      default:
        return null;
    }
  };

  const Container = isDrawer ? Drawer : Dialog;
  const Content = isDrawer ? DrawerContent : DialogContent;
  const Header = isDrawer ? DrawerHeader : DialogHeader;
  const Title = isDrawer ? DrawerTitle : DialogTitle;
  const Close = isDrawer ? DrawerClose : X;

  return (
    <Container open={open} onOpenChange={handleClose}>
      <Content className="max-w-md mx-auto">
        {step !== 'processing' && step !== 'complete' && (
          <Header className="border-b pb-2 mb-4">
            <div className="flex justify-between items-center">
              <Title>Transfer Funds</Title>
              <button 
                onClick={handleClose}
                className="rounded-full p-1 hover:bg-gray-100"
              >
                <Close className="h-4 w-4" />
              </button>
            </div>
          </Header>
        )}
        {renderContent()}
      </Content>
    </Container>
  );
};

export default TransferDialog;
