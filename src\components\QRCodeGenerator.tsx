
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { QrCode, Copy, Download, Share2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface QRCodeGeneratorProps {
  data?: string;
  size?: number;
  logo?: string;
}

const QRCodeGenerator: React.FC<QRCodeGeneratorProps> = ({ data, size = 200, logo }) => {
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [qrType, setQrType] = useState('payment');
  const [qrGenerated, setQrGenerated] = useState(false);

  // If direct data is provided, consider it already generated
  const hasDirectData = !!data;

  // If the component receives direct data, render just the QR code
  if (hasDirectData) {
    return (
      <div className="qr-code-display flex flex-col items-center justify-center">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 inline-block relative">
          <div className="w-full h-full bg-white flex items-center justify-center" style={{ width: size, height: size }}>
            <QrCode size={size * 0.6} className="text-kojaPrimary" />
            {logo && (
              <div className="absolute inset-0 flex items-center justify-center">
                <img src={logo} alt="Logo" className="w-1/4 h-1/4 object-contain" />
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  const generateQRCode = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate inputs
    if (qrType === 'payment' && (amount === '' || parseFloat(amount) <= 0)) {
      console.error("Invalid amount: Please enter a valid amount greater than zero");
      return;
    }
    
    // In a real app, this would generate a QR code on the backend
    // For now, we'll simulate that the QR is generated
    setQrGenerated(true);
    
    console.log("QR Code Generated: Your QR code has been generated successfully");
  };

  const copyLink = () => {
    // In a real app, this would copy the actual link
    console.log("Link Copied: Payment link copied to clipboard");
  };

  const downloadQR = () => {
    // In a real app, this would download the QR code image
    console.log("QR Code Downloaded: The QR code image has been downloaded");
  };

  const shareQR = () => {
    // In a real app, this would open share options
    console.log("Share Options: Sharing options opened");
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-6 text-gray-800">Generate QR Code</h2>
        
        <form onSubmit={generateQRCode}>
          <div className="space-y-4 mb-6">
            <div>
              <Label htmlFor="qrType" className="text-gray-700">QR Code Type</Label>
              <Select value={qrType} onValueChange={setQrType}>
                <SelectTrigger id="qrType" className="mt-1">
                  <SelectValue placeholder="Select QR code type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="payment">Payment QR</SelectItem>
                  <SelectItem value="store">Store QR</SelectItem>
                  <SelectItem value="account">Account QR</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {qrType === 'payment' && (
              <div>
                <Label htmlFor="amount" className="text-gray-700">Amount (₦)</Label>
                <Input
                  id="amount"
                  type="number"
                  min="0"
                  step="0.01"
                  placeholder="Enter amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="mt-1"
                />
              </div>
            )}
            
            <div>
              <Label htmlFor="description" className="text-gray-700">Description</Label>
              <Input
                id="description"
                placeholder={qrType === 'payment' ? "Payment for..." : "Description"}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="mt-1"
              />
            </div>
          </div>
          
          <Button type="submit" className="w-full flex items-center justify-center text-white">
            <QrCode className="h-4 w-4 mr-2" />
            Generate QR Code
          </Button>
        </form>
      </div>
      
      <div className="bg-white rounded-xl shadow-sm p-6 flex flex-col">
        <h2 className="text-xl font-semibold mb-6 text-gray-800">Your QR Code</h2>
        
        {qrGenerated ? (
          <>
            <div className="qr-container mx-auto mb-6 flex-grow flex items-center justify-center">
              <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 inline-block">
                <div className="w-48 h-48 bg-gray-100 flex items-center justify-center">
                  <QrCode size={120} className="text-kojaPrimary" />
                </div>
                <div className="text-center mt-2 text-sm font-medium text-gray-700">
                  {qrType === 'payment' ? `₦${amount || '0'} Payment` : qrType === 'store' ? 'Store QR' : 'Account QR'}
                </div>
              </div>
            </div>
            
            <div className="flex justify-between">
              <Button variant="outline" onClick={copyLink} className="flex items-center text-gray-700">
                <Copy className="h-4 w-4 mr-2" />
                Copy Link
              </Button>
              <Button variant="outline" onClick={downloadQR} className="flex items-center text-gray-700">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button variant="outline" onClick={shareQR} className="flex items-center text-gray-700">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </>
        ) : (
          <div className="flex-grow flex flex-col items-center justify-center text-center p-6 bg-gray-50 rounded-lg">
            <QrCode size={80} className="text-gray-300 mb-4" />
            <p className="text-gray-600">
              Generated QR codes will appear here. Fill out the form and click "Generate QR Code".
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default QRCodeGenerator;
