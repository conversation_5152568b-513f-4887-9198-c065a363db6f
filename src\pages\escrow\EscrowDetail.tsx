import React from 'react';
import { useParams } from 'react-router-dom';
import TransactionStatus, { TransactionStatusType, normalizeTransactionStatus } from '@/components/TransactionStatus';
import { EscrowStatus, Escrow, Dispute } from '@/types/escrow';

// Fix escrow detail page by ensuring it uses the TransactionStatusType
const statusMapping: Record<EscrowStatus, TransactionStatusType> = {
  'active': 'processing',
  'completed': 'success',
  'cancelled': 'failed',
  'disputed': 'processing',
  'refunded': 'failed',
  'pending': 'processing',
  'released': 'success'
};

interface EscrowState extends Omit<Escrow, 'dispute'> {
  dispute?: Dispute;
}

const EscrowDetail = () => {
  const { id } = useParams();
  const [escrow, setEscrow] = React.useState<EscrowState>({
    id: id || 'ESC-123456',
    orderId: 'ORD-123',
    title: 'Order #ORD-123 Escrow',
    description: 'Escrow for order #ORD-123',
    amount: 25000,
    currency: 'NGN',
    buyerId: 'user123',
    sellerId: 'seller456',
    buyer: {
      name: '<PERSON>',
      email: '<EMAIL>'
    },
    seller: {
      name: 'Store Owner',
      email: '<EMAIL>'
    },
    status: 'active',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
  });
  
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  const [showDispute, setShowDispute] = React.useState(false);
  const [disputeReason, setDisputeReason] = React.useState('');
  
  React.useEffect(() => {
    const fetchEscrow = async () => {
      setLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setLoading(false);
      } catch (err) {
        setError('Failed to load escrow details');
        setLoading(false);
      }
    };
    
    fetchEscrow();
  }, [id]);
  
  const handleReleaseEscrow = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setEscrow(prev => ({
        ...prev,
        status: 'completed' as EscrowStatus,
        updatedAt: new Date().toISOString()
      }));
    } catch (err) {
      setError('Failed to release escrow funds');
    }
  };
  
  const handleCancelEscrow = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setEscrow(prev => ({
        ...prev,
        status: 'cancelled' as EscrowStatus,
        updatedAt: new Date().toISOString()
      }));
    } catch (err) {
      setError('Failed to cancel escrow');
    }
  };
  
  const handleCreateDispute = async () => {
    if (!disputeReason) {
      setError('Please provide a reason for the dispute');
      return;
    }
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setEscrow(prev => ({
        ...prev,
        status: 'disputed' as EscrowStatus,
        updatedAt: new Date().toISOString(),
        dispute: {
          id: `DISP-${Date.now().toString().substring(7)}`,
          escrowId: prev.id,
          orderId: 'ORD-123',
          title: `Dispute for Order #ORD-123`,
          reason: disputeReason,
          details: disputeReason,
          status: 'pending',
          resolution: '',
          createdBy: 'buyer',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          messages: [
            {
              id: `MSG-${Date.now().toString().substring(7)}`,
              disputeId: `DISP-${Date.now().toString().substring(7)}`,
              message: disputeReason,
              content: disputeReason,
              sender: 'buyer',
              createdAt: new Date().toISOString(),
              timestamp: new Date().toISOString()
            }
          ]
        }
      }));
      
      setShowDispute(false);
      setDisputeReason('');
    } catch (err) {
      setError('Failed to create dispute');
    }
  };
  
  const getTransactionStatus = (escrowStatus: EscrowStatus): TransactionStatusType => {
    return statusMapping[escrowStatus] || 'idle';
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-kojaPrimary"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
        {error}
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md overflow-hidden">
        <div className="p-6 border-b">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-kojaDark">{escrow.title}</h1>
            <TransactionStatus status={getTransactionStatus(escrow.status as EscrowStatus)} />
          </div>
          <p className="text-kojaGray mt-2">{escrow.description}</p>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-semibold mb-4">Escrow Details</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-kojaGray">Amount:</span>
                  <span className="font-medium">₦{escrow.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-kojaGray">Status:</span>
                  <span className="font-medium capitalize">{escrow.status}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-kojaGray">Created:</span>
                  <span className="font-medium">{new Date(escrow.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-kojaGray">Last Updated:</span>
                  <span className="font-medium">{new Date(escrow.updatedAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>
            
            <div>
              <h2 className="text-lg font-semibold mb-4">Parties</h2>
              <div className="space-y-4">
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="font-medium">Buyer</div>
                  <div>{escrow.buyer?.name}</div>
                  <div className="text-sm text-kojaGray">{escrow.buyer?.email}</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="font-medium">Seller</div>
                  <div>{escrow.seller?.name}</div>
                  <div className="text-sm text-kojaGray">{escrow.seller?.email}</div>
                </div>
              </div>
            </div>
          </div>
          
          {escrow.dispute && (
            <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-md">
              <h2 className="text-lg font-semibold text-amber-800 mb-2">Dispute Information</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-amber-700">Status:</span>
                  <span className="font-medium capitalize">{escrow.dispute.status}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-amber-700">Reason:</span>
                  <span className="font-medium">{escrow.dispute.reason}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-amber-700">Created:</span>
                  <span className="font-medium">{new Date(escrow.dispute.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
              
              {escrow.dispute.messages && escrow.dispute.messages.length > 0 && (
                <div className="mt-4">
                  <h3 className="font-medium text-amber-800 mb-2">Messages</h3>
                  <div className="space-y-3">
                    {escrow.dispute.messages.map(message => (
                      <div key={message.id} className="p-3 bg-white rounded border border-amber-100">
                        <div className="flex justify-between">
                          <span className="font-medium capitalize">{message.sender}</span>
                          <span className="text-sm text-kojaGray">{new Date(message.timestamp).toLocaleString()}</span>
                        </div>
                        <p className="mt-1">{message.content}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-4">Actions</h2>
            
            <div className="flex flex-wrap gap-4">
              {escrow.status === 'active' && (
                <>
                  <button
                    onClick={handleReleaseEscrow}
                    className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Release Funds
                  </button>
                  <button
                    onClick={handleCancelEscrow}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    Cancel Escrow
                  </button>
                  <button
                    onClick={() => setShowDispute(true)}
                    className="px-4 py-2 bg-amber-500 text-white rounded hover:bg-amber-600"
                  >
                    Raise Dispute
                  </button>
                </>
              )}
              
              {escrow.status === 'disputed' && (
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  View Dispute Details
                </button>
              )}
              
              {escrow.status === 'completed' && (
                <div className="p-3 bg-green-50 text-green-700 rounded-md w-full">
                  This escrow has been completed and funds have been released to the seller.
                </div>
              )}
              
              {escrow.status === 'cancelled' && (
                <div className="p-3 bg-red-50 text-red-700 rounded-md w-full">
                  This escrow has been cancelled and funds have been returned to the buyer.
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      
      {showDispute && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
            <h2 className="text-xl font-bold mb-4">Raise a Dispute</h2>
            <p className="text-kojaGray mb-4">
              Please provide details about your issue with this transaction.
            </p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Reason for Dispute</label>
              <textarea
                className="w-full border rounded-md p-2 h-32"
                value={disputeReason}
                onChange={(e) => setDisputeReason(e.target.value)}
                placeholder="Describe your issue in detail..."
              ></textarea>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDispute(false)}
                className="px-4 py-2 border rounded hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateDispute}
                className="px-4 py-2 bg-amber-500 text-white rounded hover:bg-amber-600"
              >
                Submit Dispute
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EscrowDetail;
