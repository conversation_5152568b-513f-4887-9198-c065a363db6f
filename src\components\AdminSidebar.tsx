import React, { useState } from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Users,
  CreditCard,
  Wallet,
  FileText,
  Settings,
  Bell,
  Shield,
  Store,
  HelpCircle,
  ChevronRight,
  ChevronLeft,
  Landmark,
  BarChart3,
  Receipt,
  Lock,
  MessageSquare,
  QrCode,
  ShoppingBag,
  Key,
  UserCog,
  Cog,
  CircleDollarSign,
  PiggyBank,
  Award,
  FileQuestion,
  AlertTriangle,
  Activity,
  ShieldAlert
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import LogoutButton from '@/components/LogoutButton';

interface NavItem {
  path: string;
  name: string;
  icon: React.ReactNode;
  badge?: string;
  onClick?: () => void;
}

export const AdminSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const { toast } = useToast();
  const logoUrl = "/lovable-uploads/1928aa40-2de0-48fc-ba20-8312b8e3f05b.png";

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const showHelp = () => {
    toast({
      title: "Admin Support",
      description: "Contact system administrator for assistance",
      variant: "success"
    });
  };

  const mainNavItems: NavItem[] = [
    { path: '/admin/dashboard', name: 'Dashboard', icon: <LayoutDashboard size={18} /> },
    { path: '/admin/security-monitoring', name: 'Security Monitor', icon: <ShieldAlert size={18} />, badge: 'New' },
    { path: '/admin/fraud-alerts', name: 'Fraud Alerts', icon: <AlertTriangle size={18} /> },
    { path: '/admin/activity-logs', name: 'Activity Logs', icon: <Activity size={18} /> },
    { path: '/admin/users', name: 'User Management', icon: <Users size={18} /> },
    { path: '/admin/businesses', name: 'Business Accounts', icon: <Store size={18} /> },
    { path: '/admin/transactions', name: 'Transactions', icon: <FileText size={18} /> },
    { path: '/admin/analytics', name: 'Analytics', icon: <BarChart3 size={18} /> }
  ];

  const serviceNavItems: NavItem[] = [
    { path: '/admin/kyc', name: 'KYC Management', icon: <Key size={18} /> },
    { path: '/admin/cards', name: 'Card Management', icon: <CreditCard size={18} /> },
    { path: '/admin/wallets', name: 'Wallet Management', icon: <Wallet size={18} /> },
    { path: '/admin/savings', name: 'Savings Management', icon: <PiggyBank size={18} /> },
    { path: '/admin/loans', name: 'Loans Management', icon: <Landmark size={18} /> },
    { path: '/admin/rewards', name: 'Rewards Management', icon: <Award size={18} /> },
    { path: '/admin/bills', name: 'Bill Payments', icon: <Receipt size={18} /> },
    { path: '/admin/ecommerce', name: 'E-commerce', icon: <ShoppingBag size={18} /> }
  ];

  const settingsNavItems: NavItem[] = [
    { path: '/admin/staff', name: 'Staff Management', icon: <UserCog size={18} /> },
    { path: '/admin/roles', name: 'Roles & Permissions', icon: <Lock size={18} /> },
    { path: '/admin/system', name: 'System Config', icon: <Cog size={18} /> },
    { path: '/admin/settings', name: 'Settings', icon: <Settings size={18} /> },
    { path: '/admin/support', name: 'Help & Support', icon: <MessageSquare size={18} />, onClick: showHelp }
  ];

  return (
    <div className={cn(
      "bg-gradient-to-b from-[#09125a] to-[#1231B8] backdrop-blur-xl text-white flex flex-col h-screen relative z-20 shadow-2xl font-unica border-r border-white/10 no-scrollbar", 
      isCollapsed ? "w-20" : "w-64", 
      isMobile && "w-full h-screen"
    )}>
      <div className="flex items-center justify-between mb-3 px-4 pt-4">
        {!isCollapsed && (
          <div className="flex items-center">
            <img 
              src={logoUrl}
              alt="KojaPay Admin Logo"
              className="h-10 w-10 object-contain"
            />
            <span className="ml-2 text-white font-semibold text-sm">KojaPay Admin</span>
          </div>
        )}
        {isCollapsed && (
          <div className="w-full flex justify-center">
            <img 
              src={logoUrl}
              alt="KojaPay Admin Logo"
              className="h-10 w-10 object-contain"
            />
          </div>
        )}
        {!isMobile && (
          <Button 
            variant="ghost" 
            size="icon" 
            className="text-white hover:bg-white/10 h-8 w-8" 
            onClick={toggleSidebar} 
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <ChevronRight size={15} /> : <ChevronLeft size={15} />}
          </Button>
        )}
      </div>

      {(!isCollapsed || isMobile) && (
        <div 
          className="p-3 bg-white/5 flex items-center gap-3 cursor-pointer hover:bg-white/10 transition-colors mx-4" 
          onClick={() => navigate('/admin/profile')}
        >
          <Avatar className="w-10 h-10 border-2 border-white/30">
            <div className="h-full w-full flex items-center justify-center bg-gradient-to-br from-purple-500 to-indigo-600 text-white">
              {user?.fullName?.charAt(0) || 'A'}
            </div>
          </Avatar>
          <div className="flex flex-col">
            <p className="text-white font-medium text-sm font-unica">{user?.fullName || 'Admin'}</p>
            <p className="text-white/70 text-xs font-unica">Administrator</p>
          </div>
        </div>
      )}

      <nav className="flex-1 flex flex-col justify-between overflow-y-auto no-scrollbar mt-4 px-2">
        <div className="space-y-1">
          <div className="mb-4 grid grid-cols-1 gap-1">
            {mainNavItems.map(item => {
              const isActive = location.pathname === item.path;
              if (item.onClick) {
                return (
                  <button
                    key={item.name}
                    onClick={item.onClick}
                    className={cn(
                      "nav-item", 
                      isCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className={cn(
                        "flex-shrink-0 p-1 flex items-center justify-center",
                        isActive && "bg-white/10"
                      )}>
                        {item.icon}
                      </div>
                      {(!isCollapsed || isMobile) && (
                        <div className="flex items-center justify-between w-full">
                          <span className="text-sm font-unica">{item.name}</span>
                          {item.badge && (
                            <span className="px-2 py-0.5 text-xs bg-yellow-500 text-black rounded-full">
                              {item.badge}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </button>
                );
              }
              return (
                <NavLink 
                  key={item.path} 
                  to={item.path} 
                  className={({isActive}) => cn(
                    "nav-item", 
                    isActive ? "nav-item-active bg-[#FDE314] text-[#000000]" : "", 
                    isCollapsed ? "justify-center" : ""
                  )}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={cn(
                      "flex-shrink-0 p-1 flex items-center justify-center",
                      isActive && "bg-white/10"
                    )}>
                      {item.icon}
                    </div>
                    {(!isCollapsed || isMobile) && (
                      <div className="flex items-center justify-between w-full">
                        <span className="text-sm font-unica">{item.name}</span>
                        {item.badge && (
                          <span className="px-2 py-0.5 text-xs bg-yellow-500 text-black rounded-full">
                            {item.badge}
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </NavLink>
              );
            })}
          </div>

          <div className="border-t border-white/10 my-2"></div>
          
          <div className="mb-4 grid grid-cols-1 gap-1">
            {serviceNavItems.map(item => {
              const isActive = location.pathname === item.path;
              return (
                <NavLink 
                  key={item.path} 
                  to={item.path} 
                  className={({isActive}) => cn(
                    "nav-item", 
                    isActive ? "nav-item-active bg-[#FDE314] text-[#000000]" : "", 
                    isCollapsed ? "justify-center" : ""
                  )}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={cn(
                      "flex-shrink-0 p-1 flex items-center justify-center",
                      isActive && "bg-white/10"
                    )}>
                      {item.icon}
                    </div>
                    {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                  </div>
                </NavLink>
              );
            })}
          </div>

          <div className="border-t border-white/10 my-2"></div>
          
          <div className="grid grid-cols-1 gap-1">
            {settingsNavItems.map(item => {
              const isActive = location.pathname === item.path;
              if (item.onClick) {
                return (
                  <button
                    key={item.name}
                    onClick={item.onClick}
                    className={cn(
                      "nav-item", 
                      isCollapsed ? "justify-center" : ""
                    )}
                  >
                    <div className="flex items-center gap-3 w-full">
                      <div className={cn(
                        "flex-shrink-0 p-1 flex items-center justify-center"
                      )}>
                        {item.icon}
                      </div>
                      {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                    </div>
                  </button>
                );
              }
              return (
                <NavLink 
                  key={item.path} 
                  to={item.path} 
                  className={({isActive}) => cn(
                    "nav-item", 
                    isActive ? "nav-item-active bg-[#FDE314] text-[#000000]" : "", 
                    isCollapsed ? "justify-center" : ""
                  )}
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className={cn(
                      "flex-shrink-0 p-1 flex items-center justify-center",
                      isActive && "bg-white/10"
                    )}>
                      {item.icon}
                    </div>
                    {(!isCollapsed || isMobile) && <span className="text-sm font-unica">{item.name}</span>}
                  </div>
                </NavLink>
              );
            })}
          </div>
        </div>

        <div className="mt-auto p-4">
          <LogoutButton
            className={cn(
              "w-full text-sm border border-red-400/20 hover:border-red-400/40 rounded-xl bg-red-500/10",
              isCollapsed ? "px-0" : ""
            )}
          />
        </div>
      </nav>
    </div>
  );
};
