
import React from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, Tooltip, Legend, LineChart, Line, AreaChart, Area, CartesianGrid } from 'recharts';
import StatCard from '@/components/StatCard';
import { Calendar, Wallet, CreditCard, ArrowDown, ArrowUp, DollarSign, BarChart4, Users, TrendingUp, LineChart as LineChartIcon } from 'lucide-react';
import FinancialChart from '@/components/FinancialChart';

const BusinessAnalysis = () => {
  // Mock data for the charts
  const spendingData = [
    { name: 'Salaries', value: 4000 },
    { name: 'Rent', value: 2000 },
    { name: 'Utilities', value: 1000 },
    { name: 'Marketing', value: 1500 },
    { name: 'Office Supplies', value: 800 },
    { name: 'Others', value: 1200 },
  ];

  const revenueData = [
    { name: 'Jan', value: 12000 },
    { name: 'Feb', value: 15000 },
    { name: 'Mar', value: 18000 },
    { name: 'Apr', value: 16000 },
    { name: 'May', value: 21000 },
    { name: 'Jun', value: 19000 },
    { name: 'Jul', value: 23000 },
  ];
  
  const monthlyData = [
    { name: 'Jan', income: 40000, expenses: 24000 },
    { name: 'Feb', income: 30000, expenses: 13980 },
    { name: 'Mar', income: 20000, expenses: 19800 },
    { name: 'Apr', income: 27800, expenses: 19080 },
    { name: 'May', income: 18900, expenses: 14800 },
    { name: 'Jun', income: 23900, expenses: 18000 },
    { name: 'Jul', income: 34900, expenses: 23000 },
  ];
  
  const customerData = [
    { name: 'Jan', customers: 100 },
    { name: 'Feb', customers: 120 },
    { name: 'Mar', customers: 150 },
    { name: 'Apr', customers: 180 },
    { name: 'May', customers: 210 },
    { name: 'Jun', customers: 250 },
    { name: 'Jul', customers: 280 },
  ];
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

  const statCards = [
    { title: "Total Revenue", value: "₦15.2M", icon: <TrendingUp className="text-green-500" size={24} />, trend: { value: "+15.2%", positive: true } },
    { title: "Expenses", value: "₦6.8M", icon: <BarChart4 className="text-amber-500" size={24} />, trend: { value: "+7.8%", positive: false } },
    { title: "Total Customers", value: "2,845", icon: <Users className="text-blue-500" size={24} />, trend: { value: "+12.2%", positive: true } },
    { title: "Avg. Transaction", value: "₦24,500", icon: <CreditCard className="text-purple-500" size={24} />, trend: { value: "+4.5%", positive: true } }
  ];

  return (
    <BusinessLayout pageTitle="Business Analytics">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-kojaDark">Business Analytics</h1>
        <p className="text-kojaGray mt-1">Comprehensive analysis of your business performance</p>
      </div>
      
      {/* Summary Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
        {statCards.map((stat, index) => (
          <StatCard 
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            trend={stat.trend}
            className="h-auto"
          />
        ))}
      </div>
      
      {/* Time Period Selector */}
      <div className="flex justify-end mb-6">
        <Tabs defaultValue="month">
          <TabsList>
            <TabsTrigger value="week">Week</TabsTrigger>
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="quarter">Quarter</TabsTrigger>
            <TabsTrigger value="year">Year</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Main Revenue Chart - Moved from Dashboard */}
      <Card className="modern-glass-card mb-6">
        <CardHeader>
          <CardTitle>Revenue Overview</CardTitle>
          <CardDescription>Your business performance over time</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={revenueData}
              margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
            >
              <defs>
                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#059669" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#059669" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#9CA3AF' }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#9CA3AF' }}
                tickFormatter={(value) => `₦${value/1000}k`}
              />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke="#059669" 
                fillOpacity={1} 
                fill="url(#colorRevenue)" 
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      
      {/* Main Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card className="modern-glass-card">
          <CardHeader>
            <CardTitle>Monthly Income vs Expenses</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyData}>
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="income" fill="#4CAF50" />
                <Bar dataKey="expenses" fill="#FF5722" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card className="modern-glass-card">
          <CardHeader>
            <CardTitle>Expense Categories</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={spendingData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {spendingData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
      
      <Card className="modern-glass-card">
        <CardHeader>
          <CardTitle>Customer Growth</CardTitle>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={customerData}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
              />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="customers" stroke="#8884d8" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </BusinessLayout>
  );
};

export default BusinessAnalysis;
