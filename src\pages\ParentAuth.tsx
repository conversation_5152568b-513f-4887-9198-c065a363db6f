
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Lock, User, Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Helmet } from 'react-helmet-async';

const ParentAuth = () => {
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  const { kidsId, redirectTo } = location.state || {};

  const [formData, setFormData] = useState({
    parentEmail: '',
    parentPin: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.parentEmail || !formData.parentPin) {
      toast({
        title: 'Missing Information',
        description: 'Please enter your email and PIN',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Mock parent authentication
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Store kids user session
      localStorage.setItem('kidsUser', JSON.stringify({
        id: kidsId,
        name: 'Young User',
        type: 'kids',
        parentApproved: true
      }));
      
      toast({
        title: 'Authentication Successful',
        description: 'Your child can now access their account.',
      });
      
      navigate(redirectTo || '/kids-dashboard');
    } catch (error) {
      toast({
        title: 'Authentication Failed',
        description: 'Please check your credentials and try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeny = () => {
    toast({
      title: 'Access Denied',
      description: 'Child login request has been denied.',
      variant: 'destructive'
    });
    navigate('/');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#1231B8]/10 via-gray-50 to-[#FDE314]/10 p-4 py-8">
      <Helmet>
        <title>Parent Authentication | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-md">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <Shield className="h-16 w-16 text-[#1231B8]" />
          </div>
          <h1 className="text-3xl font-bold text-[#1231B8] mb-2">Parent Authentication Required</h1>
          <p className="text-gray-600">
            Your child (ID: <span className="font-semibold">{kidsId}</span>) is requesting access to their account
          </p>
        </div>

        <Card className="shadow-xl border-none rounded-[40px] overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-[#1231B8] to-[#1231B8]/80 text-white pb-4">
            <CardTitle className="text-xl text-center flex items-center justify-center">
              <Lock className="h-6 w-6 mr-2" />
              Verify Your Identity
            </CardTitle>
          </CardHeader>
          
          <CardContent className="p-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <Label htmlFor="parentEmail" className="text-[#1231B8] font-medium flex items-center">
                  <User size={16} className="mr-2" />
                  Parent/Guardian Email
                </Label>
                <Input
                  id="parentEmail"
                  name="parentEmail"
                  type="email"
                  placeholder="Enter your email address"
                  value={formData.parentEmail}
                  onChange={handleInputChange}
                  className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="parentPin" className="text-[#1231B8] font-medium flex items-center">
                  <Lock size={16} className="mr-2" />
                  Your PIN
                </Label>
                <div className="relative">
                  <Input
                    id="parentPin"
                    name="parentPin"
                    type={showPin ? 'text' : 'password'}
                    placeholder="Enter your PIN"
                    value={formData.parentPin}
                    onChange={handleInputChange}
                    className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                    onClick={() => setShowPin(!showPin)}
                  >
                    {showPin ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-[20px] border border-blue-200">
                <p className="text-sm text-blue-800">
                  <Shield className="h-4 w-4 inline mr-1" />
                  By approving this request, you allow your child to access their KojaPay Kids account on this device.
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleDeny}
                  className="py-3 rounded-[40px] border-red-300 text-red-600 hover:bg-red-50"
                >
                  Deny Access
                </Button>
                
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-[#1231B8] hover:bg-[#1231B8]/90 text-white py-3 rounded-[40px]"
                >
                  {isLoading ? 'Verifying...' : 'Approve Access'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
        
        <div className="text-center mt-6">
          <p className="text-gray-600 text-sm">
            Having trouble? Visit{' '}
            <a href="/parent-control" className="text-[#1231B8] font-medium hover:underline">
              Parental Controls
            </a>
            {' '}to manage authentication settings.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ParentAuth;
