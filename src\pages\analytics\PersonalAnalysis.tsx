
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, Tooltip, Legend, LineChart, Line, AreaChart, Area, CartesianGrid } from 'recharts';
import StatCard from '@/components/StatCard';
import { Calendar, Wallet, CreditCard, ArrowDown, ArrowUp, DollarSign, BarChart4, Users, TrendingUp, LineChart as LineChartIcon } from 'lucide-react';
import FinancialChart from '@/components/FinancialChart';
import { GoBackButton } from '@/components/ui/go-back-button';

const PersonalAnalysis = () => {
  // Mock data for the charts
  const spendingData = [
    { name: 'Food', value: 25000 },
    { name: 'Transport', value: 15000 },
    { name: 'Shopping', value: 20000 },
    { name: 'Bills', value: 30000 },
    { name: 'Entertainment', value: 10000 },
  ];

  const revenueData = [
    { name: 'Jan', value: 120000 },
    { name: 'Feb', value: 140000 },
    { name: 'Mar', value: 130000 },
    { name: 'Apr', value: 170000 },
    { name: 'May', value: 150000 },
    { name: 'Jun', value: 190000 },
  ];
  
  const monthlyData = [
    { name: 'Jan', income: 250000, expenses: 120000 },
    { name: 'Feb', income: 260000, expenses: 140000 },
    { name: 'Mar', income: 270000, expenses: 130000 },
    { name: 'Apr', income: 280000, expenses: 170000 },
    { name: 'May', income: 290000, expenses: 150000 },
    { name: 'Jun', income: 300000, expenses: 190000 },
  ];
  
  const categoryData = [
    { name: 'Jan', food: 40000, transport: 20000, entertainment: 15000 },
    { name: 'Feb', food: 45000, transport: 22000, entertainment: 18000 },
    { name: 'Mar', food: 42000, transport: 19000, entertainment: 14000 },
    { name: 'Apr', food: 48000, transport: 24000, entertainment: 16000 },
    { name: 'May', food: 50000, transport: 25000, entertainment: 20000 },
    { name: 'Jun', food: 47000, transport: 23000, entertainment: 19000 },
  ];
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

  const statCards = [
    { title: "Total Spending", value: "₦450,000", icon: <BarChart4 className="text-red-500" size={24} />, trend: { value: "+12.2%", positive: false } },
    { title: "Total Income", value: "₦650,000", icon: <TrendingUp className="text-green-500" size={24} />, trend: { value: "+5.8%", positive: true } },
    { title: "Savings", value: "₦200,000", icon: <Wallet className="text-blue-500" size={24} />, trend: { value: "+2.5%", positive: true } },
    { title: "Top Category", value: "Bills", icon: <CreditCard className="text-purple-500" size={24} />, trend: { value: "₦120,000", positive: null } }
  ];

  return (
    <DashboardLayout pageTitle="Personal Finance Analytics">
      <div className="mb-6">
        <GoBackButton />
        <h1 className="text-2xl font-semibold text-kojaDark">Personal Finance Analytics</h1>
        <p className="text-kojaGray mt-1">Comprehensive analysis of your personal finances</p>
      </div>
      
      {/* Summary Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6">
        {statCards.map((stat, index) => (
          <StatCard 
            key={index}
            title={stat.title}
            value={stat.value}
            icon={stat.icon}
            trend={stat.trend}
            className="h-auto"
          />
        ))}
      </div>
      
      {/* Time Period Selector */}
      <div className="flex justify-end mb-6">
        <Tabs defaultValue="month">
          <TabsList>
            <TabsTrigger value="week">Week</TabsTrigger>
            <TabsTrigger value="month">Month</TabsTrigger>
            <TabsTrigger value="quarter">Quarter</TabsTrigger>
            <TabsTrigger value="year">Year</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      
      {/* Main Income/Expenses Chart */}
      <Card className="modern-glass-card mb-6">
        <CardHeader>
          <CardTitle>Income vs Expenses</CardTitle>
          <CardDescription>Your financial performance over time</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={monthlyData}
              margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
            >
              <defs>
                <linearGradient id="colorIncome" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#059669" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#059669" stopOpacity={0} />
                </linearGradient>
                <linearGradient id="colorExpenses" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#ef4444" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#ef4444" stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#9CA3AF' }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#9CA3AF' }}
                tickFormatter={(value) => `₦${value/1000}k`}
              />
              <Tooltip />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="income" 
                stroke="#059669" 
                fillOpacity={1} 
                fill="url(#colorIncome)" 
                strokeWidth={2}
                name="Income"
              />
              <Area 
                type="monotone" 
                dataKey="expenses" 
                stroke="#ef4444" 
                fillOpacity={1} 
                fill="url(#colorExpenses)" 
                strokeWidth={2}
                name="Expenses"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
      
      {/* Main Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <Card className="modern-glass-card">
          <CardHeader>
            <CardTitle>Monthly Category Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={categoryData}>
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="food" fill="#4CAF50" name="Food" />
                <Bar dataKey="transport" fill="#2196F3" name="Transport" />
                <Bar dataKey="entertainment" fill="#FF5722" name="Entertainment" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card className="modern-glass-card">
          <CardHeader>
            <CardTitle>Spending by Category</CardTitle>
          </CardHeader>
          <CardContent className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={spendingData}
                  cx="50%"
                  cy="50%"
                  labelLine={true}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {spendingData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => `₦${value.toLocaleString()}`} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
      
      <Card className="modern-glass-card">
        <CardHeader>
          <CardTitle>Spending Trends</CardTitle>
          <CardDescription>Monthly spending pattern analysis</CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
              <XAxis 
                dataKey="name" 
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tickFormatter={(value) => `₦${value/1000}k`}
              />
              <Tooltip formatter={(value) => `₦${value.toLocaleString()}`} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="#8884d8" 
                activeDot={{ r: 8 }} 
                name="Spending"
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default PersonalAnalysis;
