
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  FileText,
  MessageSquare,
  ShieldAlert,
  AlertOctagon,
  Use<PERSON><PERSON><PERSON><PERSON>,
  Users
} from 'lucide-react';

const EscrowManagement = () => {
  const escrows = [
    { 
      id: 'ESC001', 
      title: 'Website Development Project', 
      buyer: 'TechSolutions Inc.',
      seller: 'DigitalCreators',
      amount: '₦750,000',
      createdDate: '2023-06-10',
      status: 'In Progress',
      releaseDate: '2023-07-10',
      hasDispute: false
    },
    { 
      id: 'ESC002', 
      title: 'Bulk Product Purchase', 
      buyer: 'RetailMart Ltd',
      seller: 'GlobalSuppliers',
      amount: '₦2,500,000',
      createdDate: '2023-06-08',
      status: 'Completed',
      releaseDate: '2023-06-15',
      hasDispute: false
    },
    { 
      id: 'ESC003', 
      title: 'Graphic Design Services', 
      buyer: 'Marketing Agency',
      seller: 'CreativeDesigns',
      amount: '₦120,000',
      createdDate: '2023-06-12',
      status: 'Disputed',
      releaseDate: 'Pending Resolution',
      hasDispute: true
    },
    { 
      id: 'ESC004', 
      title: 'Office Equipment Purchase', 
      buyer: 'HealthFirst Medical',
      seller: 'OfficeSolutions',
      amount: '₦950,000',
      createdDate: '2023-06-14',
      status: 'In Progress',
      releaseDate: '2023-06-30',
      hasDispute: false
    },
    { 
      id: 'ESC005', 
      title: 'Software License Renewal', 
      buyer: 'FinTech Startup',
      seller: 'SoftwareHouse',
      amount: '₦350,000',
      createdDate: '2023-06-09',
      status: 'Cancelled',
      releaseDate: 'N/A',
      hasDispute: false
    },
  ];

  const disputes = [
    {
      id: 'DSP001',
      escrowId: 'ESC003',
      title: 'Graphic Design Services',
      initiator: 'Marketing Agency (Buyer)',
      respondent: 'CreativeDesigns (Seller)',
      reason: 'Quality does not match agreed specifications',
      filedDate: '2023-06-15',
      status: 'Under Review',
      priority: 'High'
    },
    {
      id: 'DSP002',
      escrowId: 'ESC007',
      title: 'Mobile App Development',
      initiator: 'TravelApp (Buyer)',
      respondent: 'CodeMasters (Seller)',
      reason: 'Delayed delivery beyond agreed timeline',
      filedDate: '2023-06-14',
      status: 'Evidence Collection',
      priority: 'Medium'
    },
    {
      id: 'DSP003',
      escrowId: 'ESC012',
      title: 'Vehicle Purchase',
      initiator: 'John Smith (Buyer)',
      respondent: 'AutoDealers (Seller)',
      reason: 'Undisclosed mechanical issues',
      filedDate: '2023-06-13',
      status: 'Resolved',
      priority: 'Closed'
    }
  ];

  return (
    <AdminLayout pageTitle="Escrow & Dispute Management">
      <Helmet>
        <title>Escrow & Dispute Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Escrows</CardTitle>
              <CardDescription>Current transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">42</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Open Disputes</CardTitle>
              <CardDescription>Requiring attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">5</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Completed</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">28</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Value</CardTitle>
              <CardDescription>All active escrows</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">₦24.5M</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="escrows" className="w-full">
          <TabsList className="grid grid-cols-2 mb-6">
            <TabsTrigger value="escrows">Escrow Transactions</TabsTrigger>
            <TabsTrigger value="disputes">Dispute Resolution</TabsTrigger>
          </TabsList>
          
          <TabsContent value="escrows">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Escrow Transactions</CardTitle>
                  <CardDescription>Monitor and manage all escrow transactions</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Export Data
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search by ID, title or parties..." 
                      className="pl-9"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <Clock size={12} />
                      <span>In Progress</span>
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <AlertTriangle size={12} />
                      <span>Disputed</span>
                    </Badge>
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>List of escrow transactions</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Escrow ID</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Buyer</TableHead>
                        <TableHead>Seller</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Created Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {escrows.map((escrow) => (
                        <TableRow key={escrow.id}>
                          <TableCell className="font-medium">{escrow.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <FileText size={16} className="text-blue-600" />
                              {escrow.title}
                              {escrow.hasDispute && (
                                <AlertOctagon size={16} className="text-red-500" />
                              )}
                            </div>
                          </TableCell>
                          <TableCell>{escrow.buyer}</TableCell>
                          <TableCell>{escrow.seller}</TableCell>
                          <TableCell>{escrow.amount}</TableCell>
                          <TableCell>{escrow.createdDate}</TableCell>
                          <TableCell>
                            <Badge variant={
                              escrow.status === 'Completed' ? 'outline' : 
                              escrow.status === 'In Progress' ? 'secondary' : 
                              escrow.status === 'Disputed' ? 'destructive' :
                              'destructive'
                            }>
                              {escrow.status === 'Completed' && <CheckCircle size={12} className="mr-1 text-green-600" />}
                              {escrow.status === 'In Progress' && <Clock size={12} className="mr-1" />}
                              {escrow.status === 'Disputed' && <AlertTriangle size={12} className="mr-1" />}
                              {escrow.status === 'Cancelled' && <XCircle size={12} className="mr-1" />}
                              {escrow.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                {escrow.status === 'In Progress' && (
                                  <DropdownMenuItem>
                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                    <span className="text-green-600">Release Funds</span>
                                  </DropdownMenuItem>
                                )}
                                {!escrow.hasDispute && escrow.status !== 'Completed' && escrow.status !== 'Cancelled' && (
                                  <DropdownMenuItem>
                                    <XCircle className="mr-2 h-4 w-4 text-red-600" />
                                    <span className="text-red-600">Cancel Transaction</span>
                                  </DropdownMenuItem>
                                )}
                                {escrow.hasDispute && (
                                  <DropdownMenuItem>
                                    <ShieldAlert className="mr-2 h-4 w-4 text-amber-600" />
                                    <span className="text-amber-600">View Dispute</span>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="disputes">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Dispute Management</CardTitle>
                  <CardDescription>Resolve conflicts between transaction parties</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Assign Agent
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search disputes..." 
                      className="pl-9"
                    />
                  </div>
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <AlertTriangle size={12} />
                      <span>High Priority</span>
                    </Badge>
                    <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                      <Clock size={12} />
                      <span>Under Review</span>
                    </Badge>
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>List of disputes requiring resolution</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Dispute ID</TableHead>
                        <TableHead>Escrow ID</TableHead>
                        <TableHead>Title</TableHead>
                        <TableHead>Initiated By</TableHead>
                        <TableHead>Filed Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Priority</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {disputes.map((dispute) => (
                        <TableRow key={dispute.id}>
                          <TableCell className="font-medium">{dispute.id}</TableCell>
                          <TableCell>{dispute.escrowId}</TableCell>
                          <TableCell>{dispute.title}</TableCell>
                          <TableCell>{dispute.initiator}</TableCell>
                          <TableCell>{dispute.filedDate}</TableCell>
                          <TableCell>
                            <Badge variant={
                              dispute.status === 'Resolved' ? 'outline' : 
                              dispute.status === 'Evidence Collection' ? 'secondary' : 
                              'destructive'
                            }>
                              {dispute.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              dispute.priority === 'High' ? 'destructive' : 
                              dispute.priority === 'Medium' ? 'secondary' : 
                              'outline'
                            }>
                              {dispute.priority}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <MessageSquare className="mr-2 h-4 w-4" />
                                  <span>Message Parties</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Users className="mr-2 h-4 w-4" />
                                  <span>Schedule Mediation</span>
                                </DropdownMenuItem>
                                {dispute.status !== 'Resolved' && (
                                  <DropdownMenuItem>
                                    <UserCheck className="mr-2 h-4 w-4 text-green-600" />
                                    <span className="text-green-600">Resolve Dispute</span>
                                  </DropdownMenuItem>
                                )}
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default EscrowManagement;
