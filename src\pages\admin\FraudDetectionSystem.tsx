import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { Helmet } from 'react-helmet-async';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { toast } from 'sonner';
import { AlertTriangle, Check, Clock, Eye, FileText, Shield, ShieldAlert, ShieldOff, UserX, Search } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Slider } from '@/components/ui/slider';

const FraudDetectionSystem = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Mock data for suspicious activities
  const suspiciousActivities = [
    { id: '1', user: '<EMAIL>', activity: 'Multiple failed login attempts', time: '10 minutes ago', risk: 'high', status: 'flagged' },
    { id: '2', user: '<EMAIL>', activity: 'Large transaction amount', time: '30 minutes ago', risk: 'medium', status: 'reviewing' },
    { id: '3', user: '<EMAIL>', activity: 'Login from unusual location', time: '1 hour ago', risk: 'medium', status: 'resolved' },
    { id: '4', user: '<EMAIL>', activity: 'Unusual spending pattern', time: '2 hours ago', risk: 'high', status: 'blocked' },
    { id: '5', user: '<EMAIL>', activity: 'Multiple accounts created from same IP', time: '5 hours ago', risk: 'high', status: 'flagged' },
  ];
  
  // Mock data for charts
  const fraudTypeData = [
    { name: 'Account Takeover', value: 35 },
    { name: 'Identity Theft', value: 25 },
    { name: 'Payment Fraud', value: 20 },
    { name: 'Phishing', value: 15 },
    { name: 'Other', value: 5 },
  ];
  
  const fraudTrendData = [
    { month: 'Jan', attempts: 45, blocked: 42 },
    { month: 'Feb', attempts: 52, blocked: 48 },
    { month: 'Mar', attempts: 48, blocked: 45 },
    { month: 'Apr', attempts: 70, blocked: 65 },
    { month: 'May', attempts: 55, blocked: 52 },
    { month: 'Jun', attempts: 50, blocked: 48 },
  ];
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
  
  const handleActionTaken = (id: string, action: string) => {
    toast.success(`${action} action taken for activity #${id}`);
  };
  
  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'high':
        return <Badge variant="destructive" className="bg-red-500">High</Badge>;
      case 'medium':
        return <Badge variant="default" className="bg-amber-500">Medium</Badge>;
      case 'low':
        return <Badge variant="outline" className="bg-green-500 text-white">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };
  
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'flagged':
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">Flagged</Badge>;
      case 'reviewing':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Reviewing</Badge>;
      case 'resolved':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Resolved</Badge>;
      case 'blocked':
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">Blocked</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };
  
  const filteredActivities = suspiciousActivities
    .filter(activity => 
      activity.user.toLowerCase().includes(searchQuery.toLowerCase()) || 
      activity.activity.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(activity => statusFilter === 'all' || activity.status === statusFilter);
  
  return (
    <AdminLayout pageTitle="Fraud Detection System">
      <Helmet>
        <title>Fraud Detection System | KojaPay Admin</title>
      </Helmet>
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Fraud Detection System</h1>
        <p className="text-gray-500">Monitor and manage potential fraudulent activities</p>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="activities">Suspicious Activities</TabsTrigger>
          <TabsTrigger value="rules">Detection Rules</TabsTrigger>
          <TabsTrigger value="settings">System Settings</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Alerts Today</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                  <span className="text-2xl font-bold">24</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">↑ 12% from yesterday</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Blocked Activities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-green-500 mr-2" />
                  <span className="text-2xl font-bold">18</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">75% success rate</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-blue-500 mr-2" />
                  <span className="text-2xl font-bold">6</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Avg. response time: 15 min</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Fraud Prevention Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <ShieldAlert className="h-5 w-5 text-purple-500 mr-2" />
                  <span className="text-2xl font-bold">93.7%</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">↑ 3.2% this month</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Fraud Attempts Trend</CardTitle>
                <CardDescription>Last 6 months</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={fraudTrendData}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Bar dataKey="attempts" name="Fraud Attempts" fill="#f97316" />
                    <Bar dataKey="blocked" name="Blocked" fill="#059669" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Fraud Types Distribution</CardTitle>
                <CardDescription>Current period</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={fraudTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {fraudTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="activities">
          <Card>
            <CardHeader>
              <CardTitle>Suspicious Activities</CardTitle>
              <CardDescription>Review and manage flagged activities</CardDescription>
              
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="relative flex-grow">
                  <Input
                    placeholder="Search by user or activity..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
                
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="flagged">Flagged</SelectItem>
                    <SelectItem value="reviewing">Reviewing</SelectItem>
                    <SelectItem value="resolved">Resolved</SelectItem>
                    <SelectItem value="blocked">Blocked</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Suspicious Activity</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Risk Level</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredActivities.length > 0 ? (
                      filteredActivities.map((activity) => (
                        <TableRow key={activity.id}>
                          <TableCell className="font-medium">{activity.user}</TableCell>
                          <TableCell>{activity.activity}</TableCell>
                          <TableCell>{activity.time}</TableCell>
                          <TableCell>{getRiskBadge(activity.risk)}</TableCell>
                          <TableCell>{getStatusBadge(activity.status)}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                onClick={() => handleActionTaken(activity.id, 'View')}
                              >
                                <Eye size={16} />
                              </Button>
                              {activity.status !== 'blocked' && (
                                <Button 
                                  size="icon" 
                                  variant="ghost" 
                                  className="text-red-500 hover:text-red-700" 
                                  onClick={() => handleActionTaken(activity.id, 'Block')}
                                >
                                  <UserX size={16} />
                                </Button>
                              )}
                              {activity.status !== 'resolved' && (
                                <Button 
                                  size="icon" 
                                  variant="ghost" 
                                  className="text-green-500 hover:text-green-700" 
                                  onClick={() => handleActionTaken(activity.id, 'Resolve')}
                                >
                                  <Check size={16} />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-4">
                          No suspicious activities found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="rules">
          <Card>
            <CardHeader>
              <CardTitle>Fraud Detection Rules</CardTitle>
              <CardDescription>Configure and manage detection rules</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* This would contain the rules management UI */}
                <p className="text-sm text-gray-500">Configure the system's fraud detection rules and thresholds.</p>
                
                <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                  <h3 className="font-medium flex items-center text-amber-800">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Rule Configuration
                  </h3>
                  <p className="text-amber-700 text-sm mt-1">
                    Changes to detection rules will take effect immediately. Be careful when adjusting sensitivity levels.
                  </p>
                </div>
                
                {/* Rules would be listed here with configuration options */}
                <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">Multiple Failed Login Attempts</h3>
                      <p className="text-sm text-gray-500">Flag accounts with several failed login attempts in short time period</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">Active</span>
                      <Button variant="outline" size="sm" onClick={() => toast.success("Rule configuration saved")}>Configure</Button>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">Unusual Location Login</h3>
                      <p className="text-sm text-gray-500">Detect logins from new or suspicious locations</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">Active</span>
                      <Button variant="outline" size="sm" onClick={() => toast.success("Rule configuration saved")}>Configure</Button>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">Large Transaction Amount</h3>
                      <p className="text-sm text-gray-500">Flag transactions that exceed the normal pattern for a user</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">Active</span>
                      <Button variant="outline" size="sm" onClick={() => toast.success("Rule configuration saved")}>Configure</Button>
                    </div>
                  </div>
                </div>
                
                <Button className="w-full" variant="outline" onClick={() => toast.success("New rule created")}>
                  Add New Rule
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
              <CardDescription>Configure fraud detection system behavior</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">Notification Settings</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Email Alerts for High Risk Activities</p>
                        <p className="text-sm text-gray-500">Receive email notifications for high-risk fraud alerts</p>
                      </div>
                      <Switch checked={true} onCheckedChange={() => toast.success("Setting updated")} />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">SMS Alerts</p>
                        <p className="text-sm text-gray-500">Receive SMS for critical security events</p>
                      </div>
                      <Switch checked={false} onCheckedChange={() => toast.success("Setting updated")} />
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Dashboard Notifications</p>
                        <p className="text-sm text-gray-500">Show fraud alerts in the admin dashboard</p>
                      </div>
                      <Switch checked={true} onCheckedChange={() => toast.success("Setting updated")} />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-4">System Behavior</h3>
                  <div className="space-y-4">
                    <div>
                      <p className="font-medium mb-2">Default Action for High Risk Activities</p>
                      <Select defaultValue="flag">
                        <SelectTrigger>
                          <SelectValue placeholder="Select default action" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="flag">Flag for Review</SelectItem>
                          <SelectItem value="block">Block Automatically</SelectItem>
                          <SelectItem value="monitor">Monitor Only</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <p className="font-medium mb-2">System Sensitivity</p>
                      <div className="flex items-center space-x-2">
                        <p className="text-sm text-gray-500">Low</p>
                        <Slider
                          defaultValue={[75]}
                          max={100}
                          step={1}
                          className="flex-grow"
                        />
                        <p className="text-sm text-gray-500">High</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">Auto-block Suspicious IPs</p>
                        <p className="text-sm text-gray-500">Automatically block IPs with suspicious activity</p>
                      </div>
                      <Switch checked={true} onCheckedChange={() => toast.success("Setting updated")} />
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => toast.error("Changes discarded")}>Cancel</Button>
                  <Button onClick={() => toast.success("Settings saved successfully")}>Save Settings</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </AdminLayout>
  );
};

export default FraudDetectionSystem;
