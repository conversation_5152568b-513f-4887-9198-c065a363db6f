
import React from 'react';
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

interface LoanAmountSelectorProps {
  amount: number;
  minAmount: number;
  maxAmount: number;
  onAmountChange: (value: number[]) => void;
  formatCurrency: (value: number) => string;
}

const LoanAmountSelector = ({
  amount,
  minAmount,
  maxAmount,
  onAmountChange,
  formatCurrency
}: LoanAmountSelectorProps) => {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center">
        <Label htmlFor="amount" className="text-gray-700">Loan Amount</Label>
        <span className="text-sm font-medium text-gray-900">{formatCurrency(amount)}</span>
      </div>
      <Slider
        id="amount"
        min={minAmount}
        max={maxAmount}
        step={10000}
        value={[amount]}
        onValueChange={onAmountChange}
        className="py-4"
      />
      <div className="flex justify-between text-xs text-gray-500">
        <span>{formatCurrency(minAmount)}</span>
        <span>{formatCurrency(maxAmount)}</span>
      </div>
    </div>
  );
};

export default LoanAmountSelector;
