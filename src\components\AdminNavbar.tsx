
import React from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { Bell, Settings, UserCog, Shield, HelpCircle, ChevronDown, LogOut } from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { NotificationBadge } from "@/components/ui/notification-badge";
import { cn } from '@/lib/utils';

const adminNavLinks = [
  { path: '/admin/dashboard', label: 'Dashboard' },
  { path: '/admin/users', label: 'Users' },
  { path: '/admin/transactions', label: 'Transactions' },
  { path: '/admin/analytics', label: 'Analytics' },
  { path: '/admin/security', label: 'Security' }
];

export const AdminNavbar: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-between p-2 bg-white shadow-sm">
      <div className="flex items-center space-x-4">
        {adminNavLinks.map((link) => (
          <NavLink
            key={link.path}
            to={link.path}
            className={({ isActive }) => cn(
              "admin-nav-link",
              isActive && "admin-nav-link-active"
            )}
          >
            {link.label}
          </NavLink>
        ))}
      </div>
      
      <div className="flex items-center space-x-2">
        <Button 
          variant="ghost" 
          size="icon" 
          className="relative"
          onClick={() => navigate('/admin/notifications')}
        >
          <Bell className="h-5 w-5" />
          <NotificationBadge variant="destructive" size="sm" className="absolute -top-0.5 -right-0.5">3</NotificationBadge>
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-kojaPrimary text-white">A</AvatarFallback>
              </Avatar>
              <ChevronDown className="h-4 w-4 text-kojaGray" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Admin Controls</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/staff')}>
              <UserCog className="mr-2 h-4 w-4" /> Manage Staff
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/settings')}>
              <Settings className="mr-2 h-4 w-4" /> Settings
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/security')}>
              <Shield className="mr-2 h-4 w-4" /> Security
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/support')}>
              <HelpCircle className="mr-2 h-4 w-4" /> Support
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-500 cursor-pointer" onClick={() => navigate('/admin/login')}>
              <LogOut className="mr-2 h-4 w-4" /> Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};

export default AdminNavbar;
