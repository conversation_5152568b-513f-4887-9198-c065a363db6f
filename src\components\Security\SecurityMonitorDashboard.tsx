import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  AlertTriangle, 
  Shield, 
  ShieldAlert, 
  Clock, 
  Eye, 
  UserX, 
  Check, 
  Search,
  RefreshCw,
  Lock,
  Map,
  Smartphone,
  Activity,
  FileText,
  Bell
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip as RechartsTool<PERSON>, Legend, ResponsiveContainer, PieChart, Pie, Cell, LineChart, Line } from 'recharts';

// Types for the security monitoring data
interface SecurityAlert {
  id: string;
  userId: string;
  username: string;
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  location: string;
  ipAddress: string;
  deviceInfo: string;
  status: 'pending' | 'reviewing' | 'resolved' | 'blocked';
  description: string;
}

interface RiskMetrics {
  totalAlerts: number;
  criticalAlerts: number;
  highRiskAlerts: number;
  mediumRiskAlerts: number;
  lowRiskAlerts: number;
  blockedAttempts: number;
  securityScore: number;
}

interface SecurityMonitorDashboardProps {
  className?: string;
}

const SecurityMonitorDashboard: React.FC<SecurityMonitorDashboardProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [severityFilter, setSeverityFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Mock data for security alerts
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([
    {
      id: 'SA-001',
      userId: 'user123',
      username: '<EMAIL>',
      alertType: 'Unusual Login Location',
      severity: 'high',
      timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
      location: 'Kiev, Ukraine',
      ipAddress: '************',
      deviceInfo: 'Chrome on Windows',
      status: 'blocked',
      description: 'Login attempt from a location 5000km from usual activity'
    },
    {
      id: 'SA-002',
      userId: 'user456',
      username: '<EMAIL>',
      alertType: 'Multiple Failed Login Attempts',
      severity: 'medium',
      timestamp: new Date(Date.now() - 45 * 60000).toISOString(),
      location: 'Lagos, Nigeria',
      ipAddress: '************',
      deviceInfo: 'Safari on Mac',
      status: 'reviewing',
      description: '5 failed login attempts within 10 minutes'
    },
    {
      id: 'SA-003',
      userId: 'user789',
      username: '<EMAIL>',
      alertType: 'Large Transaction',
      severity: 'critical',
      timestamp: new Date(Date.now() - 120 * 60000).toISOString(),
      location: 'Abuja, Nigeria',
      ipAddress: '*************',
      deviceInfo: 'Firefox on Linux',
      status: 'pending',
      description: 'Transaction of ₦5,000,000 exceeds usual pattern by 500%'
    },
    {
      id: 'SA-004',
      userId: 'user321',
      username: '<EMAIL>',
      alertType: 'New Device Login',
      severity: 'low',
      timestamp: new Date(Date.now() - 180 * 60000).toISOString(),
      location: 'Port Harcourt, Nigeria',
      ipAddress: '************',
      deviceInfo: 'Android 13',
      status: 'resolved',
      description: 'First time login from a new Android device'
    },
    {
      id: 'SA-005',
      userId: 'user654',
      username: '<EMAIL>',
      alertType: 'Suspicious Pattern',
      severity: 'high',
      timestamp: new Date(Date.now() - 240 * 60000).toISOString(),
      location: 'Kano, Nigeria',
      ipAddress: '*************',
      deviceInfo: 'iPhone (iOS 16)',
      status: 'blocked',
      description: 'Multiple small transactions followed by large withdrawal'
    },
  ]);
  
  // Mock risk metrics data
  const [riskMetrics, setRiskMetrics] = useState<RiskMetrics>({
    totalAlerts: 27,
    criticalAlerts: 3,
    highRiskAlerts: 8,
    mediumRiskAlerts: 12,
    lowRiskAlerts: 4,
    blockedAttempts: 15,
    securityScore: 87
  });
  
  // Chart data
  const alertTrendData = [
    { day: 'Mon', alerts: 12, blocked: 10 },
    { day: 'Tue', alerts: 19, blocked: 15 },
    { day: 'Wed', alerts: 15, blocked: 13 },
    { day: 'Thu', alerts: 22, blocked: 18 },
    { day: 'Fri', alerts: 27, blocked: 23 },
    { day: 'Sat', alerts: 13, blocked: 11 },
    { day: 'Sun', alerts: 8, blocked: 7 },
  ];
  
  const alertTypeData = [
    { name: 'Login Location', value: 35 },
    { name: 'Failed Logins', value: 25 },
    { name: 'Large Transactions', value: 20 },
    { name: 'New Devices', value: 12 },
    { name: 'Suspicious Patterns', value: 8 },
  ];
  
  const locationData = [
    { name: 'Lagos', value: 42 },
    { name: 'Abuja', value: 28 },
    { name: 'Port Harcourt', value: 15 },
    { name: 'Kano', value: 10 },
    { name: 'Other', value: 5 },
  ];
  
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
  
  // Filter alerts based on search query and filters
  const filteredAlerts = securityAlerts
    .filter(alert => 
      alert.username.toLowerCase().includes(searchQuery.toLowerCase()) || 
      alert.alertType.toLowerCase().includes(searchQuery.toLowerCase()) ||
      alert.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .filter(alert => severityFilter === 'all' || alert.severity === severityFilter)
    .filter(alert => statusFilter === 'all' || alert.status === statusFilter);
  
  // Handle alert status change
  const handleStatusChange = (alertId: string, newStatus: 'pending' | 'reviewing' | 'resolved' | 'blocked') => {
    setSecurityAlerts(prevAlerts => 
      prevAlerts.map(alert => 
        alert.id === alertId ? { ...alert, status: newStatus } : alert
      )
    );
    
    toast.success(`Alert ${alertId} status updated to ${newStatus}`);
  };
  
  // Refresh data
  const handleRefresh = () => {
    setIsRefreshing(true);
    
    // Simulate API call
    setTimeout(() => {
      // In a real app, this would fetch fresh data from the API
      setIsRefreshing(false);
      toast.success('Security data refreshed');
    }, 1000);
  };
  
  // Get severity badge
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive" className="bg-red-500">Critical</Badge>;
      case 'high':
        return <Badge variant="destructive">High</Badge>;
      case 'medium':
        return <Badge variant="default" className="bg-amber-500">Medium</Badge>;
      case 'low':
        return <Badge variant="outline" className="border-blue-500 text-blue-700">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200">Pending</Badge>;
      case 'reviewing':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">Reviewing</Badge>;
      case 'resolved':
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">Resolved</Badge>;
      case 'blocked':
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">Blocked</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };
  
  return (
    <div className={className}>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Security Monitoring</h1>
        <p className="text-gray-500">Real-time security monitoring and alerts</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Alerts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
              <span className="text-2xl font-bold">{riskMetrics.totalAlerts}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Last 24 hours</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Critical Alerts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <ShieldAlert className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-2xl font-bold">{riskMetrics.criticalAlerts}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Require immediate action</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Blocked Attempts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Shield className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-2xl font-bold">{riskMetrics.blockedAttempts}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">{Math.round((riskMetrics.blockedAttempts / riskMetrics.totalAlerts) * 100)}% prevention rate</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Security Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Activity className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-2xl font-bold">{riskMetrics.securityScore}%</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Overall platform security</p>
          </CardContent>
        </Card>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Security Alerts</TabsTrigger>
          <TabsTrigger value="reports">Reports & Analysis</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
            <Card>
              <CardHeader>
                <CardTitle>Alert Trend</CardTitle>
                <CardDescription>Last 7 days</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={alertTrendData}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis dataKey="day" />
                    <YAxis />
                    <RechartsTooltip />
                    <Legend />
                    <Line type="monotone" dataKey="alerts" name="Alerts" stroke="#f97316" strokeWidth={2} />
                    <Line type="monotone" dataKey="blocked" name="Blocked" stroke="#059669" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Alert Types</CardTitle>
                <CardDescription>Distribution by category</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={alertTypeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {alertTypeData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <RechartsTooltip formatter={(value) => `${value}%`} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Geographic Distribution</CardTitle>
              <CardDescription>Alert locations</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center py-4">
                <Map className="h-16 w-16 text-gray-300 mb-2" />
                <div className="ml-8">
                  {locationData.map((location, index) => (
                    <div key={index} className="flex items-center mb-2">
                      <div 
                        className="w-3 h-3 rounded-full mr-2" 
                        style={{ backgroundColor: COLORS[index % COLORS.length] }}
                      />
                      <span className="text-sm">{location.name}: {location.value}%</span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div>
                  <CardTitle>Security Alerts</CardTitle>
                  <CardDescription>Monitor and manage security incidents</CardDescription>
                </div>
                
                <Button 
                  variant="outline" 
                  className="flex items-center gap-2"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
                  <span>Refresh</span>
                </Button>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="relative flex-grow">
                  <Input
                    placeholder="Search alerts..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                </div>
                
                <div className="flex gap-2">
                  <Select value={severityFilter} onValueChange={setSeverityFilter}>
                    <SelectTrigger className="w-full sm:w-[150px]">
                      <SelectValue placeholder="Severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Severities</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-[150px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="reviewing">Reviewing</SelectItem>
                      <SelectItem value="resolved">Resolved</SelectItem>
                      <SelectItem value="blocked">Blocked</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Alert ID</TableHead>
                      <TableHead>User</TableHead>
                      <TableHead>Alert Type</TableHead>
                      <TableHead>Severity</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAlerts.length > 0 ? (
                      filteredAlerts.map((alert) => (
                        <TableRow key={alert.id}>
                          <TableCell className="font-medium">{alert.id}</TableCell>
                          <TableCell>{alert.username}</TableCell>
                          <TableCell>{alert.alertType}</TableCell>
                          <TableCell>{getSeverityBadge(alert.severity)}</TableCell>
                          <TableCell>{new Date(alert.timestamp).toLocaleTimeString()}</TableCell>
                          <TableCell>{alert.location}</TableCell>
                          <TableCell>{getStatusBadge(alert.status)}</TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                className="h-8 w-8"
                                onClick={() => toast.success(`Viewing details for alert ${alert.id}`)}
                              >
                                <Eye size={16} />
                              </Button>
                              {alert.status !== 'blocked' && (
                                <Button 
                                  size="icon" 
                                  variant="ghost" 
                                  className="h-8 w-8 text-red-500 hover:text-red-700" 
                                  onClick={() => handleStatusChange(alert.id, 'blocked')}
                                >
                                  <UserX size={16} />
                                </Button>
                              )}
                              {alert.status !== 'resolved' && (
                                <Button 
                                  size="icon" 
                                  variant="ghost" 
                                  className="h-8 w-8 text-green-500 hover:text-green-700" 
                                  onClick={() => handleStatusChange(alert.id, 'resolved')}
                                >
                                  <Check size={16} />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-4">
                          No security alerts found matching your filters
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle>Security Reports</CardTitle>
              <CardDescription>Analysis and reporting tools</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="border rounded-lg p-6 hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="flex items-start">
                    <FileText className="h-10 w-10 text-blue-500 mr-4" />
                    <div>
                      <h3 className="font-medium text-lg">Daily Security Report</h3>
                      <p className="text-sm text-gray-500 mt-1">Comprehensive overview of all security events from the past 24 hours</p>
                      <Button className="mt-4" variant="outline" onClick={() => toast.success('Generating daily report...')}>
                        Generate Report
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-6 hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="flex items-start">
                    <Activity className="h-10 w-10 text-purple-500 mr-4" />
                    <div>
                      <h3 className="font-medium text-lg">Risk Analysis</h3>
                      <p className="text-sm text-gray-500 mt-1">Detailed analysis of security risks and vulnerability assessment</p>
                      <Button className="mt-4" variant="outline" onClick={() => toast.success('Generating risk analysis...')}>
                        Run Analysis
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-6 hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="flex items-start">
                    <Smartphone className="h-10 w-10 text-green-500 mr-4" />
                    <div>
                      <h3 className="font-medium text-lg">Device Report</h3>
                      <p className="text-sm text-gray-500 mt-1">Analysis of device usage, suspicious devices, and access patterns</p>
                      <Button className="mt-4" variant="outline" onClick={() => toast.success('Generating device report...')}>
                        Generate Report
                      </Button>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-6 hover:bg-gray-50 transition-colors cursor-pointer">
                  <div className="flex items-start">
                    <Map className="h-10 w-10 text-amber-500 mr-4" />
                    <div>
                      <h3 className="font-medium text-lg">Geographic Analysis</h3>
                      <p className="text-sm text-gray-500 mt-1">Location-based security analysis and suspicious location detection</p>
                      <Button className="mt-4" variant="outline" onClick={() => toast.success('Generating geographic analysis...')}>
                        View Analysis
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SecurityMonitorDashboard;
