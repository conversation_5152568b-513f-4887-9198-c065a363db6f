
import { useState, useCallback } from 'react';

export interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success';
  action?: React.ReactNode;
}

interface ToastState {
  toasts: Toast[];
}

let toastState: ToastState = { toasts: [] };
let listeners: Array<(state: ToastState) => void> = [];

const notify = () => {
  listeners.forEach(listener => listener(toastState));
};

export const toast = ({
  title,
  description,
  variant = 'default',
  action
}: Omit<Toast, 'id'>) => {
  const id = Math.random().toString(36).substring(2, 9);
  const newToast: Toast = { id, title, description, variant, action };
  
  toastState.toasts.push(newToast);
  notify();
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    toastState.toasts = toastState.toasts.filter(t => t.id !== id);
    notify();
  }, 5000);
};

export const useToast = () => {
  const [state, setState] = useState<ToastState>(toastState);

  const subscribe = useCallback((listener: (state: ToastState) => void) => {
    listeners.push(listener);
    return () => {
      listeners = listeners.filter(l => l !== listener);
    };
  }, []);

  // Subscribe to state changes
  useState(() => {
    const unsubscribe = subscribe(setState);
    return unsubscribe;
  });

  const dismiss = useCallback((toastId: string) => {
    toastState.toasts = toastState.toasts.filter(t => t.id !== toastId);
    notify();
  }, []);

  return {
    toast,
    toasts: state.toasts,
    dismiss
  };
};
