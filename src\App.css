#root {
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
}

.pattern-dots {
  background-image: radial-gradient(currentColor 1px, transparent 1px);
  background-size: 8px 8px;
}

/* Modern glassmorphism card styles */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
  border-radius: 20px;
}

/* Futuristic glass card with glow */
.futuristic-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 4px 24px rgba(0, 0, 0, 0.2),
    0 0 15px rgba(255, 227, 20, 0.1);
  border-radius: 24px;
  transition: all 0.3s ease;
}

.futuristic-card:hover {
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 227, 20, 0.2);
  transform: translateY(-2px);
}

/* Dark theme cards */
.dark-card {
  background: rgba(13, 17, 23, 0.7);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 20px;
}

.rounded-card {
  border-radius: 20px;
}

.rounded-button {
  border-radius: 20px;
}

/* Logo styles with enhanced animations */
.logo-container {
  position: fixed;
  width: 45px;
  height: 45px;
  background-color: #FDE314;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.15),
    0 0 20px rgba(253, 227, 20, 0.3);
  z-index: 40;
  transition: all 0.3s ease;
}

.logo-container:hover {
  transform: scale(1.05);
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.2),
    0 0 30px rgba(253, 227, 20, 0.4);
}

.logo-image {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

/* Animation classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-pulse-light {
  animation: pulse-light 2s ease-in-out infinite;
}

@keyframes pulse-light {
  0% {
    opacity: 0.6;
    box-shadow: 0 0 0 0 rgba(253, 227, 20, 0.4);
  }
  70% {
    opacity: 1;
    box-shadow: 0 0 0 10px rgba(253, 227, 20, 0);
  }
  100% {
    opacity: 0.6;
    box-shadow: 0 0 0 0 rgba(253, 227, 20, 0);
  }
}

.animate-float-subtle {
  animation: float-subtle 6s ease-in-out infinite;
}

@keyframes float-subtle {
  0% {
    transform: translate(0px, 0px);
  }
  33% {
    transform: translate(5px, -5px);
  }
  66% {
    transform: translate(-5px, 5px);
  }
  100% {
    transform: translate(0px, 0px);
  }
}

.animate-slide-up {
  animation: slide-up 0.6s ease forwards;
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-up-fade {
  animation: slide-up-fade 0.8s ease forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

@keyframes slide-up-fade {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 0.7;
    transform: translateY(0);
  }
}

/* New animations for cyberpunk login - removed slow-spin */
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.7;
    filter: blur(15px);
  }
  50% {
    opacity: 1;
    filter: blur(20px);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

/* Business specific styles */
.business-btn-primary {
  background: linear-gradient(135deg, #2A41E8, #3C4FD0);
  color: white;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(42, 65, 232, 0.2);
}

.business-btn-primary:hover {
  background: linear-gradient(135deg, #2A41E8, #2433B8);
  box-shadow: 0 6px 16px rgba(42, 65, 232, 0.3);
}

.business-btn-outline {
  border: 1px solid rgba(253, 227, 20, 0.3);
  border-radius: 12px;
  color: rgba(253, 227, 20, 0.8);
  transition: all 0.3s ease;
}

.business-btn-outline:hover {
  background: rgba(253, 227, 20, 0.1);
  border-color: rgba(253, 227, 20, 0.5);
}

.business-card {
  background: rgba(18, 49, 184, 0.1);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.business-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.business-gradient-bg {
  background: linear-gradient(to right, #1231b8, #3451E8);
  border-radius: 12px;
}

/* Sidebar styles */
.sidebar-toggle {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.sidebar-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Custom Badge Variants */
.badge-success {
  background-color: rgba(22, 163, 74, 0.1);
  color: rgb(22, 163, 74);
  border: 1px solid rgba(22, 163, 74, 0.2);
}

/* POS Specific Styles */
.pos-keypad button {
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.15s ease;
}

.pos-keypad button:active {
  transform: scale(0.97);
}

.payment-method-card {
  transition: all 0.2s ease;
  border-radius: 12px;
  overflow: hidden;
}

.payment-method-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.receipt-paper {
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 15px;
  font-family: 'Courier New', monospace;
  padding: 16px;
  max-width: 320px;
}

.receipt-divider {
  border-top: 1px dashed #ddd;
  margin: 12px 0;
}

.receipt-logo {
  display: block;
  width: 60px;
  height: 60px;
  margin: 0 auto 12px;
  object-fit: contain;
}

.receipt-merchant {
  text-align: center;
  margin-bottom: 12px;
}

.receipt-merchant-name {
  font-weight: bold;
  font-size: 16px;
}

.receipt-merchant-address {
  font-size: 12px;
  color: #666;
}

.receipt-date {
  text-align: center;
  font-size: 12px;
  color: #666;
  margin-bottom: 12px;
}

.receipt-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.receipt-item-name {
  max-width: 70%;
}

.receipt-total {
  font-weight: bold;
  font-size: 14px;
  margin-top: 8px;
  border-top: 1px solid #ddd;
  padding-top: 8px;
}

.receipt-footer {
  text-align: center;
  font-size: 11px;
  color: #666;
  margin-top: 16px;
}

.receipt-barcode {
  text-align: center;
  margin: 16px 0;
  font-family: monospace;
  letter-spacing: 2px;
}

.receipt-print-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: #4c1d95;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-weight: 500;
  margin-top: 12px;
  cursor: pointer;
  border: none;
}

.receipt-print-button:hover {
  background-color: #6d28d9;
}

.receipt-print-button svg {
  margin-right: 8px;
}

/* Terminal container styles with phone-sized dimensions */
.terminal-container {
  max-width: 375px;
  width: 100%;
  border-radius: 15px;
  box-shadow: 
    0 0 0 2px rgba(0, 0, 0, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.2),
    0 8px 24px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  margin: 0 auto;
  background-color: white;
}

/* Card reader connection indicator */
.card-reader-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.reader-connected {
  background-color: #10b981;
  box-shadow: 0 0 5px rgba(16, 185, 129, 0.5);
}

.reader-disconnected {
  background-color: #ef4444;
  box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
}

/* Payment status indicators */
.payment-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.payment-status-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.payment-status-pending {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.payment-status-failed {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.payment-status-processing {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Loading Spinner with Transparent Background */
.app-loading-spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  backdrop-filter: blur(8px);
  z-index: 9999;
}

.spinner-container {
  position: relative;
  width: 80px;
  height: 80px;
}

.loading-spinner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: #1231B8;
  border-bottom-color: #FDE314;
  animation: spin 1.5s linear infinite;
}

.loading-spinner:before {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 4px solid transparent;
  border-left-color: #FDE314;
  border-right-color: #1231B8;
  animation: spin 1s linear infinite reverse;
}

.spinner-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  object-fit: contain;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive breakpoints */
@media (max-width: 640px) {
  .mobile-padding {
    padding: 0.75rem;
  }
  
  .mobile-hidden {
    display: none;
  }
  
  .logo-container {
    width: 40px;
    height: 40px;
    top: 8px;
    left: 8px;
  }
  
  .logo-image {
    width: 25px;
    height: 25px;
  }
  
  .pos-container {
    padding: 0.5rem;
  }
  
  .pos-grid {
    grid-template-columns: 1fr;
  }
  
  .payment-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  /* Mobile Terminal Styles */
  .terminal-container {
    max-width: 100%;
    border-radius: 16px;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-padding {
    padding: 1rem;
  }
  
  .logo-container {
    width: 45px;
    height: 45px;
  }
  
  .pos-container {
    padding: 1rem;
  }
  
  .pos-grid {
    grid-template-columns: 1fr;
  }
  
  .cart-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (min-width: 1025px) {
  .desktop-padding {
    padding: 1.5rem;
  }
  
  .pos-container {
    padding: 1.5rem;
  }
  
  .pos-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
  }
}

/* PWA related styles */
@media (display-mode: standalone) {
  body {
    overscroll-behavior-y: contain;
    -webkit-touch-callout: none;
    user-select: none;
  }
  
  .pwa-top-spacing {
    padding-top: env(safe-area-inset-top);
  }
  
  .pwa-bottom-spacing {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Profile sidebar styling */
.profile-sidebar-nav {
  @apply space-y-2;
}

.profile-nav-item {
  @apply flex items-center justify-between p-3 rounded-xl transition-all cursor-pointer;
  @apply hover:bg-indigo-50 hover:shadow-sm;
}

.profile-nav-item-active {
  @apply bg-gradient-to-r from-indigo-50 to-white border-l-4 border-indigo-600;
}

.profile-nav-icon {
  @apply h-5 w-5 text-indigo-600 mr-3;
}

.profile-nav-text {
  @apply text-sm font-medium text-gray-700 flex-1;
}

.progress-circle {
  @apply relative h-20 w-20 rounded-full flex items-center justify-center mx-auto border-4 border-kojaYellow/20;
}

.progress-circle::before {
  content: "";
  @apply absolute top-0 left-0 h-full w-full rounded-full;
  background: conic-gradient(#FDE314 0%, #FDE314 85%, transparent 85%, transparent 100%);
  mask: radial-gradient(transparent 55%, black 55%);
  -webkit-mask: radial-gradient(transparent 55%, black 55%);
}

.progress-circle-text {
  @apply text-sm font-bold relative z-10;
}
