import axios from 'axios';
import {
  PersonalSavingsPlan,
  GroupSavings,
  GroupSavingsMember,
  SavingsContribution,
  CreatePersonalSavingsPlanRequest,
  CreateGroupSavingsRequest,
  JoinGroupSavingsRequest,
  ContributeToSavingsRequest,
  UpdateSavingsPlanRequest,
  SavingsSummary,
  SavingsActivity
} from '@/types/savings';

const API_BASE_URL = import.meta.env.VITE_BACKEND_URL;

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errorCode?: string;
}

class ApiError extends Error {
  constructor(message: string, public errorCode?: string) {
    super(message);
    this.name = 'ApiError';
  }
}

// Personal Savings Plans (GoFundMe-style) API
export const personalSavingsApi = {
  // Create a new personal savings plan
  async createPlan(planData: CreatePersonalSavingsPlanRequest): Promise<PersonalSavingsPlan> {
    try {
      const response = await axios.post<ApiResponse<PersonalSavingsPlan>>(
        `${API_BASE_URL}/api/savings/personal/create`,
        planData,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to create savings plan', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while creating savings plan');
    }
  },

  // Get user's personal savings plans
  async getUserPlans(): Promise<PersonalSavingsPlan[]> {
    try {
      const response = await axios.get<ApiResponse<PersonalSavingsPlan[]>>(
        `${API_BASE_URL}/api/savings/personal/user`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success) {
        throw new ApiError(response.data.message || 'Failed to fetch savings plans', response.data.errorCode);
      }

      return response.data.data || [];
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching savings plans');
    }
  },

  // Get public savings plans (for contributions)
  async getPublicPlans(): Promise<PersonalSavingsPlan[]> {
    try {
      const response = await axios.get<ApiResponse<PersonalSavingsPlan[]>>(
        `${API_BASE_URL}/api/savings/personal/public`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success) {
        throw new ApiError(response.data.message || 'Failed to fetch public savings plans', response.data.errorCode);
      }

      return response.data.data || [];
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching public savings plans');
    }
  },

  // Get a specific savings plan by ID
  async getPlanById(planId: string): Promise<PersonalSavingsPlan> {
    try {
      const response = await axios.get<ApiResponse<PersonalSavingsPlan>>(
        `${API_BASE_URL}/api/savings/personal/${planId}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to fetch savings plan', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching savings plan');
    }
  },

  // Update a savings plan
  async updatePlan(planId: string, updateData: UpdateSavingsPlanRequest): Promise<PersonalSavingsPlan> {
    try {
      const response = await axios.put<ApiResponse<PersonalSavingsPlan>>(
        `${API_BASE_URL}/api/savings/personal/${planId}`,
        updateData,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to update savings plan', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while updating savings plan');
    }
  },

  // Contribute to a savings plan
  async contributeToPlan(contributionData: ContributeToSavingsRequest): Promise<SavingsContribution> {
    try {
      const response = await axios.post<ApiResponse<SavingsContribution>>(
        `${API_BASE_URL}/api/savings/personal/contribute`,
        contributionData,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to contribute to savings plan', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while contributing to savings plan');
    }
  },

  // Withdraw from a savings plan (only creator can withdraw)
  async withdrawFromPlan(planId: string, amount: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post<ApiResponse<{ success: boolean; message: string }>>(
        `${API_BASE_URL}/api/savings/personal/${planId}/withdraw`,
        { amount },
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to withdraw from savings plan', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while withdrawing from savings plan');
    }
  },

  // Get contributions for a specific plan
  async getPlanContributions(planId: string): Promise<SavingsContribution[]> {
    try {
      const response = await axios.get<ApiResponse<SavingsContribution[]>>(
        `${API_BASE_URL}/api/savings/personal/${planId}/contributions`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success) {
        throw new ApiError(response.data.message || 'Failed to fetch plan contributions', response.data.errorCode);
      }

      return response.data.data || [];
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching plan contributions');
    }
  }
};

// Group Savings API (Individual target motivation system)
export const groupSavingsApi = {
  // Create a new group savings
  async createGroup(groupData: CreateGroupSavingsRequest): Promise<GroupSavings> {
    try {
      const response = await axios.post<ApiResponse<GroupSavings>>(
        `${API_BASE_URL}/api/savings/group/create`,
        groupData,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to create group savings', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while creating group savings');
    }
  },

  // Get available group savings to join
  async getAvailableGroups(): Promise<GroupSavings[]> {
    try {
      const response = await axios.get<ApiResponse<GroupSavings[]>>(
        `${API_BASE_URL}/api/savings/group/available`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success) {
        throw new ApiError(response.data.message || 'Failed to fetch available groups', response.data.errorCode);
      }

      return response.data.data || [];
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching available groups');
    }
  },

  // Join a group savings
  async joinGroup(joinData: JoinGroupSavingsRequest): Promise<GroupSavingsMember> {
    try {
      const response = await axios.post<ApiResponse<GroupSavingsMember>>(
        `${API_BASE_URL}/api/savings/group/join`,
        joinData,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to join group savings', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while joining group savings');
    }
  },

  // Get user's group savings memberships
  async getUserGroups(): Promise<GroupSavingsMember[]> {
    try {
      const response = await axios.get<ApiResponse<GroupSavingsMember[]>>(
        `${API_BASE_URL}/api/savings/group/user`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success) {
        throw new ApiError(response.data.message || 'Failed to fetch user groups', response.data.errorCode);
      }

      return response.data.data || [];
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching user groups');
    }
  },

  // Add money to individual group savings
  async addToGroupSavings(memberId: string, amount: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post<ApiResponse<{ success: boolean; message: string }>>(
        `${API_BASE_URL}/api/savings/group/member/${memberId}/add`,
        { amount },
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to add to group savings', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while adding to group savings');
    }
  },

  // Withdraw from individual group savings (when target is reached or matured)
  async withdrawFromGroup(memberId: string, amount: number): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post<ApiResponse<{ success: boolean; message: string }>>(
        `${API_BASE_URL}/api/savings/group/member/${memberId}/withdraw`,
        { amount },
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to withdraw from group savings', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while withdrawing from group savings');
    }
  }
};

// General Savings API
export const savingsApi = {
  // Get savings summary for dashboard
  async getSavingsSummary(): Promise<SavingsSummary> {
    try {
      const response = await axios.get<ApiResponse<SavingsSummary>>(
        `${API_BASE_URL}/api/savings/summary`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success || !response.data.data) {
        throw new ApiError(response.data.message || 'Failed to fetch savings summary', response.data.errorCode);
      }

      return response.data.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching savings summary');
    }
  },

  // Get recent savings activity
  async getSavingsActivity(): Promise<SavingsActivity[]> {
    try {
      const response = await axios.get<ApiResponse<SavingsActivity[]>>(
        `${API_BASE_URL}/api/savings/activity`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      if (!response.data.success) {
        throw new ApiError(response.data.message || 'Failed to fetch savings activity', response.data.errorCode);
      }

      return response.data.data || [];
    } catch (error: any) {
      if (error.response?.data?.message) {
        throw new ApiError(error.response.data.message, error.response.data.errorCode);
      }
      throw new ApiError('Network error occurred while fetching savings activity');
    }
  }
};
