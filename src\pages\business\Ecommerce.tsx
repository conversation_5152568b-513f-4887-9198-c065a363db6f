import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Package, ShoppingBag, Tag, BarChart4, Globe, Settings, Plus, Search, Filter, Edit, Trash2, Eye, MoreHorizontal } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const BusinessEcommerce = () => {
  const { toast } = useToast();
  const [selectedTab, setSelectedTab] = useState("products");
  const [searchQuery, setSearchQuery] = useState("");
  const [showAddProductForm, setShowAddProductForm] = useState(false);
  
  // Mock products data with updated images
  const products = [
    {
      id: 1,
      name: "Wireless Earbuds",
      price: 15000,
      inventory: 23,
      status: "In Stock",
      category: "Electronics",
      image: "/lovable-uploads/c6ef850a-0def-4097-aff9-b8dfc8bb22e6.png"
    },
    {
      id: 2,
      name: "Smart Watch",
      price: 25000,
      inventory: 12,
      status: "In Stock",
      category: "Electronics",
      image: "/lovable-uploads/f9c0ea5d-a465-49fb-8b2f-ccac2b858bad.png"
    },
    {
      id: 3, 
      name: "Leather Wallet",
      price: 8500,
      inventory: 45,
      status: "In Stock",
      category: "Fashion",
      image: "/lovable-uploads/7efdf088-98ad-45d6-b51d-a15cdcbef2c6.png"
    },
    {
      id: 4,
      name: "Portable Blender",
      price: 12000,
      inventory: 18,
      status: "In Stock",
      category: "Home & Kitchen",
      image: "/lovable-uploads/2abc36d0-01c9-4395-b926-976d7adb4088.png"
    }
  ];
  
  // Mock orders data
  const orders = [
    {
      id: "ORD-001",
      customer: "John Doe",
      date: "May 15, 2023",
      amount: "₦15,000",
      status: "Completed",
      items: 2
    },
    {
      id: "ORD-002",
      customer: "Jane Smith",
      date: "May 14, 2023",
      amount: "₦25,000",
      status: "Processing",
      items: 3
    },
    {
      id: "ORD-003",
      customer: "Michael Brown",
      date: "May 12, 2023",
      amount: "₦8,500",
      status: "Shipped",
      items: 1
    }
  ];
  
  const handleAddProduct = (e: React.FormEvent) => {
    e.preventDefault();
    setShowAddProductForm(false);
    toast({
      title: "Product Added",
      description: "New product has been added successfully",
    });
  };
  
  return (
    <BusinessLayout pageTitle="E-commerce Store">
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div>
            <h2 className="text-xl font-bold tracking-tight font-unica">E-commerce Store</h2>
            <p className="text-sm text-muted-foreground">Manage your online store and products</p>
          </div>
          <Dialog open={showAddProductForm} onOpenChange={setShowAddProductForm}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add New Product
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[550px]">
              <DialogHeader>
                <DialogTitle className="font-unica text-lg">Add New Product</DialogTitle>
                <DialogDescription className="text-xs">
                  Enter the details of your new product below
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleAddProduct}>
                <div className="grid gap-3 py-3">
                  <div className="grid grid-cols-4 items-center gap-3">
                    <Label htmlFor="name" className="text-right text-xs">
                      Name
                    </Label>
                    <Input
                      id="name"
                      placeholder="Product name"
                      className="col-span-3 text-sm"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-3">
                    <Label htmlFor="category" className="text-right text-xs">
                      Category
                    </Label>
                    <Select>
                      <SelectTrigger className="col-span-3 text-xs">
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="electronics">Electronics</SelectItem>
                        <SelectItem value="clothing">Clothing</SelectItem>
                        <SelectItem value="home">Home & Kitchen</SelectItem>
                        <SelectItem value="beauty">Health & Beauty</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-3">
                    <Label htmlFor="price" className="text-right text-xs">
                      Price (₦)
                    </Label>
                    <Input
                      id="price"
                      type="number"
                      placeholder="0.00"
                      className="col-span-3 text-sm"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-3">
                    <Label htmlFor="inventory" className="text-right text-xs">
                      Inventory
                    </Label>
                    <Input
                      id="inventory"
                      type="number"
                      placeholder="0"
                      className="col-span-3 text-sm"
                      required
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-3">
                    <Label htmlFor="description" className="text-right text-xs">
                      Description
                    </Label>
                    <Input
                      id="description"
                      placeholder="Product description"
                      className="col-span-3 text-sm"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-3">
                    <Label htmlFor="image" className="text-right text-xs">
                      Image
                    </Label>
                    <Input
                      id="image"
                      type="file"
                      className="col-span-3 text-xs"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" size="sm">Add Product</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full">
          <TabsList className="mb-3">
            <TabsTrigger value="products" className="text-xs font-unica">Products</TabsTrigger>
            <TabsTrigger value="orders" className="text-xs font-unica">Orders</TabsTrigger>
            <TabsTrigger value="customers" className="text-xs font-unica">Customers</TabsTrigger>
            <TabsTrigger value="analytics" className="text-xs font-unica">Analytics</TabsTrigger>
            <TabsTrigger value="settings" className="text-xs font-unica">Settings</TabsTrigger>
          </TabsList>
          
          <TabsContent value="products" className="space-y-3">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-3">
              <div className="relative w-full sm:w-64">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search products..."
                  className="pl-8 w-full text-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="w-full sm:w-auto flex gap-2">
                <Select defaultValue="all">
                  <SelectTrigger className="w-[140px] text-xs">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="electronics">Electronics</SelectItem>
                    <SelectItem value="clothing">Clothing</SelectItem>
                    <SelectItem value="home">Home & Kitchen</SelectItem>
                    <SelectItem value="beauty">Health & Beauty</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline" size="icon">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {products.map((item) => (
                <Card key={item.id} className="border border-[#1231B8]/30 shadow-none">
                  <CardContent className="p-0">
                    <div className="aspect-square w-full bg-muted/20 relative flex items-center justify-center p-2">
                      <img 
                        src={item.image} 
                        alt={item.name} 
                        className="w-full h-full object-contain"
                      />
                      <div className="absolute top-2 right-2 bg-primary text-white text-xs px-2 py-1 rounded">
                        {item.status}
                      </div>
                    </div>
                    <div className="p-3">
                      <h3 className="font-medium text-sm font-unica">{item.name}</h3>
                      <div className="flex items-center justify-between mt-2">
                        <div className="font-bold text-sm">₦{item.price.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">{item.inventory} in stock</div>
                      </div>
                      <div className="flex justify-between items-center mt-3">
                        <Badge variant="outline" className="text-xs">{item.category}</Badge>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="icon" className="h-7 w-7">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="ghost" size="icon" className="h-7 w-7">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <Card className="border-dashed border border-[#1231B8]/30 shadow-none flex items-center justify-center h-full min-h-[180px]">
                <Button variant="ghost" onClick={() => setShowAddProductForm(true)}>
                  <Plus className="mr-2 h-5 w-5" />
                  <span className="font-unica text-sm">Add Product</span>
                </Button>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="orders">
            <Card className="border border-[#1231B8]/30 shadow-none">
              <CardHeader>
                <CardTitle className="text-base font-unica">Recent Orders</CardTitle>
                <CardDescription>Manage your customer orders</CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-xs font-unica">Order ID</TableHead>
                      <TableHead className="text-xs font-unica">Customer</TableHead>
                      <TableHead className="text-xs font-unica">Date</TableHead>
                      <TableHead className="text-xs font-unica">Items</TableHead>
                      <TableHead className="text-xs font-unica">Amount</TableHead>
                      <TableHead className="text-xs font-unica">Status</TableHead>
                      <TableHead className="text-right text-xs font-unica">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium text-xs">{order.id}</TableCell>
                        <TableCell className="text-xs">{order.customer}</TableCell>
                        <TableCell className="text-xs">{order.date}</TableCell>
                        <TableCell className="text-xs">{order.items}</TableCell>
                        <TableCell className="text-xs">{order.amount}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={`text-xs ${
                              order.status === "Completed" ? "bg-green-100 text-green-800 border-green-200" :
                              order.status === "Processing" ? "bg-blue-100 text-blue-800 border-blue-200" :
                              "bg-orange-100 text-orange-800 border-orange-200"
                            }`}
                          >
                            {order.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="icon" className="h-7 w-7">
                            <Eye className="h-3 w-3" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="customers">
            <Card className="border border-[#1231B8]/30 shadow-none">
              <CardHeader>
                <CardTitle className="text-base font-unica">Customers</CardTitle>
                <CardDescription>View and manage your store customers</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-xs">Customer management content will appear here</p>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics">
            <Card className="border border-[#1231B8]/30 shadow-none">
              <CardHeader>
                <CardTitle className="text-base font-unica">Store Analytics</CardTitle>
                <CardDescription>View your store performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-xs">Analytics content will appear here</p>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="settings">
            <Card className="border border-[#1231B8]/30 shadow-none">
              <CardHeader>
                <CardTitle className="text-base font-unica">Store Settings</CardTitle>
                <CardDescription>Configure your store settings</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-xs">Store settings content will appear here</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>
  );
};

export default BusinessEcommerce;
