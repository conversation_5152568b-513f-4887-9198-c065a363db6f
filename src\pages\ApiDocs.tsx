import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Code, Copy, ExternalLink, Book, LucideGithub } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { GoBackButton } from '@/components/ui/go-back-button';

const ApiDocs = () => {
  const { toast } = useToast();
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: "API key copied to clipboard successfully",
      variant: "success",
    });
  };

  return (
    <DashboardLayout pageTitle="API Documentation">
      <div className="max-w-4xl mx-auto">
        <GoBackButton />
        
        <Card className="mb-6">
          <CardHeader className="flex flex-row items-center gap-4">
            <Code className="h-8 w-8 text-kojaPrimary" />
            <CardTitle>KojaPay API Reference</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-kojaGray">
              Integrate KojaPay's powerful financial services directly into your applications with our comprehensive API.
            </p>
            <div className="flex flex-wrap gap-4">
              <Button className="bg-kojaPrimary hover:bg-kojaPrimary/90">
                <Book className="mr-2 h-4 w-4" />
                Get Started
              </Button>
              <Button variant="outline" className="border-kojaPrimary text-kojaPrimary hover:bg-kojaPrimary/10">
                <LucideGithub className="mr-2 h-4 w-4" />
                GitHub
              </Button>
            </div>
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Authentication</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-kojaGray mb-4">
                All API requests require authentication using your API key. Include it in the header of each request.
              </p>
              <div className="bg-gray-100 p-3 rounded-md font-mono text-xs flex justify-between items-center">
                <code>Authorization: Bearer YOUR_API_KEY</code>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => copyToClipboard('Authorization: Bearer YOUR_API_KEY')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Rate Limits</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-kojaGray">
                The API is rate-limited to ensure stability. Standard accounts have the following limits:
              </p>
              <ul className="list-disc list-inside mt-2 text-sm text-kojaGray">
                <li>100 requests per minute</li>
                <li>5,000 requests per day</li>
                <li>100,000 requests per month</li>
              </ul>
            </CardContent>
          </Card>
        </div>
        
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">API Endpoints</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 border border-gray-200 rounded-md">
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-medium">GET</span>
                  <code className="text-sm font-mono">/api/v1/accounts</code>
                </div>
                <p className="text-sm text-kojaGray">
                  Retrieve account information and balance details.
                </p>
              </div>
              
              <div className="p-3 border border-gray-200 rounded-md">
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs font-medium">POST</span>
                  <code className="text-sm font-mono">/api/v1/transfers</code>
                </div>
                <p className="text-sm text-kojaGray">
                  Initiate a transfer between accounts or to external recipients.
                </p>
              </div>
              
              <div className="p-3 border border-gray-200 rounded-md">
                <div className="flex items-center gap-2 mb-2">
                  <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded text-xs font-medium">GET</span>
                  <code className="text-sm font-mono">/api/v1/transactions</code>
                </div>
                <p className="text-sm text-kojaGray">
                  Retrieve transaction history with filtering options.
                </p>
              </div>
            </div>
            
            <div className="mt-6 text-center">
              <Button variant="outline" className="gap-2">
                View Full Documentation
                <ExternalLink size={16} />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default ApiDocs;
