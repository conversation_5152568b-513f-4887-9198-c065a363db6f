
import React from 'react';

interface CurrencyDisplayProps {
  amount: number;
  className?: string;
  showSymbol?: boolean;
  showDecimal?: boolean;
  currency?: 'NGN' | 'USD'; // Added currency option, defaulting to NGN
}

/**
 * A utility component for displaying currency values with NGN symbol (₦)
 */
const CurrencyDisplay: React.FC<CurrencyDisplayProps> = ({ 
  amount, 
  className = "",
  showSymbol = true,
  showDecimal = true,
  currency = 'NGN' // Default to NGN
}) => {
  const formatCurrency = (value: number) => {
    const formatter = new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: showDecimal ? 2 : 0,
      maximumFractionDigits: showDecimal ? 2 : 0,
    });
    
    const formatted = formatter.format(value);
    
    // If showSymbol is false, remove the currency symbol
    return showSymbol ? formatted : formatted.replace(currency === 'NGN' ? '₦' : '$', '');
  };

  return (
    <span className={className}>
      {formatCurrency(amount)}
    </span>
  );
};

/**
 * A utility function to format currency values with NGN symbol (₦)
 */
export const formatNaira = (amount: number, showDecimal = true): string => {
  const formatter = new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: showDecimal ? 2 : 0,
    maximumFractionDigits: showDecimal ? 2 : 0,
  });
  
  return formatter.format(amount);
};

/**
 * A utility function to format currency values with the specified currency symbol
 */
export const formatCurrency = (amount: number, currency = 'NGN', showDecimal = true): string => {
  const formatter = new Intl.NumberFormat(currency === 'NGN' ? 'en-NG' : 'en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: showDecimal ? 2 : 0,
    maximumFractionDigits: showDecimal ? 2 : 0,
  });
  
  return formatter.format(amount);
};

export default CurrencyDisplay;
