import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ShieldCheck,
  Search,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  Clock,
  XCircle,
  ExternalLink,
  Eye,
} from 'lucide-react';
import { Escrow, EscrowStatus } from '@/types/escrow';
import escrowEcommerceService from '@/services/escrowEcommerceService';
import { useToast } from '@/hooks/use-toast';

interface EscrowTransactionsProps {
  userId: string;
  userType: 'buyer' | 'seller';
}

const EscrowTransactions: React.FC<EscrowTransactionsProps> = ({ userId, userType }) => {
  const [escrows, setEscrows] = useState<Escrow[]>([]);
  const [filteredEscrows, setFilteredEscrows] = useState<Escrow[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    fetchEscrows();
  }, [userId, userType]);

  useEffect(() => {
    filterEscrows();
  }, [escrows, activeTab, searchQuery]);

  const fetchEscrows = async () => {
    try {
      setLoading(true);
      const data = await escrowEcommerceService.getUserOrderEscrows(userId);
      setEscrows(data as unknown as Escrow[]);
    } catch (error) {
      console.error('Error fetching escrows:', error);
      toast({
        title: 'Error',
        description: 'Failed to load escrow transactions',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const filterEscrows = () => {
    let filtered = [...escrows];

    // Apply status filter
    if (activeTab !== 'all') {
      filtered = filtered.filter(escrow => escrow.status === activeTab);
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        escrow =>
          escrow.id.toLowerCase().includes(query) ||
          (escrow.title?.toLowerCase().includes(query) || false) ||
          (escrow.description?.toLowerCase().includes(query) || false)
      );
    }

    setFilteredEscrows(filtered);
  };

  const handleViewEscrow = (escrowId: string) => {
    navigate(`/escrow/${escrowId}`);
  };

  const handleViewDispute = (disputeId: string) => {
    navigate(`/escrow/dispute/${disputeId}`);
  };

  const handleViewOrder = (orderId: string) => {
    navigate(`/account/orders/${orderId}`);
  };

  const renderStatusBadge = (status: EscrowStatus) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'active':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Active</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Cancelled</Badge>;
      case 'disputed':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">Disputed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const extractOrderId = (title: string): string | null => {
    const match = title?.match(/Order #([\w-]+)/);
    return match ? match[1] : null;
  };

  const countByStatus = (status: EscrowStatus): number => {
    return escrows.filter(escrow => escrow.status === status).length;
  };

  const getStatusIcon = (status: EscrowStatus) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'active':
        return <ShieldCheck className="h-4 w-4 text-blue-500" />;
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'disputed':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-xl flex items-center">
            <ShieldCheck className="mr-2 h-5 w-5 text-[#fde314]" />
            Escrow Transactions
          </CardTitle>
          <CardDescription>
            Secure transactions for your e-commerce orders
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="bg-gray-50">
              <CardHeader className="pb-2 pt-4">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{escrows.length}</div>
              </CardContent>
            </Card>
            <Card className="bg-blue-50">
              <CardHeader className="pb-2 pt-4">
                <CardTitle className="text-sm font-medium text-blue-800">Active</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-700">{countByStatus('active')}</div>
              </CardContent>
            </Card>
            <Card className="bg-green-50">
              <CardHeader className="pb-2 pt-4">
                <CardTitle className="text-sm font-medium text-green-800">Completed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-700">{countByStatus('completed')}</div>
              </CardContent>
            </Card>
            <Card className="bg-orange-50">
              <CardHeader className="pb-2 pt-4">
                <CardTitle className="text-sm font-medium text-orange-800">Disputed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-700">{countByStatus('disputed')}</div>
              </CardContent>
            </Card>
          </div>

          <div className="flex flex-col md:flex-row justify-between mb-4 gap-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full md:w-auto">
              <TabsList className="grid grid-cols-5 w-full md:w-[500px]">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="active">Active</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
                <TabsTrigger value="disputed">Disputed</TabsTrigger>
                <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="flex gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search escrows..."
                  className="pl-9 w-full md:w-[250px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" onClick={fetchEscrows} className="px-3">
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="rounded-md border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[100px]">ID</TableHead>
                  <TableHead>Title</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex justify-center items-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-gray-400 mr-2" />
                        <span>Loading escrow transactions...</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : filteredEscrows.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center justify-center text-gray-500">
                        <ShieldCheck className="h-8 w-8 mb-2" />
                        <p>No escrow transactions found</p>
                        <p className="text-sm">Try adjusting your filters</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredEscrows.map((escrow) => (
                    <TableRow key={escrow.id}>
                      <TableCell className="font-medium">{escrow.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getStatusIcon(escrow.status)}
                          <span className="ml-2">{escrow.title}</span>
                        </div>
                      </TableCell>
                      <TableCell>u20a6{escrow.amount.toLocaleString()}</TableCell>
                      <TableCell>{renderStatusBadge(escrow.status)}</TableCell>
                      <TableCell>{new Date(escrow.createdAt).toLocaleDateString()}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewEscrow(escrow.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          
                          {escrow.status === 'disputed' && escrow.dispute && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewDispute(escrow.dispute!.id)}
                              className="text-orange-600"
                            >
                              <AlertTriangle className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {extractOrderId(escrow.title) && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                const orderId = extractOrderId(escrow.title);
                                if (orderId) handleViewOrder(orderId);
                              }}
                            >
                              <ExternalLink className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <ShieldCheck className="mr-2 h-5 w-5 text-[#fde314]" />
            About Escrow Protection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">
            KojaPay Escrow protects both buyers and sellers in e-commerce transactions by holding the payment until both parties are satisfied with the transaction.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="bg-blue-50 p-4 rounded-md">
              <h3 className="font-medium text-blue-800 mb-2">For Buyers</h3>
              <ul className="space-y-2 text-blue-700">
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Pay securely knowing your money is held until you receive your item</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Verify the item meets your expectations before releasing payment</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Dispute resolution if there's an issue with your purchase</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-green-50 p-4 rounded-md">
              <h3 className="font-medium text-green-800 mb-2">For Sellers</h3>
              <ul className="space-y-2 text-green-700">
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Ship with confidence knowing the buyer has already paid</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Protection against fraudulent chargebacks</span>
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle2 className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <span>Increased buyer trust leads to more sales</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-md">
              <h3 className="font-medium text-purple-800 mb-2">How It Works</h3>
              <ol className="space-y-2 text-purple-700">
                <li className="flex items-start gap-2">
                  <div className="bg-purple-200 rounded-full w-5 h-5 flex items-center justify-center text-purple-800 flex-shrink-0 mt-0.5">1</div>
                  <span>Buyer makes a purchase and payment is held in escrow</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="bg-purple-200 rounded-full w-5 h-5 flex items-center justify-center text-purple-800 flex-shrink-0 mt-0.5">2</div>
                  <span>Seller ships the item to the buyer</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="bg-purple-200 rounded-full w-5 h-5 flex items-center justify-center text-purple-800 flex-shrink-0 mt-0.5">3</div>
                  <span>Buyer confirms receipt and releases payment to seller</span>
                </li>
              </ol>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EscrowTransactions;
