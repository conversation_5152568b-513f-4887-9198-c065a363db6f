
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, ArrowRight, Building, CheckCircle2 } from 'lucide-react';
import TransactionStatus, { TransactionStatusType, normalizeTransactionStatus } from '@/components/TransactionStatus';
import { formatDate } from '@/lib/utils';
import { 
  Dialog, 
  DialogContent, 
  DialogTitle, 
  DialogHeader,
  DialogFooter,
  DialogDescription 
} from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';

const TransferOtherBanks = () => {
  const [accountNumber, setAccountNumber] = useState('');
  const [bankCode, setBankCode] = useState('');
  const [amount, setAmount] = useState('');
  const [narration, setNarration] = useState('');
  const [accountName, setAccountName] = useState('');
  const [transferStatus, setTransferStatus] = useState<TransactionStatusType>('idle');
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const { toast } = useToast();
  
  const handleAccountLookup = () => {
    if (accountNumber.length === 10 && bankCode) {
      // Simulate account lookup
      setTimeout(() => {
        setAccountName('Jane Smith');
      }, 800);
    } else {
      setAccountName('');
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setConfirmDialogOpen(true);
  };
  
  const handleTransfer = () => {
    setConfirmDialogOpen(false);
    setIsProcessing(true);
    setTransferStatus('pending');
    
    // Simulate progress updates
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 90) {
          clearInterval(interval);
          return 90;
        }
        return prev + 15;
      });
    }, 500);
    
    // Simulate API call
    setTimeout(() => {
      // Randomly succeed or fail for demo purposes
      const success = Math.random() > 0.2;
      setTransferStatus(success ? 'success' : 'failed');
      setProgress(100);
      setIsProcessing(false);
      
      toast({
        title: success ? "Transfer Successful" : "Transfer Failed",
        description: success 
          ? `Successfully transferred ₦${amount} to ${accountName}` 
          : "There was an issue with your transfer. Please try again.",
        variant: success ? "default" : "destructive",
      });
      
      // Reset form on success
      if (success) {
        setTimeout(() => {
          setAccountNumber('');
          setAccountName('');
          setAmount('');
          setNarration('');
          setBankCode('');
          setTransferStatus('idle');
          setProgress(0);
        }, 3000);
      }
    }, 3500);
  };
  
  const recentTransactions = [
    { id: 1, receiver: 'Jane Smith', accountNumber: '**********', bank: 'First Bank', amount: '₦250,000', date: new Date(2023, 7, 15), status: 'success' as TransactionStatusType },
    { id: 2, receiver: 'Michael Brown', accountNumber: '**********', bank: 'GTBank', amount: '₦120,000', date: new Date(2023, 7, 13), status: 'success' as TransactionStatusType },
    { id: 3, receiver: 'David Wilson', accountNumber: '**********', bank: 'Access Bank', amount: '₦85,000', date: new Date(2023, 7, 10), status: 'failed' as TransactionStatusType },
  ];
  
  const savedBeneficiaries = [
    { id: 1, name: 'Jane Smith', accountNumber: '**********', bankCode: '011', bankName: 'First Bank' },
    { id: 2, name: 'Michael Brown', accountNumber: '**********', bankCode: '058', bankName: 'GTBank' },
    { id: 3, name: 'David Wilson', accountNumber: '**********', bankCode: '044', bankName: 'Access Bank' },
  ];
  
  const banks = [
    { code: '011', name: 'First Bank' },
    { code: '058', name: 'GTBank' },
    { code: '044', name: 'Access Bank' },
    { code: '033', name: 'UBA' },
    { code: '050', name: 'EcoBank' },
    { code: '070', name: 'Fidelity Bank' },
    { code: '221', name: 'Stanbic IBTC' },
    { code: '214', name: 'FCMB' },
    { code: '101', name: 'Providus Bank' },
    { code: '035', name: 'Wema Bank' },
  ];
  
  const getBankName = (code: string) => {
    const bank = banks.find(bank => bank.code === code);
    return bank ? bank.name : '';
  };
  
  return (
    <BusinessLayout pageTitle="Transfer (Other Banks)">
      <div className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Transfer to Other Banks</CardTitle>
                <CardDescription>Send money to accounts in other banks</CardDescription>
              </CardHeader>
              <CardContent>
                {isProcessing && (
                  <div className="mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg mb-4">
                      <div className="flex justify-between mb-2">
                        <span className="text-sm text-gray-500">Transaction Processing</span>
                        <span className="text-sm font-medium">{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>
                    <TransactionStatus 
                      status={transferStatus}
                      message={transferStatus === 'success' 
                        ? "Your transfer has been processed successfully." 
                        : transferStatus === 'failed'
                        ? "There was an issue with your transfer. Please try again."
                        : "Processing your transfer..."}
                    />
                  </div>
                )}
                
                {transferStatus === 'success' && !isProcessing && (
                  <div className="mb-6 text-center py-8 bg-green-50 rounded-lg">
                    <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold mb-2">Transfer Successful!</h3>
                    <p className="text-gray-600 mb-4">
                      Your transfer of ₦{amount} to {accountName} was successful.
                    </p>
                    <Button onClick={() => setTransferStatus('idle')}>
                      Make Another Transfer
                    </Button>
                  </div>
                )}
                
                {(transferStatus === 'idle' || (transferStatus === 'failed' && !isProcessing)) && (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="bank">Select Bank</Label>
                        <Select value={bankCode} onValueChange={(value) => {
                          setBankCode(value);
                          setAccountName('');
                        }}>
                          <SelectTrigger id="bank" className="mt-1">
                            <SelectValue placeholder="Select a bank" />
                          </SelectTrigger>
                          <SelectContent>
                            {banks.map(bank => (
                              <SelectItem key={bank.code} value={bank.code}>{bank.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label htmlFor="accountNumber">Account Number</Label>
                        <div className="flex mt-1">
                          <Input 
                            id="accountNumber" 
                            value={accountNumber}
                            onChange={(e) => {
                              setAccountNumber(e.target.value);
                              setAccountName('');
                            }}
                            placeholder="Enter 10-digit account number"
                            className="flex-1"
                          />
                          <Button 
                            type="button" 
                            variant="outline" 
                            className="ml-2"
                            onClick={handleAccountLookup}
                            disabled={!bankCode || accountNumber.length !== 10}
                          >
                            <Search className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      {accountName && (
                        <div className="bg-gray-50 p-3 rounded-md">
                          <p className="text-sm font-medium">Account Name</p>
                          <p className="text-base">{accountName}</p>
                        </div>
                      )}
                      
                      <div>
                        <Label htmlFor="amount">Amount (₦)</Label>
                        <Input 
                          id="amount" 
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          type="number" 
                          placeholder="0.00"
                          className="mt-1"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="narration">Narration (Optional)</Label>
                        <Input 
                          id="narration" 
                          value={narration}
                          onChange={(e) => setNarration(e.target.value)}
                          placeholder="What's this transfer for?"
                          className="mt-1"
                        />
                      </div>
                      
                      <div className="bg-gray-50 p-3 rounded-md">
                        <p className="text-sm font-medium mb-1">Transaction Fee:</p>
                        <p className="text-base">₦25.00</p>
                        <p className="text-xs text-kojaGray mt-1">Fee applies to transfers to other banks</p>
                      </div>
                      
                      <Button 
                        type="submit" 
                        className="w-full" 
                        disabled={!accountName || !amount}
                      >
                        Send Money
                      </Button>
                    </div>
                  </form>
                )}
              </CardContent>
            </Card>
          </div>
          
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Saved Beneficiaries</CardTitle>
              </CardHeader>
              <CardContent>
                {savedBeneficiaries.length > 0 ? (
                  <div className="space-y-3">
                    {savedBeneficiaries.map(beneficiary => (
                      <div 
                        key={beneficiary.id} 
                        className="flex items-center justify-between p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                        onClick={() => {
                          setAccountNumber(beneficiary.accountNumber);
                          setBankCode(beneficiary.bankCode);
                          setAccountName(beneficiary.name);
                        }}
                      >
                        <div className="flex items-center">
                          <div className="w-10 h-10 rounded-full bg-[#1231b8] flex items-center justify-center text-white">
                            {beneficiary.name.charAt(0)}
                          </div>
                          <div className="ml-3">
                            <p className="font-medium">{beneficiary.name}</p>
                            <p className="text-sm text-kojaGray">{beneficiary.bankName}</p>
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-kojaGray" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <Building className="h-10 w-10 mx-auto text-kojaGray opacity-50 mb-2" />
                    <p className="text-kojaGray">No saved beneficiaries</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
        
        <Tabs defaultValue="recent">
          <TabsList className="w-full mb-4">
            <TabsTrigger value="recent">Recent Transfers</TabsTrigger>
            <TabsTrigger value="bulk">Bulk Transfers</TabsTrigger>
          </TabsList>
          
          <TabsContent value="recent">
            <Card>
              <CardContent className="p-6">
                {recentTransactions.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Recipient</th>
                          <th className="pb-3 font-medium">Account</th>
                          <th className="pb-3 font-medium">Bank</th>
                          <th className="pb-3 font-medium">Amount</th>
                          <th className="pb-3 font-medium">Date</th>
                          <th className="pb-3 font-medium">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentTransactions.map(transaction => (
                          <tr key={transaction.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{transaction.receiver}</td>
                            <td className="py-4">{transaction.accountNumber}</td>
                            <td className="py-4">{transaction.bank}</td>
                            <td className="py-4">{transaction.amount}</td>
                            <td className="py-4">{formatDate(transaction.date)}</td>
                            <td className="py-4">
                              <TransactionStatus 
                                status={transaction.status}
                                compact={true}
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Building className="h-10 w-10 mx-auto text-kojaGray opacity-50 mb-2" />
                    <p className="text-kojaGray">No recent transfers</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="bulk">
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-8">
                  <Building className="h-10 w-10 mx-auto text-kojaGray opacity-50 mb-2" />
                  <p className="text-kojaGray mb-2">Upload a CSV file to process multiple transfers at once</p>
                  <Button>Upload CSV Template</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Transfer</DialogTitle>
            <DialogDescription>
              Please review the transaction details below before proceeding.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Bank</p>
                <p className="text-base font-semibold">{getBankName(bankCode)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Account Number</p>
                <p className="text-base font-semibold">{accountNumber}</p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Account Name</p>
              <p className="text-base font-semibold">{accountName}</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-500">Amount</p>
                <p className="text-lg font-semibold">₦{amount}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Fee</p>
                <p className="text-base font-semibold">₦25.00</p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-500">Total</p>
              <p className="text-lg font-semibold">₦{parseFloat(amount) + 25}</p>
            </div>
            
            {narration && (
              <div>
                <p className="text-sm font-medium text-gray-500">Narration</p>
                <p className="text-base">{narration}</p>
              </div>
            )}
            
            <div className="bg-amber-50 p-3 rounded-md mt-4">
              <p className="text-sm text-amber-700">
                Please confirm that the details above are correct. This transaction cannot be reversed once completed.
              </p>
            </div>
          </div>
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleTransfer}
              className="sm:flex-1"
            >
              Confirm and Transfer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </BusinessLayout>
  );
};

export default TransferOtherBanks;
