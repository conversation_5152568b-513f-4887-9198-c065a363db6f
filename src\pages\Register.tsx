import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useToast } from '@/hooks/use-toast';
import { AccountType, PersonalAccount, BusinessAccount } from '@/types/account';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, Eye, EyeOff } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const Register = () => {
  const [accountType, setAccountType] = useState<AccountType>('personal');
  const [currentStep, setCurrentStep] = useState(1);
  const [showPin, setShowPin] = useState(false);
  const { register, isLoading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [personalData, setPersonalData] = useState<Partial<PersonalAccount>>({
    fullName: '',
    dateOfBirth: '',
    address: '',
    email: '',
    phoneNumber: '',
    pin: '',
  });

  const [businessData, setBusinessData] = useState<Partial<BusinessAccount>>({
    fullName: '',
    businessName: '',
    address: '',
    businessAddress: '',
    email: '',
    phoneNumber: '',
    businessType: '',
    pin: '',
  });

  const handlePersonalDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPersonalData(prev => ({ ...prev, [name]: value }));
  };

  const handleBusinessDataChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setBusinessData(prev => ({ ...prev, [name]: value }));
  };

  const validateStep1 = () => {
    if (accountType === 'personal') {
      const { fullName, dateOfBirth, address, email, phoneNumber } = personalData;
      if (!fullName || !dateOfBirth || !address || !email || !phoneNumber) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields',
          variant: 'destructive',
        });
        return false;
      }
    } else {
      const { fullName, businessName, address, businessAddress, email, phoneNumber, businessType } = businessData;
      if (!fullName || !businessName || !address || !businessAddress || !email || !phoneNumber || !businessType) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields',
          variant: 'destructive',
        });
        return false;
      }
    }
    return true;
  };

  const validateStep2 = () => {
    const pin = accountType === 'personal' ? personalData.pin : businessData.pin;
    
    if (!pin || pin.length !== 6 || !/^\d+$/.test(pin)) {
      toast({
        title: 'Validation Error',
        description: 'Please enter a valid 6-digit PIN',
        variant: 'destructive',
      });
      return false;
    }
    return true;
  };

  const handleContinue = () => {
    if (currentStep === 1 && validateStep1()) {
      setCurrentStep(2);
    } else if (currentStep === 2 && validateStep2()) {
      setCurrentStep(3);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    try {
      if (accountType === 'personal') {
        await register(personalData as PersonalAccount, 'personal');
      } else {
        await register(businessData as BusinessAccount, 'business');
      }
      
      toast({
        title: 'Registration Successful',
        description: 'Your account has been created successfully!',
      });
      
      if (accountType === 'personal') {
        navigate('/dashboard');
      } else {
        navigate('/business-dashboard');
      }
    } catch (err) {
    }
  };

  return (
    <div className="min-h-screen bg-kojaLight p-4 py-8">
      <div className="container mx-auto max-w-2xl">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-kojaPrimary text-white font-bold text-2xl">K</div>
          </div>
          <h1 className="text-3xl font-bold text-kojaDark">Create Your <span className="text-kojaPrimary">Koja</span><span className="text-kojaYellow">Pay</span> Account</h1>
          <p className="text-kojaGray mt-2">Register to start enjoying our services</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 animate-fade-in">
          <Tabs value={accountType} onValueChange={(value) => setAccountType(value as AccountType)} className="mb-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="personal">Personal Account</TabsTrigger>
              <TabsTrigger value="business">Business Account</TabsTrigger>
            </TabsList>
          </Tabs>
          
          <div className="mb-8">
            <div className="flex items-center">
              {[1, 2, 3].map((step) => (
                <React.Fragment key={step}>
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                    currentStep >= step
                      ? 'bg-kojaPrimary text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}>
                    {step}
                  </div>
                  {step < 3 && (
                    <div className={`flex-1 h-1 mx-2 ${
                      currentStep > step ? 'bg-kojaPrimary' : 'bg-gray-200'
                    }`} />
                  )}
                </React.Fragment>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-kojaGray">
              <span>Basic Info</span>
              <span>Security</span>
              <span>Verification</span>
            </div>
          </div>

          {currentStep === 1 && (
            <div className="space-y-6 animate-fade-in">
              <TabsContent value="personal" className="mt-0">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="fullName" className="block text-kojaDark font-medium mb-2">Full Name</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      placeholder="Enter your full name"
                      value={personalData.fullName}
                      onChange={handlePersonalDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="dateOfBirth" className="block text-kojaDark font-medium mb-2">Date of Birth</Label>
                    <Input
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      value={personalData.dateOfBirth}
                      onChange={handlePersonalDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="address" className="block text-kojaDark font-medium mb-2">Address</Label>
                    <Input
                      id="address"
                      name="address"
                      placeholder="Enter your residential address"
                      value={personalData.address}
                      onChange={handlePersonalDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="block text-kojaDark font-medium mb-2">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      value={personalData.email}
                      onChange={handlePersonalDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="phoneNumber" className="block text-kojaDark font-medium mb-2">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      name="phoneNumber"
                      type="tel"
                      placeholder="Enter your phone number"
                      value={personalData.phoneNumber}
                      onChange={handlePersonalDataChange}
                    />
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="business" className="mt-0">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="businessName" className="block text-kojaDark font-medium mb-2">Business Name</Label>
                    <Input
                      id="businessName"
                      name="businessName"
                      placeholder="Enter your business name"
                      value={businessData.businessName}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="fullName" className="block text-kojaDark font-medium mb-2">Owner's Full Name</Label>
                    <Input
                      id="fullName"
                      name="fullName"
                      placeholder="Enter owner's full name"
                      value={businessData.fullName}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="businessType" className="block text-kojaDark font-medium mb-2">Business Type</Label>
                    <Input
                      id="businessType"
                      name="businessType"
                      placeholder="E.g. Retail, Services, Manufacturing"
                      value={businessData.businessType}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="businessAddress" className="block text-kojaDark font-medium mb-2">Business Address</Label>
                    <Input
                      id="businessAddress"
                      name="businessAddress"
                      placeholder="Enter your business address"
                      value={businessData.businessAddress}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="address" className="block text-kojaDark font-medium mb-2">Owner's Address</Label>
                    <Input
                      id="address"
                      name="address"
                      placeholder="Enter owner's residential address"
                      value={businessData.address}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="email" className="block text-kojaDark font-medium mb-2">Email Address</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter business email address"
                      value={businessData.email}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="phoneNumber" className="block text-kojaDark font-medium mb-2">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      name="phoneNumber"
                      type="tel"
                      placeholder="Enter business phone number"
                      value={businessData.phoneNumber}
                      onChange={handleBusinessDataChange}
                    />
                  </div>
                </div>
              </TabsContent>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6 animate-fade-in">
              <div>
                <Label htmlFor="pin" className="block text-kojaDark font-medium mb-2">Create 6-Digit PIN</Label>
                <div className="relative">
                  <Input
                    id="pin"
                    name="pin"
                    type={showPin ? 'text' : 'password'}
                    placeholder="Enter 6-digit PIN"
                    maxLength={6}
                    value={accountType === 'personal' ? personalData.pin : businessData.pin}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (!/^\d*$/.test(value) || value.length > 6) return;
                      
                      if (accountType === 'personal') {
                        setPersonalData(prev => ({ ...prev, pin: value }));
                      } else {
                        setBusinessData(prev => ({ ...prev, pin: value }));
                      }
                    }}
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-kojaGray hover:text-kojaDark"
                    onClick={() => setShowPin(!showPin)}
                  >
                    {showPin ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                <p className="text-sm text-kojaGray mt-2">
                  Your PIN will be used for login and transactions
                </p>
              </div>
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6 animate-fade-in">
              <div className="bg-kojaLight p-6 rounded-xl">
                <h3 className="text-lg font-semibold text-kojaDark mb-4">Verification Requirements</h3>
                
                <div className="space-y-4">
                  <div>
                    <p className="font-medium text-kojaDark">BVN Verification</p>
                    <p className="text-sm text-kojaGray">
                      Your Bank Verification Number is required for KYC compliance.
                    </p>
                    <Input
                      className="mt-2"
                      placeholder="Enter your 11-digit BVN"
                      maxLength={11}
                    />
                  </div>
                  
                  {accountType === 'business' && (
                    <div>
                      <p className="font-medium text-kojaDark">CAC Registration Number</p>
                      <p className="text-sm text-kojaGray">
                        Your Corporate Affairs Commission registration number.
                      </p>
                      <Input
                        className="mt-2"
                        placeholder="Enter CAC registration number"
                      />
                    </div>
                  )}
                  
                  <div>
                    <p className="font-medium text-kojaDark">Government ID</p>
                    <p className="text-sm text-kojaGray">
                      Upload a valid government-issued ID (National ID, Passport, Driver's License).
                    </p>
                    <div className="mt-2 p-4 border border-dashed border-kojaGray rounded-lg text-center">
                      <p className="text-sm text-kojaGray">
                        Click to upload or drag and drop
                      </p>
                      <input type="file" className="hidden" />
                      <Button 
                        variant="outline" 
                        className="mt-2"
                        onClick={() => {
                          const fileInput = document.querySelector('input[type="file"]');
                          if (fileInput) {
                            (fileInput as HTMLInputElement).click();
                          }
                        }}
                      >
                        Upload Document
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div className="flex justify-between mt-8">
            {currentStep > 1 ? (
              <Button
                type="button"
                variant="outline"
                onClick={handleBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft size={16} /> Back
              </Button>
            ) : (
              <Link to="/login">
                <Button
                  type="button"
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <ArrowLeft size={16} /> Back to Login
                </Button>
              </Link>
            )}
            
            {currentStep < 3 ? (
              <Button
                type="button"
                onClick={handleContinue}
                className="bg-kojaPrimary hover:bg-kojaPrimary/90"
              >
                Continue
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleSubmit}
                className="bg-kojaPrimary hover:bg-kojaPrimary/90"
                disabled={isLoading}
              >
                {isLoading ? 'Processing...' : 'Complete Registration'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
