
import React, { useState } from 'react';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  FileText, 
  Download, 
  Eye, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Filter,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Avatar } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

type Transaction = {
  id: string;
  date: string;
  user: {
    name: string;
    email: string;
    avatar?: string;
  };
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  status: 'success' | 'pending' | 'failed';
  reference: string;
};

const TransactionManagement = () => {
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const handleViewTransaction = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setDialogOpen(true);
  };

  const transactions: Transaction[] = [
    {
      id: 'TX12345',
      date: '2023-06-15 14:30',
      user: {
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=John+Doe',
      },
      amount: 125000,
      type: 'credit',
      description: 'Deposit',
      status: 'success',
      reference: 'REF872365',
    },
    {
      id: 'TX12346',
      date: '2023-06-15 15:20',
      user: {
        name: 'Sarah Williams',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=Sarah+Williams',
      },
      amount: 50000,
      type: 'debit',
      description: 'Withdrawal',
      status: 'success',
      reference: 'REF872366',
    },
    {
      id: 'TX12347',
      date: '2023-06-16 09:10',
      user: {
        name: 'Michael Johnson',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=Michael+Johnson',
      },
      amount: 75000,
      type: 'credit',
      description: 'Bill Payment',
      status: 'failed',
      reference: 'REF872367',
    },
    {
      id: 'TX12348',
      date: '2023-06-16 11:45',
      user: {
        name: 'Emily Davis',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=Emily+Davis',
      },
      amount: 200000,
      type: 'credit',
      description: 'Transfer',
      status: 'pending',
      reference: 'REF872368',
    },
    {
      id: 'TX12349',
      date: '2023-06-17 13:30',
      user: {
        name: 'Robert Brown',
        email: '<EMAIL>',
        avatar: 'https://ui-avatars.com/api/?name=Robert+Brown',
      },
      amount: 100000,
      type: 'debit',
      description: 'E-commerce',
      status: 'success',
      reference: 'REF872369',
    },
  ];

  const getStatusBadge = (status: 'success' | 'pending' | 'failed') => {
    switch (status) {
      case 'success':
        return (
          <Badge className="bg-green-100 text-green-700 hover:bg-green-200 font-unica">
            <CheckCircle className="h-3 w-3 mr-1" />
            Successful
          </Badge>
        );
      case 'pending':
        return (
          <Badge className="bg-yellow-100 text-yellow-700 hover:bg-yellow-200 font-unica">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge className="bg-red-100 text-red-700 hover:bg-red-200 font-unica">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <AdminLayout pageTitle="Transaction Management">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-2xl font-bold tracking-tight font-unica">Transaction Management</h2>
            <p className="text-muted-foreground font-unica">Monitor and manage all transactions in the system</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" className="flex items-center font-unica">
              <FileText className="mr-2 h-4 w-4" />
              Export Report
            </Button>
            <Button variant="outline" size="sm" className="flex items-center font-unica">
              <Download className="mr-2 h-4 w-4" />
              Download CSV
            </Button>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <div className="flex flex-col space-y-2 md:flex-row md:items-center md:justify-between md:space-y-0">
              <CardTitle className="font-unica">Transaction History</CardTitle>
              <div className="flex space-x-2">
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search transactions..."
                      className="pl-8 w-[200px] md:w-[300px] font-unica"
                    />
                  </div>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                  </Button>
                  <Select defaultValue="all">
                    <SelectTrigger className="w-[130px] font-unica">
                      <SelectValue placeholder="Filter by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Transactions</SelectItem>
                      <SelectItem value="credit">Credits</SelectItem>
                      <SelectItem value="debit">Debits</SelectItem>
                      <SelectItem value="success">Successful</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="font-unica">ID</TableHead>
                  <TableHead className="font-unica">User</TableHead>
                  <TableHead className="font-unica">Date</TableHead>
                  <TableHead className="font-unica">Description</TableHead>
                  <TableHead className="font-unica">Amount</TableHead>
                  <TableHead className="font-unica">Status</TableHead>
                  <TableHead className="font-unica">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map(transaction => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-unica">{transaction.id}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <img src={transaction.user.avatar} alt={transaction.user.name} />
                        </Avatar>
                        <div className="space-y-1">
                          <div className="font-unica">{transaction.user.name}</div>
                          <div className="text-xs text-muted-foreground font-unica">{transaction.user.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-unica">{transaction.date}</TableCell>
                    <TableCell className="font-unica">{transaction.description}</TableCell>
                    <TableCell className={`font-unica font-medium ${
                      transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.type === 'credit' ? '+' : '-'}₦{transaction.amount.toLocaleString()}
                    </TableCell>
                    <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewTransaction(transaction)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
      
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md">
          {selectedTransaction && (
            <div className="space-y-4">
              <div className="text-center font-unica">
                <h3 className="text-lg font-medium">Transaction Details</h3>
                <p className="text-sm text-muted-foreground">Transaction ID: {selectedTransaction.id}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium font-unica">Date</p>
                  <p className="text-sm font-unica">{selectedTransaction.date}</p>
                </div>
                <div>
                  <p className="text-sm font-medium font-unica">Status</p>
                  <div>{getStatusBadge(selectedTransaction.status)}</div>
                </div>
                <div>
                  <p className="text-sm font-medium font-unica">User</p>
                  <p className="text-sm font-unica">{selectedTransaction.user.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium font-unica">Email</p>
                  <p className="text-sm font-unica">{selectedTransaction.user.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium font-unica">Amount</p>
                  <p className={`text-sm font-medium ${
                    selectedTransaction.type === 'credit' ? 'text-green-600' : 'text-red-600'
                  } font-unica`}>
                    {selectedTransaction.type === 'credit' ? '+' : '-'}₦{selectedTransaction.amount.toLocaleString()}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium font-unica">Reference</p>
                  <p className="text-sm font-unica">{selectedTransaction.reference}</p>
                </div>
                <div className="col-span-2">
                  <p className="text-sm font-medium font-unica">Description</p>
                  <p className="text-sm font-unica">{selectedTransaction.description}</p>
                </div>
              </div>
              
              <div className="flex justify-between">
                <Button 
                  variant="outline" 
                  className="font-unica"
                  onClick={() => setDialogOpen(false)}
                >
                  Close
                </Button>
                <Button 
                  className="font-unica"
                  onClick={() => {
                    // Handle action such as downloading receipt
                    setDialogOpen(false);
                  }}
                >
                  Download Receipt
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default TransactionManagement;
