
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Shield, FileText, AlertTriangle } from 'lucide-react';

interface LegalContentProps {
  type: 'privacy' | 'terms' | 'escrow' | 'security'; 
}

const LegalContent: React.FC<LegalContentProps> = ({ type }) => {
  const renderPrivacyPolicy = () => (
    <div className="legal-section">
      <h1 className="legal-heading">Privacy Policy</h1>
      <p className="legal-text">
        At KojaPay, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform.
      </p>

      <h2 className="legal-subheading">Information We Collect</h2>
      <p className="legal-text">
        We collect personal information that you provide directly to us, such as when you register for an account, make a transaction, or contact customer support. This may include:
      </p>
      <ul className="legal-list">
        <li>Name, email address, and contact information</li>
        <li>Financial information such as account numbers and transaction history</li>
        <li>Identity verification documents</li>
        <li>Device information and usage data</li>
      </ul>

      <h2 className="legal-subheading">How We Use Your Information</h2>
      <p className="legal-text">
        We use your information to provide, maintain, and improve our services, including to:
      </p>
      <ul className="legal-list">
        <li>Process transactions and manage your account</li>
        <li>Verify your identity and prevent fraud</li>
        <li>Communicate with you about your account and our services</li>
        <li>Comply with legal and regulatory requirements</li>
      </ul>

      <h2 className="legal-subheading">Data Security</h2>
      <p className="legal-text">
        We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure.
      </p>
    </div>
  );

  const renderTermsOfService = () => (
    <div className="legal-section">
      <h1 className="legal-heading">Terms of Service</h1>
      <p className="legal-text">
        By using KojaPay's services, you agree to be bound by these Terms of Service. Please read them carefully.
      </p>

      <h2 className="legal-subheading">Account Responsibilities</h2>
      <p className="legal-text">
        You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You must notify us immediately of any unauthorized use of your account.
      </p>

      <h2 className="legal-subheading">Transactions</h2>
      <p className="legal-text">
        When you initiate a transaction through our service, you authorize us to process the transaction on your behalf. You must ensure that you have sufficient funds in your account to cover any transaction you initiate.
      </p>

      <h2 className="legal-subheading">Prohibited Activities</h2>
      <p className="legal-text">
        You agree not to use our services for any illegal or unauthorized purpose, including but not limited to:
      </p>
      <ul className="legal-list">
        <li>Violating any applicable law or regulation</li>
        <li>Infringing on the rights of others</li>
        <li>Engaging in fraudulent activities</li>
        <li>Attempting to interfere with or disrupt our services</li>
      </ul>

      <h2 className="legal-subheading">Limitation of Liability</h2>
      <p className="legal-text">
        To the maximum extent permitted by law, KojaPay shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues.
      </p>
    </div>
  );

  const renderEscrowWarning = () => (
    <Card className="modern-card mb-6">
      <CardHeader className="flex flex-row items-start space-x-4 pb-2">
        <AlertTriangle className="h-6 w-6 text-amber-500 mt-1" />
        <div>
          <CardTitle className="text-lg">Escrow Service Agreement</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          By using our escrow service, you agree to the following terms:
        </p>
        <ul className="space-y-2 text-sm">
          <li className="flex items-start gap-2">
            <span className="bg-amber-100 text-amber-800 p-1 rounded-full">•</span>
            <span>Funds will be held securely until all agreed conditions are met.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="bg-amber-100 text-amber-800 p-1 rounded-full">•</span>
            <span>KojaPay acts as a neutral third party and will release funds according to the escrow agreement.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="bg-amber-100 text-amber-800 p-1 rounded-full">•</span>
            <span>Disputes must be resolved according to our dispute resolution process.</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="bg-amber-100 text-amber-800 p-1 rounded-full">•</span>
            <span>A service fee applies to all escrow transactions as outlined in our fee schedule.</span>
          </li>
        </ul>
        <div className="mt-4 text-sm text-muted-foreground italic">
          By proceeding with the escrow service, you acknowledge that you have read and agree to these terms.
        </div>
      </CardContent>
    </Card>
  );

  const renderSecurityInfo = () => (
    <Card className="modern-card mb-6">
      <CardHeader className="flex flex-row items-start space-x-4 pb-2">
        <Shield className="h-6 w-6 text-green-500 mt-1" />
        <div>
          <CardTitle className="text-lg">Security Information</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          Your security is our priority. KojaPay employs several measures to protect your account:
        </p>
        <ul className="space-y-2 text-sm">
          <li className="flex items-start gap-2">
            <span className="bg-green-100 text-green-800 p-1 rounded-full">•</span>
            <span>End-to-end encryption for all transactions</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="bg-green-100 text-green-800 p-1 rounded-full">•</span>
            <span>Multi-factor authentication to prevent unauthorized access</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="bg-green-100 text-green-800 p-1 rounded-full">•</span>
            <span>Real-time fraud monitoring systems</span>
          </li>
          <li className="flex items-start gap-2">
            <span className="bg-green-100 text-green-800 p-1 rounded-full">•</span>
            <span>Regular security audits and compliance with industry standards</span>
          </li>
        </ul>
        <div className="mt-4 text-sm text-muted-foreground">
          If you notice any suspicious activity on your account, please contact our support team immediately.
        </div>
      </CardContent>
    </Card>
  );

  const renderContent = () => {
    switch (type) {
      case 'privacy':
        return renderPrivacyPolicy();
      case 'terms':
        return renderTermsOfService();
      case 'escrow':
        return renderEscrowWarning();
      case 'security':
        return renderSecurityInfo();
      default:
        return <p>Select a legal document to view</p>;
    }
  };

  return renderContent();
};

export default LegalContent;
