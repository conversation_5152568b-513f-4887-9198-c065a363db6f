import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { securityService, SecuritySettings, FraudAlert } from '@/services/securityService';
import { useToast } from '@/hooks/use-toast';

interface SecurityContextType {
  settings: SecuritySettings | null;
  alerts: Fraud<PERSON>lert[];
  loading: boolean;
  updateSettings: (settings: Partial<SecuritySettings>) => Promise<boolean>;
  refreshAlerts: () => Promise<void>;
  updateAlertStatus: (alertId: string, status: 'pending' | 'reviewing' | 'resolved' | 'blocked', actionBy: string) => Promise<boolean>;
  detectFraud: (transaction: any) => Promise<any>;
}

const SecurityContext = createContext<SecurityContextType | null>(null);

export const SecurityProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [settings, setSettings] = useState<SecuritySettings | null>(null);
  const [alerts, setAlerts] = useState<FraudAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();
  
  useEffect(() => {
    const fetchSecurityData = async () => {
      setLoading(true);
      try {
        const userId = 'user123';
        const [settingsData, alertsData] = await Promise.all([
          securityService.getSecuritySettings(userId),
          securityService.getFraudAlerts(userId)
        ]);
        
        setSettings(settingsData);
        setAlerts(alertsData);
      } catch (error) {
        console.error('Error fetching security data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load security settings',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchSecurityData();
  }, [toast]);
  
  const updateSettings = async (updatedSettings: Partial<SecuritySettings>): Promise<boolean> => {
    try {
      const success = await securityService.updateSecuritySettings(updatedSettings);
      
      if (success && settings) {
        setSettings({
          ...settings,
          ...updatedSettings
        });
      }
      
      return success;
    } catch (error) {
      console.error('Error updating security settings:', error);
      return false;
    }
  };
  
  const refreshAlerts = async (): Promise<void> => {
    try {
      const userId = 'user123';
      const alertsData = await securityService.getFraudAlerts(userId);
      setAlerts(alertsData);
    } catch (error) {
      console.error('Error refreshing alerts:', error);
    }
  };
  
  const updateAlertStatus = async (
    alertId: string,
    status: 'pending' | 'reviewing' | 'resolved' | 'blocked',
    actionBy: string
  ): Promise<boolean> => {
    try {
      const success = await securityService.updateAlertStatus(alertId, status, actionBy);
      
      if (success) {
        setAlerts(alerts.map(alert => 
          alert.id === alertId 
            ? { ...alert, status, actionBy, actionTimestamp: new Date().toISOString() }
            : alert
        ));
      }
      
      return success;
    } catch (error) {
      console.error('Error updating alert status:', error);
      return false;
    }
  };
  
  const detectFraud = async (transaction: any) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const isHighAmount = transaction.amount > 10000;
      const isInternational = transaction.isInternational || false;
      const isNewRecipient = transaction.isNewRecipient || false;
      
      const riskScore = (isHighAmount ? 25 : 0) + 
                        (isInternational ? 30 : 0) + 
                        (isNewRecipient ? 15 : 0);
      
      const riskFactors = [];
      if (isHighAmount) riskFactors.push('high_amount');
      if (isInternational) riskFactors.push('international_transfer');
      if (isNewRecipient) riskFactors.push('new_recipient');
      
      let action = 'allow';
      if (riskScore >= 50) {
        action = 'block';
      } else if (riskScore >= 25) {
        action = 'review';
      }
      
      return {
        isFraudulent: riskScore >= 50,
        riskScore,
        riskFactors,
        action
      };
    } catch (error) {
      console.error('Error in fraud detection:', error);
      throw error;
    }
  };
  
  const value = {
    settings,
    alerts,
    loading,
    updateSettings,
    refreshAlerts,
    updateAlertStatus,
    detectFraud
  };
  
  return (
    <SecurityContext.Provider value={value}>
      {children}
    </SecurityContext.Provider>
  );
};

export const useSecurity = () => {
  const context = useContext(SecurityContext);
  if (!context) {
    throw new Error('useSecurity must be used within a SecurityProvider');
  }
  return context;
};

export const useSecurityContext = useSecurity;

export default SecurityProvider;
