// Database setup script for KojaPay BankOne Integration

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

/**
 * Setup database for KojaPay application
 */
async function setupDatabase() {
  try {
    console.log('🔄 Starting database setup...');

    // Check if PostgreSQL is installed
    try {
      console.log('✅ Checking PostgreSQL installation...');
      execSync('postgres --version', { stdio: 'ignore' });
    } catch (error) {
      console.error('❌ PostgreSQL is not installed or not in PATH. Please install PostgreSQL before running this script.');
      process.exit(1);
    }

    // Create database if it doesn't exist
    console.log('🔄 Creating database if it doesn\'t exist...');
    
    const dbConnectionString = process.env.DATABASE_URL;
    if (!dbConnectionString) {
      console.error('❌ DATABASE_URL environment variable is not set. Please check your .env file.');
      process.exit(1);
    }

    // Extract database name from connection string
    const dbName = dbConnectionString.split('/').pop().split('?')[0];
    console.log(`📊 Target database: ${dbName}`);

    try {
      // Run prisma migrate to create database and apply migrations
      console.log('🔄 Running Prisma migrations...');
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      
      console.log('✅ Database migration completed successfully!');
    } catch (error) {
      console.error('❌ Error during database migration:', error.message);
      process.exit(1);
    }

    // Generate Prisma client
    console.log('🔄 Generating Prisma client...');
    try {
      execSync('npx prisma generate', { stdio: 'inherit' });
      console.log('✅ Prisma client generated successfully!');
    } catch (error) {
      console.error('❌ Error generating Prisma client:', error.message);
      process.exit(1);
    }

    console.log('🎉 Database setup completed successfully!');
    console.log('📝 You can now start the application with: npm run dev');
    
  } catch (error) {
    console.error('❌ Unexpected error during database setup:', error);
    process.exit(1);
  }
}

// Run the setup function
setupDatabase().catch(err => {
  console.error('Failed to setup database:', err);
  process.exit(1);
});
