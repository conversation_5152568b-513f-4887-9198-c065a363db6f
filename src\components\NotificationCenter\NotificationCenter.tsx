
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Off, Check, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import notificationService, { Notification, NotificationType } from '@/services/notificationService';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { 
  Alert,
  AlertTitle,
  AlertDescription,
} from '@/components/ui/alert';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

interface NotificationCenterProps {
  userId?: string;
}

interface ComponentNotification extends Omit<Notification, 'isRead'> {
  read: boolean;
}

const NotificationCenter: React.FC<NotificationCenterProps> = ({ userId }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const actualUserId = userId || user?.id;
  
  const [notifications, setNotifications] = useState<ComponentNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const { toast } = useToast();

  useEffect(() => {
    if (actualUserId) {
      fetchNotifications();
    }
  }, [actualUserId]);

  useEffect(() => {
    if (actualUserId) {
      fetchUnreadCount();
      const interval = setInterval(fetchUnreadCount, 60000);
      return () => clearInterval(interval);
    }
  }, [actualUserId]);

  const fetchNotifications = async () => {
    if (!actualUserId) return;
    
    setIsLoading(true);
    try {
      const response = await notificationService.getNotifications(actualUserId);
      if (response.success && response.data) {
        const componentNotifications = response.data.map(notification => ({
          ...notification,
          read: notification.isRead
        }));
        
        setNotifications(componentNotifications);
        fetchUnreadCount();
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setError('An error occurred while fetching notifications');
      if (retryCount < 3) {
        setTimeout(() => {
          setRetryCount(retryCount + 1);
          fetchNotifications();
        }, 2000 * (retryCount + 1));
      }
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUnreadCount = async () => {
    if (!actualUserId) return;
    
    try {
      const response = await notificationService.getUnreadCount(actualUserId);
      if (response.success && response.data) {
        setUnreadCount(response.data.count);
      }
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const response = await notificationService.markAsRead(notificationId);
      if (response.success) {
        setNotifications(prevNotifications =>
          prevNotifications.map(notification =>
            notification.id === notificationId
              ? { ...notification, read: true }
              : notification
          )
        );
        fetchUnreadCount();
      } else {
        setError('Failed to mark notification as read');
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      setError('An error occurred while updating notification');
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!actualUserId) return;
    
    try {
      const response = await notificationService.markAllAsRead(actualUserId);
      if (response.success) {
        setNotifications(prevNotifications =>
          prevNotifications.map(notification => ({ ...notification, read: true }))
        );
        setUnreadCount(0);
        toast({
          title: 'Success',
          description: 'All notifications marked as read',
        });
      } else {
        setError('Failed to mark all notifications as read');
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      setError('An error occurred while updating notifications');
    }
  };

  const handleDeleteNotification = async (notificationId: string) => {
    try {
      const response = await notificationService.deleteNotification(notificationId);
      if (response.success) {
        setNotifications(prevNotifications =>
          prevNotifications.filter(notification => notification.id !== notificationId)
        );
        fetchUnreadCount();
        toast({
          title: 'Success',
          description: 'Notification deleted',
        });
      } else {
        setError('Failed to delete notification');
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
      setError('An error occurred while deleting notification');
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeTab === 'all') return true;
    return notification.type.toLowerCase() === activeTab.toLowerCase();
  });

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.TRANSACTION:
        return <Bell className="h-4 w-4 text-blue-500" />;
      case NotificationType.SECURITY:
        return <Bell className="h-4 w-4 text-red-500" />;
      case NotificationType.SYSTEM:
        return <Bell className="h-4 w-4 text-amber-500" />;
      case NotificationType.MARKETING:
        return <Bell className="h-4 w-4 text-purple-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="notification-center">
      <div className="flex items-center justify-between border-b p-3">
        <h4 className="font-medium">Notifications</h4>
        <div className="flex gap-1">
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleMarkAllAsRead}
              title="Mark all as read"
            >
              <Check className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => navigate('/notifications')}
            title="View all"
          >
            <Bell className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {error && (
        <Alert variant="destructive" className="m-2">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="transaction">Trans</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>
        <TabsContent value={activeTab} className="p-0">
          <ScrollArea className="h-[300px]">
            {isLoading ? (
              <div className="flex h-[300px] items-center justify-center">
                <p className="text-sm text-gray-500">Loading notifications...</p>
              </div>
            ) : filteredNotifications.length > 0 ? (
              <div className="space-y-1 p-1">
                {filteredNotifications.map(notification => (
                  <div
                    key={notification.id}
                    className={cn(
                      'flex items-start gap-2 rounded-md p-2 transition-colors hover:bg-gray-100',
                      !notification.read && 'bg-blue-50'
                    )}
                  >
                    <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between gap-2">
                        <h5 className="font-medium">{notification.title}</h5>
                        <div className="flex items-center gap-1">
                          {!notification.read && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => handleMarkAsRead(notification.id)}
                              title="Mark as read"
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => handleDeleteNotification(notification.id)}
                            title="Delete"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-500">{notification.message}</p>
                      <span className="mt-1 text-xs text-gray-400">
                        {new Date(notification.createdAt).toLocaleString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex h-[300px] flex-col items-center justify-center gap-2 p-4">
                <BellOff className="h-8 w-8 text-gray-400" />
                <p className="text-center text-sm text-gray-500">
                  No notifications to display
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
      <div className="border-t p-2">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => navigate('/notifications')}
        >
          View all notifications
        </Button>
      </div>
    </div>
  );
};

export default NotificationCenter;
