
import React from 'react';
import { AdminSidebar } from './AdminSidebar';
import { useIsMobile } from '../hooks/use-mobile';
import { Button } from './ui/button';
import { MenuIcon, XIcon, Bell, User, ChevronDown } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { Avatar, AvatarFallback } from './ui/avatar';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "./ui/dropdown-menu";
import { NotificationBadge } from "./ui/notification-badge";

interface AdminLayoutProps {
  children: React.ReactNode;
  pageTitle?: string;
}

export const AdminLayout = ({ children, pageTitle }: AdminLayoutProps) => {
  const [sidebarOpen, setSidebarOpen] = React.useState(true);
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const logoUrl = "/lovable-uploads/1928aa40-2de0-48fc-ba20-8312b8e3f05b.png";

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="min-h-screen bg-background flex">
      <div
        className={`fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-200 lg:hidden ${
          sidebarOpen && !isMobile ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={toggleSidebar}
      />
      <div
        className={`fixed lg:static w-[280px] h-full z-50 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
        } transition-transform duration-300`}
      >
        <AdminSidebar />
      </div>
      <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
        <div className="sticky top-0 z-30 flex items-center justify-between bg-background/95 backdrop-blur-sm px-4 h-16 border-b">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={toggleSidebar}
            >
              {sidebarOpen ? (
                <XIcon className="h-5 w-5" />
              ) : (
                <MenuIcon className="h-5 w-5" />
              )}
            </Button>
            <div className="flex items-center gap-2">
              <img 
                src={logoUrl}
                alt="KojaPay Admin"
                className="h-8 w-8 hidden lg:block"
              />
              <h1 className="text-xl font-semibold">{pageTitle || 'Admin Portal'}</h1>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="icon" className="relative" onClick={() => navigate('/admin/notifications')}>
              <Bell className="h-5 w-5" />
              <NotificationBadge variant="destructive" size="sm" className="absolute -top-0.5 -right-0.5">3</NotificationBadge>
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center gap-2 cursor-pointer p-1 rounded-full hover:bg-gray-100 transition-colors">
                  <Avatar className="h-8 w-8 border-2 border-transparent">
                    <AvatarFallback className="bg-kojaPrimary text-white">A</AvatarFallback>
                  </Avatar>
                  <ChevronDown className="h-4 w-4 text-kojaGray" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/staff')}>Manage Staff</DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/roles')}>Roles & Permissions</DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/settings')}>Settings</DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/admin/support')}>Support</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-500 cursor-pointer" onClick={() => navigate('/admin/login')}>
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <main className="flex-1 overflow-y-auto">
          <div className="container mx-auto p-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
