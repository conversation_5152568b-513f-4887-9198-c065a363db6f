import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { ArrowUpRight, BarChart4, CreditCard, LineChart, Lock, Shield, TrendingUp, Users, Receipt, Send, Building, FileText, QrCode, Landmark, Terminal, ReceiptText, LinkIcon, Smartphone, Settings, Wallet } from 'lucide-react';
import BusinessLayout from '../components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import BusinessServiceCard from '@/components/BusinessServiceCard';
import StatCard from '@/components/StatCard';
import EnhancedVirtualCard from '@/components/EnhancedVirtualCard';
import { useNavigate } from 'react-router-dom';

const BusinessDashboard = () => {
  const {
    user
  } = useAuth();
  const {
    toast
  } = useToast();
  const isMobile = useIsMobile();
  const navigate = useNavigate();

  const businessUser = user as any;
  const username = businessUser?.businessName || 'Your Business';
  const statCards = [{
    title: "Balance",
    value: `₦${businessUser?.balance?.toLocaleString() || '0'}`,
    icon: <CreditCard className="text-purple-500" size={24} />,
    trend: {
      value: "****%",
      positive: true
    }
  }, {
    title: "Revenue",
    value: "₦230,400",
    icon: <TrendingUp className="text-emerald-500" size={24} />,
    trend: {
      value: "****%",
      positive: true
    }
  }, {
    title: "Customers",
    value: "2,345",
    icon: <Users className="text-blue-500" size={24} />,
    trend: {
      value: "+12.2%",
      positive: true
    }
  }, {
    title: "Expenses",
    value: "₦78,500",
    icon: <BarChart4 className="text-amber-500" size={24} />,
    trend: {
      value: "-1.8%",
      positive: false
    }
  }];
  const insights = [{
    title: "Cash Flow",
    value: "+12%",
    description: "Your cash flow has improved compared to last month.",
    icon: <LineChart className="text-green-500" size={20} />
  }, {
    title: "Security Score",
    value: "85/100",
    description: "Your account security is good, but could be improved.",
    icon: <Shield className="text-blue-500" size={20} />
  }, {
    title: "Payment Success",
    value: "98.3%",
    description: "Your payment success rate is above average.",
    icon: <Lock className="text-purple-500" size={20} />
  }];

  const businessServices = [{
    title: "Bill Payment",
    description: "Pay utilities, subscriptions and other bills",
    icon: Receipt,
    route: "/business/bill-payment",
    color: "blue" as const
  }, {
    title: "Send Money",
    description: "Transfer to accounts within KojaPay",
    icon: Send,
    route: "/business/transfer/same-bank",
    color: "yellow" as const
  }, {
    title: "Other Bank Transfer",
    description: "Send money to other banks",
    icon: Building,
    route: "/business/transfer/other-banks",
    color: "default" as const
  }, {
    title: "Loan Application",
    description: "Access business financing options",
    icon: Landmark,
    route: "/business/loans",
    color: "blue" as const
  }, {
    title: "POS Terminal",
    description: "Request and manage POS terminals",
    icon: Terminal,
    route: "/business/pos-management",
    color: "yellow" as const
  }, {
    title: "Invoice Generator",
    description: "Create and manage invoices",
    icon: FileText,
    route: "/business/invoices",
    color: "default" as const
  }, {
    title: "Payment Links",
    description: "Generate shareable payment links",
    icon: LinkIcon,
    route: "/business/payment-links",
    color: "blue" as const
  }, {
    title: "QR Code",
    description: "Create and manage payment QR codes",
    icon: QrCode,
    route: "/business/qr-codes",
    color: "yellow" as const
  }];

  return <BusinessLayout pageTitle="Business Dashboard">
      <div className="space-y-5">
        <Card className="card-shadow-hover animate-fade-in backdrop-blur-md bg-white/70 border border-white/30">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <h2 className="text-xl sm:text-2xl font-semibold mb-2">Welcome back, {username}!</h2>
                <p className="text-kojaGray text-sm sm:text-base">Here's what's happening with your business today.</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button variant="outline" className="card-shadow-hover">
                  Generate Report <ArrowUpRight className="ml-2 h-4 w-4" />
                </Button>
                <Button className="cta-button">Fund Account</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
          {statCards.map((stat, index) => <StatCard key={index} title={stat.title} value={stat.value} icon={stat.icon} trend={stat.trend} className="h-auto" />)}
        </div>

        <div className="animate-fade-in">
          <h3 className="text-lg font-medium mb-3 sm:mb-4">Business Virtual Card</h3>
          <Card className="glass-morph border-kojaPrimary/20 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                <div className="w-full md:w-auto">
                  <EnhancedVirtualCard
                    cardNumber="**** **** **** 7690"
                    name={businessUser?.businessName?.toUpperCase() || "YOUR BUSINESS"}
                    expiryDate="08/26"
                    cardType="business"
                    variant="virtual"
                    className="mx-auto"
                  />
                </div>
                <div className="w-full md:w-auto space-y-4">
                  <div className="space-y-2">
                    <p className="text-kojaGray text-sm">Card Balance</p>
                    <h3 className="text-2xl font-bold">₦120,000</h3>
                    <p className="text-sm text-kojaGray">Limit: ₦500,000</p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button 
                      variant="outline" 
                      className="flex items-center gap-2 border-kojaPrimary/20 text-kojaPrimary shadow-sm hover:shadow-md"
                      onClick={() => navigate('/business/cards')}
                    >
                      <Shield size={16} />
                      <span>Card Details</span>
                    </Button>
                    <Button 
                      className="flex items-center gap-2 bg-[#9b87f5] hover:bg-[#9b87f5]/90 text-white shadow-sm hover:shadow-md"
                      onClick={() => navigate('/business/cards')}
                    >
                      <Settings size={16} />
                      <span>Manage Card</span>
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="animate-fade-in">
          <h3 className="text-lg font-medium mb-3 sm:mb-4">quick Services</h3>
          <div className="grid grid-cols-2 xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
            {businessServices.map((service, index) => <BusinessServiceCard key={index} title={service.title} description={service.description} icon={service.icon} route={service.route} color={service.color} />)}
          </div>
        </div>

        <Tabs defaultValue="overview" className="animate-fade-in">
          <TabsList className="grid w-full grid-cols-3 mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {insights.map((insight, i) => <Card key={i} className="bg-white/80 backdrop-blur-md border border-white/30 shadow-sm hover:shadow-md rounded-xl">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-sm font-medium">{insight.title}</CardTitle>
                      {insight.icon}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold mb-1">{insight.value}</div>
                    <p className="text-xs text-kojaGray">{insight.description}</p>
                  </CardContent>
                </Card>)}
            </div>
            <Card className="modern-glass-card">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Actions you can take right now</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                  <Button variant="outline" size="sm" className="text-sm h-auto py-2 flex gap-2">
                    <Receipt size={16} /> Create Invoice
                  </Button>
                  <Button variant="outline" size="sm" className="text-sm h-auto py-2 flex gap-2">
                    <Send size={16} /> Transfer Funds
                  </Button>
                  <Button variant="outline" size="sm" className="text-sm h-auto py-2 flex gap-2">
                    <ReceiptText size={16} /> Generate Report
                  </Button>
                  <Button variant="outline" size="sm" className="text-sm h-auto py-2 flex gap-2">
                    <Smartphone size={16} /> Request POS
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="transactions">
            <Card className="modern-glass-card">
              <CardHeader>
                <CardTitle>Recent Transactions</CardTitle>
                <CardDescription>Your latest business transactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[{
                  recipient: 'Office Supplies',
                  date: 'Today, 14:30',
                  amount: '₦45,200',
                  status: 'Completed'
                }, {
                  recipient: 'Staff Salary',
                  date: 'Yesterday',
                  amount: '₦120,000',
                  status: 'Completed'
                }, {
                  recipient: 'Equipment Purchase',
                  date: '12 Jun, 2023',
                  amount: '₦350,000',
                  status: 'Pending'
                }].map((transaction, index) => <div key={index} className="flex justify-between items-center py-3 border-b border-gray-100 last:border-0">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-600">
                          {transaction.recipient.charAt(0)}
                        </div>
                        <div>
                          <div className="font-medium text-kojaDark text-sm sm:text-base">{transaction.recipient}</div>
                          <div className="text-xs text-gray-500">{transaction.date}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-kojaDark text-sm sm:text-base">{transaction.amount}</div>
                        <div className={`text-xs ${transaction.status === 'Completed' ? 'text-green-500' : 'text-amber-500'}`}>
                          {transaction.status}
                        </div>
                      </div>
                    </div>)}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="insights">
            <Card className="modern-glass-card">
              <CardHeader>
                <CardTitle>Business Insights</CardTitle>
                <CardDescription>Key business metrics and trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <p className="text-kojaGray">Visit the Analytics page for detailed charts and insights</p>
                  <Button className="mt-4" onClick={() => window.location.href = '/business/analysis'}>
                    Go to Analytics
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>;
};
export default BusinessDashboard;
