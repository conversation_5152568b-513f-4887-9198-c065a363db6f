# KojaPay Production Environment

# BankOne API Configuration
BANKONE_CORE_API_URL=https://staging.mybankone.com/BankOneWebAPI/
BANKONE_CHANNELS_API_URL=https://staging.mybankone.com/thirdpartyapiservice/apiservice/
BANKONE_INSTITUTION_CODE=100575
BANKONE_AUTH_TOKEN=C964E4F0-8F2D-45BB-8457-C2C99B1191A6
BANKONE_API_KEY=a67d9e3f-8982-4c37-ab95-6efe8bb3cd39

# Database Configuration
DATABASE_URL=postgresql://kojapay_user:strongPassword123@localhost:5432/kojapay_db?schema=public

# Application Configuration
NODE_ENV=development
PORT=3000
JWT_SECRET=kojapay-secure-jwt-secret-key-12345
JWT_EXPIRES_IN=24h
BACKEND_URL=http://localhost:3000
FRONTEND_URL=http://localhost:5173

# Redis Configuration (for session/cache)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email Configuration
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=emailPassword123
EMAIL_FROM=<EMAIL>

# Mapbox Configuration
NEXT_PUBLIC_MAPBOX_TOKEN=pk.eyJ1Ijoia29qYXBheSIsImEiOiJjbHM4NWVxMmQwMXgzMnFvNXBqeGRoNWdqIn0.example_mapbox_token
