import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

interface OTPVerificationProps {
  onVerify: (otp: string) => void;
  onCancel: () => void;
  isBusinessAccount?: boolean;
  phoneNumber?: string;
}

const OTPVerification: React.FC<OTPVerificationProps> = ({
  onVerify,
  onCancel,
  isBusinessAccount = false,
  phoneNumber = "+234•••••1234"
}) => {
  const [otp, setOtp] = useState("");
  const [countdown, setCountdown] = useState(60);
  const [isResending, setIsResending] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [countdown]);

  const handleVerify = () => {
    if (otp.length < 6) {
      toast({
        variant: "destructive",
        title: "Invalid OTP",
        description: "Please enter a valid 6-digit OTP code"
      });
      return;
    }
    
    onVerify(otp);
  };

  const handleResendOTP = () => {
    setIsResending(true);
    
    // Simulate OTP resend
    setTimeout(() => {
      setCountdown(60);
      setIsResending(false);
      toast({
        title: "OTP Resent",
        description: `A new OTP has been sent to ${phoneNumber}`,
        variant: "success"
      });
    }, 1500);
  };

  return (
    <Card className="w-full max-w-md mx-auto backdrop-blur-xl bg-white/80">
      <CardHeader>
        <CardTitle>
          {isBusinessAccount ? 'Business Verification' : 'Security Verification'}
        </CardTitle>
        <CardDescription>
          {isBusinessAccount 
            ? `Enter the SMS token sent to ${phoneNumber} to complete this transaction.` 
            : `Enter the 6-digit code sent to ${phoneNumber} to verify this transaction.`}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <InputOTP
          maxLength={6}
          value={otp}
          onChange={setOtp}
        >
          <InputOTPGroup className="gap-2">
            {[...Array(6)].map((_, index) => (
              <InputOTPSlot key={index} index={index} className="rounded-[20px] border-gray-300" />
            ))}
          </InputOTPGroup>
        </InputOTP>
        
        <div className="text-center text-sm text-muted-foreground">
          {countdown > 0 ? (
            <p>Resend code in {countdown} seconds</p>
          ) : (
            <button 
              onClick={handleResendOTP} 
              disabled={isResending}
              className="text-kojaPrimary hover:underline focus:outline-none disabled:text-gray-400"
            >
              {isResending ? 'Sending...' : 'Resend Code'}
            </button>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel} className="rounded-[20px]">
          Cancel
        </Button>
        <Button onClick={handleVerify} className="rounded-[20px]">
          Verify & Complete
        </Button>
      </CardFooter>
    </Card>
  );
};

export default OTPVerification;
