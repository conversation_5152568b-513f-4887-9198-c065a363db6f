
import React, { useState, useEffect } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  UserPlus, 
  Users, 
  User, 
  UserCheck, 
  UserMinus, 
  Search,
  MoreHorizontal,
  Mail,
  Phone,
  MapPin,
  Filter,
  Download,
  Pencil,
  Trash2,
  Building,
  Calendar,
  DollarSign,
  FileText,
  ClipboardList,
  Briefcase,
  AlertCircle,
  CheckCircle,
  X,
  Plus,
  RefreshCw,
  ChevronDown,
  Loader2,
  Banknote
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Staff, Department, staffService } from '@/services/staffService';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Textarea } from "@/components/ui/textarea";
import TransactionStatus from '@/components/TransactionStatus';

const BusinessStaff = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  
  // States for staff management
  const [staff, setStaff] = useState<Staff[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('staff');
  const [selectedStaff, setSelectedStaff] = useState<Staff | null>(null);
  const [staffDetails, setStaffDetails] = useState<Staff | null>(null);
  const [showStaffForm, setShowStaffForm] = useState(false);
  const [showDepartmentForm, setShowDepartmentForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<string | null>(null);
  const [departmentToDelete, setDepartmentToDelete] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDepartment, setFilterDepartment] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [staffStats, setStaffStats] = useState<{
    totalStaff: number;
    activeStaff: number;
    inactiveStaff: number;
    onLeaveStaff: number;
    departmentCounts: { name: string; count: number }[];
  } | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [showDepartmentDeleteConfirm, setShowDepartmentDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Form states
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    salary: '',
    dateHired: '',
    status: 'active',
    bankName: '',
    accountNumber: '',
    taxId: '',
    address: '',
    emergencyContact: '',
    profileImage: ''
  });
  
  const [departmentFormData, setDepartmentFormData] = useState({
    name: '',
    description: '',
    managerId: ''
  });

  // Load staff and departments on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchStaffData();
      await fetchDepartments();
      await fetchStaffStatistics();
    };
    
    loadInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // Fetch staff data with filters
  const fetchStaffData = async () => {
    setIsLoading(true);
    try {
      const filters = {
        department: filterDepartment || undefined,
        status: filterStatus || undefined,
        search: searchQuery || undefined
      };
      
      const staffData = await staffService.getStaff(filters);
      setStaff(staffData);
    } catch (error) {
      console.error('Error fetching staff:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch staff data',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch departments
  const fetchDepartments = async () => {
    try {
      const departmentsData = await staffService.getDepartments();
      setDepartments(departmentsData);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch departments',
        variant: 'destructive'
      });
    }
  };
  
  // Fetch staff statistics
  const fetchStaffStatistics = async () => {
    try {
      const stats = await staffService.getStaffStatistics();
      setStaffStats(stats);
    } catch (error) {
      console.error('Error fetching staff statistics:', error);
    }
  };
  
  // Apply filters
  useEffect(() => {
    fetchStaffData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterDepartment, filterStatus, searchQuery]);
  
  // Reset form data
  const resetFormData = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      position: '',
      department: '',
      salary: '',
      dateHired: '',
      status: 'active',
      bankName: '',
      accountNumber: '',
      taxId: '',
      address: '',
      emergencyContact: '',
      profileImage: ''
    });
  };
  
  // Reset department form data
  const resetDepartmentFormData = () => {
    setDepartmentFormData({
      name: '',
      description: '',
      managerId: ''
    });
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'salary' ? value.replace(/[^0-9]/g, '') : value
    });
  };
  
  // Handle select input changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };
  
  // Handle department form input changes
  const handleDepartmentInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setDepartmentFormData({
      ...departmentFormData,
      [name]: value
    });
  };
  
  // Handle department select changes
  const handleDepartmentSelectChange = (name: string, value: string) => {
    setDepartmentFormData({
      ...departmentFormData,
      [name]: value
    });
  };
  
  // Open staff form for creating new staff
  const handleAddStaff = () => {
    setFormMode('create');
    resetFormData();
    setShowStaffForm(true);
  };
  
  // Open staff form for editing existing staff
  const handleEditStaff = (staff: Staff) => {
    setFormMode('edit');
    setFormData({
      firstName: staff.firstName,
      lastName: staff.lastName,
      email: staff.email,
      phone: staff.phone,
      position: staff.position,
      department: staff.department,
      salary: staff.salary.toString(),
      dateHired: staff.dateHired,
      status: staff.status,
      bankName: staff.bankName || '',
      accountNumber: staff.accountNumber || '',
      taxId: staff.taxId || '',
      address: staff.address || '',
      emergencyContact: staff.emergencyContact || '',
      profileImage: staff.profileImage || ''
    });
    setSelectedStaff(staff);
    setShowStaffForm(true);
  };
  
  // Open department form for creating new department
  const handleAddDepartment = () => {
    resetDepartmentFormData();
    setShowDepartmentForm(true);
  };
  
  // Open department form for editing existing department
  const handleEditDepartment = (department: Department) => {
    const manager = staff.find(s => s.id === department.managerId);
    
    setDepartmentFormData({
      name: department.name,
      description: department.description || '',
      managerId: department.managerId || ''
    });
    
    setDepartmentToDelete(department.id);
    setShowDepartmentForm(true);
  };
  
  // Handle staff form submission
  const handleStaffFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Validate form
      if (!formData.firstName || !formData.lastName || !formData.email || !formData.position || 
          !formData.department || !formData.salary || !formData.dateHired) {
        toast({
          title: 'Validation Error',
          description: 'Please fill all required fields',
          variant: 'destructive'
        });
        setIsSubmitting(false);
        return;
      }
      
      if (formMode === 'create') {
        // Create new staff
        await staffService.createStaff({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          position: formData.position,
          department: formData.department,
          salary: parseFloat(formData.salary),
          dateHired: formData.dateHired,
          status: formData.status as 'active' | 'inactive' | 'on_leave',
          bankName: formData.bankName,
          accountNumber: formData.accountNumber,
          taxId: formData.taxId,
          address: formData.address,
          emergencyContact: formData.emergencyContact,
          profileImage: formData.profileImage,
          joinDate: formData.dateHired
        });
        
        toast({
          title: 'Success',
          description: 'Staff member created successfully'
        });
      } else {
        // Update existing staff
        if (!selectedStaff) {
          toast({
            title: 'Error',
            description: 'No staff selected for update',
            variant: 'destructive'
          });
          setIsSubmitting(false);
          return;
        }
        
        await staffService.updateStaff(selectedStaff.id, {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          position: formData.position,
          department: formData.department,
          salary: parseFloat(formData.salary),
          status: formData.status as 'active' | 'inactive' | 'on_leave',
          bankName: formData.bankName,
          accountNumber: formData.accountNumber,
          taxId: formData.taxId,
          address: formData.address,
          emergencyContact: formData.emergencyContact,
          profileImage: formData.profileImage
        });
        
        toast({
          title: 'Success',
          description: 'Staff member updated successfully'
        });
      }
      
      // Refresh staff data
      fetchStaffData();
      fetchStaffStatistics();
      
      // Close form
      setShowStaffForm(false);
      setSelectedStaff(null);
    } catch (error) {
      console.error('Error submitting staff form:', error);
      toast({
        title: 'Error',
        description: 'Failed to save staff data',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle department form submission
  const handleDepartmentFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      // Validate form
      if (!departmentFormData.name) {
        toast({
          title: 'Validation Error',
          description: 'Please enter a department name',
          variant: 'destructive'
        });
        setIsSubmitting(false);
        return;
      }
      
      if (departmentToDelete) {
        // Update existing department
        await staffService.updateDepartment(departmentToDelete, {
          name: departmentFormData.name,
          description: departmentFormData.description,
          managerId: departmentFormData.managerId || undefined
        });
        
        toast({
          title: 'Success',
          description: 'Department updated successfully'
        });
      } else {
        // Create new department
        await staffService.createDepartment({
          name: departmentFormData.name,
          description: departmentFormData.description,
          managerId: departmentFormData.managerId || undefined
        });
        
        toast({
          title: 'Success',
          description: 'Department created successfully'
        });
      }
      
      // Refresh department data
      fetchDepartments();
      
      // Close form
      setShowDepartmentForm(false);
      setDepartmentToDelete(null);
    } catch (error) {
      console.error('Error submitting department form:', error);
      toast({
        title: 'Error',
        description: 'Failed to save department data',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle staff deletion
  const handleDeleteStaff = async () => {
    if (!staffToDelete) return;
    
    setIsSubmitting(true);
    try {
      await staffService.deleteStaff(staffToDelete);
      
      toast({
        title: 'Success',
        description: 'Staff member deleted successfully'
      });
      
      // Refresh staff data
      fetchStaffData();
      fetchStaffStatistics();
      
      // Close dialog
      setShowDeleteConfirm(false);
      setStaffToDelete(null);
    } catch (error) {
      console.error('Error deleting staff:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete staff member',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle department deletion
  const handleDeleteDepartment = async () => {
    if (!departmentToDelete) return;
    
    setIsSubmitting(true);
    try {
      await staffService.deleteDepartment(departmentToDelete);
      
      toast({
        title: 'Success',
        description: 'Department deleted successfully'
      });
      
      // Refresh department data
      fetchDepartments();
      
      // Close dialog
      setShowDepartmentDeleteConfirm(false);
      setDepartmentToDelete(null);
    } catch (error) {
      console.error('Error deleting department:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete department. Make sure no staff are assigned to this department.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // View staff details
  const handleViewStaffDetails = async (staffId: string) => {
    try {
      const staffData = await staffService.getStaffById(staffId);
      setStaffDetails(staffData);
    } catch (error) {
      console.error('Error fetching staff details:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch staff details',
        variant: 'destructive'
      });
    }
  };
  
  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">Inactive</Badge>;
      case 'on_leave':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">On Leave</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">{status}</Badge>;
    }
  };
  
  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <BusinessLayout pageTitle="Staff Management">
      <div className="space-y-6">
        {/* Tabs for Staff and Departments */}
        <Tabs defaultValue="staff" className="w-full" onValueChange={setActiveTab}>
          <div className="flex items-center justify-between mb-4">
            <TabsList className="bg-gray-100">
              <TabsTrigger value="staff" className="data-[state=active]:bg-[#1231B8] data-[state=active]:text-white">
                <Users className="mr-2 h-4 w-4" /> Staff
              </TabsTrigger>
              <TabsTrigger value="departments" className="data-[state=active]:bg-[#1231B8] data-[state=active]:text-white">
                <Building className="mr-2 h-4 w-4" /> Departments
              </TabsTrigger>
            </TabsList>
            
            {activeTab === 'staff' ? (
              <Button onClick={handleAddStaff} className="bg-[#1231B8] hover:bg-[#1231B8]/90">
                <UserPlus className="mr-2 h-4 w-4" /> Add Staff
              </Button>
            ) : (
              <Button onClick={handleAddDepartment} className="bg-[#1231B8] hover:bg-[#1231B8]/90">
                <Plus className="mr-2 h-4 w-4" /> Add Department
              </Button>
            )}
          </div>
          
          {/* Staff Tab Content */}
          <TabsContent value="staff" className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search staff..." 
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex gap-2 w-full md:w-auto">
                <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.name}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="on_leave">On Leave</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button variant="outline" size="icon" onClick={() => {
                  setSearchQuery('');
                  setFilterDepartment('');
                  setFilterStatus('');
                }}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {isLoading ? (
                // Loading skeletons
                Array(8).fill(0).map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-12 w-12 rounded-full" />
                        <div className="space-y-2">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    </CardContent>
                    <CardFooter className="border-t pt-3">
                      <Skeleton className="h-8 w-full" />
                    </CardFooter>
                  </Card>
                ))
              ) : staff.length === 0 ? (
                <div className="col-span-full flex items-center justify-center py-16">
                  <div className="text-center">
                    <UserMinus size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-1">No staff found</h3>
                    <p className="text-muted-foreground">
                      {searchQuery || filterDepartment || filterStatus 
                        ? "Try adjusting your filters" 
                        : "Add your first staff member to get started"}
                    </p>
                    {(searchQuery || filterDepartment || filterStatus) && (
                      <Button 
                        variant="outline" 
                        className="mt-4"
                        onClick={() => {
                          setSearchQuery('');
                          setFilterDepartment('');
                          setFilterStatus('');
                        }}
                      >
                        Clear Filters
                      </Button>
                    )}
                  </div>
                </div>
              ) : (
                // Staff cards
                staff.map((staffMember) => (
                  <Card key={staffMember.id} className="overflow-hidden hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between">
                        <div className="flex items-center gap-3">
                          <Avatar className="h-12 w-12 border">
                            {staffMember.profileImage ? (
                              <AvatarImage src={staffMember.profileImage} alt={`${staffMember.firstName} ${staffMember.lastName}`} />
                            ) : (
                              <AvatarFallback className="bg-[#1231B8]/10 text-[#1231B8]">
                                {getInitials(staffMember.firstName, staffMember.lastName)}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <div>
                            <h4 className="font-medium">{staffMember.firstName} {staffMember.lastName}</h4>
                            <p className="text-sm text-muted-foreground">{staffMember.position}</p>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewStaffDetails(staffMember.id)}>
                              <User className="mr-2 h-4 w-4" /> View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditStaff(staffMember)}>
                              <Pencil className="mr-2 h-4 w-4" /> Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => {
                                setStaffToDelete(staffMember.id);
                                setShowDeleteConfirm(true);
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" /> Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-gray-400" />
                          <span className="truncate">{staffMember.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-400" />
                          <span>{staffMember.phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Building className="h-4 w-4 text-gray-400" />
                          <span>{staffMember.department}</span>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="border-t pt-3 flex justify-between">
                      <div>
                        {getStatusBadge(staffMember.status)}
                      </div>
                      <div className="text-sm font-medium">
                        {formatCurrency(staffMember.salary)}
                      </div>
                    </CardFooter>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
          
          {/* Departments Tab Content */}
          <TabsContent value="departments" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {isLoading ? (
                // Loading skeletons
                Array(6).fill(0).map((_, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <Skeleton className="h-5 w-32" />
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-3/4" />
                      </div>
                    </CardContent>
                    <CardFooter className="border-t pt-3">
                      <Skeleton className="h-8 w-full" />
                    </CardFooter>
                  </Card>
                ))
              ) : departments.length === 0 ? (
                <div className="col-span-full flex items-center justify-center py-16">
                  <div className="text-center">
                    <Building size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium mb-1">No departments found</h3>
                    <p className="text-muted-foreground">Add your first department to get started</p>
                  </div>
                </div>
              ) : (
                // Department cards
                departments.map((department) => {
                  const staffCount = staffStats?.departmentCounts.find(d => d.name === department.name)?.count || 0;
                  const manager = staff.find(s => s.id === department.managerId);
                  
                  return (
                    <Card key={department.id} className="overflow-hidden hover:shadow-md transition-shadow">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">{department.name}</CardTitle>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleEditDepartment(department)}>
                                <Pencil className="mr-2 h-4 w-4" /> Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                className="text-red-600"
                                onClick={() => {
                                  setDepartmentToDelete(department.id);
                                  setShowDepartmentDeleteConfirm(true);
                                }}
                              >
                                <Trash2 className="mr-2 h-4 w-4" /> Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        {department.description && (
                          <CardDescription>{department.description}</CardDescription>
                        )}
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {manager && (
                            <div className="flex items-center gap-2">
                              <UserCheck className="h-4 w-4 text-gray-400" />
                              <span className="text-sm">Manager: {manager.firstName} {manager.lastName}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">{staffCount} Staff Members</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-sm">Created: {new Date(department.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="border-t pt-3">
                        <Button 
                          variant="outline" 
                          className="w-full"
                          onClick={() => {
                            setFilterDepartment(department.name);
                            setActiveTab('staff');
                          }}
                        >
                          <Users className="mr-2 h-4 w-4" /> View Staff
                        </Button>
                      </CardFooter>
                    </Card>
                  );
                })
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Staff Form Dialog */}
      <Dialog open={showStaffForm} onOpenChange={setShowStaffForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{formMode === 'create' ? 'Add New Staff' : 'Edit Staff'}</DialogTitle>
            <DialogDescription>
              {formMode === 'create' 
                ? 'Add a new staff member to your organization.' 
                : 'Update the staff member\'s information.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleStaffFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input 
                    id="firstName" 
                    name="firstName" 
                    value={formData.firstName} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input 
                    id="lastName" 
                    name="lastName" 
                    value={formData.lastName} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  value={formData.email} 
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input 
                  id="phone" 
                  name="phone" 
                  value={formData.phone} 
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="position">Position</Label>
                  <Input 
                    id="position" 
                    name="position" 
                    value={formData.position} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Select 
                    name="department" 
                    value={formData.department} 
                    onValueChange={(value) => handleSelectChange('department', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept.id} value={dept.name}>
                          {dept.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="salary">Salary</Label>
                  <Input 
                    id="salary" 
                    name="salary" 
                    type="number" 
                    value={formData.salary} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    name="status" 
                    value={formData.status} 
                    onValueChange={(value) => handleSelectChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="on_leave">On Leave</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Textarea 
                  id="address" 
                  name="address" 
                  value={formData.address} 
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowStaffForm(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {formMode === 'create' ? 'Adding...' : 'Updating...'}
                  </>
                ) : (
                  formMode === 'create' ? 'Add Staff' : 'Update Staff'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Department Form Dialog */}
      <Dialog open={showDepartmentForm} onOpenChange={setShowDepartmentForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{formMode === 'create' ? 'Add New Department' : 'Edit Department'}</DialogTitle>
            <DialogDescription>
              {formMode === 'create' 
                ? 'Create a new department for your organization.' 
                : 'Update the department\'s information.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleDepartmentFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Department Name</Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={departmentFormData.name} 
                  onChange={handleDepartmentInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea 
                  id="description" 
                  name="description" 
                  value={departmentFormData.description} 
                  onChange={handleDepartmentInputChange}
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="managerId">Department Manager</Label>
                <Select 
                  name="managerId" 
                  value={departmentFormData.managerId} 
                  onValueChange={(value) => handleDepartmentSelectChange('managerId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select manager" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Manager</SelectItem>
                    {staff
                      .filter(s => s.status === 'active')
                      .map((staffMember) => (
                        <SelectItem key={staffMember.id} value={staffMember.id}>
                          {staffMember.firstName} {staffMember.lastName}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowDepartmentForm(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {formMode === 'create' ? 'Creating...' : 'Updating...'}
                  </>
                ) : (
                  formMode === 'create' ? 'Create Department' : 'Update Department'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Staff Details Dialog */}
      <Dialog open={!!staffDetails} onOpenChange={(open) => !open && setStaffDetails(null)}>
        <DialogContent className="max-w-md">
          {staffDetails && (
            <>
              <DialogHeader>
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16 border">
                    {staffDetails.profileImage ? (
                      <AvatarImage src={staffDetails.profileImage} alt={`${staffDetails.firstName} ${staffDetails.lastName}`} />
                    ) : (
                      <AvatarFallback className="bg-[#1231B8]/10 text-[#1231B8] text-xl">
                        {getInitials(staffDetails.firstName, staffDetails.lastName)}
                      </AvatarFallback>
                    )}
                  </Avatar>
                  <div>
                    <DialogTitle className="text-xl">{staffDetails.firstName} {staffDetails.lastName}</DialogTitle>
                    <DialogDescription className="text-base">{staffDetails.position}</DialogDescription>
                  </div>
                </div>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status</span>
                  {getStatusBadge(staffDetails.status)}
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Contact Information</h4>
                  <div className="grid grid-cols-[20px_1fr] gap-x-2 gap-y-3 items-center">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span>{staffDetails.email}</span>
                    
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{staffDetails.phone}</span>
                    
                    {staffDetails.address && (
                      <>
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span>{staffDetails.address}</span>
                      </>
                    )}
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <h4 className="text-sm font-medium">Employment Details</h4>
                  <div className="grid grid-cols-[20px_1fr] gap-x-2 gap-y-3 items-center">
                    <Building className="h-4 w-4 text-gray-400" />
                    <span>Department: {staffDetails.department}</span>
                    
                    <Banknote className="h-4 w-4 text-gray-400" />
                    <span>Salary: {formatCurrency(staffDetails.salary)}</span>
                    
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span>Joined: {new Date(staffDetails.joinDate).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
              <DialogFooter className="flex gap-2">
                <Button 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => {
                    handleEditStaff(staffDetails);
                    setStaffDetails(null);
                  }}
                >
                  <Pencil className="mr-2 h-4 w-4" /> Edit
                </Button>
                <Button 
                  variant="default" 
                  className="flex-1 bg-[#1231B8] hover:bg-[#1231B8]/90"
                  onClick={() => {
                    // Navigate to payroll with this staff member selected
                    // This would be implemented when we build the Payroll page
                    setStaffDetails(null);
                  }}
                >
                  <Banknote className="mr-2 h-4 w-4" /> View Payroll
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the staff member
              and remove their data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-600 hover:bg-red-700"
              onClick={handleDeleteStaff}
            >
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* Department Delete Confirmation Dialog */}
      <AlertDialog open={showDepartmentDeleteConfirm} onOpenChange={setShowDepartmentDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the department.
              Any staff members in this department will need to be reassigned.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-600 hover:bg-red-700"
              onClick={handleDeleteDepartment}
            >
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </BusinessLayout>
  );
};

export default BusinessStaff;
