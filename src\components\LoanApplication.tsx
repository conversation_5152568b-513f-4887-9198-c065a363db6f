
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, CheckCircle, Clock, CreditCard, Calendar, Info, Calculator } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger 
} from '@/components/ui/accordion';
import { Slider } from '@/components/ui/slider';

interface LoanApplicationProps {
  type: 'personal' | 'business';
}

const LoanApplication: React.FC<LoanApplicationProps> = ({ type }) => {
  const [amount, setAmount] = useState(50000);
  const [duration, setDuration] = useState(3);
  const [purpose, setPurpose] = useState('');
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [step, setStep] = useState(1);
  const { toast } = useToast();
  
  const minAmount = type === 'personal' ? 10000 : 50000;
  const maxAmount = type === 'personal' ? 500000 : 5000000;
  const installmentAmount = Math.round(amount * (1 + 0.03 * duration) / (duration * 30));
  
  const handleAmountChange = (value: number[]) => {
    setAmount(value[0]);
  };
  
  const handleDurationChange = (value: number[]) => {
    setDuration(value[0]);
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!agreedToTerms) {
      toast({
        title: "Terms not accepted",
        description: "You must agree to the terms and conditions to continue",
        variant: "destructive"
      });
      return;
    }
    
    // In a real app, this would submit to backend
    toast({
      title: "Loan Application Submitted",
      description: "Your loan application has been submitted for review",
      variant: "success"
    });
    
    // Reset form
    setAmount(minAmount);
    setDuration(3);
    setPurpose('');
    setAgreedToTerms(false);
    setStep(1);
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h2 className="text-xl font-semibold mb-2">
        {type === 'personal' ? 'Personal Loan Application' : 'Business Loan Application'}
      </h2>
      <p className="text-sm text-gray-500 mb-6">
        {type === 'personal'
          ? 'Get quick access to funds for your personal needs'
          : 'Finance your business operations and growth'
        }
      </p>
      
      {step === 1 ? (
        <div className="space-y-6">
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label>Loan Amount</Label>
              <div className="font-semibold text-kojaPrimary">₦{amount.toLocaleString('en-NG')}</div>
            </div>
            <Slider
              value={[amount]}
              min={minAmount}
              max={maxAmount}
              step={type === 'personal' ? 5000 : 50000}
              onValueChange={handleAmountChange}
              className="my-4"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>₦{minAmount.toLocaleString('en-NG')}</span>
              <span>₦{maxAmount.toLocaleString('en-NG')}</span>
            </div>
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label>Loan Duration (months)</Label>
              <div className="font-semibold text-kojaPrimary">{duration} month{duration > 1 ? 's' : ''}</div>
            </div>
            <Slider
              value={[duration]}
              min={1}
              max={type === 'personal' ? 12 : 24}
              step={1}
              onValueChange={handleDurationChange}
              className="my-4"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>1 month</span>
              <span>{type === 'personal' ? '12' : '24'} months</span>
            </div>
          </div>
          
          <div>
            <Label htmlFor="purpose">Purpose of Loan</Label>
            <Select value={purpose} onValueChange={setPurpose}>
              <SelectTrigger id="purpose" className="mt-1">
                <SelectValue placeholder="Select purpose" />
              </SelectTrigger>
              <SelectContent>
                {type === 'personal' ? (
                  <>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="medical">Medical Expenses</SelectItem>
                    <SelectItem value="home">Home Improvement</SelectItem>
                    <SelectItem value="debt">Debt Consolidation</SelectItem>
                    <SelectItem value="other">Other Personal Expense</SelectItem>
                  </>
                ) : (
                  <>
                    <SelectItem value="expansion">Business Expansion</SelectItem>
                    <SelectItem value="equipment">Equipment Purchase</SelectItem>
                    <SelectItem value="inventory">Inventory</SelectItem>
                    <SelectItem value="operations">Working Capital</SelectItem>
                    <SelectItem value="other">Other Business Expense</SelectItem>
                  </>
                )}
              </SelectContent>
            </Select>
          </div>
          
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-start space-x-3">
              <Calculator className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <h3 className="font-medium text-blue-800">Loan Summary</h3>
                <div className="mt-3 grid grid-cols-2 gap-y-2 text-sm">
                  <div className="text-gray-600">Principal Amount:</div>
                  <div className="font-medium">₦{amount.toLocaleString('en-NG')}</div>
                  
                  <div className="text-gray-600">Duration:</div>
                  <div className="font-medium">{duration} month{duration > 1 ? 's' : ''}</div>
                  
                  <div className="text-gray-600">Interest Rate:</div>
                  <div className="font-medium">3.0% per month</div>
                  
                  <div className="text-gray-600">Total Repayment:</div>
                  <div className="font-medium">₦{(amount * (1 + 0.03 * duration)).toLocaleString('en-NG')}</div>
                  
                  <div className="text-gray-600">Daily Payment:</div>
                  <div className="font-semibold text-kojaPrimary">₦{installmentAmount.toLocaleString('en-NG')}</div>
                </div>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={() => {
              if (!purpose) {
                toast({
                  title: "Missing information",
                  description: "Please select the purpose of the loan",
                  variant: "destructive"
                });
                return;
              }
              setStep(2);
            }}
            className="w-full"
          >
            Continue
          </Button>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            {type === 'business' && (
              <>
                <div>
                  <Label htmlFor="businessDetails">Business Description</Label>
                  <Textarea 
                    id="businessDetails"
                    placeholder="Briefly describe your business operations..."
                    className="mt-1"
                    rows={3}
                  />
                </div>
                
                <div>
                  <Label htmlFor="revenue">Monthly Revenue (₦)</Label>
                  <Input 
                    id="revenue"
                    type="number"
                    placeholder="Enter your average monthly revenue"
                    className="mt-1"
                  />
                </div>
              </>
            )}
            
            <div>
              <Label htmlFor="additionalInfo">Additional Information</Label>
              <Textarea 
                id="additionalInfo"
                placeholder={`Any additional information to support your ${type} loan application...`}
                className="mt-1"
                rows={3}
              />
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg flex items-start space-x-3">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <h3 className="font-medium text-green-800">Eligible for Instant Approval</h3>
                <p className="text-sm text-green-600 mt-1">
                  Based on your account history, this loan may qualify for instant approval.
                </p>
              </div>
            </div>
            
            <Accordion type="single" collapsible className="border rounded-lg">
              <AccordionItem value="terms">
                <AccordionTrigger className="px-4">
                  <span className="flex items-center">
                    <Info className="h-4 w-4 mr-2" />
                    Terms and Conditions
                  </span>
                </AccordionTrigger>
                <AccordionContent className="px-4 text-sm text-gray-600 space-y-2">
                  <p>
                    By applying for this loan, you agree to the following terms:
                  </p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li>An interest rate of 3.0% per month will be applied to the principal amount.</li>
                    <li>Repayments will be automatically deducted from your account on a daily basis.</li>
                    <li>Early repayment is allowed without any additional fees.</li>
                    <li>Late payments may incur additional fees and affect your credit score.</li>
                    <li>KojaPay reserves the right to review and adjust loan terms based on account activity.</li>
                  </ul>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
            
            <div className="flex items-center space-x-2">
              <Switch 
                id="terms" 
                checked={agreedToTerms}
                onCheckedChange={setAgreedToTerms}
              />
              <Label htmlFor="terms" className="text-sm">
                I agree to the terms and conditions
              </Label>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button type="button" variant="outline" onClick={() => setStep(1)} className="flex-1">
              Back
            </Button>
            <Button type="submit" className="flex-1">
              Submit Application
            </Button>
          </div>
        </form>
      )}
    </div>
  );
};

export default LoanApplication;
