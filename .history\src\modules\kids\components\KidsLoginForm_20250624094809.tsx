import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Baby, Lock, Eye, EyeOff, ArrowLeft, User, Key } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { FirstLoginDTO, SetupChildCredentialsDTO, LoginDTO } from '@/types/kids';
import { loginChild } from '@/services/kidsApi';

const KidsLoginForm = () => {
  const [loginType, setLoginType] = useState<'first-time' | 'returning'>('returning');
  const [step, setStep] = useState(1);
  const [showPin, setShowPin] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const [firstLoginData, setFirstLoginData] = useState<FirstLoginDTO>({
    username: '',
    secretKey: ''
  });

  const [setupData, setSetupData] = useState<SetupChildCredentialsDTO>({
    username: '',
    password: '',
    pin: ''
  });

  const [confirmPassword, setConfirmPassword] = useState('');

  const [loginData, setLoginData] = useState<LoginDTO>({
    username: '',
    password: ''
  });

  const handleFirstLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFirstLoginData(prev => ({ ...prev, [name]: value }));
  };

  const handleSetupChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSetupData(prev => ({ ...prev, [name]: value }));
  };

  const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginData(prev => ({ ...prev, [name]: value }));
  };

  const handleFirstLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!firstLoginData.username || !firstLoginData.secretKey) {
      toast({
        title: 'Missing Information',
        description: 'Please enter your username and secret key',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Mock API call to validate username and secret key
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // If valid, move to setup step
      setSetupData(prev => ({ ...prev, username: firstLoginData.username }));
      setStep(2);
      
      toast({
        title: 'Verification Successful',
        description: 'Now set up your login credentials',
      });
    } catch (error) {
      toast({
        title: 'Verification Failed',
        description: 'Please check your username and secret key',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetupCredentials = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!setupData.password || !confirmPassword || !setupData.pin) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all fields',
        variant: 'destructive'
      });
      return;
    }

    if (setupData.password !== confirmPassword) {
      toast({
        title: 'Password Mismatch',
        description: 'Passwords do not match',
        variant: 'destructive'
      });
      return;
    }

    if (setupData.pin.length !== 4) {
      toast({
        title: 'Invalid PIN',
        description: 'PIN must be 4 digits',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    
    try {
      // Mock API call to set up credentials
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Store child user data
      localStorage.setItem('kidsUser', JSON.stringify({
        username: setupData.username,
        name: 'Young User',
        type: 'kids',
        isSetup: true
      }));
      
      toast({
        title: 'Setup Complete!',
        description: 'Your account is ready to use.',
      });
      
      navigate('/kids-dashboard');
    } catch (error) {
      toast({
        title: 'Setup Failed',
        description: 'Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegularLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!loginData.username || !loginData.password) {
      toast({
        title: 'Missing Information',
        description: 'Please enter your username and password',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      // Use backend API for regular child login
      const result = await loginChild(loginData);
      localStorage.setItem('kidsUser', JSON.stringify(result.child));
      localStorage.setItem('kidsToken', result.token);
      toast({
        title: 'Welcome back!',
        description: 'You have successfully logged in.',
      });
      navigate('/kids-dashboard');
    } catch (error: any) {
      toast({
        title: 'Login Failed',
        description: error.message || 'Please check your credentials and try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Login Type Selection */}
      <div className="mb-6">
        <div className="grid grid-cols-2 gap-2 p-1 bg-gray-100 rounded-[40px]">
          <button
            onClick={() => setLoginType('returning')}
            className={`py-2 px-4 rounded-[40px] text-sm font-medium transition-colors ${
              loginType === 'returning'
                ? 'bg-[#1231B8] text-white'
                : 'text-gray-600 hover:text-[#1231B8]'
            }`}
          >
            Returning User
          </button>
          <button
            onClick={() => setLoginType('first-time')}
            className={`py-2 px-4 rounded-[40px] text-sm font-medium transition-colors ${
              loginType === 'first-time'
                ? 'bg-[#1231B8] text-white'
                : 'text-gray-600 hover:text-[#1231B8]'
            }`}
          >
            First Time
          </button>
        </div>
      </div>

      <Card className="shadow-xl border-none rounded-[40px] overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-[#FDE314] to-[#FDE314]/80 text-[#1231B8] pb-4">
          <div className="flex items-center justify-center mb-4">
            <Baby className="h-12 w-12" />
          </div>
          <CardTitle className="text-xl text-center">
            {loginType === 'first-time' 
              ? (step === 1 ? 'First Time Setup' : 'Create Your Credentials')
              : 'Welcome Back!'
            }
          </CardTitle>
          <CardDescription className="text-[#1231B8]/80 text-center font-medium">
            {loginType === 'first-time' 
              ? (step === 1 ? 'Enter the credentials your parent gave you' : 'Set up your login details')
              : 'Enter your username and password'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-6">
          {/* First Time Login - Step 1: Verify Credentials */}
          {loginType === 'first-time' && step === 1 && (
            <form onSubmit={handleFirstLogin} className="space-y-6">
              <div>
                <Label htmlFor="username" className="text-[#1231B8] font-medium flex items-center">
                  <User size={16} className="mr-2" />
                  Username
                </Label>
                <Input
                  id="username"
                  name="username"
                  placeholder="Enter your username"
                  value={firstLoginData.username}
                  onChange={handleFirstLoginChange}
                  className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="secretKey" className="text-[#1231B8] font-medium flex items-center">
                  <Key size={16} className="mr-2" />
                  Secret Key
                </Label>
                <Input
                  id="secretKey"
                  name="secretKey"
                  placeholder="Enter your secret key"
                  value={firstLoginData.secretKey}
                  onChange={handleFirstLoginChange}
                  className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                  required
                />
              </div>
              
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 text-white py-3 rounded-[40px]"
              >
                {isLoading ? 'Verifying...' : 'Verify Credentials'}
              </Button>
            </form>
          )}

          {/* First Time Login - Step 2: Setup Credentials */}
          {loginType === 'first-time' && step === 2 && (
            <form onSubmit={handleSetupCredentials} className="space-y-6">
              <div>
                <Label htmlFor="password" className="text-[#1231B8] font-medium flex items-center">
                  <Lock size={16} className="mr-2" />
                  Create Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Create a strong password"
                    value={setupData.password}
                    onChange={handleSetupChange}
                    className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>

              <div>
                <Label htmlFor="confirmPassword" className="text-[#1231B8] font-medium flex items-center">
                  <Lock size={16} className="mr-2" />
                  Confirm Password
                </Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder="Confirm your password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>

              <div>
                <Label htmlFor="pin" className="text-[#1231B8] font-medium flex items-center">
                  <Lock size={16} className="mr-2" />
                  4-Digit PIN (for transfers)
                </Label>
                <div className="relative">
                  <Input
                    id="pin"
                    name="pin"
                    type={showPin ? 'text' : 'password'}
                    placeholder="Create a 4-digit PIN"
                    value={setupData.pin}
                    onChange={handleSetupChange}
                    className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                    maxLength={4}
                    pattern="[0-9]{4}"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                    onClick={() => setShowPin(!showPin)}
                  >
                    {showPin ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>
              
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 text-white py-3 rounded-[40px]"
              >
                {isLoading ? 'Setting Up...' : 'Complete Setup'}
              </Button>
            </form>
          )}

          {/* Regular Login */}
          {loginType === 'returning' && (
            <form onSubmit={handleRegularLogin} className="space-y-6">
              <div>
                <Label htmlFor="loginUsername" className="text-[#1231B8] font-medium flex items-center">
                  <User size={16} className="mr-2" />
                  Username
                </Label>
                <Input
                  id="loginUsername"
                  name="username"
                  placeholder="Enter your username"
                  value={loginData.username}
                  onChange={handleLoginChange}
                  className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="loginPassword" className="text-[#1231B8] font-medium flex items-center">
                  <Lock size={16} className="mr-2" />
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="loginPassword"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={loginData.password}
                    onChange={handleLoginChange}
                    className="mt-1 rounded-[40px] border-gray-300 focus:border-[#1231B8]"
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>
              
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 text-white py-3 rounded-[40px]"
              >
                {isLoading ? 'Logging in...' : 'Login'}
              </Button>
            </form>
          )}
          
          <div className="mt-6 text-center">
            <Link 
              to="/"
              className="inline-flex items-center text-[#1231B8] hover:underline"
            >
              <ArrowLeft size={16} className="mr-1" />
              Back to Home
            </Link>
          </div>
        </CardContent>
      </Card>
    </>
  );
};

export default KidsLoginForm;
