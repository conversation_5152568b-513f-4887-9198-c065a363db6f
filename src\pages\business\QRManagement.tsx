
import React from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  QrCode, 
  BarChart, 
  Users, 
  CreditCard, 
  ShoppingCart,
  Download,
  History,
  Smartphone
} from 'lucide-react';
import QRCodeGenerator from '@/components/QRCodeGenerator';

const QRManagementPage = () => {
  const stats = {
    totalScans: 1245,
    activeQRCodes: 12,
    totalPayments: 567,
    conversionRate: 45.5
  };
  
  const recentActivities = [
    { id: 1, type: 'scan', user: 'Unknown User', amount: null, time: '10 minutes ago', location: 'Lagos' },
    { id: 2, type: 'payment', user: 'Customer #1208', amount: 15000, time: '25 minutes ago', location: 'Abuja' },
    { id: 3, type: 'scan', user: 'Unknown User', amount: null, time: '45 minutes ago', location: 'Port Harcourt' },
    { id: 4, type: 'payment', user: 'Customer #0934', amount: 5000, time: '1 hour ago', location: 'Lagos' },
    { id: 5, type: 'payment', user: 'Customer #2156', amount: 25000, time: '2 hours ago', location: 'Ibadan' }
  ];

  return (
    <BusinessLayout pageTitle="QR Code Management">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">QR Code Management</h1>
            <p className="text-muted-foreground">Create and track QR codes for your business</p>
          </div>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalScans}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                <QrCode className="h-3 w-3 mr-1" />
                Lifetime scans
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Active QR Codes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.activeQRCodes}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                <Smartphone className="h-3 w-3 mr-1" />
                Currently active
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPayments}</div>
              <p className="text-xs text-muted-foreground flex items-center">
                <CreditCard className="h-3 w-3 mr-1" />
                Via QR payments
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.conversionRate}%</div>
              <p className="text-xs text-muted-foreground flex items-center">
                <BarChart className="h-3 w-3 mr-1" />
                Scans to payments
              </p>
            </CardContent>
          </Card>
        </div>
        
        <Tabs defaultValue="generate" className="space-y-4">
          <TabsList className="grid grid-cols-3 sm:w-[400px]">
            <TabsTrigger value="generate">Generate QR</TabsTrigger>
            <TabsTrigger value="manage">Manage QR</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="generate">
            <QRCodeGenerator />
          </TabsContent>
          
          <TabsContent value="manage">
            <Card>
              <CardHeader>
                <CardTitle>My QR Codes</CardTitle>
                <CardDescription>View and manage all your QR codes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <div key={i} className="border rounded-lg p-4 bg-white flex flex-col">
                      <div className="flex justify-between items-start mb-3">
                        <div className="text-sm font-medium">
                          {i % 3 === 0 ? 'Payment QR' : i % 3 === 1 ? 'Store QR' : 'Account QR'}
                        </div>
                        <div className={`text-xs px-2 py-0.5 rounded-full ${
                          i < 4 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {i < 4 ? 'Active' : 'Inactive'}
                        </div>
                      </div>
                      
                      <div className="flex-grow flex items-center justify-center my-3">
                        <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                          <QrCode className="h-16 w-16 text-gray-400" />
                        </div>
                      </div>
                      
                      <div className="mt-2 space-y-1">
                        <div className="text-xs text-gray-500 flex justify-between">
                          <span>Created:</span>
                          <span>June {10 + i}, 2023</span>
                        </div>
                        <div className="text-xs text-gray-500 flex justify-between">
                          <span>Scans:</span>
                          <span>{(200 - i * 30) > 0 ? (200 - i * 30) : 0}</span>
                        </div>
                        {i % 3 === 0 && (
                          <div className="text-xs text-gray-500 flex justify-between">
                            <span>Amount:</span>
                            <span>₦{(i + 1) * 5000}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="mt-4 grid grid-cols-2 gap-2">
                        <Button variant="outline" size="sm">View</Button>
                        <Button variant="outline" size="sm" className={
                          i < 4 ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'
                        }>
                          {i < 4 ? 'Deactivate' : 'Activate'}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>QR Code Performance</CardTitle>
                  <CardDescription>Metrics and insights on your QR codes</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg border">
                    <p className="text-gray-500">QR analytics chart would appear here</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activities</CardTitle>
                  <CardDescription>Latest scans and payments via QR</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recentActivities.map(activity => (
                      <div key={activity.id} className="flex justify-between items-start border-b pb-4 last:border-0">
                        <div>
                          <div className="flex items-center gap-2">
                            {activity.type === 'scan' ? (
                              <QrCode className="h-4 w-4 text-blue-500" />
                            ) : (
                              <CreditCard className="h-4 w-4 text-green-500" />
                            )}
                            <span className="font-medium">
                              {activity.type === 'scan' ? 'QR Scan' : 'QR Payment'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">{activity.user}</div>
                          <div className="flex items-center gap-2 mt-1 text-xs text-gray-400">
                            <History className="h-3 w-3" />
                            <span>{activity.time}</span>
                            <span>•</span>
                            <span>{activity.location}</span>
                          </div>
                        </div>
                        {activity.amount && (
                          <div className="text-right">
                            <div className="font-medium text-green-600">₦{activity.amount.toLocaleString('en-NG')}</div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>
  );
};

export default QRManagementPage;
