
export interface PayrollRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  month: string;
  year: number;
  grossSalary: number;
  netSalary: number;
  deductions: Deduction[];
  allowances: Allowance[];
  status: string;
  paymentDate: string;
  createdAt: string;
}

export interface Deduction {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage';
}

export interface Allowance {
  id: string;
  name: string;
  amount: number;
  type: 'fixed' | 'percentage';
}

export interface PayrollFilters {
  search?: string;
  month?: string;
  status?: string;
  year?: number;
}

export interface CreatePayrollParams {
  employeeId: string;
  employeeName: string;
  month: string;
  year: number;
  grossSalary: number;
  netSalary: number;
  deductions: Deduction[];
  allowances: Allowance[];
  status: string;
  paymentDate: string;
  staffIds?: string[];
}

export interface PayrollSummary {
  totalEmployees: number;
  totalPayroll: number;
  averageSalary: number;
  totalDeductions: number;
  totalAllowances: number;
}

export interface PayrollStatistics {
  totalPayroll: number;
  activeEmployees: number;
  nextPayday: string;
  pendingPayroll: number;
  averageSalary: number;
  totalDeductions: number;
}

export class PayrollService {
  private mockPayrollData: PayrollRecord[] = [
    {
      id: '1',
      employeeId: 'emp-001',
      employeeName: 'John Doe',
      month: 'january',
      year: 2024,
      grossSalary: 150000,
      netSalary: 135000,
      deductions: [
        { id: '1', name: 'Tax', amount: 15000, type: 'fixed' }
      ],
      allowances: [],
      status: 'paid',
      paymentDate: '2024-01-31T00:00:00Z',
      createdAt: '2024-01-01T00:00:00Z'
    }
  ];

  async getPayrollRecords(filters: PayrollFilters = {}): Promise<PayrollRecord[]> {
    let filtered = [...this.mockPayrollData];

    if (filters.search) {
      filtered = filtered.filter(record => 
        record.employeeName.toLowerCase().includes(filters.search!.toLowerCase())
      );
    }

    if (filters.month) {
      filtered = filtered.filter(record => record.month === filters.month);
    }

    if (filters.status) {
      filtered = filtered.filter(record => record.status === filters.status);
    }

    return filtered;
  }

  async getPayrollById(id: string): Promise<PayrollRecord | null> {
    return this.mockPayrollData.find(record => record.id === id) || null;
  }

  async createPayrollRecord(params: CreatePayrollParams): Promise<PayrollRecord> {
    const newRecord: PayrollRecord = {
      id: Math.random().toString(36).substr(2, 9),
      ...params,
      createdAt: new Date().toISOString()
    };
    this.mockPayrollData.push(newRecord);
    return newRecord;
  }

  async updatePayrollRecord(id: string, updates: Partial<PayrollRecord>): Promise<PayrollRecord> {
    const index = this.mockPayrollData.findIndex(record => record.id === id);
    if (index === -1) throw new Error('Payroll record not found');
    
    this.mockPayrollData[index] = { ...this.mockPayrollData[index], ...updates };
    return this.mockPayrollData[index];
  }

  async deletePayrollRecord(id: string): Promise<void> {
    const index = this.mockPayrollData.findIndex(record => record.id === id);
    if (index === -1) throw new Error('Payroll record not found');
    
    this.mockPayrollData.splice(index, 1);
  }

  async processPayroll(params: {
    payDate: string;
    startDate: string;
    endDate: string;
    description: string;
    employeeIds?: string[];
  }): Promise<void> {
    // Mock implementation for processing payroll
    console.log('Processing payroll:', params);
  }

  async getPayrollStatistics(): Promise<PayrollStatistics> {
    return {
      totalPayroll: 500000,
      activeEmployees: 15,
      nextPayday: '2024-02-28T00:00:00Z',
      pendingPayroll: 75000,
      averageSalary: 125000,
      totalDeductions: 45000
    };
  }
}

export const payrollService = new PayrollService();
