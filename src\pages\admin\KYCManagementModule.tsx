import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Shield, 
  Search, 
  Filter, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Eye, 
  User, 
  FileText,
  Download
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const KYCManagementModule = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const handleKYCAction = (action: string, userId: string) => {
    toast({
      title: `${action} KYC Verification`,
      description: `Successfully ${action.toLowerCase()}ed KYC for user ${userId}`,
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // Mock data
  const kycVerifications = [
    { 
      id: 'KYC001', 
      user: 'John Doe', 
      userId: 'P001',
      userType: 'personal', 
      documentType: 'National ID', 
      submitDate: '2023-08-10', 
      status: 'pending' 
    },
    { 
      id: 'KYC002', 
      user: 'Jane Smith', 
      userId: 'P002',
      userType: 'personal', 
      documentType: 'Passport', 
      submitDate: '2023-08-09', 
      status: 'approved' 
    },
    { 
      id: 'KYC003', 
      user: 'Acme Corp', 
      userId: 'B001',
      userType: 'business', 
      documentType: 'Business Registration', 
      submitDate: '2023-08-08', 
      status: 'rejected',
      rejectReason: 'Unclear document'
    },
    { 
      id: 'KYC004', 
      user: 'Michael Johnson', 
      userId: 'P003',
      userType: 'personal', 
      documentType: 'Driver\'s License', 
      submitDate: '2023-08-07', 
      status: 'pending' 
    },
    { 
      id: 'KYC005', 
      user: 'TechSolutions LLC', 
      userId: 'B002',
      userType: 'business', 
      documentType: 'Tax Certificate', 
      submitDate: '2023-08-06', 
      status: 'approved' 
    },
    { 
      id: 'KYC006', 
      user: 'Sarah Williams', 
      userId: 'P004',
      userType: 'personal', 
      documentType: 'Utility Bill', 
      submitDate: '2023-08-05', 
      status: 'pending' 
    },
  ];

  const renderStatusBadge = (status: string) => {
    let color = '';
    let icon = null;
    
    switch(status) {
      case 'approved':
        color = 'bg-green-100 text-green-800';
        icon = <CheckCircle className="h-3 w-3 mr-1" />;
        break;
      case 'pending':
        color = 'bg-yellow-100 text-yellow-800';
        icon = <Clock className="h-3 w-3 mr-1" />;
        break;
      case 'rejected':
        color = 'bg-red-100 text-red-800';
        icon = <XCircle className="h-3 w-3 mr-1" />;
        break;
      default:
        color = 'bg-blue-100 text-blue-800';
    }
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
        {icon}
        {status}
      </span>
    );
  };

  const filteredKYC = kycVerifications.filter(kyc => {
    if (activeTab !== 'all' && activeTab !== kyc.status) {
      return false;
    }
    
    if (searchQuery) {
      return kyc.id.toLowerCase().includes(searchQuery.toLowerCase()) || 
             kyc.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
             kyc.documentType.toLowerCase().includes(searchQuery.toLowerCase());
    }
    
    return true;
  });

  return (
    <>
      <Helmet>
        <title>KYC Verification Management | KojaPay Admin Portal</title>
      </Helmet>
      <AdminLayout pageTitle="KYC Verification Management">
        <div className="space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold">KYC Verification Management</h1>
              <p className="text-gray-600">Verify user identities and manage documentation</p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search verifications..." 
                  className="pl-10"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
              
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Pending Verifications</p>
                  <p className="text-2xl font-bold">3</p>
                </div>
                <div className="p-3 rounded-full bg-yellow-100">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Approved Verifications</p>
                  <p className="text-2xl font-bold">2</p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Rejected Verifications</p>
                  <p className="text-2xl font-bold">1</p>
                </div>
                <div className="p-3 rounded-full bg-red-100">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="all">All Verifications</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="approved">Approved</TabsTrigger>
              <TabsTrigger value="rejected">Rejected</TabsTrigger>
            </TabsList>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>KYC Verification Requests</CardTitle>
                <CardDescription>
                  {filteredKYC.length} verification requests found
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Request ID</th>
                        <th className="text-left py-3 px-4">User</th>
                        <th className="text-left py-3 px-4">Document Type</th>
                        <th className="text-left py-3 px-4">Submit Date</th>
                        <th className="text-left py-3 px-4">Status</th>
                        <th className="text-right py-3 px-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredKYC.map(kyc => (
                        <tr key={kyc.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium">{kyc.id}</td>
                          <td className="py-3 px-4">
                            <div className="flex flex-col">
                              <span>{kyc.user}</span>
                              <span className="text-xs text-gray-500 capitalize">{kyc.userType}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">{kyc.documentType}</td>
                          <td className="py-3 px-4">{kyc.submitDate}</td>
                          <td className="py-3 px-4">
                            {renderStatusBadge(kyc.status)}
                            {kyc.status === 'rejected' && kyc.rejectReason && (
                              <div className="text-xs text-red-600 mt-1">{kyc.rejectReason}</div>
                            )}
                          </td>
                          <td className="py-3 px-4 text-right">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-blue-600"
                              onClick={() => handleKYCAction('View', kyc.userId)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-green-600"
                              onClick={() => handleKYCAction('Approve', kyc.userId)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-red-600"
                              onClick={() => handleKYCAction('Reject', kyc.userId)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  
                  {filteredKYC.length === 0 && (
                    <div className="text-center py-10">
                      <p className="text-gray-500">No verification requests found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </Tabs>
        </div>
      </AdminLayout>
    </>
  );
};

export default KYCManagementModule;
