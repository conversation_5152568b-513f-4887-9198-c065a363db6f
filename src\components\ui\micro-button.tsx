
import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

const microButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[20px] text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 active:scale-95 font-katina",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
        outline: "border border-input bg-background/60 backdrop-blur-sm hover:bg-accent hover:text-accent-foreground shadow-sm hover:shadow-md",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm hover:shadow-md",
        ghost: "hover:bg-accent/40 backdrop-blur-sm hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        gradient: "bg-gradient-to-r from-kojaPrimary to-blue-600 text-white hover:from-kojaPrimary/90 hover:to-blue-600/90 shadow-sm hover:shadow-md",
        yellowGradient: "bg-gradient-to-r from-kojaYellow to-amber-400 text-kojaDark hover:from-kojaYellow/90 hover:to-amber-400/90 shadow-sm hover:shadow-md",
        glass: "bg-white/50 backdrop-blur-sm border border-white/30 text-kojaDark hover:bg-white/70 shadow-sm hover:shadow-md",
        futuristic: "bg-gradient-to-r from-white/80 to-white/60 backdrop-blur-lg border border-white/50 text-kojaDark shadow-lg hover:shadow-xl hover:bg-white/90",
        minimal: "bg-transparent hover:bg-white/20 backdrop-blur-sm text-kojaDark",
        pill: "rounded-full bg-white/80 backdrop-blur-sm text-kojaDark hover:bg-white/90 shadow-sm hover:shadow-md",
      },
      size: {
        default: "h-10 px-4 py-2",
        xs: "h-7 rounded-[20px] px-2 text-xs",
        sm: "h-9 rounded-[20px] px-3",
        lg: "h-11 rounded-[20px] px-8",
        icon: "h-10 w-10",
      },
      animation: {
        none: "",
        pulse: "animate-pulse",
        ripple: "overflow-hidden relative",
        bounce: "hover:animate-bounce",
        shimmer: "overflow-hidden relative before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent"
      },
      rounded: {
        default: "rounded-[20px]",
        sm: "rounded-sm",
        lg: "rounded-lg",
        xl: "rounded-xl",
        full: "rounded-full",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      animation: "none",
      rounded: "default",
    },
  }
);

export interface MicroButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof microButtonVariants> {
  loading?: boolean;
}

const MicroButton = React.forwardRef<HTMLButtonElement, MicroButtonProps>(
  ({ className, variant, size, animation, rounded, loading, children, ...props }, ref) => {
    return (
      <button
        className={cn(microButtonVariants({ variant, size, animation, rounded, className }))}
        ref={ref}
        disabled={props.disabled || loading}
        {...props}
      >
        {loading && <Loader2 className="mr-1.5 h-4 w-4 animate-spin" />}
        {children}
      </button>
    );
  }
);
MicroButton.displayName = "MicroButton";

export { MicroButton, microButtonVariants };
