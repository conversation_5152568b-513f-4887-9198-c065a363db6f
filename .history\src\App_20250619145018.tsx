import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import { Toaster } from '@/components/ui/toaster';
import { AuthProvider } from '@/contexts/AuthContext';
import { CartProvider } from '@/contexts/CartContext';
import { SecurityProvider } from '@/contexts/SecurityContext';
import { useAuth } from '@/contexts/AuthContext';
import { EnhancedToaster } from '@/components/ui/enhanced-toaster';

// Import pages
import Index from '@/pages/Index';
import UnifiedLogin from '@/pages/UnifiedLogin';
import CreateAccount from '@/pages/CreateAccount';
import Dashboard from '@/pages/Dashboard';
import BusinessDashboard from '@/pages/BusinessDashboard';
import Wallet from '@/pages/Wallet';
import EnhancedWallet from '@/pages/EnhancedWallet';
import Transactions from '@/pages/Transactions';
import Profile from '@/pages/Profile';
import Settings from '@/pages/Settings';
import Cards from '@/pages/Cards';
import CardServices from '@/pages/CardServices';
import BillPayment from '@/pages/BillPayment';
import Loans from '@/pages/Loans';
import Savings from '@/pages/Savings';
import RewardPoints from '@/pages/RewardPoints';
import Security from '@/pages/Security';
import Notifications from '@/pages/Notifications';
import Analysis from '@/pages/Analysis';
import Insights from '@/pages/Insights';
import KYC from '@/pages/KYC';
import AccountUpgrade from '@/pages/AccountUpgrade';
import ForgotPin from '@/pages/ForgotPin';
import Ecommerce from '@/pages/Ecommerce';
import SafetyTips from '@/pages/SafetyTips';
import NotFound from '@/pages/NotFound';
import KidsAccountManager from '@/components/KidsAccountManager';

// Admin pages
import AdminLogin from '@/pages/admin/AdminLogin';
import AdminPortal from '@/pages/AdminPortal';
import AdminDashboard from '@/pages/admin/AdminDashboard';

// Business pages
import BusinessWallet from '@/pages/business/Wallet';
import BusinessTransactions from '@/pages/business/Transactions';
import BusinessProfile from '@/pages/business/Profile';
import BusinessSettings from '@/pages/business/Settings';
import BusinessCards from '@/pages/business/Cards';
import BusinessBillPayment from '@/pages/business/BillPayment';
import BusinessLoans from '@/pages/business/Loans';
import BusinessNotifications from '@/pages/business/Notifications';
import BusinessAnalysis from '@/pages/business/Analysis';
import BusinessSecurity from '@/pages/business/Security';
import BusinessEcommerce from '@/pages/business/Ecommerce';
import BusinessEscrow from '@/pages/business/Escrow';
import BusinessInvoicing from '@/pages/business/Invoicing';
import BusinessPaymentLinks from '@/pages/business/PaymentLinks';
import BusinessQRCodes from '@/pages/business/QRCodes';
import BusinessPOS from '@/pages/business/POS';
import BusinessStaff from '@/pages/business/Staff';
import BusinessBranches from '@/pages/business/Branches';
import BusinessPayroll from '@/pages/business/Payroll';
import BusinessAPI from '@/pages/business/API';
import BusinessStatements from '@/pages/business/Statements';

// Legal pages
import TermsOfService from '@/pages/legal/TermsOfService';
import PrivacyPolicy from '@/pages/legal/PrivacyPolicy';

// Escrow pages
import EscrowPage from '@/pages/escrow/Escrow';
import EscrowDetail from '@/pages/escrow/EscrowDetail';
import EscrowTerms from '@/pages/escrow/EscrowTerms';

// User security pages
import SecuritySettings from '@/pages/user/SecuritySettings';
import FraudAlertDashboard from '@/pages/user/FraudAlertDashboard';

import './App.css';

// Protected Route Component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

function App() {
  return (
    <HelmetProvider>
      <AuthProvider>
        <CartProvider>
          <SecurityProvider>
            <Router>
              <Helmet>
                <title>KojaPay - Digital Banking & Financial Services</title>
                <meta name="description" content="Experience next-generation digital banking with KojaPay. Secure transactions, business solutions, and innovative financial services." />
                <meta name="viewport" content="width=device-width, initial-scale=1" />
                <link rel="preconnect" href="https://fonts.googleapis.com" />
                <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
                <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
              </Helmet>
              
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Index />} />
                <Route path="/login" element={<UnifiedLogin />} />
                <Route path="/create-account" element={<CreateAccount />} />
                <Route path="/forgot-pin" element={<ForgotPin />} />
                <Route path="/personal-signup" element={<CreateAccount />} />
                
                {/* Legal Routes */}
                <Route path="/terms" element={<TermsOfService />} />
                <Route path="/privacy" element={<PrivacyPolicy />} />
                <Route path="/safety-tips" element={<SafetyTips />} />
                
                {/* Admin Routes */}
                <Route path="/admin/login" element={<AdminLogin />} />
                <Route path="/admin/*" element={<AdminPortal />} />
                
                {/* Protected Personal Routes */}
                <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
                <Route path="/wallet" element={<ProtectedRoute><EnhancedWallet /></ProtectedRoute>} />
                <Route path="/transactions" element={<ProtectedRoute><Transactions /></ProtectedRoute>} />
                <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
                <Route path="/settings" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
                <Route path="/cards" element={<ProtectedRoute><Cards /></ProtectedRoute>} />
                <Route path="/card-services" element={<ProtectedRoute><CardServices /></ProtectedRoute>} />
                <Route path="/bill-payment" element={<ProtectedRoute><BillPayment /></ProtectedRoute>} />
                <Route path="/loans" element={<ProtectedRoute><Loans /></ProtectedRoute>} />
                <Route path="/savings" element={<ProtectedRoute><Savings /></ProtectedRoute>} />
                <Route path="/reward-points" element={<ProtectedRoute><RewardPoints /></ProtectedRoute>} />
                <Route path="/security" element={<ProtectedRoute><Security /></ProtectedRoute>} />
                <Route path="/notifications" element={<ProtectedRoute><Notifications /></ProtectedRoute>} />
                <Route path="/analysis" element={<ProtectedRoute><Analysis /></ProtectedRoute>} />
                <Route path="/insights" element={<ProtectedRoute><Insights /></ProtectedRoute>} />
                <Route path="/kyc" element={<ProtectedRoute><KYC /></ProtectedRoute>} />
                <Route path="/account-upgrade" element={<ProtectedRoute><AccountUpgrade /></ProtectedRoute>} />
                <Route path="/ecommerce" element={<ProtectedRoute><Ecommerce /></ProtectedRoute>} />
                <Route path="/kids-accounts" element={<ProtectedRoute><KidsAccountManager /></ProtectedRoute>} />
                
                {/* Protected Business Routes */}
                <Route path="/business-dashboard" element={<ProtectedRoute><BusinessDashboard /></ProtectedRoute>} />
                <Route path="/business/wallet" element={<ProtectedRoute><BusinessWallet /></ProtectedRoute>} />
                <Route path="/business/transactions" element={<ProtectedRoute><BusinessTransactions /></ProtectedRoute>} />
                <Route path="/business/profile" element={<ProtectedRoute><BusinessProfile /></ProtectedRoute>} />
                <Route path="/business/settings" element={<ProtectedRoute><BusinessSettings /></ProtectedRoute>} />
                <Route path="/business/cards" element={<ProtectedRoute><BusinessCards /></ProtectedRoute>} />
                <Route path="/business/bill-payment" element={<ProtectedRoute><BusinessBillPayment /></ProtectedRoute>} />
                <Route path="/business/loans" element={<ProtectedRoute><BusinessLoans /></ProtectedRoute>} />
                <Route path="/business/notifications" element={<ProtectedRoute><BusinessNotifications /></ProtectedRoute>} />
                <Route path="/business/analysis" element={<ProtectedRoute><BusinessAnalysis /></ProtectedRoute>} />
                <Route path="/business/security" element={<ProtectedRoute><BusinessSecurity /></ProtectedRoute>} />
                <Route path="/business/ecommerce" element={<ProtectedRoute><BusinessEcommerce /></ProtectedRoute>} />
                <Route path="/business/escrow" element={<ProtectedRoute><BusinessEscrow /></ProtectedRoute>} />
                <Route path="/business/invoicing" element={<ProtectedRoute><BusinessInvoicing /></ProtectedRoute>} />
                <Route path="/business/payment-links" element={<ProtectedRoute><BusinessPaymentLinks /></ProtectedRoute>} />
                <Route path="/business/qr-codes" element={<ProtectedRoute><BusinessQRCodes /></ProtectedRoute>} />
                <Route path="/business/pos" element={<ProtectedRoute><BusinessPOS /></ProtectedRoute>} />
                <Route path="/business/staff" element={<ProtectedRoute><BusinessStaff /></ProtectedRoute>} />
                <Route path="/business/branches" element={<ProtectedRoute><BusinessBranches /></ProtectedRoute>} />
                <Route path="/business/payroll" element={<ProtectedRoute><BusinessPayroll /></ProtectedRoute>} />
                <Route path="/business/api" element={<ProtectedRoute><BusinessAPI /></ProtectedRoute>} />
                <Route path="/business/statements" element={<ProtectedRoute><BusinessStatements /></ProtectedRoute>} />
                
                {/* Escrow Routes */}
                <Route path="/escrow" element={<ProtectedRoute><EscrowPage /></ProtectedRoute>} />
                <Route path="/escrow/:id" element={<ProtectedRoute><EscrowDetail /></ProtectedRoute>} />
                <Route path="/escrow/terms" element={<EscrowTerms />} />
                
                {/* Security Routes */}
                <Route path="/security-settings" element={<ProtectedRoute><SecuritySettings /></ProtectedRoute>} />
                <Route path="/fraud-alerts" element={<ProtectedRoute><FraudAlertDashboard /></ProtectedRoute>} />
                
                {/* 404 Route */}
                <Route path="*" element={<NotFound />} />
              </Routes>
              
              <Toaster />
              <EnhancedToaster />
            </Router>
          </SecurityProvider>
        </CartProvider>
      </AuthProvider>
    </HelmetProvider>
  );
}

export default App;
