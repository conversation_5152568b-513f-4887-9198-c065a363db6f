
import { v4 as uuidv4 } from 'uuid';

export enum NotificationType {
  TRANSACTION = "transaction",
  SECURITY = "security",
  SYSTEM = "system", 
  LOAN = "loan",
  MARKETING = "marketing",
  INFO = "info"
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: string; // Using string instead of enum to fix comparison issues
  isRead: boolean;
  createdAt: string;
  metadata?: Record<string, unknown>;
}

interface NotificationCountResponse {
  count: number;
}

interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export class NotificationService {
  private notifications: Notification[] = [
    {
      id: uuidv4(),
      userId: "user_1",
      title: "Payment Received",
      message: "You've received ₦50,000 from <PERSON>",
      type: NotificationType.TRANSACTION,
      isRead: false,
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
    },
    {
      id: uuidv4(),
      userId: "user_1",
      title: "Security Alert",
      message: "New device logged into your account from Lagos, Nigeria",
      type: NotificationType.SECURITY,
      isRead: false,
      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
    },
    {
      id: uuidv4(),
      userId: "user_1",
      title: "Loan Approved",
      message: "Your loan request of ₦500,000 has been approved",
      type: NotificationType.LOAN,
      isRead: true,
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: uuidv4(),
      userId: "user_1",
      title: "Transfer Completed",
      message: "Your transfer of ₦20,000 to Alice Smith has been completed",
      type: NotificationType.TRANSACTION,
      isRead: true,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: uuidv4(),
      userId: "user_1",
      title: "Limited Time Offer",
      message: "Get 5% cashback on all transactions this weekend!",
      type: NotificationType.MARKETING,
      isRead: false,
      createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString()
    }
  ];

  async getNotifications(userId: string): Promise<ServiceResponse<Notification[]>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    const userNotifications = this.notifications.filter(notification => notification.userId === userId);
    return {
      success: true,
      data: userNotifications
    };
  }
  
  async getUnreadCount(userId: string): Promise<ServiceResponse<NotificationCountResponse>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    const count = this.notifications.filter(
      notification => notification.userId === userId && !notification.isRead
    ).length;
    
    return {
      success: true,
      data: { count }
    };
  }
  
  async markAsRead(notificationId: string): Promise<ServiceResponse<null>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const notificationIndex = this.notifications.findIndex(n => n.id === notificationId);
    if (notificationIndex === -1) {
      return {
        success: false,
        error: "Notification not found"
      };
    }
    
    this.notifications[notificationIndex].isRead = true;
    
    return {
      success: true
    };
  }
  
  async markAllAsRead(userId: string): Promise<ServiceResponse<null>> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    this.notifications
      .filter(notification => notification.userId === userId)
      .forEach(notification => {
        notification.isRead = true;
      });
    
    return {
      success: true
    };
  }
  
  async deleteNotification(notificationId: string): Promise<ServiceResponse<null>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const notificationIndex = this.notifications.findIndex(n => n.id === notificationId);
    if (notificationIndex === -1) {
      return {
        success: false,
        error: "Notification not found"
      };
    }
    
    this.notifications.splice(notificationIndex, 1);
    
    return {
      success: true
    };
  }
  
  async createNotification(notification: Omit<Notification, "id" | "createdAt">): Promise<ServiceResponse<Notification>> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const newNotification: Notification = {
      id: uuidv4(),
      ...notification,
      createdAt: new Date().toISOString()
    };
    
    this.notifications.unshift(newNotification);
    
    return {
      success: true,
      data: newNotification
    };
  }
}

// Export as both named export and default export for backward compatibility
export const notificationService = new NotificationService();
export default notificationService;
