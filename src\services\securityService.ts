import { toast } from 'sonner';
import { SecurityNotification } from '@/components/Security/SecurityNotifications';

export interface FraudAlert {
  id: string;
  userId: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  status: 'pending' | 'reviewing' | 'resolved' | 'blocked';
  alertType: string;
  location?: string;
  ipAddress?: string;
  deviceInfo?: string;
  transactionId?: string;
  transactionAmount?: number;
  riskFactors?: string[];
  actionTaken?: string;
  actionTimestamp?: string;
  actionBy?: string;
}

export interface SecuritySettings {
  userId: string;
  loginNotifications: boolean;
  transactionAlerts: boolean;
  deviceVerification: boolean;
  twoFactorAuth: boolean;
  biometricLogin: boolean;
  transactionLimit: number;
  suspiciousLoginDetection: boolean;
  emailAlerts: boolean;
  smsAlerts: boolean;
  pushNotifications: boolean;
  alertSensitivity: 'low' | 'medium' | 'high';
  blockInternationalTransactions: boolean;
  requireOtpForAllTransactions: boolean;
  velocityChecks: boolean;
  requirePasswordChangeEvery90Days: boolean;
}

export interface SecurityLog {
  id: string;
  userId: string;
  eventType: string;
  timestamp: string;
  ipAddress: string;
  deviceInfo: string;
  location: string;
  details: string;
  success: boolean;
}

export interface RiskAssessment {
  overallRiskScore: number;
  riskFactors: string[];
  recommendations: string[];
}

class SecurityService {
  private apiUrl = process.env.NODE_ENV === 'production' 
    ? '/api/security' 
    : 'http://localhost:3001/api/security';
  
  // In a real implementation, these would be fetched from an API
  private mockAlerts: FraudAlert[] = [
    {
      id: 'FA-001',
      userId: 'user123',
      title: 'Unusual Login Location',
      description: 'Login attempt detected from Kiev, Ukraine, which is 5000km from your usual location.',
      severity: 'high',
      timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
      status: 'blocked',
      alertType: 'login_location',
      location: 'Kiev, Ukraine',
      ipAddress: '************',
      deviceInfo: 'Chrome on Windows',
      riskFactors: ['unusual_location', 'first_time_country', 'high_risk_region']
    },
    {
      id: 'FA-002',
      userId: 'user123',
      title: 'Multiple Failed Login Attempts',
      description: '5 failed login attempts detected within 10 minutes.',
      severity: 'medium',
      timestamp: new Date(Date.now() - 45 * 60000).toISOString(),
      status: 'reviewing',
      alertType: 'failed_login',
      location: 'Lagos, Nigeria',
      ipAddress: '************',
      deviceInfo: 'Safari on Mac',
      riskFactors: ['multiple_failures', 'password_guessing']
    },
    {
      id: 'FA-003',
      userId: 'user123',
      title: 'Large Transaction',
      description: 'Transaction of ₦5,000,000 exceeds your usual pattern by 500%.',
      severity: 'critical',
      timestamp: new Date(Date.now() - 120 * 60000).toISOString(),
      status: 'pending',
      alertType: 'large_transaction',
      location: 'Abuja, Nigeria',
      ipAddress: '*************',
      deviceInfo: 'Firefox on Linux',
      transactionId: 'TX-987654',
      transactionAmount: 5000000,
      riskFactors: ['amount_threshold', 'unusual_pattern', 'new_recipient']
    }
  ];
  
  private mockNotifications: SecurityNotification[] = [
    {
      id: 'SN-001',
      title: 'Unusual Login Detected',
      message: 'We noticed a login from a new location (Kiev, Ukraine). Was this you?',
      type: 'warning',
      timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
      isRead: false,
      actionRequired: true,
      actionText: 'Review Activity'
    },
    {
      id: 'SN-002',
      title: 'Security Tip',
      message: 'Enable two-factor authentication to add an extra layer of security to your account.',
      type: 'info',
      timestamp: new Date(Date.now() - 2 * 3600000).toISOString(),
      isRead: true
    },
    {
      id: 'SN-003',
      title: 'Large Transaction Blocked',
      message: 'A suspicious transaction of ₦5,000,000 was automatically blocked.',
      type: 'critical',
      timestamp: new Date(Date.now() - 5 * 3600000).toISOString(),
      isRead: false,
      actionRequired: true,
      actionText: 'Review Transaction'
    }
  ];
  
  private mockSettings: SecuritySettings = {
    userId: 'user123',
    loginNotifications: true,
    transactionAlerts: true,
    deviceVerification: true,
    twoFactorAuth: false,
    biometricLogin: false,
    transactionLimit: 100000,
    suspiciousLoginDetection: true,
    emailAlerts: true,
    smsAlerts: true,
    pushNotifications: false,
    alertSensitivity: 'medium',
    blockInternationalTransactions: false,
    requireOtpForAllTransactions: true,
    velocityChecks: true,
    requirePasswordChangeEvery90Days: true
  };
  
  // Get all fraud alerts for a user
  async getFraudAlerts(userId: string): Promise<FraudAlert[]> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/alerts?userId=${userId}`);
      // const data = await response.json();
      // return data;
      
      // For now, return mock data
      return this.mockAlerts.filter(alert => alert.userId === userId);
    } catch (error) {
      console.error('Error fetching fraud alerts:', error);
      toast.error('Failed to fetch security alerts');
      return [];
    }
  }
  
  // Get security notifications for a user
  async getSecurityNotifications(userId: string): Promise<SecurityNotification[]> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/notifications?userId=${userId}`);
      // const data = await response.json();
      // return data;
      
      // For now, return mock data
      return this.mockNotifications;
    } catch (error) {
      console.error('Error fetching security notifications:', error);
      toast.error('Failed to fetch security notifications');
      return [];
    }
  }
  
  // Update alert status
  async updateAlertStatus(
    alertId: string, 
    status: 'pending' | 'reviewing' | 'resolved' | 'blocked',
    actionBy: string
  ): Promise<boolean> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/alerts/${alertId}`, {
      //   method: 'PATCH',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({ status, actionBy })
      // });
      // const data = await response.json();
      // return data.success;
      
      // For now, update mock data
      const alertIndex = this.mockAlerts.findIndex(alert => alert.id === alertId);
      if (alertIndex !== -1) {
        this.mockAlerts[alertIndex] = {
          ...this.mockAlerts[alertIndex],
          status,
          actionTaken: status,
          actionTimestamp: new Date().toISOString(),
          actionBy
        };
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error updating alert status:', error);
      toast.error('Failed to update alert status');
      return false;
    }
  }
  
  // Mark notification as read
  async markNotificationAsRead(notificationId: string): Promise<boolean> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/notifications/${notificationId}/read`, {
      //   method: 'PATCH'
      // });
      // const data = await response.json();
      // return data.success;
      
      // For now, update mock data
      const notificationIndex = this.mockNotifications.findIndex(notification => notification.id === notificationId);
      if (notificationIndex !== -1) {
        this.mockNotifications[notificationIndex] = {
          ...this.mockNotifications[notificationIndex],
          isRead: true
        };
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return false;
    }
  }
  
  // Mark all notifications as read
  async markAllNotificationsAsRead(): Promise<boolean> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/notifications/read-all`, {
      //   method: 'PATCH'
      // });
      // const data = await response.json();
      // return data.success;
      
      // For now, update mock data
      this.mockNotifications = this.mockNotifications.map(notification => ({
        ...notification,
        isRead: true
      }));
      return true;
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return false;
    }
  }
  
  // Get security settings for a user
  async getSecuritySettings(userId: string): Promise<SecuritySettings> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/settings?userId=${userId}`);
      // const data = await response.json();
      // return data;
      
      // For now, return mock data
      return this.mockSettings;
    } catch (error) {
      console.error('Error fetching security settings:', error);
      toast.error('Failed to fetch security settings');
      return this.mockSettings;
    }
  }
  
  // Update security settings
  async updateSecuritySettings(settings: Partial<SecuritySettings>): Promise<boolean> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/settings`, {
      //   method: 'PATCH',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(settings)
      // });
      // const data = await response.json();
      // return data.success;
      
      // For now, update mock data
      this.mockSettings = {
        ...this.mockSettings,
        ...settings
      };
      return true;
    } catch (error) {
      console.error('Error updating security settings:', error);
      toast.error('Failed to update security settings');
      return false;
    }
  }
  
  // Perform real-time fraud detection on a transaction
  async detectFraud(transaction: any): Promise<{
    isFraudulent: boolean;
    riskScore: number;
    riskFactors: string[];
    action: 'allow' | 'review' | 'block';
  }> {
    try {
      // In a real implementation, this would be an API call to a fraud detection service
      // const response = await fetch(`${this.apiUrl}/detect-fraud`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(transaction)
      // });
      // const data = await response.json();
      // return data;
      
      // For now, implement a simple fraud detection algorithm
      const riskFactors: string[] = [];
      let riskScore = 0;
      
      // Check transaction amount
      if (transaction.amount > this.mockSettings.transactionLimit) {
        riskFactors.push('amount_threshold');
        riskScore += 30;
      }
      
      // Check for new recipient
      if (transaction.isNewRecipient) {
        riskFactors.push('new_recipient');
        riskScore += 15;
      }
      
      // Check for international transaction
      if (transaction.isInternational && this.mockSettings.blockInternationalTransactions) {
        riskFactors.push('international_transaction');
        riskScore += 25;
      }
      
      // Check for unusual time
      const hour = new Date().getHours();
      if (hour < 6 || hour > 23) {
        riskFactors.push('unusual_time');
        riskScore += 10;
      }
      
      // Determine action based on risk score
      let action: 'allow' | 'review' | 'block' = 'allow';
      if (riskScore >= 50) {
        action = 'block';
      } else if (riskScore >= 25) {
        action = 'review';
      }
      
      return {
        isFraudulent: riskScore >= 50,
        riskScore,
        riskFactors,
        action
      };
    } catch (error) {
      console.error('Error detecting fraud:', error);
      // Default to safe behavior if the fraud detection fails
      return {
        isFraudulent: false,
        riskScore: 0,
        riskFactors: ['detection_failed'],
        action: 'review' // Default to review if detection fails
      };
    }
  }
  
  // Log security event
  async logSecurityEvent(event: Omit<SecurityLog, 'id'>): Promise<boolean> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/logs`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // });
      // const data = await response.json();
      // return data.success;
      
      // For now, just log to console
      console.log('Security event logged:', event);
      return true;
    } catch (error) {
      console.error('Error logging security event:', error);
      return false;
    }
  }
  
  // Perform device fingerprinting
  async fingerprintDevice(): Promise<string> {
    // In a real implementation, this would use a library like FingerprintJS
    // to generate a unique device identifier based on browser and system characteristics
    
    // For now, return a mock fingerprint
    return 'mock-device-fingerprint-' + Math.random().toString(36).substring(2, 15);
  }
  
  // Verify if the current device is trusted
  async isDeviceTrusted(userId: string, deviceFingerprint: string): Promise<boolean> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/trusted-devices?userId=${userId}&fingerprint=${deviceFingerprint}`);
      // const data = await response.json();
      // return data.trusted;
      
      // For now, return a mock result
      return Math.random() > 0.2; // 80% chance the device is trusted
    } catch (error) {
      console.error('Error checking if device is trusted:', error);
      return false; // Default to untrusted if check fails
    }
  }
  
  // Perform a security risk assessment for a user
  async performRiskAssessment(userId: string): Promise<RiskAssessment> {
    try {
      // In a real implementation, this would be an API call
      // const response = await fetch(`${this.apiUrl}/risk-assessment?userId=${userId}`);
      // const data = await response.json();
      // return data;
      
      // For now, return a mock assessment
      const hasEnabledTwoFactor = this.mockSettings.twoFactorAuth;
      const hasStrongPassword = Math.random() > 0.3; // Mock check
      const hasRecentSuspiciousActivity = this.mockAlerts.some(
        alert => alert.userId === userId && 
                new Date(alert.timestamp).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
      );
      
      const riskFactors: string[] = [];
      let overallRiskScore = 0;
      
      if (!hasEnabledTwoFactor) {
        riskFactors.push('no_two_factor_auth');
        overallRiskScore += 30;
      }
      
      if (!hasStrongPassword) {
        riskFactors.push('weak_password');
        overallRiskScore += 25;
      }
      
      if (hasRecentSuspiciousActivity) {
        riskFactors.push('recent_suspicious_activity');
        overallRiskScore += 20;
      }
      
      if (!this.mockSettings.deviceVerification) {
        riskFactors.push('device_verification_disabled');
        overallRiskScore += 15;
      }
      
      // Calculate security score (inverse of risk score, capped at 100)
      const securityScore = Math.max(0, Math.min(100, 100 - overallRiskScore));
      
      // Generate recommendations
      const recommendations: string[] = [];
      
      if (!hasEnabledTwoFactor) {
        recommendations.push('Enable two-factor authentication for stronger account security');
      }
      
      if (!hasStrongPassword) {
        recommendations.push('Update your password to a stronger one with a mix of characters');
      }
      
      if (!this.mockSettings.deviceVerification) {
        recommendations.push('Enable device verification to protect against unauthorized access');
      }
      
      if (hasRecentSuspiciousActivity) {
        recommendations.push('Review recent account activity for any unauthorized actions');
      }
      
      return {
        overallRiskScore: securityScore,
        riskFactors,
        recommendations
      };
    } catch (error) {
      console.error('Error performing risk assessment:', error);
      return {
        overallRiskScore: 50, // Neutral score if assessment fails
        riskFactors: ['assessment_failed'],
        recommendations: ['Contact support for a security review of your account']
      };
    }
  }
}

export const securityService = new SecurityService();
export default securityService;
