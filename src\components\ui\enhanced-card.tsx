
import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const enhancedCardVariants = cva(
  "rounded-[15px] transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-white/80 backdrop-blur-xl border border-[#D3E4FD] shadow-none",
        glass: "bg-white/60 backdrop-blur-2xl border border-[#D3E4FD] shadow-none",
        gradient: "bg-gradient-to-br from-kojaPrimary/5 to-kojaPrimary/10 backdrop-blur-xl border border-[#D3E4FD] shadow-none",
        yellowGradient: "bg-gradient-to-br from-kojaYellow/5 to-kojaYellow/10 backdrop-blur-xl border border-[#D3E4FD] shadow-none",
        dark: "bg-kojaDark/80 backdrop-blur-xl text-white border border-[#D3E4FD] shadow-none",
        outline: "bg-transparent backdrop-blur-xl border border-[#D3E4FD]",
        futuristic: "bg-gradient-to-br from-white/70 to-white/50 backdrop-blur-2xl border border-white/30 shadow-lg hover:shadow-xl",
        minimal: "bg-white/30 backdrop-blur-xl border border-[#D3E4FD] shadow-none",
        neo: "bg-white/80 backdrop-blur-xl border-t border-l border-r border-b-4 border-[#D3E4FD] shadow-lg",
        future: "bg-gradient-to-br from-white/70 via-white/60 to-white/40 backdrop-blur-3xl border border-white/40 shadow-[0_8px_32px_rgba(0,0,0,0.1)] overflow-hidden",
      },
      size: {
        default: "p-6",
        sm: "p-4",
        lg: "p-8",
        xl: "p-10",
      },
      animation: {
        none: "",
        float: "hover:translate-y-[-5px]",
        scale: "hover:scale-[1.02]",
        shadow: "hover:shadow-xl",
        all: "hover:translate-y-[-5px] hover:shadow-xl",
        glow: "hover:shadow-[0_0_15px_rgba(255,255,255,0.5)] hover:translate-y-[-3px]",
      }
    },
    defaultVariants: {
      variant: "future",
      size: "default",
      animation: "glow",
    },
  }
);

export interface EnhancedCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof enhancedCardVariants> {}

const EnhancedCard = React.forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ className, variant, size, animation, ...props }, ref) => {
    return (
      <div
        className={cn(enhancedCardVariants({ variant, size, animation, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
EnhancedCard.displayName = "EnhancedCard";

const EnhancedCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5", className)}
    {...props}
  />
));
EnhancedCardHeader.displayName = "EnhancedCardHeader";

const EnhancedCardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-roboto-condensed font-semibold leading-none tracking-tight text-gray-900",
      className
    )}
    {...props}
  />
));
EnhancedCardTitle.displayName = "EnhancedCardTitle";

const EnhancedCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-gray-600", className)}
    {...props}
  />
));
EnhancedCardDescription.displayName = "EnhancedCardDescription";

const EnhancedCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("", className)} {...props} />
));
EnhancedCardContent.displayName = "EnhancedCardContent";

const EnhancedCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center mt-4 pt-4 border-t border-gray-100", className)}
    {...props}
  />
));
EnhancedCardFooter.displayName = "EnhancedCardFooter";

export { 
  EnhancedCard, 
  EnhancedCardHeader, 
  EnhancedCardFooter, 
  EnhancedCardTitle, 
  EnhancedCardDescription, 
  EnhancedCardContent,
  enhancedCardVariants 
};
