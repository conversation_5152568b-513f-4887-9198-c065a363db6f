
import { ApiErrorResponse } from '@/types/errorHandling';

/**
 * Handles API errors consistently
 */
export const handleApiError = (error: unknown): ApiErrorResponse => {
  console.error('API Error:', error);
  
  if (error instanceof Error) {
    return {
      success: false,
      message: error.message || 'An error occurred during the API request',
      code: 'API_ERROR'
    };
  }
  
  return {
    success: false,
    message: 'An unknown error occurred during the API request',
    code: 'UNKNOWN_ERROR'
  };
};
