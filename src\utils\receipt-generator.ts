import { Transaction } from '@/components/TransactionTable';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Type definitions for receipt data
export type ReceiptItem = {
  name: string;
  price: number;
  quantity: number;
};

export type ReceiptData = {
  id: string;
  transactionDate: string;
  transactionTime: string;
  recipient: string;
  amount: number;
  status: string;
  description: string;
  reference: string;
  type: string;
  fee: string;
  totalAmount: number;
  items?: ReceiptItem[];
  paymentMethod?: string;
  tax?: number;
  subtotal?: number;
  merchantName?: string;
  merchantAddress?: string;
  customerName?: string;
};

// Generate a receipt object for viewing
export const generateTransactionReceipt = (
  transaction: Transaction, 
  download: boolean = false
): ReceiptData => {
  // Create receipt data
  const receipt: ReceiptData = {
    id: transaction.id,
    transactionDate: format(new Date(transaction.date), 'PPP'),
    transactionTime: format(new Date(transaction.date), 'p'),
    recipient: transaction.recipient,
    amount: transaction.amount,
    status: transaction.status,
    description: transaction.description || 'Transaction',
    reference: transaction.reference || `REF-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
    type: transaction.type || 'outgoing',
    fee: transaction.fee || '₦0.00',
    totalAmount: transaction.amount,
    items: transaction.items || [],
    paymentMethod: transaction.paymentMethod || 'Card',
    tax: transaction.tax || 0,
    subtotal: transaction.subtotal || transaction.amount,
    merchantName: 'KojaPay',
    merchantAddress: '123 Business Avenue, Lagos',
    customerName: transaction.customerName || 'Valued Customer'
  };

  if (download) {
    // Download receipt as PDF or image
    downloadReceipt(receipt);
  }

  return receipt;
};

// Function for POS transactions specifically
export const generatePOSReceipt = (
  cartItems: any[],
  total: number,
  subtotal: number,
  tax: number,
  paymentMethod: string = 'Card',
  download: boolean = false
): ReceiptData => {
  // Generate transaction ID
  const transactionId = `KP${Math.floor(Math.random() * 1000000)}`;
  
  // Format items for receipt
  const items = cartItems.map(item => ({
    name: item.name,
    price: item.price,
    quantity: item.quantity
  }));
  
  // Create receipt data
  const now = new Date();
  const receipt = {
    id: transactionId,
    transactionDate: format(now, 'PPP'),
    transactionTime: format(now, 'p'),
    recipient: 'Customer Purchase',
    amount: total,
    status: 'success',
    description: `POS Sale - ${cartItems.length} items`,
    reference: `REF-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
    type: 'sale',
    fee: '₦0.00',
    totalAmount: total,
    items: items,
    paymentMethod: paymentMethod,
    tax: tax,
    subtotal: subtotal,
    merchantName: 'KojaPay',
    merchantAddress: '123 Business Avenue, Lagos',
    customerName: 'Valued Customer'
  };

  if (download) {
    // Download receipt as PDF or image
    downloadReceipt(receipt);
  }

  return receipt;
};

// Function to print POS receipt directly to printer
export const printPOSReceipt = (receipt: ReceiptData): void => {
  // Create a hidden div to hold the receipt content for printing
  const printWindow = window.open('', '_blank', 'width=400,height=600');
  
  if (!printWindow) {
    alert('Please allow popups to print receipts');
    return;
  }
  
  // Add receipt styles
  printWindow.document.write(`
    <html>
    <head>
      <title>KojaPay Receipt - ${receipt.id}</title>
      <style>
        body {
          font-family: 'Courier New', monospace;
          width: 300px;
          margin: 0 auto;
          padding: 10px;
          font-size: 12px;
        }
        .header {
          text-align: center;
          margin-bottom: 10px;
        }
        .logo {
          margin-bottom: 10px;
          text-align: center;
        }
        .logo img {
          width: 60px;
          height: 60px;
        }
        .title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .address {
          font-size: 10px;
          margin-bottom: 10px;
        }
        .info {
          margin-bottom: 10px;
        }
        .divider {
          border-top: 1px dashed #000;
          margin: 10px 0;
        }
        .item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }
        .total {
          font-weight: bold;
          margin-top: 5px;
          text-align: right;
        }
        .footer {
          text-align: center;
          margin-top: 20px;
          font-size: 10px;
        }
        .status {
          text-align: center;
          padding: 5px;
          margin: 10px 0;
          background-color: #f0f0f0;
          border-radius: 3px;
        }
        .status.success {
          background-color: #d4edda;
          color: #155724;
        }
        .status.pending {
          background-color: #fff3cd;
          color: #856404;
        }
        .status.failed {
          background-color: #f8d7da;
          color: #721c24;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">
          <img src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" alt="KojaPay Logo">
        </div>
        <div class="title">KojaPay</div>
        <div class="address">${receipt.merchantAddress}</div>
      </div>
      
      <div class="info">
        <div>Transaction ID: ${receipt.id}</div>
        <div>Date: ${receipt.transactionDate}</div>
        <div>Time: ${receipt.transactionTime}</div>
        <div>Payment Method: ${receipt.paymentMethod}</div>
      </div>
      
      <div class="divider"></div>
      
      <div class="items">
        <div style="font-weight: bold; margin-bottom: 5px;">Items:</div>
        ${receipt.items?.map(item => `
          <div class="item">
            <div>${item.name} x${item.quantity}</div>
            <div>₦${(item.price * item.quantity).toLocaleString()}</div>
          </div>
        `).join('') || ''}
      </div>
      
      <div class="divider"></div>
      
      <div>
        <div class="item">
          <div>Subtotal:</div>
          <div>₦${receipt.subtotal?.toLocaleString()}</div>
        </div>
        <div class="item">
          <div>Tax (7.5%):</div>
          <div>₦${receipt.tax?.toLocaleString()}</div>
        </div>
        <div class="item total">
          <div>Total:</div>
          <div>₦${receipt.totalAmount.toLocaleString()}</div>
        </div>
      </div>
      
      <div class="status ${receipt.status.toLowerCase()}">
        ${receipt.status.toUpperCase()}
      </div>
      
      <div class="divider"></div>
      
      <div class="footer">
        <div>Thank you for your business!</div>
        <div>www.kojapay.io</div>
        <div>Reference: ${receipt.reference}</div>
      </div>
    </body>
    </html>
  `);
  
  // Focus and print
  printWindow.document.close();
  printWindow.focus();
  
  // Add slight delay to ensure content is loaded before printing
  setTimeout(() => {
    printWindow.print();
    printWindow.close();
  }, 250);
};

// Function to download receipt
const downloadReceipt = (receipt: ReceiptData) => {
  // Create a hidden canvas element
  const canvas = document.createElement('canvas');
  canvas.width = 400;
  canvas.height = 600;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) return;
  
  // Set background
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, canvas.width, canvas.height);
  
  // Add a slight border
  ctx.strokeStyle = '#eeeeee';
  ctx.lineWidth = 4;
  ctx.strokeRect(10, 10, canvas.width - 20, canvas.height - 20);
  
  // Add KojaPay logo - first load the image
  const logoImg = new Image();
  logoImg.onload = () => {
    // Draw the logo
    ctx.drawImage(logoImg, canvas.width/2 - 30, 30, 60, 60);
    
    // Add title
    ctx.fillStyle = '#1231b8';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('TRANSACTION RECEIPT', canvas.width / 2, 110);
    
    // Add KojaPay text
    ctx.fillStyle = '#1231b8';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('KojaPay', canvas.width / 2, 130);
    
    // Add divider
    ctx.strokeStyle = '#eeeeee';
    ctx.beginPath();
    ctx.moveTo(50, 150);
    ctx.lineTo(350, 150);
    ctx.stroke();
    
    // Add receipt content
    ctx.fillStyle = '#333333';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    
    // Transaction info
    const contentStart = 180;
    const lineHeight = 25;
    let currentY = contentStart;
    
    // Helper function to draw a line of content
    const drawContentLine = (label: string, value: string) => {
      ctx.fillStyle = '#777777';
      ctx.textAlign = 'left';
      ctx.fillText(label, 50, currentY);
      
      ctx.fillStyle = '#333333';
      ctx.textAlign = 'right';
      ctx.fillText(value, 350, currentY);
      
      currentY += lineHeight;
    };
    
    drawContentLine('Transaction ID:', receipt.id);
    drawContentLine('Date:', receipt.transactionDate);
    drawContentLine('Time:', receipt.transactionTime);
    drawContentLine('Recipient:', receipt.recipient);
    drawContentLine('Description:', receipt.description);
    
    // Add items if present
    if (receipt.items && receipt.items.length > 0) {
      currentY += 10;
      ctx.fillStyle = '#333333';
      ctx.textAlign = 'left';
      ctx.fillText('Items:', 50, currentY);
      currentY += lineHeight;
      
      receipt.items.forEach(item => {
        ctx.fillStyle = '#555555';
        ctx.textAlign = 'left';
        ctx.fillText(`${item.name} x${item.quantity}`, 70, currentY);
        
        ctx.textAlign = 'right';
        ctx.fillText(`₦${(item.price * item.quantity).toLocaleString()}`, 350, currentY);
        
        currentY += lineHeight - 5;
      });
      
      currentY += 5;
    }
    
    // Financial details
    if (receipt.subtotal && receipt.subtotal !== receipt.totalAmount) {
      drawContentLine('Subtotal:', `₦${receipt.subtotal.toLocaleString()}`);
    }
    
    if (receipt.tax) {
      drawContentLine('Tax (7.5%):', `₦${receipt.tax.toLocaleString()}`);
    }
    
    if (receipt.fee && receipt.fee !== '₦0.00') {
      drawContentLine('Fee:', receipt.fee);
    }
    
    drawContentLine('Total:', `₦${receipt.totalAmount.toLocaleString()}`);
    drawContentLine('Status:', receipt.status.toUpperCase());
    drawContentLine('Reference:', receipt.reference);
    
    if (receipt.paymentMethod) {
      drawContentLine('Payment Method:', receipt.paymentMethod);
    }
    
    // Add bottom divider
    currentY += 15;
    ctx.strokeStyle = '#eeeeee';
    ctx.beginPath();
    ctx.moveTo(50, currentY);
    ctx.lineTo(350, currentY);
    ctx.stroke();
    currentY += 30;
    
    // Add thank you note
    ctx.fillStyle = '#777777';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Thank you for using KojaPay', canvas.width / 2, currentY);
    
    // Add timestamp
    currentY += 20;
    ctx.font = '12px Arial';
    ctx.fillText(`Generated on ${new Date().toLocaleString()}`, canvas.width / 2, currentY);
    
    // Convert canvas to image and trigger download
    const dataUrl = canvas.toDataURL('image/png');
    const link = document.createElement('a');
    link.download = `KojaPay-Receipt-${receipt.id}.png`;
    link.href = dataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Set the source of the image to load
  logoImg.src = '/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png';
};

// Function to generate PDF receipt
export const generatePDFReceipt = (receipt: ReceiptData) => {
  // Create new PDF document
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a5'
  });
  
  // Set up the document
  pdf.setFillColor(255, 255, 255);
  pdf.rect(0, 0, 210, 297, 'F');
  
  // Add KojaPay logo
  // Note: For production, we'd include a base64 encoded logo
  // pdf.addImage(logoBase64, 'PNG', 70, 10, 60, 60);
  
  // Add title
  pdf.setTextColor(18, 49, 184); // KojaPay blue
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.text('TRANSACTION RECEIPT', 105, 80, { align: 'center' });
  
  pdf.setFontSize(14);
  pdf.text('KojaPay', 105, 90, { align: 'center' });
  
  // Add divider
  pdf.setDrawColor(200, 200, 200);
  pdf.line(20, 100, 190, 100);
  
  // Add receipt content
  pdf.setTextColor(60, 60, 60);
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  
  let yPos = 110;
  const lineHeight = 8;
  
  // Helper function to add a line of content
  const addReceiptLine = (label: string, value: string) => {
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(120, 120, 120);
    pdf.text(label, 25, yPos);
    
    pdf.setTextColor(40, 40, 40);
    pdf.setFont('helvetica', 'bold');
    pdf.text(value, 185, yPos, { align: 'right' });
    
    yPos += lineHeight;
  };
  
  // Add transaction details
  addReceiptLine('Transaction ID:', receipt.id);
  addReceiptLine('Date:', receipt.transactionDate);
  addReceiptLine('Time:', receipt.transactionTime);
  addReceiptLine('Recipient:', receipt.recipient);
  addReceiptLine('Description:', receipt.description);
  
  // Add items if present
  if (receipt.items && receipt.items.length > 0) {
    yPos += 5;
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(60, 60, 60);
    pdf.text('Items:', 25, yPos);
    yPos += lineHeight;
    
    receipt.items.forEach(item => {
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(80, 80, 80);
      pdf.text(`${item.name} x${item.quantity}`, 35, yPos);
      
      pdf.text(`₦${(item.price * item.quantity).toLocaleString()}`, 185, yPos, { align: 'right' });
      
      yPos += lineHeight - 2;
    });
    
    yPos += 5;
  }
  
  // Add divider
  pdf.setDrawColor(200, 200, 200);
  pdf.line(20, yPos, 190, yPos);
  yPos += lineHeight;
  
  // Financial details
  if (receipt.subtotal) {
    addReceiptLine('Subtotal:', `₦${receipt.subtotal.toLocaleString()}`);
  }
  
  if (receipt.tax) {
    addReceiptLine('Tax (7.5%):', `₦${receipt.tax.toLocaleString()}`);
  }
  
  if (receipt.fee && receipt.fee !== '₦0.00') {
    addReceiptLine('Fee:', receipt.fee);
  }
  
  pdf.setFont('helvetica', 'bold');
  addReceiptLine('Total:', `₦${receipt.totalAmount.toLocaleString()}`);
  
  // Status - fixed the RGB color values to use proper syntax
  yPos += 5;
  
  // Use proper RGB syntax with setFillColor - this was the source of the error
  if (receipt.status === 'success') {
    pdf.setFillColor(212, 237, 218);
    pdf.setDrawColor(195, 230, 203);
    pdf.setTextColor(21, 87, 36);
  } else if (receipt.status === 'pending') {
    pdf.setFillColor(255, 243, 205);
    pdf.setDrawColor(255, 238, 186);
    pdf.setTextColor(133, 100, 4);
  } else {
    pdf.setFillColor(248, 215, 218);
    pdf.setDrawColor(245, 198, 203);
    pdf.setTextColor(114, 28, 36);
  }
  
  pdf.roundedRect(70, yPos - 5, 70, 10, 2, 2, 'FD');
  pdf.text(receipt.status.toUpperCase(), 105, yPos + 1, { align: 'center' });
  
  // Add bottom divider
  yPos += 15;
  pdf.setDrawColor(200, 200, 200);
  pdf.line(20, yPos, 190, yPos);
  yPos += 20;
  
  // Add thank you note
  pdf.setTextColor(120, 120, 120);
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text('Thank you for using KojaPay', 105, yPos, { align: 'center' });
  
  yPos += 10;
  pdf.setFontSize(9);
  pdf.text('www.kojapay.io', 105, yPos, { align: 'center' });
  
  yPos += 8;
  pdf.text(`Reference: ${receipt.reference}`, 105, yPos, { align: 'center' });
  
  yPos += 12;
  pdf.setFontSize(8);
  pdf.text(`Generated on ${new Date().toLocaleString()}`, 105, yPos, { align: 'center' });
  
  // Save the PDF
  pdf.save(`KojaPay-Receipt-${receipt.id}.pdf`);
};

// Export downloadReceipt function only - fixes the duplicate export
export { downloadReceipt };
