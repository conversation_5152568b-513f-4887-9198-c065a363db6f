import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { 
  CreditCard, 
  Lock, 
  Key, 
  Shield, 
  Sliders, 
  Truck, 
  RefreshCw,
  Check,
  Smartphone,
  Globe,
  ShoppingCart,
  AlertCircle
} from 'lucide-react';
import { MicroButton } from '@/components/ui/micro-button';

const CardServices = () => {
  const [selectedCard, setSelectedCard] = useState('debit');
  const [currentPIN, setCurrentPIN] = useState('');
  const [newPIN, setNewPIN] = useState('');
  const [confirmPIN, setConfirmPIN] = useState('');
  const [dailyLimit, setDailyLimit] = useState(100000);
  const [onlineLimit, setOnlineLimit] = useState(50000);
  const [internationalEnabled, setInternationalEnabled] = useState(true);
  const [contactlessEnabled, setContactlessEnabled] = useState(true);
  const [onlineEnabled, setOnlineEnabled] = useState(true);
  const [atmEnabled, setAtmEnabled] = useState(true);
  const [posEnabled, setPosEnabled] = useState(true);
  const [replacementReason, setReplacementReason] = useState('');
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [activeTab, setActiveTab] = useState('pin');
  const { toast } = useToast();

  const handleChangePIN = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPIN || !newPIN || !confirmPIN) {
      toast({
        title: "Missing information",
        description: "Please fill in all PIN fields",
        variant: "destructive"
      });
      return;
    }

    if (newPIN !== confirmPIN) {
      toast({
        title: "PIN mismatch",
        description: "New PIN and confirm PIN do not match",
        variant: "destructive"
      });
      return;
    }

    // Simulate PIN change
    setTimeout(() => {
      toast({
        title: "PIN Changed",
        description: "Your card PIN has been successfully updated",
        variant: "success"
      });
      setCurrentPIN('');
      setNewPIN('');
      setConfirmPIN('');
    }, 1500);
  };

  const handleLimitChange = () => {
    // Simulate limit update
    toast({
      title: "Limits Updated",
      description: "Your card spending limits have been updated",
      variant: "success"
    });
  };

  const handleCardReplacement = (e: React.FormEvent) => {
    e.preventDefault();
    if (!replacementReason || !deliveryAddress) {
      toast({
        title: "Missing information",
        description: "Please provide the reason and delivery address",
        variant: "destructive"
      });
      return;
    }

    // Simulate card replacement request
    setTimeout(() => {
      toast({
        title: "Replacement Requested",
        description: "Your card replacement request has been submitted",
        variant: "success"
      });
      setReplacementReason('');
      setDeliveryAddress('');
    }, 1500);
  };

  return (
    <DashboardLayout pageTitle="Card Services">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-kojaDark">Card Services</h1>
        <p className="text-kojaGray mt-1">Manage your card settings and services</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <CardTitle>Select Card</CardTitle>
              <CardDescription>Choose the card you want to manage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div 
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedCard === 'debit' 
                      ? 'border-kojaPrimary bg-kojaPrimary/5' 
                      : 'border-gray-200 hover:border-kojaPrimary/50'
                  }`}
                  onClick={() => setSelectedCard('debit')}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-500 rounded-md flex items-center justify-center">
                      <CreditCard className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium">Debit Card</div>
                      <div className="text-xs text-kojaGray mt-1">**** **** **** 0329</div>
                    </div>
                  </div>
                </div>
                
                <div 
                  className={`p-4 border rounded-lg cursor-pointer transition-all ${
                    selectedCard === 'virtual' 
                      ? 'border-kojaPrimary bg-kojaPrimary/5' 
                      : 'border-gray-200 hover:border-kojaPrimary/50'
                  }`}
                  onClick={() => setSelectedCard('virtual')}
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-purple-500 rounded-md flex items-center justify-center">
                      <CreditCard className="text-white" />
                    </div>
                    <div>
                      <div className="font-medium">Virtual Card</div>
                      <div className="text-xs text-kojaGray mt-1">**** **** **** 4532</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <Tabs defaultValue={activeTab} onValueChange={setActiveTab}>
                <TabsList className="w-full">
                  <TabsTrigger value="pin" className="flex-1">
                    <Key className="h-4 w-4 mr-2" />
                    Change PIN
                  </TabsTrigger>
                  <TabsTrigger value="limits" className="flex-1">
                    <Sliders className="h-4 w-4 mr-2" />
                    Transaction Limits
                  </TabsTrigger>
                  <TabsTrigger value="replace" className="flex-1">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Card Replacement
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="pin" className="mt-6">
                  <form onSubmit={handleChangePIN} className="space-y-6">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="currentPin">Current PIN</Label>
                        <Input 
                          id="currentPin" 
                          type="password" 
                          maxLength={4} 
                          placeholder="Enter current PIN" 
                          value={currentPIN}
                          onChange={(e) => setCurrentPIN(e.target.value)}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="newPin">New PIN</Label>
                        <Input 
                          id="newPin" 
                          type="password" 
                          maxLength={4} 
                          placeholder="Enter new PIN" 
                          value={newPIN}
                          onChange={(e) => setNewPIN(e.target.value)}
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="confirmPin">Confirm New PIN</Label>
                        <Input 
                          id="confirmPin" 
                          type="password" 
                          maxLength={4} 
                          placeholder="Confirm new PIN" 
                          value={confirmPIN}
                          onChange={(e) => setConfirmPIN(e.target.value)}
                        />
                      </div>
                    </div>
                    
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div>
                          <h3 className="font-medium text-yellow-800">PIN Security</h3>
                          <p className="text-sm text-yellow-600 mt-1">
                            Never share your PIN with anyone, including bank staff.
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <Button type="submit" className="w-full">
                      Change PIN
                    </Button>
                  </form>
                </TabsContent>
                
                <TabsContent value="limits" className="mt-6">
                  <div className="space-y-6">
                    <div>
                      <Label htmlFor="dailyLimit">Daily Transaction Limit</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <Input
                          id="dailyLimit"
                          type="number"
                          value={dailyLimit}
                          onChange={(e) => setDailyLimit(Number(e.target.value))}
                          className="flex-1"
                        />
                        <span className="text-sm text-kojaGray">₦</span>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="onlineLimit">Online Transaction Limit</Label>
                      <div className="flex items-center gap-4 mt-2">
                        <Input
                          id="onlineLimit"
                          type="number"
                          value={onlineLimit}
                          onChange={(e) => setOnlineLimit(Number(e.target.value))}
                          className="flex-1"
                        />
                        <span className="text-sm text-kojaGray">₦</span>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <Label>Transaction Channels</Label>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Globe size={16} className="text-kojaGray" />
                          <Label htmlFor="international">International Transactions</Label>
                        </div>
                        <Switch 
                          id="international" 
                          checked={internationalEnabled}
                          onCheckedChange={setInternationalEnabled}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Smartphone size={16} className="text-kojaGray" />
                          <Label htmlFor="contactless">Contactless Payments</Label>
                        </div>
                        <Switch 
                          id="contactless" 
                          checked={contactlessEnabled}
                          onCheckedChange={setContactlessEnabled}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <ShoppingCart size={16} className="text-kojaGray" />
                          <Label htmlFor="online">Online Purchases</Label>
                        </div>
                        <Switch 
                          id="online" 
                          checked={onlineEnabled}
                          onCheckedChange={setOnlineEnabled}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CreditCard size={16} className="text-kojaGray" />
                          <Label htmlFor="atm">ATM Withdrawals</Label>
                        </div>
                        <Switch 
                          id="atm" 
                          checked={atmEnabled}
                          onCheckedChange={setAtmEnabled}
                        />
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <CreditCard size={16} className="text-kojaGray" />
                          <Label htmlFor="pos">POS Transactions</Label>
                        </div>
                        <Switch 
                          id="pos" 
                          checked={posEnabled}
                          onCheckedChange={setPosEnabled}
                        />
                      </div>
                    </div>
                    
                    <Button onClick={handleLimitChange} className="w-full">
                      Save Changes
                    </Button>
                  </div>
                </TabsContent>
                
                <TabsContent value="replace" className="mt-6">
                  <form onSubmit={handleCardReplacement} className="space-y-6">
                    <div>
                      <Label htmlFor="reason">Reason for Replacement</Label>
                      <select 
                        id="reason"
                        className="w-full p-2 border rounded-md mt-1"
                        value={replacementReason}
                        onChange={(e) => setReplacementReason(e.target.value)}
                      >
                        <option value="">Select Reason</option>
                        <option value="lost">Lost Card</option>
                        <option value="stolen">Stolen Card</option>
                        <option value="damaged">Damaged Card</option>
                        <option value="expired">Expired Card</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                    
                    <div>
                      <Label htmlFor="address">Delivery Address</Label>
                      <Input 
                        id="address"
                        placeholder="Enter delivery address"
                        value={deliveryAddress}
                        onChange={(e) => setDeliveryAddress(e.target.value)}
                      />
                    </div>
                    
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                        <div>
                          <h3 className="font-medium text-yellow-800">Important Notice</h3>
                          <p className="text-sm text-yellow-600 mt-1">
                            A fee of ₦1,000 will be charged for card replacement. Your new card will be delivered within 5-7 business days.
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <Button type="submit" className="w-full">
                      Request Replacement
                    </Button>
                  </form>
                </TabsContent>
              </Tabs>
            </CardHeader>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <CardTitle>Card Security</CardTitle>
              <CardDescription>Manage your card security settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="bg-kojaPrimary/10 p-2 rounded-md">
                    <Lock size={20} className="text-kojaPrimary" />
                  </div>
                  <div>
                    <p className="font-medium text-kojaDark">Block Card</p>
                    <p className="text-xs text-kojaGray">Temporarily lock your card</p>
                  </div>
                </div>
                <MicroButton variant="outline" size="sm">
                  Block
                </MicroButton>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="bg-kojaPrimary/10 p-2 rounded-md">
                    <Shield size={20} className="text-kojaPrimary" />
                  </div>
                  <div>
                    <p className="font-medium text-kojaDark">3D Secure</p>
                    <p className="text-xs text-kojaGray">Secure online transactions</p>
                  </div>
                </div>
                <Switch checked={true} />
              </div>
              
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="bg-kojaPrimary/10 p-2 rounded-md">
                    <CreditCard size={20} className="text-kojaPrimary" />
                  </div>
                  <div>
                    <p className="font-medium text-kojaDark">Transaction Alerts</p>
                    <p className="text-xs text-kojaGray">Get notified for all transactions</p>
                  </div>
                </div>
                <Switch checked={true} />
              </div>
            </CardContent>
          </Card>
          
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3">
                <Button variant="outline" className="justify-start">
                  <Truck className="mr-2 h-4 w-4" />
                  Track Card Delivery
                </Button>
                
                <Button variant="outline" className="justify-start" onClick={() => setActiveTab('pin')}>
                  <Key className="mr-2 h-4 w-4" />
                  Change Card PIN
                </Button>
                
                <Button variant="outline" className="justify-start" onClick={() => setActiveTab('limits')}>
                  <Sliders className="mr-2 h-4 w-4" />
                  Update Limits
                </Button>
                
                <Button variant="outline" className="justify-start" onClick={() => setActiveTab('replace')}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Replace Card
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CardServices;
