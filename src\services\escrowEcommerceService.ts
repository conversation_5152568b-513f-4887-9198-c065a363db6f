import { v4 as uuidv4 } from 'uuid';

// Define types for the escrow service
export type EscrowStatus = 'pending' | 'active' | 'completed' | 'cancelled' | 'disputed';
export type DisputeStatus = 'pending' | 'resolved';
export type DisputeSender = 'buyer' | 'seller';

export interface Escrow {
  id: string;
  orderId: string;
  title: string;
  description: string;
  amount: number;
  currency: string;
  buyerId: string;
  sellerId: string;
  buyer: {
    name: string;
    email: string;
  };
  seller: {
    name: string;
    email: string;
  };
  status: EscrowStatus;
  createdAt: string;
  updatedAt: string;
  dispute?: Dispute;
}

export interface Dispute {
  id: string;
  escrowId: string;
  reason: string;
  details: string;
  sender: DisputeSender;
  status: DisputeStatus;
  createdAt: string;
  updatedAt: string;
}

class EscrowEcommerceService {
  private escrows: Escrow[] = [
    {
      id: 'esc_1',
      orderId: 'order_1',
      title: 'Order #1234',
      description: 'Payment for smartphone purchase',
      amount: 150000,
      currency: 'NGN',
      buyerId: 'buyer_1',
      sellerId: 'seller_1',
      buyer: {
        name: '<PERSON>',
        email: '<EMAIL>'
      },
      seller: {
        name: 'Electronics Store',
        email: '<EMAIL>'
      },
      status: 'active',
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'esc_2',
      orderId: 'order_2',
      title: 'Order #5678',
      description: 'Payment for a laptop',
      amount: 550000,
      currency: 'NGN',
      buyerId: 'buyer_2',
      sellerId: 'seller_1',
      buyer: {
        name: 'Alice Smith',
        email: '<EMAIL>'
      },
      seller: {
        name: 'Electronics Store',
        email: '<EMAIL>'
      },
      status: 'pending',
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'esc_3',
      orderId: 'order_3',
      title: 'Order #9012',
      description: 'Payment for a TV',
      amount: 750000,
      currency: 'NGN',
      buyerId: 'buyer_1',
      sellerId: 'seller_2',
      buyer: {
        name: 'John Doe',
        email: '<EMAIL>'
      },
      seller: {
        name: 'Home Appliances Ltd',
        email: '<EMAIL>'
      },
      status: 'completed',
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'esc_4',
      orderId: 'order_4',
      title: 'Order #3456',
      description: 'Payment for a refrigerator',
      amount: 950000,
      currency: 'NGN',
      buyerId: 'buyer_2',
      sellerId: 'seller_2',
      buyer: {
        name: 'Alice Smith',
        email: '<EMAIL>'
      },
      seller: {
        name: 'Home Appliances Ltd',
        email: '<EMAIL>'
      },
      status: 'disputed',
      createdAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 9 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 'esc_5',
      orderId: 'order_5',
      title: 'Order #7890',
      description: 'Payment for a washing machine',
      amount: 850000,
      currency: 'NGN',
      buyerId: 'buyer_1',
      sellerId: 'seller_3',
      buyer: {
        name: 'John Doe',
        email: '<EMAIL>'
      },
      seller: {
        name: 'Laundry Solutions',
        email: '<EMAIL>'
      },
      status: 'cancelled',
      createdAt: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 11 * 24 * 60 * 60 * 1000).toISOString(),
    }
  ];

  private disputes: Dispute[] = [
    {
      id: 'disp_1',
      escrowId: 'esc_4',
      reason: 'Item not as described',
      details: 'The product received does not match the description in the listing.',
      sender: 'buyer',
      status: 'pending',
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    }
  ];

  // Get user escrow transactions
  async getUserOrderEscrows(userId: string): Promise<Escrow[]> {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return this.escrows.filter(escrow => 
      escrow.buyerId === userId || escrow.sellerId === userId
    ).map(escrow => {
      const dispute = this.disputes.find(d => d.escrowId === escrow.id);
      return {
        ...escrow,
        dispute: dispute
      };
    });
  }

  async getEscrowTransaction(escrowId: string): Promise<Escrow | undefined> {
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const escrow = this.escrows.find(escrow => escrow.id === escrowId);
    if (escrow) {
      const dispute = this.disputes.find(d => d.escrowId === escrow.id);
      return {
        ...escrow,
        dispute: dispute
      };
    }
    return undefined;
  }

  async createDispute(escrowId: string, reason: string, details: string, sender: DisputeSender): Promise<Dispute> {
    await new Promise(resolve => setTimeout(resolve, 400));

    const newDispute: Dispute = {
      id: uuidv4(),
      escrowId: escrowId,
      reason: reason,
      details: details,
      sender: sender,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.disputes.push(newDispute);
    return newDispute;
  }

  async getDispute(disputeId: string): Promise<Dispute | undefined> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return this.disputes.find(dispute => dispute.id === disputeId);
  }

  async resolveDispute(disputeId: string): Promise<Dispute | undefined> {
    await new Promise(resolve => setTimeout(resolve, 400));

    const disputeIndex = this.disputes.findIndex(dispute => dispute.id === disputeId);
    if (disputeIndex === -1) {
      return undefined;
    }

    this.disputes[disputeIndex] = {
      ...this.disputes[disputeIndex],
      status: 'resolved',
      updatedAt: new Date().toISOString(),
    };

    return this.disputes[disputeIndex];
  }
}

const escrowEcommerceService = new EscrowEcommerceService();
export default escrowEcommerceService;
