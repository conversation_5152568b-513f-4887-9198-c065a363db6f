
import * as React from 'react';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Shield, Eye, EyeOff, Lock, User, AlertTriangle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';

const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const AdminLogin: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();
  const logoUrl = "/lovable-uploads/1928aa40-2de0-48fc-ba20-8312b8e3f05b.png";

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    
    try {
      // Demo admin login - in a real app, this would validate against the backend
      if (data.username === 'admin' && data.password === 'admin123') {
        // Simulate login success
        toast.success('Login successful!');
        
        // Simulate short backend delay
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // Navigate to admin dashboard
        navigate('/admin/dashboard');
      } else {
        toast.error('Invalid username or password');
      }
    } catch (error) {
      toast.error('Login failed. Please try again.');
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Admin Login | KojaPay</title>
        <meta name="description" content="Login to the KojaPay admin portal" />
        <meta name="theme-color" content="#1231B8" />
      </Helmet>
      
      <div className="min-h-screen bg-gradient-to-b from-[#09125a] to-[#1231B8] flex items-center justify-center p-4">
        <div className="absolute top-4 left-4 flex items-center gap-2">
          <img 
            src={logoUrl}
            alt="KojaPay Admin"
            className="h-10 w-10"
          />
          <span className="text-white text-lg font-bold">KojaPay Admin</span>
        </div>
        
        <Card className="w-full max-w-md bg-white/10 backdrop-blur-xl border border-white/20 text-white">
          <CardHeader className="space-y-1 text-center">
            <div className="flex justify-center mb-2">
              <img 
                src={logoUrl}
                alt="KojaPay Logo"
                className="h-24 w-24 mb-2"
              />
            </div>
            <CardTitle className="text-2xl font-bold">Admin Portal</CardTitle>
            <CardDescription className="text-white/70">
              Log in to access the admin dashboard
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Username</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <User className="absolute left-3 top-3 h-4 w-4 text-white/60" />
                          <Input
                            {...field}
                            placeholder="admin"
                            className="pl-10 bg-white/5 border-white/10 text-white placeholder:text-white/40"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-white">Password</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Lock className="absolute left-3 top-3 h-4 w-4 text-white/60" />
                          <Input
                            {...field}
                            type={showPassword ? "text" : "password"}
                            placeholder="••••••••"
                            className="pl-10 pr-10 bg-white/5 border-white/10 text-white placeholder:text-white/40"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-2 top-2 h-6 w-6 text-white/60 hover:text-white hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="pt-2">
                  <div className="flex items-center space-x-2 text-sm mb-4">
                    <AlertTriangle className="h-4 w-4 text-yellow-400" />
                    <span className="text-white/80">
                      Demo credentials: admin / admin123
                    </span>
                  </div>
                  
                  <Button 
                    type="submit" 
                    className="w-full bg-[#FDE314] text-[#09125a] hover:bg-[#FDE314]/90"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Logging in...' : 'Log In'}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
          
          <CardFooter className="flex justify-center">
            <p className="text-sm text-white/60">
              Protected area. Authorized personnel only.
            </p>
          </CardFooter>
        </Card>
      </div>
    </>
  );
};

export default AdminLogin;
