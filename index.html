
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/png" href="/lovable-uploads/057fa150-97d3-41c5-a696-d0ef1698883f.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no" />
    <meta name="theme-color" content="#1231b8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="KojaPay Admin" />
    <meta name="description" content="KojaPay Admin Portal - Manage financial services, users, and transactions" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="mobile-web-app-capable" content="yes" />
    
    <link rel="apple-touch-icon" href="/lovable-uploads/057fa150-97d3-41c5-a696-d0ef1698883f.png" />
    <link rel="manifest" href="/manifest.json" />
    
    <title>KojaPay </title>
    
    <!-- Preconnect to important domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Unica+One&display=swap" rel="stylesheet">
    
    <!-- Structured data for Organization -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "KojaPay",
        "url": "https://kojapay.io",
        "logo": "https://kojapay.io/lovable-uploads/057fa150-97d3-41c5-a696-d0ef1698883f.png",
        "sameAs": [
          "https://twitter.com/kojapay",
          "https://facebook.com/kojapay",
          "https://linkedin.com/company/kojapay"
        ],
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "+234-800-KOJAPAY",
          "contactType": "customer service",
          "availableLanguage": ["English"]
        }
      }
    </script>
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://kojapay.io/">
    <meta property="og:title" content="KojaPay ">
    <meta property="og:description" content="KojaPay - Manage financial services, users, and transactions">
    <meta property="og:image" content="https://kojapay.com/lovable-uploads/057fa150-97d3-41c5-a696-d0ef1698883f.png">

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://kojapay.io/">
    <meta property="twitter:title" content="KojaPay">
    <meta property="twitter:description" content="KojaPay - Manage financial services, users, and transactions">
    <meta property="twitter:image" content="https://kojapay.io/lovable-uploads/057fa150-97d3-41c5-a696-d0ef1698883f.png">
  </head>
  <body>
    <div id="root"></div>
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
              console.log('SW registered: ', registration);
            })
            .catch(error => {
              console.log('SW registration failed: ', error);
            });
        });
      }
      
      // Add to homescreen prompt handling
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        // Prevent Chrome 67 and earlier from automatically showing the prompt
        e.preventDefault();
        // Stash the event so it can be triggered later
        deferredPrompt = e;
      });
    </script>
  </body>
</html>
