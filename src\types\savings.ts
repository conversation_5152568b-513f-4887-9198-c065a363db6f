// Savings-related types for the new GoFundMe and Group Savings features

export type SavingsPlanStatus = 'ACTIVE' | 'COMPLETED' | 'PAUSED' | 'CANCELLED';

export interface PersonalSavingsPlan {
  id: string;
  title: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  targetDate?: Date;
  isPublic: boolean;
  allowContributions: boolean;
  status: SavingsPlanStatus;
  creatorId: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  creator?: User;
  contributions?: SavingsContribution[];
}

export interface GroupSavings {
  id: string;
  name: string;
  description?: string;
  targetAmount: number;
  targetDate?: Date;
  creatorId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations
  creator?: User;
  members?: GroupSavingsMember[];
}

export interface GroupSavingsMember {
  id: string;
  groupId: string;
  userId: string;
  individualTarget: number;
  currentAmount: number;
  joinedAt: Date;
  isActive: boolean;
  
  // Relations
  group?: GroupSavings;
  user?: User;
}

export interface SavingsContribution {
  id: string;
  planId: string;
  contributorId: string;
  amount: number;
  message?: string;
  isAnonymous: boolean;
  createdAt: Date;
  
  // Relations
  plan?: PersonalSavingsPlan;
  contributor?: User;
}

// User interface for savings context
export interface User {
  id: string;
  fullName: string;
  email: string;
  profilePicture?: string;
}

// API Request/Response types
export interface CreatePersonalSavingsPlanRequest {
  title: string;
  description?: string;
  targetAmount: number;
  targetDate?: Date;
  isPublic?: boolean;
  allowContributions?: boolean;
}

export interface CreateGroupSavingsRequest {
  name: string;
  description?: string;
  targetAmount: number;
  targetDate?: Date;
}

export interface JoinGroupSavingsRequest {
  groupId: string;
  individualTarget: number;
}

export interface ContributeToSavingsRequest {
  planId: string;
  amount: number;
  message?: string;
  isAnonymous?: boolean;
}

export interface UpdateSavingsPlanRequest {
  title?: string;
  description?: string;
  targetAmount?: number;
  targetDate?: Date;
  status?: SavingsPlanStatus;
}

// Account tier limits
export interface TierLimits {
  maxDeposit: number;
  maxWithdrawal: number;
  dailyTransactionLimit?: number;
  monthlyTransactionLimit?: number;
}

export const TIER_LIMITS: Record<'tier1' | 'tier2', TierLimits> = {
  tier1: {
    maxDeposit: 100000, // ₦100,000
    maxWithdrawal: 100000, // ₦100,000
    dailyTransactionLimit: 100000,
    monthlyTransactionLimit: 1000000
  },
  tier2: {
    maxDeposit: Infinity, // Unlimited
    maxWithdrawal: 5000000, // ₦5,000,000
    dailyTransactionLimit: 5000000,
    monthlyTransactionLimit: ********
  }
};

// Dashboard summary types
export interface SavingsSummary {
  totalPersonalSavings: number;
  totalGroupSavings: number;
  totalContributions: number;
  activePlansCount: number;
  completedPlansCount: number;
}

export interface SavingsActivity {
  id: string;
  type: 'contribution' | 'withdrawal' | 'plan_created' | 'plan_completed';
  description: string;
  amount?: number;
  createdAt: Date;
  relatedPlan?: {
    id: string;
    title: string;
  };
}
