
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Wallet } from "lucide-react";

const LoanEligibilityCard = () => {
  return (
    <Card className="bg-gradient-to-br from-purple-50 to-blue-50 border-purple-100">
      <CardContent className="p-4">
        <div className="flex justify-between items-center mb-4">
          <div>
            <p className="text-sm text-gray-600">Eligible Amount</p>
            <p className="text-xl sm:text-2xl font-bold text-gray-900">Up to ₦500,000</p>
          </div>
          <div className="bg-white p-2 rounded-full">
            <Wallet className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
          </div>
        </div>
        
        <div className="grid grid-cols-3 gap-2 text-center">
          <div className="bg-white/60 p-2 rounded-lg">
            <p className="text-xs text-gray-600">Interest Rate</p>
            <p className="text-sm font-medium text-gray-900">15% p.a.</p>
          </div>
          <div className="bg-white/60 p-2 rounded-lg">
            <p className="text-xs text-gray-600">Processing Fee</p>
            <p className="text-sm font-medium text-gray-900">1.5%</p>
          </div>
          <div className="bg-white/60 p-2 rounded-lg">
            <p className="text-xs text-gray-600">Disbursement</p>
            <p className="text-sm font-medium text-green-600">Instant</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoanEligibilityCard;
