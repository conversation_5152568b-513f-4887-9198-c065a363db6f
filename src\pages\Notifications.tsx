
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Bell, CheckCircle, AlertTriangle, Info, Clock, CreditCard, DollarSign, ShoppingCart, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

interface Notification {
  id: string;
  type: 'transaction' | 'security' | 'system';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
  icon: React.ReactNode;
}

const Notifications = () => {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'transaction',
      title: 'Payment Successful',
      message: 'Your payment of ₦15,000 to Shoprite has been processed successfully.',
      timestamp: '10 minutes ago',
      read: false,
      priority: 'low',
      icon: <CheckCircle className="h-5 w-5 text-green-500" />
    },
    {
      id: '2',
      type: 'security',
      title: 'New Login Detected',
      message: 'A new login was detected from Lagos, Nigeria. Was this you?',
      timestamp: '2 hours ago',
      read: false,
      priority: 'high',
      icon: <AlertTriangle className="h-5 w-5 text-red-500" />
    },
    {
      id: '3',
      type: 'system',
      title: 'System Maintenance',
      message: 'KojaPay will be undergoing scheduled maintenance on Friday, 7pm - 9pm WAT.',
      timestamp: '1 day ago',
      read: true,
      priority: 'medium',
      icon: <Info className="h-5 w-5 text-blue-500" />
    },
    {
      id: '4',
      type: 'transaction',
      title: 'Bill Payment Reminder',
      message: 'Your DSTV subscription will expire in 3 days. Tap to renew now.',
      timestamp: '6 hours ago',
      read: false,
      priority: 'medium',
      icon: <Clock className="h-5 w-5 text-amber-500" />
    },
    {
      id: '5',
      type: 'transaction',
      title: 'Card Transaction',
      message: 'Your card was charged ₦5,000 at ShopRite Lekki.',
      timestamp: '1 day ago',
      read: true,
      priority: 'low',
      icon: <CreditCard className="h-5 w-5 text-purple-500" />
    }
  ]);

  // Filter notifications by type
  const transactionNotifications = notifications.filter(notif => notif.type === 'transaction');
  const securityNotifications = notifications.filter(notif => notif.type === 'security');
  const systemNotifications = notifications.filter(notif => notif.type === 'system');
  
  // Count unread notifications
  const unreadCount = notifications.filter(notif => !notif.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notif => ({ ...notif, read: true }))
    );
    toast({
      title: "All notifications marked as read",
      description: "You have no new notifications",
    });
  };

  const deleteNotification = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.filter(notif => notif.id !== id)
    );
    toast({
      title: "Notification deleted",
      description: "The notification has been removed",
    });
  };

  const getNotificationCard = (notification: Notification) => (
    <Card 
      key={notification.id} 
      className={`mb-4 transition-all duration-300 hover:shadow-md ${!notification.read ? 'border-l-4 border-kojaPrimary' : ''}`}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="mt-1">
            {notification.icon}
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold text-kojaDark">{notification.title}</h3>
                <p className="text-sm text-kojaGray mt-1">{notification.message}</p>
                <p className="text-xs text-kojaGray mt-2">{notification.timestamp}</p>
              </div>
              <div className="flex items-center gap-2">
                {!notification.read && (
                  <Badge variant="outline" className="bg-kojaPrimary/10 text-kojaPrimary border-kojaPrimary/20">
                    New
                  </Badge>
                )}
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7 rounded-full hover:bg-red-100 hover:text-red-500"
                  onClick={() => deleteNotification(notification.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {!notification.read && (
              <Button 
                variant="link" 
                className="text-sm p-0 h-auto mt-2 text-kojaPrimary"
                onClick={() => markAsRead(notification.id)}
              >
                Mark as read
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <DashboardLayout pageTitle="Notifications">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-semibold text-kojaDark flex items-center gap-2">
              <Bell className="h-6 w-6 text-kojaPrimary" />
              Notifications
              {unreadCount > 0 && (
                <Badge className="ml-2 bg-kojaPrimary">
                  {unreadCount} new
                </Badge>
              )}
            </h1>
            <p className="text-kojaGray">Stay updated with your account activities</p>
          </div>
          
          <Button 
            variant="outline" 
            className="text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5"
            onClick={markAllAsRead}
          >
            Mark all as read
          </Button>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-6 grid grid-cols-4 bg-muted/50">
            <TabsTrigger value="all">
              All
              {unreadCount > 0 && <Badge className="ml-2 bg-kojaPrimary">{unreadCount}</Badge>}
            </TabsTrigger>
            <TabsTrigger value="transactions">
              Transactions
              {transactionNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-green-500">
                  {transactionNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="security">
              Security
              {securityNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-red-500">
                  {securityNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="system">
              System
              {systemNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-blue-500">
                  {systemNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="all">
            {notifications.length > 0 ? (
              <div>
                {notifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Bell className="h-12 w-12 text-kojaPrimary/30 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No notifications</h3>
                <p className="text-kojaGray">You don't have any notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="transactions">
            {transactionNotifications.length > 0 ? (
              <div>
                {transactionNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12">
                <DollarSign className="h-12 w-12 text-green-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No transaction notifications</h3>
                <p className="text-kojaGray">You don't have any transaction notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="security">
            {securityNotifications.length > 0 ? (
              <div>
                {securityNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12">
                <AlertTriangle className="h-12 w-12 text-red-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No security notifications</h3>
                <p className="text-kojaGray">You don't have any security notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="system">
            {systemNotifications.length > 0 ? (
              <div>
                {systemNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Info className="h-12 w-12 text-blue-300 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No system notifications</h3>
                <p className="text-kojaGray">You don't have any system notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Notifications;
