
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Building,
  Plus,
  MapPin,
  Users,
  Phone,
  Mail,
  Edit,
  Trash2,
  Search,
  Clock,
  Eye,
  ChevronDown,
} from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Sample branch data
const branchData = [
  {
    id: 1,
    name: 'Lagos Headquarters', 
    address: '123 Marina Street, Lagos Island', 
    phone: '+************', 
    email: '<EMAIL>',
    staff: 12,
    manager: '<PERSON>',
    hours: '8:00 AM - 5:00 PM'
  },
  {
    id: 2,
    name: '<PERSON>ja Branch', 
    address: '45 Wuse Zone 2, Abuja', 
    phone: '+************', 
    email: '<EMAIL>',
    staff: 8,
    manager: '<PERSON> Smith',
    hours: '8:00 AM - 5:00 PM'
  },
  {
    id: 3,
    name: 'Port Harcourt Branch', 
    address: '78 Aba Road, Port Harcourt', 
    phone: '+************', 
    email: '<EMAIL>',
    staff: 6,
    manager: 'Mark Johnson',
    hours: '8:00 AM - 5:00 PM'
  },
];

const BusinessBranches = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBranch, setSelectedBranch] = useState<typeof branchData[0] | null>(null);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const { toast } = useToast();

  // Filter branches based on search term
  const filteredBranches = branchData.filter(branch => 
    branch.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    branch.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handlers
  const handleViewDetails = (branch: typeof branchData[0]) => {
    setSelectedBranch(branch);
    setViewDialogOpen(true);
  };

  const handleEditBranch = (branch: typeof branchData[0]) => {
    // This would open edit dialog in a real application
    toast({
      title: "Edit Branch",
      description: `Editing ${branch.name}. This feature is coming soon.`,
    });
  };

  const handleDeleteBranch = (branch: typeof branchData[0]) => {
    // This would confirm deletion in a real application
    toast({
      title: "Delete Branch",
      description: `${branch.name} would be deleted. This feature is coming soon.`,
      variant: "destructive",
    });
  };

  return (
    <BusinessLayout pageTitle="Branches Management">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input 
                placeholder="Search branches..." 
                className="pl-9" 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add New Branch
            </Button>
          </div>
          
          {/* Desktop Table View */}
          <Card className="hidden md:block backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardContent className="p-6">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Branch</TableHead>
                    <TableHead>Manager</TableHead>
                    <TableHead>Staff</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredBranches.map((branch) => (
                    <TableRow key={branch.id}>
                      <TableCell>
                        <div className="font-medium">{branch.name}</div>
                        <div className="text-sm text-muted-foreground flex items-center">
                          <MapPin className="h-3 w-3 mr-1" /> {branch.address}
                        </div>
                      </TableCell>
                      <TableCell>{branch.manager}</TableCell>
                      <TableCell>{branch.staff} employees</TableCell>
                      <TableCell>
                        <div className="text-sm">{branch.phone}</div>
                        <div className="text-sm">{branch.email}</div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleViewDetails(branch)}>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleEditBranch(branch)}>
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-500" onClick={() => handleDeleteBranch(branch)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
          
          {/* Mobile Card View */}
          <div className="grid grid-cols-1 gap-4 md:hidden">
            {filteredBranches.map((branch) => (
              <Card key={branch.id} className="backdrop-blur-sm bg-white/80 shadow-sm border border-gray-100">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-base">{branch.name}</CardTitle>
                      <CardDescription className="flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3" /> {branch.address}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="space-y-1">
                      <div className="text-sm text-gray-500">Branch Manager</div>
                      <div className="font-medium">{branch.manager}</div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm text-gray-500 flex items-center gap-1">
                        <Users className="h-3 w-3" /> Staff Count
                      </div>
                      <div className="font-medium">{branch.staff} employees</div>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    <Button variant="outline" size="sm" className="flex-1" onClick={() => handleViewDetails(branch)}>
                      <Eye className="h-4 w-4 mr-1" /> View
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1" onClick={() => handleEditBranch(branch)}>
                      <Edit className="h-4 w-4 mr-1" /> Edit
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 text-red-500" onClick={() => handleDeleteBranch(branch)}>
                      <Trash2 className="h-4 w-4 mr-1" /> Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
        
        <div className="space-y-6">
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>Branch Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Total Branches</span>
                  <span className="font-semibold">{branchData.length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Total Staff</span>
                  <span className="font-semibold">{branchData.reduce((sum, branch) => sum + branch.staff, 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Most Staffed</span>
                  <span className="font-semibold">
                    {branchData.reduce((prev, current) => (prev.staff > current.staff) ? prev : current).name} 
                    ({branchData.reduce((prev, current) => (prev.staff > current.staff) ? prev : current).staff})
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Least Staffed</span>
                  <span className="font-semibold">
                    {branchData.reduce((prev, current) => (prev.staff < current.staff) ? prev : current).name}
                    ({branchData.reduce((prev, current) => (prev.staff < current.staff) ? prev : current).staff})
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>Add New Branch</CardTitle>
              <CardDescription>Create a new branch location</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4">
                <div>
                  <label htmlFor="branch-name" className="block text-sm font-medium mb-1">Branch Name</label>
                  <Input id="branch-name" placeholder="Enter branch name" />
                </div>
                
                <div>
                  <label htmlFor="branch-address" className="block text-sm font-medium mb-1">Address</label>
                  <Input id="branch-address" placeholder="Enter branch address" />
                </div>
                
                <div>
                  <label htmlFor="branch-manager" className="block text-sm font-medium mb-1">Branch Manager</label>
                  <Input id="branch-manager" placeholder="Select branch manager" />
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label htmlFor="branch-phone" className="block text-sm font-medium mb-1">Phone</label>
                    <Input id="branch-phone" placeholder="Phone number" />
                  </div>
                  <div>
                    <label htmlFor="branch-email" className="block text-sm font-medium mb-1">Email</label>
                    <Input id="branch-email" placeholder="Email address" />
                  </div>
                </div>
                
                <Button type="submit" className="w-full">
                  Create Branch
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* View Branch Details Dialog */}
      <Dialog open={viewDialogOpen} onOpenChange={setViewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{selectedBranch?.name}</DialogTitle>
            <DialogDescription>Branch details and information</DialogDescription>
          </DialogHeader>
          
          {selectedBranch && (
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Address</div>
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4 text-muted-foreground" /> 
                  {selectedBranch.address}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Phone</div>
                  <div className="flex items-center gap-1">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    {selectedBranch.phone}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Email</div>
                  <div className="flex items-center gap-1">
                    <Mail className="h-4 w-4 text-muted-foreground" />
                    {selectedBranch.email}
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Branch Manager</div>
                  <div className="font-medium">{selectedBranch.manager}</div>
                </div>
                
                <div className="space-y-2">
                  <div className="text-sm text-muted-foreground">Staff Count</div>
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    {selectedBranch.staff} employees
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">Working Hours</div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  {selectedBranch.hours}
                </div>
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  variant="outline"
                  onClick={() => handleEditBranch(selectedBranch)}
                >
                  <Edit className="h-4 w-4 mr-2" /> Edit Branch
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </BusinessLayout>
  );
};

export default BusinessBranches;
