import * as React from 'react';
import { HelmetProvider } from 'react-helmet-async';
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from './contexts/AuthContext';
import { CartProvider } from './contexts/CartContext';
import Index from "./pages/Index";
import CreateAccount from "./pages/CreateAccount";
import UnifiedLogin from "./pages/UnifiedLogin";
import PersonalLogin from "./pages/PersonalLogin";
import BusinessLogin from "./pages/BusinessLogin";
import Dashboard from "./pages/Dashboard";
import BusinessDashboard from "./pages/BusinessDashboard";
import Wallet from "./pages/Wallet";
import Transactions from "./pages/Transactions";
import Profile from "./pages/Profile";
import Settings from "./pages/Settings";
import Security from "./pages/Security";
import NotFound from "./pages/NotFound";
import PrivacyPolicy from "./pages/legal/PrivacyPolicy";
import TermsOfService from "./pages/legal/TermsOfService";
import SafetyTips from "./pages/SafetyTips";
import AdminLogin from "./pages/AdminLogin";
import AdminDashboard from './pages/admin/AdminDashboard';
import UserManagement from './pages/admin/UserManagement';
import ProductCategoryManagement from './pages/admin/ProductCategoryManagement';
import TransactionManagement from './pages/admin/TransactionManagement';

const queryClient = new QueryClient();

const App = () => {
  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <CartProvider>
            <TooltipProvider>
              <Toaster />
              <BrowserRouter>
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/create-account" element={<CreateAccount />} />
                  <Route path="/unified-login" element={<UnifiedLogin />} />
                  <Route path="/personal-login" element={<PersonalLogin />} />
                  <Route path="/business-login" element={<BusinessLogin />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/business-dashboard" element={<BusinessDashboard />} />
                  <Route path="/wallet" element={<Wallet />} />
                  <Route path="/transactions" element={<Transactions />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/security" element={<Security />} />
                  <Route path="/privacy" element={<PrivacyPolicy />} />
                  <Route path="/terms" element={<TermsOfService />} />
                  <Route path="/safety-tips" element={<SafetyTips />} />
                  <Route path="/admin-login" element={<AdminLogin />} />
                  <Route path="/admin/dashboard" element={<AdminDashboard />} />
                  <Route path="/admin/users" element={<UserManagement />} />
                  <Route path="/admin/products" element={<ProductCategoryManagement />} />
                  <Route path="/admin/orders" element={<TransactionManagement />} />
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </BrowserRouter>
            </TooltipProvider>
          </CartProvider>
        </AuthProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
