
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Calendar, 
  Download, 
  Filter, 
  Search,
  Printer
} from 'lucide-react';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import TransactionTable, { Transaction } from '@/components/TransactionTable';
import { useAuth } from '@/contexts/AuthContext';

const Transactions = () => {
  const [activeTab, setActiveTab] = useState("all");
  const { accountType } = useAuth();
  
  const transactions: Transaction[] = [
    {
      id: '1',
      recipient: 'Netflix Subscription',
      date: '2022-01-12',
      amount: 1200,
      status: 'pending',
      initials: 'NF',
      image: 'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/227_Netflix_logo-512.png',
      type: 'outgoing',
      description: 'Monthly subscription payment'
    },
    {
      id: '2',
      recipient: 'Figma Subscription',
      date: '2022-01-12',
      amount: 1200,
      status: 'success',
      initials: 'FG',
      image: 'https://cdn.icon-icons.com/icons2/2699/PNG/512/figma_logo_icon_170157.png',
      type: 'outgoing',
      description: 'Monthly subscription payment'
    },
    {
      id: '3',
      recipient: 'Sent to Alex',
      date: '2022-01-12',
      amount: 1200,
      status: 'success',
      initials: 'AL',
      type: 'outgoing',
      description: 'Transfer to friend'
    },
    {
      id: '4',
      recipient: 'Received from John',
      date: '2022-01-10',
      amount: 5000,
      status: 'success',
      initials: 'JD',
      type: 'incoming',
      description: 'Payment for services'
    },
    {
      id: '5',
      recipient: 'Amazon Purchase',
      date: '2022-01-09',
      amount: 3500,
      status: 'success',
      initials: 'AM',
      image: 'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/1_Amazon_logo-512.png',
      type: 'outgoing',
      description: 'Online shopping'
    },
    {
      id: '6',
      recipient: 'Received from Sarah',
      date: '2022-01-08',
      amount: 2800,
      status: 'success',
      initials: 'SM',
      type: 'incoming',
      description: 'Debt repayment'
    },
    {
      id: '7',
      recipient: 'Uber Ride',
      date: '2022-01-07',
      amount: 800,
      status: 'success',
      initials: 'UB',
      image: 'https://cdn4.iconfinder.com/data/icons/logos-and-brands/512/363_Uber_logo-512.png',
      type: 'outgoing',
      description: 'Transportation'
    }
  ];

  const filteredTransactions = activeTab === "all" 
    ? transactions 
    : transactions.filter(t => t.type === activeTab);

  return (
    <DashboardLayout 
      pageTitle="Transactions"
    >
      <div className="mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-semibold text-kojaDark">Transaction History</h1>
          <p className="text-kojaGray mt-1">View and manage your transaction history</p>
        </div>
        
        <Button variant="outline" className="flex items-center gap-2 rounded-[20px]">
          <Download size={16} />
          <span>Export</span>
        </Button>
      </div>
      
      <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
        <CardHeader className="pb-2">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full max-w-xs">
              <TabsList className="grid grid-cols-3 w-full rounded-[20px]">
                <TabsTrigger value="all" className="rounded-[20px] data-[state=active]:bg-[#fde314] data-[state=active]:text-gray-900">All</TabsTrigger>
                <TabsTrigger value="incoming" className="rounded-[20px] data-[state=active]:bg-[#fde314] data-[state=active]:text-gray-900">Income</TabsTrigger>
                <TabsTrigger value="outgoing" className="rounded-[20px] data-[state=active]:bg-[#fde314] data-[state=active]:text-gray-900">Expenses</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex flex-wrap items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-kojaGray" />
                <Input 
                  placeholder="Search transactions..." 
                  className="pl-9 w-full sm:w-64 rounded-[20px] border-[#fde314]/30 focus:border-[#fde314]"
                />
              </div>
              
              <Button variant="outline" size="icon" className="rounded-[20px]">
                <Filter size={16} />
              </Button>
              
              <Button variant="outline" size="icon" className="rounded-[20px]">
                <Calendar size={16} />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          <TransactionTable 
            transactions={filteredTransactions} 
            requireOTP={true}
          />
          
          <div className="flex justify-center mt-6">
            <Button variant="outline" className="mx-1 px-3 py-1 text-sm rounded-[20px]">1</Button>
            <Button variant="outline" className="mx-1 px-3 py-1 text-sm rounded-[20px]">2</Button>
            <Button variant="outline" className="mx-1 px-3 py-1 text-sm rounded-[20px]">3</Button>
            <Button variant="ghost" className="mx-1 px-3 py-1 text-sm">...</Button>
            <Button variant="outline" className="mx-1 px-3 py-1 text-sm rounded-[20px]">10</Button>
          </div>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default Transactions;
