
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Bell, ChevronDown, Menu, X, User, CreditCard, FileText, Settings, ShoppingBag } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { NotificationBadge } from "@/components/ui/notification-badge";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { useAuth } from '@/contexts/AuthContext';

const navItems = [
  { path: '/dashboard', name: 'Dashboard', icon: <User size={16} /> },
  { path: '/transactions', name: 'Transactions', icon: <FileText size={16} /> },
  { path: '/cards', name: 'Cards', icon: <CreditCard size={16} /> },
  { path: '/payments', name: 'Payments', icon: <ShoppingBag size={16} /> },
  { path: '/settings', name: 'Settings', icon: <Settings size={16} /> },
];

const AppHeader = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  useEffect(() => {
    const handleScroll = () => setIsScrolled(window.scrollY > 10);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  return (
    <header className={`w-full bg-white/95 backdrop-blur-md border-b py-2 px-4 md:px-6 sticky top-0 z-50 transition-all ${isScrolled ? 'shadow-md' : 'shadow-sm'}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/" className="mr-6">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-kojaPrimary text-white font-bold text-lg flex items-center justify-center">K</div>
              <span className="font-bold text-lg text-kojaDark">Koja<span className="text-kojaPrimary">Pay</span></span>
            </div>
          </Link>
          
          <nav className="hidden md:flex space-x-4">
            {navItems.map((item) => (
              <Link 
                key={item.path}
                to={item.path} 
                className={`text-kojaDark hover:text-kojaPrimary transition-colors py-1 flex items-center gap-1.5 text-sm`}
              >
                {item.icon}
                {item.name}
              </Link>
            ))}
          </nav>
        </div>
        
        <div className="hidden md:flex items-center space-x-3">
          <Button variant="ghost" size="icon" className="relative">
            <Bell className="h-5 w-5" />
            <NotificationBadge variant="destructive" size="sm" className="absolute -top-0.5 -right-0.5">3</NotificationBadge>
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 cursor-pointer p-1 rounded-full hover:bg-gray-100 transition-colors">
                <Avatar className="h-8 w-8 border-2 border-transparent">
                  <AvatarImage src="https://github.com/shadcn.png" alt="User" />
                  <AvatarFallback className="bg-kojaPrimary text-white">KP</AvatarFallback>
                </Avatar>
                <ChevronDown className="h-4 w-4 text-kojaGray" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/profile')}>Profile</DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/settings')}>Settings</DropdownMenuItem>
              <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/support')}>Support</DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-500 cursor-pointer">Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        
        <Button 
          variant="ghost" 
          size="icon" 
          className="md:hidden" 
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </Button>
      </div>

      {isMobileMenuOpen && (
        <div className="md:hidden mt-3 py-3 border-t">
          <nav className="flex flex-col space-y-2">
            {navItems.map((item) => (
              <Link 
                key={item.path}
                to={item.path} 
                className={`text-kojaDark hover:text-kojaPrimary transition-colors py-2 px-3 rounded-lg text-sm flex items-center gap-2 ${location.pathname === item.path ? 'bg-kojaPrimary/10 text-kojaPrimary' : ''}`}
              >
                {item.icon}
                {item.name}
              </Link>
            ))}
            
            <div className="pt-2 border-t border-gray-100 mt-2">
              <div className="flex items-center justify-between px-3">
                <div className="flex items-center gap-2">
                  <Avatar className="h-7 w-7">
                    <AvatarImage src="https://github.com/shadcn.png" alt="User" />
                    <AvatarFallback className="bg-kojaPrimary text-white text-xs">KP</AvatarFallback>
                  </Avatar>
                  <p className="text-sm font-medium text-kojaDark">{user?.fullName || 'User'}</p>
                </div>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="relative h-8 w-8" 
                  onClick={() => navigate('/notifications')}
                >
                  <Bell className="h-4 w-4" />
                  <NotificationBadge variant="destructive" size="sm" className="absolute -top-0.5 -right-0.5">3</NotificationBadge>
                </Button>
              </div>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
};

export default AppHeader;
