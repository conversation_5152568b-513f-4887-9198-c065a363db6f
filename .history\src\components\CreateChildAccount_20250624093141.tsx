import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON>, Copy, Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { CreateChildDTO, ChildCredentials } from '@/types/kids';
import { registerChildAccount } from '@/services/userApi';

interface CreateChildAccountProps {
  isOpen: boolean;
  onClose: () => void;
  onChildCreated: () => void;
}

const CreateChildAccount: React.FC<CreateChildAccountProps> = ({ isOpen, onClose, onChildCreated }) => {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<CreateChildDTO>({
    firstName: '',
    lastName: '',
    dob: ''
  });
  const [credentials, setCredentials] = useState<ChildCredentials | null>(null);
  const [showCredentials, setShowCredentials] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const generateUsername = (firstName: string, lastName: string): string => {
    const firstInitial = firstName.charAt(0).toLowerCase();
    const lastNamePart = lastName.substring(0, 3).toLowerCase();
    const randomPart = Math.random().toString(36).substring(2, 4);
    return `${firstInitial}${lastNamePart}${randomPart}`;
  };

  const generateSecretKey = (): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.firstName || !formData.lastName || !formData.dob) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    // Check if child is not too old (under 18)
    const birthDate = new Date(formData.dob);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (age >= 18) {
      toast({
        title: 'Age Restriction',
        description: 'Child accounts are only available for children under 18 years old',
        variant: 'destructive'
      });
      return;
    }

    setIsLoading(true);
    try {
      // Use backend API
      const token = localStorage.getItem('token');
      if (!token) {
        toast({
          title: 'Not Authenticated',
          description: 'You must be logged in as a parent to create a child account.',
          variant: 'destructive'
        });
        setIsLoading(false);
        return;
      }
      const result = await registerChildAccount(token, formData);
      setCredentials({
        username: result.user.username,
        secretKey: result.password
      });
      setStep(2);
      toast({
        title: 'Child Account Created',
        description: 'Please save the credentials shown below',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create child account. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied',
      description: `${type} copied to clipboard`,
    });
  };

  const handleClose = () => {
    setStep(1);
    setFormData({ firstName: '', lastName: '', dob: '' });
    setCredentials(null);
    setShowCredentials(false);
    onClose();
  };

  const handleDone = () => {
    onChildCreated();
    handleClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center text-[#1231B8]">
            <Baby className="h-6 w-6 mr-2" />
            Create Child Account
          </DialogTitle>
        </DialogHeader>

        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Child Information</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="rounded-[40px]"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="rounded-[40px]"
                    required
                  />
                </div>
                
                <div>
                  <Label htmlFor="dob">Date of Birth</Label>
                  <Input
                    id="dob"
                    name="dob"
                    type="date"
                    value={formData.dob}
                    onChange={handleInputChange}
                    className="rounded-[40px]"
                    required
                  />
                </div>
                
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 rounded-[40px]"
                >
                  {isLoading ? 'Creating Account...' : 'Create Child Account'}
                </Button>
              </form>
            </CardContent>
          </Card>
        )}

        {step === 2 && credentials && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg text-center">Account Created!</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                  <span className="font-medium text-yellow-800">Important!</span>
                </div>
                <p className="text-sm text-yellow-700">
                  Copy and share this information with your child. This will not be shown again.
                </p>
              </div>

              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">Username</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={credentials.username}
                      readOnly
                      className="rounded-[40px] bg-gray-50"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(credentials.username, 'Username')}
                      className="rounded-[40px]"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Secret Key</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      value={showCredentials ? credentials.secretKey : '••••••••'}
                      readOnly
                      className="rounded-[40px] bg-gray-50"
                    />
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowCredentials(!showCredentials)}
                      className="rounded-[40px]"
                    >
                      {showCredentials ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(credentials.secretKey, 'Secret Key')}
                      className="rounded-[40px]"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleDone}
                className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 rounded-[40px]"
              >
                Done
              </Button>
            </CardContent>
          </Card>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default CreateChildAccount;
