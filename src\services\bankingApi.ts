
// Banking API service for handling financial operations
export interface BankAccount {
  id: string;
  accountNumber: string;
  accountName: string;
  balance: number;
  bankName: string;
  accountType: 'savings' | 'current';
}

export interface Transaction {
  id: string;
  amount: number;
  type: 'credit' | 'debit';
  description: string;
  date: string;
  reference: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface TransferRequest {
  amount: number;
  recipientAccount: string;
  recipientBank: string;
  narration: string;
  pin: string;
}

export class BankingApiService {
  private mockAccounts: BankAccount[] = [
    {
      id: '1',
      accountNumber: '**********',
      accountName: 'John Doe',
      balance: 150000,
      bankName: 'KojaPay Bank',
      accountType: 'savings'
    }
  ];

  private mockTransactions: Transaction[] = [
    {
      id: '1',
      amount: 5000,
      type: 'credit',
      description: 'Salary payment',
      date: new Date().toISOString(),
      reference: 'REF123456',
      status: 'completed'
    }
  ];

  async getAccounts(): Promise<BankAccount[]> {
    return [...this.mockAccounts];
  }

  async getTransactions(accountId: string): Promise<Transaction[]> {
    return [...this.mockTransactions];
  }

  async transfer(request: TransferRequest): Promise<Transaction> {
    const transaction: Transaction = {
      id: Math.random().toString(36).substr(2, 9),
      amount: request.amount,
      type: 'debit',
      description: request.narration,
      date: new Date().toISOString(),
      reference: Math.random().toString(36).substr(2, 9).toUpperCase(),
      status: 'completed'
    };
    
    this.mockTransactions.push(transaction);
    return transaction;
  }

  async validateAccount(accountNumber: string, bankCode: string): Promise<{ accountName: string; isValid: boolean }> {
    return {
      accountName: 'Sample Account Name',
      isValid: true
    };
  }
}

export const bankingApi = new BankingApiService();
