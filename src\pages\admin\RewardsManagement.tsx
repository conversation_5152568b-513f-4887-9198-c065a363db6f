import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash,
  Gift,
  Wallet,
  TrendingUp,
  CheckCircle,
  Clock,
  RefreshCcw,
  User,
  Coins
} from 'lucide-react';

const RewardsManagement = () => {
  const rewardTransactions = [
    { 
      id: 'RT001', 
      user: '<EMAIL>', 
      type: 'Purchase',
      points: 50,
      amount: '₦2,500',
      date: '2023-06-15 10:24:36',
      status: 'Completed'
    },
    { 
      id: 'RT002', 
      user: '<EMAIL>', 
      type: 'Referral',
      points: 100,
      amount: '₦5,000',
      date: '2023-06-15 14:12:45',
      status: 'Completed'
    },
    { 
      id: 'RT003', 
      user: '<EMAIL>', 
      type: 'Redemption',
      points: -250,
      amount: '₦12,500',
      date: '2023-06-15 16:35:22',
      status: 'Processing'
    },
    { 
      id: 'RT004', 
      user: '<EMAIL>', 
      type: 'Bonus',
      points: 75,
      amount: '₦3,750',
      date: '2023-06-15 18:47:09',
      status: 'Completed'
    },
    { 
      id: 'RT005', 
      user: '<EMAIL>', 
      type: 'Purchase',
      points: 30,
      amount: '₦1,500',
      date: '2023-06-15 20:22:58',
      status: 'Pending'
    },
  ];

  return (
    <AdminLayout pageTitle="Rewards Management">
      <Helmet>
        <title>Rewards Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Points Issued</CardTitle>
              <CardDescription>Reward points distributed</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">12,487</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Redemptions</CardTitle>
              <CardDescription>Points redeemed for cash</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">₦4.5M</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Users</CardTitle>
              <CardDescription>Earning rewards</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">285</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Pending Rewards</CardTitle>
              <CardDescription>Awaiting approval</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">12</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Rewards Management</CardTitle>
              <CardDescription>Manage reward points and transactions</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Add New Reward
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search transactions..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <Gift size={12} />
                  <span>Bonus</span>
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <Wallet size={12} />
                  <span>Redemption</span>
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <TrendingUp size={12} />
                  <span>Purchase</span>
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>Reward point transactions and redemptions</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transaction ID</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {rewardTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User size={14} className="text-purple-600" />
                          {transaction.user}
                        </div>
                      </TableCell>
                      <TableCell>
                        {transaction.type === 'Purchase' && 
                          <Badge variant="outline">
                            <TrendingUp size={12} className="mr-1" />
                            Purchase
                          </Badge>
                        }
                        {transaction.type === 'Referral' && 
                          <Badge variant="secondary">
                            <User size={12} className="mr-1" />
                            Referral
                          </Badge>
                        }
                        {transaction.type === 'Redemption' && 
                          <Badge variant="destructive">
                            <Wallet size={12} className="mr-1" />
                            Redemption
                          </Badge>
                        }
                        {transaction.type === 'Bonus' && 
                          <Badge variant="outline">
                            <Gift size={12} className="mr-1" />
                            Bonus
                          </Badge>
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Coins size={14} className="text-green-600" />
                          {transaction.points}
                        </div>
                      </TableCell>
                      <TableCell>{transaction.amount}</TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>
                        <Badge variant={
                          transaction.status === 'Completed' ? 'outline' : 
                          transaction.status === 'Pending' ? 'secondary' : 
                          'secondary'
                        }>
                          {transaction.status === 'Completed' && <CheckCircle size={12} className="mr-1" />}
                          {transaction.status === 'Pending' && <Clock size={12} className="mr-1" />}
                          {transaction.status === 'Processing' && <RefreshCcw size={12} className="mr-1" />}
                          {transaction.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Edit Transaction</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Remove Transaction</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default RewardsManagement;
