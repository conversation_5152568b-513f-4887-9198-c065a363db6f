import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash,
  Wallet,
  ArrowDown,
  ArrowUp,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react';

const BillPaymentManagement = () => {
  const transactions = [
    { 
      id: 'TXN001', 
      user: '<EMAIL>', 
      date: '2023-06-15 10:24:36',
      service: 'Electricity Bill',
      amount: '₦5,000',
      status: 'Completed'
    },
    { 
      id: 'TXN002', 
      user: '<EMAIL>', 
      date: '2023-06-15 11:35:22',
      service: 'Internet Subscription',
      amount: '₦10,000',
      status: 'Completed'
    },
    { 
      id: 'TXN003', 
      user: '<EMAIL>', 
      date: '2023-06-15 13:47:09',
      service: 'Cable TV',
      amount: '₦3,500',
      status: 'Pending'
    },
    { 
      id: 'TXN004', 
      user: '<EMAIL>', 
      date: '2023-06-15 15:22:58',
      service: 'Airtime Recharge',
      amount: '₦2,000',
      status: 'Completed'
    },
    { 
      id: 'TXN005', 
      user: '<EMAIL>', 
      date: '2023-06-15 17:58:45',
      service: 'Data Bundle',
      amount: '₦1,500',
      status: 'Failed'
    },
  ];

  return (
    <AdminLayout pageTitle="Bill Payment Management">
      <Helmet>
        <title>Bill Payment Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Transactions</CardTitle>
              <CardDescription>All bill payments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">245</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Successful Payments</CardTitle>
              <CardDescription>Completed transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">198</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Pending Payments</CardTitle>
              <CardDescription>Awaiting confirmation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">27</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Failed Payments</CardTitle>
              <CardDescription>Unsuccessful transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">20</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Bill Payment Transactions</CardTitle>
              <CardDescription>Manage and monitor bill payment transactions</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Export Data
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search transactions..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="cursor-pointer hover:bg-slate-100">
                  Electricity Bill
                </Badge>
                <Badge variant="outline" className="cursor-pointer hover:bg-slate-100">
                  Completed
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>Transaction history for bill payments</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Transaction ID</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">{transaction.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Wallet size={14} className="text-purple-600" />
                          {transaction.user}
                        </div>
                      </TableCell>
                      <TableCell>{transaction.date}</TableCell>
                      <TableCell>{transaction.service}</TableCell>
                      <TableCell>{transaction.amount}</TableCell>
                      <TableCell>
                        <Badge variant={
                          transaction.status === 'Completed' ? 'outline' : 
                          transaction.status === 'Failed' ? 'destructive' : 
                          'secondary'
                        }>
                          {transaction.status === 'Completed' && <CheckCircle size={12} className="mr-1 text-green-600" />}
                          {transaction.status === 'Failed' && <XCircle size={12} className="mr-1" />}
                          {transaction.status === 'Pending' && <Clock size={12} className="mr-1" />}
                          {transaction.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Edit Transaction</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Remove Transaction</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default BillPaymentManagement;
