
export interface Staff {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  salary: number;
  status: 'active' | 'inactive' | 'on_leave';
  joinDate: string;
  managerId?: string;
  name?: string;
  description?: string;
  dateHired: string;
  bankName?: string;
  accountNumber?: string;
  taxId?: string;
  address?: string;
  emergencyContact?: string;
  profileImage?: string;
}

export interface Department {
  id: string;
  name: string;
  description?: string;
  managerId?: string;
  createdAt: string;
}

export interface StaffFilters {
  search?: string;
  department?: string;
  status?: string;
  position?: string;
}

export interface StaffStatistics {
  totalStaff: number;
  activeStaff: number;
  inactiveStaff: number;
  onLeaveStaff: number;
  departmentCounts: { name: string; count: number }[];
}

export class StaffService {
  private mockStaffData: Staff[] = [
    {
      id: 'emp-001',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '+234-************',
      position: 'Software Engineer',
      department: 'Engineering',
      salary: 150000,
      status: 'active',
      joinDate: '2023-01-15T00:00:00Z',
      dateHired: '2023-01-15',
      bankName: 'First Bank',
      accountNumber: '**********',
      taxId: 'TAX001',
      address: '123 Main St, Lagos',
      emergencyContact: '+234-************'
    },
    {
      id: 'emp-002',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '+***********-6789',
      position: 'Product Manager',
      department: 'Product',
      salary: 180000,
      status: 'active',
      joinDate: '2023-03-20T00:00:00Z',
      dateHired: '2023-03-20',
      bankName: 'GTBank',
      accountNumber: '**********',
      taxId: 'TAX002',
      address: '456 Victoria Island, Lagos',
      emergencyContact: '+234-************'
    }
  ];

  private mockDepartments: Department[] = [
    {
      id: 'dept-001',
      name: 'Engineering',
      description: 'Software development and technical operations',
      managerId: 'emp-001',
      createdAt: '2023-01-01T00:00:00Z'
    },
    {
      id: 'dept-002',
      name: 'Product',
      description: 'Product management and strategy',
      managerId: 'emp-002',
      createdAt: '2023-01-01T00:00:00Z'
    }
  ];

  async getStaff(filters: StaffFilters = {}): Promise<Staff[]> {
    let filtered = [...this.mockStaffData];

    if (filters.search) {
      filtered = filtered.filter(staff => 
        `${staff.firstName} ${staff.lastName}`.toLowerCase().includes(filters.search!.toLowerCase()) ||
        staff.email.toLowerCase().includes(filters.search!.toLowerCase())
      );
    }

    if (filters.department) {
      filtered = filtered.filter(staff => staff.department === filters.department);
    }

    if (filters.status) {
      filtered = filtered.filter(staff => staff.status === filters.status);
    }

    if (filters.position) {
      filtered = filtered.filter(staff => staff.position === filters.position);
    }

    return filtered;
  }

  async getStaffById(id: string): Promise<Staff | null> {
    return this.mockStaffData.find(staff => staff.id === id) || null;
  }

  async createStaff(staffData: Omit<Staff, 'id'>): Promise<Staff> {
    const newStaff: Staff = {
      id: Math.random().toString(36).substr(2, 9),
      ...staffData
    };
    this.mockStaffData.push(newStaff);
    return newStaff;
  }

  async updateStaff(id: string, updates: Partial<Staff>): Promise<Staff> {
    const index = this.mockStaffData.findIndex(staff => staff.id === id);
    if (index === -1) throw new Error('Staff member not found');
    
    this.mockStaffData[index] = { ...this.mockStaffData[index], ...updates };
    return this.mockStaffData[index];
  }

  async deleteStaff(id: string): Promise<void> {
    const index = this.mockStaffData.findIndex(staff => staff.id === id);
    if (index === -1) throw new Error('Staff member not found');
    
    this.mockStaffData.splice(index, 1);
  }

  async getDepartments(): Promise<Department[]> {
    return [...this.mockDepartments];
  }

  async createDepartment(departmentData: Omit<Department, 'id' | 'createdAt'>): Promise<Department> {
    const newDepartment: Department = {
      id: Math.random().toString(36).substr(2, 9),
      ...departmentData,
      createdAt: new Date().toISOString()
    };
    this.mockDepartments.push(newDepartment);
    return newDepartment;
  }

  async updateDepartment(id: string, updates: Partial<Department>): Promise<Department> {
    const index = this.mockDepartments.findIndex(dept => dept.id === id);
    if (index === -1) throw new Error('Department not found');
    
    this.mockDepartments[index] = { ...this.mockDepartments[index], ...updates };
    return this.mockDepartments[index];
  }

  async deleteDepartment(id: string): Promise<void> {
    const index = this.mockDepartments.findIndex(dept => dept.id === id);
    if (index === -1) throw new Error('Department not found');
    
    this.mockDepartments.splice(index, 1);
  }

  async getStaffStatistics(): Promise<StaffStatistics> {
    const totalStaff = this.mockStaffData.length;
    const activeStaff = this.mockStaffData.filter(s => s.status === 'active').length;
    const inactiveStaff = this.mockStaffData.filter(s => s.status === 'inactive').length;
    const onLeaveStaff = this.mockStaffData.filter(s => s.status === 'on_leave').length;
    
    const departmentCounts = this.mockDepartments.map(dept => ({
      name: dept.name,
      count: this.mockStaffData.filter(staff => staff.department === dept.name).length
    }));

    return {
      totalStaff,
      activeStaff,
      inactiveStaff,
      onLeaveStaff,
      departmentCounts
    };
  }
}

export const staffService = new StaffService();
