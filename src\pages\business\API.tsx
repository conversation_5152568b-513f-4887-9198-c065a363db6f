
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Code, 
  Copy, 
  Key, 
  RefreshCw, 
  Eye, 
  EyeOff,
  Check,
  ExternalLink,
  Network,
  Webhook,
  Terminal,
  LucideIcon
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const BusinessAPI = () => {
  const { toast } = useToast();
  const [showApiKey, setShowApiKey] = useState(false);
  const [copied, setCopied] = useState(false);
  
  const dummyApiKey = "koj_test_api_key_01HQZGV9ABCDEFGHIJKLMNOPQ";
  
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(true);
      toast({
        title: "Copied to clipboard",
        description: "API key has been copied to your clipboard",
      });
      setTimeout(() => setCopied(false), 2000);
    });
  };
  
  const regenerateKey = () => {
    toast({
      title: "API Key Regenerated",
      description: "Your new API key has been generated",
    });
  };
  
  const ApiFeatureCard = ({ 
    icon: Icon, 
    title, 
    description 
  }: { 
    icon: LucideIcon; 
    title: string; 
    description: string;
  }) => (
    <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
      <CardHeader className="pb-2">
        <div className="flex items-start gap-4">
          <div className="p-2 bg-blue-50 rounded-lg">
            <Icon className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-2">
        <Button variant="outline" className="w-full mt-2 text-sm">
          Learn More
          <ExternalLink className="ml-2 h-3 w-3" />
        </Button>
      </CardContent>
    </Card>
  );
  
  return (
    <BusinessLayout pageTitle="API Integration">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>Manage your API keys for integration</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <Label className="font-medium">Live API Key</Label>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? <EyeOff size={16} /> : <Eye size={16} />}
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        onClick={() => copyToClipboard(dummyApiKey)}
                      >
                        {copied ? <Check size={16} className="text-green-500" /> : <Copy size={16} />}
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        onClick={regenerateKey}
                      >
                        <RefreshCw size={16} />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 border rounded-md px-3 py-2 bg-white">
                    <Key className="h-4 w-4 text-gray-400" />
                    <code className="text-sm font-mono flex-1">
                      {showApiKey ? dummyApiKey : '•'.repeat(dummyApiKey.length)}
                    </code>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    This key gives access to your live business data. Keep it secure and never share it publicly.
                  </p>
                </div>
                
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex justify-between items-center mb-2">
                    <Label className="font-medium">Test API Key</Label>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        onClick={() => copyToClipboard('koj_test_api_key_01HQZGV9RSTUVWXYZ1234567890')}
                      >
                        <Copy size={16} />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 border rounded-md px-3 py-2 bg-white">
                    <Key className="h-4 w-4 text-gray-400" />
                    <code className="text-sm font-mono flex-1">
                      koj_test_api_key_01HQZGV9RSTUVWXYZ1234567890
                    </code>
                  </div>
                  <p className="text-xs text-gray-500 mt-2">
                    Use this key for testing your integration. It doesn't affect your live data.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>API Usage</CardTitle>
              <CardDescription>Monitor your API usage and limits</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Requests Today</div>
                    <div className="text-2xl font-bold">127</div>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Requests This Month</div>
                    <div className="text-2xl font-bold">2,341</div>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-500 mb-1">Monthly Limit</div>
                    <div className="text-2xl font-bold">10,000</div>
                  </div>
                </div>
                
                <div className="p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Recent API Requests</h3>
                  <div className="space-y-2">
                    {[
                      { endpoint: '/api/transactions', method: 'GET', status: 200, time: '2 min ago' },
                      { endpoint: '/api/customers', method: 'GET', status: 200, time: '15 min ago' },
                      { endpoint: '/api/payments', method: 'POST', status: 201, time: '23 min ago' },
                      { endpoint: '/api/products', method: 'GET', status: 200, time: '1 hour ago' },
                    ].map((request, index) => (
                      <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 text-xs rounded ${
                            request.method === 'GET' 
                              ? 'bg-blue-100 text-blue-700' 
                              : 'bg-green-100 text-green-700'
                          }`}>
                            {request.method}
                          </span>
                          <code className="text-sm font-mono">{request.endpoint}</code>
                        </div>
                        <div className="flex items-center gap-4">
                          <span className="text-sm text-gray-500">{request.time}</span>
                          <span className={`px-2 py-1 text-xs rounded ${
                            request.status < 300 
                              ? 'bg-green-100 text-green-700' 
                              : 'bg-red-100 text-red-700'
                          }`}>
                            {request.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="space-y-6">
          <ApiFeatureCard 
            icon={Terminal} 
            title="API Documentation" 
            description="Comprehensive guides and examples for integrating with our API"
          />
          
          <ApiFeatureCard 
            icon={Code} 
            title="Code Samples" 
            description="Ready-to-use code examples in various programming languages"
          />
          
          <ApiFeatureCard 
            icon={Webhook} 
            title="Webhooks" 
            description="Configure webhooks to receive real-time updates"
          />
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
              <CardDescription>Contact our developer support</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm mb-4">
                Our developer support team is ready to help you with any integration questions.
              </p>
              <Button className="w-full">
                Contact Support
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </BusinessLayout>
  );
};

export default BusinessAPI;
