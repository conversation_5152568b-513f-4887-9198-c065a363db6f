
import React from 'react';
import { Button } from "@/components/ui/button";
import { Plus } from 'lucide-react';
import PaymentCard from './PaymentCard';

const CardSection = () => {
  return (
    <div className="p-5 glass-card">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-kojaDark">Cards</h2>
        <button className="text-kojaPrimary hover:text-kojaDark transition-colors">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="24" 
            height="24" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <circle cx="12" cy="12" r="1" />
            <circle cx="19" cy="12" r="1" />
            <circle cx="5" cy="12" r="1" />
          </svg>
        </button>
      </div>
      
      <div className="relative overflow-hidden mb-4">
        <PaymentCard 
          cardNumber="5324 4235 7641 0329"
          name="Daramola Olalekan"
          expiryDate="03/24"
        />
      </div>
      
      <div className="space-y-3">
        <div className="flex justify-between items-center">
          <p className="text-sm text-kojaGray">Card balance</p>
          <p className="text-lg font-semibold text-kojaDark">₦110,000</p>
        </div>
        
        <div className="flex justify-between items-center">
          <p className="text-sm text-kojaGray">Credit limit</p>
          <p className="text-lg font-semibold text-kojaDark">₦200,000</p>
        </div>
      </div>
      
      <Button className="w-full mt-4 flex items-center justify-center gap-2 text-sm bg-kojaLight hover:bg-kojaLight/80 text-kojaPrimary">
        <Plus size={16} />
        <span>Add new card</span>
      </Button>
    </div>
  );
};

export default CardSection;
