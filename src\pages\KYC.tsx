
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Ta<PERSON>, 
  Ta<PERSON>Content, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  CheckCircle, 
  Circle, 
  Upload, 
  Camera, 
  CreditCard, 
  FileText, 
  User, 
  MapPin,
  Phone,
  Clock,
  AlertCircle,
  CheckCircle2,
  Shield 
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const KYC = () => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [idType, setIdType] = useState('nationalId');
  
  const handleFileUpload = () => {
    toast({
      title: 'File Uploaded',
      description: 'Your document has been uploaded successfully',
    });
  };
  
  const nextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    } else {
      toast({
        title: 'KYC Completed',
        description: 'Your KYC verification is now pending review.',
      });
    }
  };
  
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <DashboardLayout pageTitle="KYC Verification">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-kojaDark">KYC Verification</h1>
        <p className="text-kojaGray mt-1">Complete your KYC verification to unlock all features</p>
      </div>
      
      {/* KYC Progress */}
      <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow mb-6">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div className="flex items-center mb-4 md:mb-0">
              <div className={`rounded-full w-10 h-10 flex items-center justify-center ${currentStep >= 1 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-kojaGray'}`}>
                {currentStep > 1 ? <CheckCircle size={20} /> : '1'}
              </div>
              <div className={`h-1 w-12 ${currentStep >= 2 ? 'bg-green-500' : 'bg-gray-200'}`}></div>
              <div className={`rounded-full w-10 h-10 flex items-center justify-center ${currentStep >= 2 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-kojaGray'}`}>
                {currentStep > 2 ? <CheckCircle size={20} /> : '2'}
              </div>
              <div className={`h-1 w-12 ${currentStep >= 3 ? 'bg-green-500' : 'bg-gray-200'}`}></div>
              <div className={`rounded-full w-10 h-10 flex items-center justify-center ${currentStep >= 3 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-kojaGray'}`}>
                {currentStep > 3 ? <CheckCircle size={20} /> : '3'}
              </div>
              <div className={`h-1 w-12 ${currentStep >= 4 ? 'bg-green-500' : 'bg-gray-200'}`}></div>
              <div className={`rounded-full w-10 h-10 flex items-center justify-center ${currentStep >= 4 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-kojaGray'}`}>
                4
              </div>
            </div>
            
            <div>
              <p className="text-sm text-kojaGray">Step {currentStep} of 4</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* KYC Steps Content */}
      <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow">
        <CardHeader>
          <CardTitle>
            {currentStep === 1 && 'Personal Information'}
            {currentStep === 2 && 'Identity Verification'}
            {currentStep === 3 && 'Address Verification'}
            {currentStep === 4 && 'Review & Submit'}
          </CardTitle>
          <CardDescription>
            {currentStep === 1 && 'Please provide your basic personal details'}
            {currentStep === 2 && 'Upload a valid government-issued ID'}
            {currentStep === 3 && 'Provide proof of your current address'}
            {currentStep === 4 && 'Review your information and submit for verification'}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {/* Step 1: Personal Information */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input id="fullName" placeholder="John Doe" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="dob">Date of Birth</Label>
                  <Input id="dob" type="date" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="gender">Gender</Label>
                  <select 
                    id="gender" 
                    className="w-full px-3 py-2 mt-1 rounded-md border border-input bg-background"
                  >
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                  </select>
                </div>
                
                <div>
                  <Label htmlFor="nationality">Nationality</Label>
                  <Input id="nationality" placeholder="Nigerian" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" placeholder="+234 ************" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" className="mt-1" />
                </div>
              </div>
              
              <div className="flex justify-end">
                <Button onClick={nextStep} className="bg-kojaPrimary hover:bg-kojaPrimary/90">
                  Continue
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 2: Identity Verification */}
          {currentStep === 2 && (
            <div className="space-y-6">
              <div>
                <Label className="mb-3 block">Select ID Type</Label>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div 
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${idType === 'nationalId' ? 'border-kojaPrimary bg-kojaPrimary/5' : 'border-gray-200 hover:border-kojaPrimary/50'}`}
                    onClick={() => setIdType('nationalId')}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <CreditCard className={idType === 'nationalId' ? 'text-kojaPrimary' : 'text-kojaGray'} />
                      {idType === 'nationalId' ? 
                        <CheckCircle2 className="text-kojaPrimary" size={18} /> : 
                        <Circle className="text-kojaGray" size={18} />
                      }
                    </div>
                    <p className={`font-medium ${idType === 'nationalId' ? 'text-kojaDark' : 'text-kojaGray'}`}>National ID</p>
                  </div>
                  
                  <div 
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${idType === 'passport' ? 'border-kojaPrimary bg-kojaPrimary/5' : 'border-gray-200 hover:border-kojaPrimary/50'}`}
                    onClick={() => setIdType('passport')}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <FileText className={idType === 'passport' ? 'text-kojaPrimary' : 'text-kojaGray'} />
                      {idType === 'passport' ? 
                        <CheckCircle2 className="text-kojaPrimary" size={18} /> : 
                        <Circle className="text-kojaGray" size={18} />
                      }
                    </div>
                    <p className={`font-medium ${idType === 'passport' ? 'text-kojaDark' : 'text-kojaGray'}`}>Passport</p>
                  </div>
                  
                  <div 
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${idType === 'driversLicense' ? 'border-kojaPrimary bg-kojaPrimary/5' : 'border-gray-200 hover:border-kojaPrimary/50'}`}
                    onClick={() => setIdType('driversLicense')}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <CreditCard className={idType === 'driversLicense' ? 'text-kojaPrimary' : 'text-kojaGray'} />
                      {idType === 'driversLicense' ? 
                        <CheckCircle2 className="text-kojaPrimary" size={18} /> : 
                        <Circle className="text-kojaGray" size={18} />
                      }
                    </div>
                    <p className={`font-medium ${idType === 'driversLicense' ? 'text-kojaDark' : 'text-kojaGray'}`}>Driver's License</p>
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="idNumber">ID Number</Label>
                <Input 
                  id="idNumber" 
                  placeholder={
                    idType === 'nationalId' ? 'NIN: *********01' : 
                    idType === 'passport' ? '*********' : 
                    '**********'
                  } 
                  className="mt-1" 
                />
              </div>
              
              <div>
                <Label className="mb-3 block">Upload ID Front</Label>
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors" onClick={handleFileUpload}>
                  <Upload className="mx-auto text-kojaGray mb-2" />
                  <p className="text-kojaGray text-sm">Click to upload or drag and drop</p>
                  <p className="text-xs text-kojaGray mt-1">PNG, JPG or PDF (max. 5MB)</p>
                </div>
              </div>
              
              <div>
                <Label className="mb-3 block">Upload ID Back</Label>
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors" onClick={handleFileUpload}>
                  <Upload className="mx-auto text-kojaGray mb-2" />
                  <p className="text-kojaGray text-sm">Click to upload or drag and drop</p>
                  <p className="text-xs text-kojaGray mt-1">PNG, JPG or PDF (max. 5MB)</p>
                </div>
              </div>
              
              <div className="flex justify-between">
                <Button variant="outline" onClick={prevStep}>
                  Back
                </Button>
                <Button onClick={nextStep} className="bg-kojaPrimary hover:bg-kojaPrimary/90">
                  Continue
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 3: Address Verification */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label htmlFor="address">Full Address</Label>
                  <Input id="address" placeholder="123 Main Street" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input id="city" placeholder="Lagos" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="state">State</Label>
                  <Input id="state" placeholder="Lagos State" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input id="postalCode" placeholder="100001" className="mt-1" />
                </div>
                
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input id="country" placeholder="Nigeria" className="mt-1" />
                </div>
              </div>
              
              <div>
                <Label className="mb-3 block">Proof of Address</Label>
                <div className="border-2 border-dashed border-gray-200 rounded-lg p-6 text-center cursor-pointer hover:bg-gray-50 transition-colors" onClick={handleFileUpload}>
                  <Upload className="mx-auto text-kojaGray mb-2" />
                  <p className="text-kojaGray text-sm">Upload a utility bill or bank statement</p>
                  <p className="text-xs text-kojaGray mt-1">PNG, JPG or PDF (max. 5MB)</p>
                </div>
                <p className="text-xs text-kojaGray mt-2">Document must be less than 3 months old</p>
              </div>
              
              <div className="flex justify-between">
                <Button variant="outline" onClick={prevStep}>
                  Back
                </Button>
                <Button onClick={nextStep} className="bg-kojaPrimary hover:bg-kojaPrimary/90">
                  Continue
                </Button>
              </div>
            </div>
          )}
          
          {/* Step 4: Review & Submit */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-100 rounded-lg p-4">
                <div className="flex items-start">
                  <CheckCircle2 className="text-green-600 mr-3 mt-0.5" />
                  <div>
                    <p className="font-medium text-kojaDark">Almost done!</p>
                    <p className="text-sm text-kojaGray mt-1">Please review your information before submitting</p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm font-medium text-kojaGray mb-2">Personal Information</p>
                  <div className="grid grid-cols-2 gap-2">
                    <p className="text-xs text-kojaGray">Full Name:</p>
                    <p className="text-xs text-kojaDark">John Doe</p>
                    <p className="text-xs text-kojaGray">Date of Birth:</p>
                    <p className="text-xs text-kojaDark">15/05/1985</p>
                    <p className="text-xs text-kojaGray">Gender:</p>
                    <p className="text-xs text-kojaDark">Male</p>
                    <p className="text-xs text-kojaGray">Nationality:</p>
                    <p className="text-xs text-kojaDark">Nigerian</p>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm font-medium text-kojaGray mb-2">ID Verification</p>
                  <div className="grid grid-cols-2 gap-2">
                    <p className="text-xs text-kojaGray">ID Type:</p>
                    <p className="text-xs text-kojaDark">{idType === 'nationalId' ? 'National ID' : idType === 'passport' ? 'Passport' : 'Driver\'s License'}</p>
                    <p className="text-xs text-kojaGray">ID Number:</p>
                    <p className="text-xs text-kojaDark">*********</p>
                    <p className="text-xs text-kojaGray">Documents:</p>
                    <p className="text-xs text-kojaDark">2 files uploaded</p>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-sm font-medium text-kojaGray mb-2">Address Information</p>
                  <div className="grid grid-cols-2 gap-2">
                    <p className="text-xs text-kojaGray">Address:</p>
                    <p className="text-xs text-kojaDark">123 Main Street, Lagos</p>
                    <p className="text-xs text-kojaGray">City:</p>
                    <p className="text-xs text-kojaDark">Lagos</p>
                    <p className="text-xs text-kojaGray">State:</p>
                    <p className="text-xs text-kojaDark">Lagos State</p>
                    <p className="text-xs text-kojaGray">Country:</p>
                    <p className="text-xs text-kojaDark">Nigeria</p>
                    <p className="text-xs text-kojaGray">Proof of Address:</p>
                    <p className="text-xs text-kojaDark">1 file uploaded</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-start p-4 bg-yellow-50 border border-yellow-100 rounded-lg">
                <AlertCircle className="text-yellow-600 mr-3 mt-0.5" size={18} />
                <div>
                  <p className="text-sm text-kojaDark">By proceeding, you confirm that:</p>
                  <ul className="text-xs text-kojaGray list-disc pl-5 mt-1 space-y-1">
                    <li>All information provided is accurate and complete</li>
                    <li>You agree to KojaPay's terms and conditions</li>
                    <li>You consent to verification of your identity</li>
                  </ul>
                </div>
              </div>
              
              <div className="flex justify-between">
                <Button variant="outline" onClick={prevStep}>
                  Back
                </Button>
                <Button onClick={nextStep} className="bg-kojaPrimary hover:bg-kojaPrimary/90">
                  Submit for Verification
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* KYC Information */}
      <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 hover:shadow-lg transition-shadow mt-6">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex items-start gap-3 flex-1">
              <Shield className="text-kojaPrimary mt-1" />
              <div>
                <h3 className="font-medium text-kojaDark">Why is KYC necessary?</h3>
                <p className="text-sm text-kojaGray mt-1">KYC (Know Your Customer) verification helps us prevent fraud and comply with regulations.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 flex-1">
              <Clock className="text-kojaPrimary mt-1" />
              <div>
                <h3 className="font-medium text-kojaDark">How long does it take?</h3>
                <p className="text-sm text-kojaGray mt-1">Most verifications are processed within 24-48 hours after submission.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 flex-1">
              <User className="text-kojaPrimary mt-1" />
              <div>
                <h3 className="font-medium text-kojaDark">Need help?</h3>
                <p className="text-sm text-kojaGray mt-1">Contact our support <NAME_EMAIL> for assistance.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </DashboardLayout>
  );
};

export default KYC;
