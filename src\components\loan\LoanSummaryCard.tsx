
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Calculator } from "lucide-react";

interface LoanSummaryCardProps {
  monthlyPayment: number;
  totalPayable: number;
  totalInterest: number;
  firstPaymentDate: Date;
  formatCurrency: (value: number) => string;
}

const LoanSummaryCard = ({
  monthlyPayment,
  totalPayable,
  totalInterest,
  firstPaymentDate,
  formatCurrency
}: LoanSummaryCardProps) => {
  return (
    <Card className="bg-gray-50 border-gray-100">
      <CardContent className="p-4">
        <h3 className="font-medium text-gray-900 mb-3 flex items-center">
          <Calculator className="h-4 w-4 mr-2 text-kojaPrimary" />
          Loan Summary
        </h3>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Monthly Payment</span>
            <span className="font-medium text-gray-900">{formatCurrency(monthlyPayment)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Total Repayment</span>
            <span className="font-medium text-gray-900">{formatCurrency(totalPayable)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Total Interest</span>
            <span className="font-medium text-gray-900">{formatCurrency(totalInterest)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">First Payment Date</span>
            <span className="font-medium text-gray-900">{firstPaymentDate.toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoanSummaryCard;
