import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Roles } from '../auth/decorator/roles.decorator';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Throttle } from '@nestjs/throttler';
import { User } from '../user/entities/user.entity';
import { Userx } from 'src/decorator/userx.decorator';
import { PhysicalCardChargeDto, PhysicalCardFundDto } from './dto/pysicalCardFund.dto';
import { PageOptionsDto } from 'src/utilities/pagination/dtos';
import { PhysicalCardService } from './physical-card.service';

@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('user')
@ApiBearerAuth('JWT-auth')
@ApiTags('Physical card')
@Controller('physical-card')
export class PhysicalCardController {
  constructor(private _vcard: PhysicalCardService) {}

  /**
   * Creates a new physical card for an existing card holder.
   *
   * This endpoint is throttled to allow only 1 request per 10 seconds.
   *
   * @param {Request} req - The request object, containing the authenticated user.
   * @returns {Promise<{status: boolean, message: string, data: any}>} A promise that resolves to an object containing the status, message, and data of the created physical card.
   *
   * @throws {BadRequestException} If there is an error creating the physical card.
   */
  @Throttle({ default: { ttl: 10000, limit: 1 } })
  @ApiOkResponse()
  @HttpCode(201)
  @Post('create/card')
  async createCard(@Userx() user: User) {
    const data = await this._vcard.createCard(user);
    return {
      status: true,
      message: 'physical card created',
      data,
    };
  }

  /**
   * Retrieves the details of a physical card.
   *
   * @param {Request} req - The request object, containing the authenticated user.
   * @param {string} reference - The reference of the physical card to retrieve details for.
   * @returns {Promise<{status: boolean, message: string, data: any}>} A promise that resolves to an object containing the status, message, and details of the physical card.
   *
   */
  @ApiOkResponse()
  @HttpCode(200)
  @ApiParam({ name: 'reference', description: 'card reference' })
  @ApiQuery({ name: 'source', required: false })
  @Get('details/:reference')
  async getCardDetail(
    @Userx() user: User,
    @Param('reference') reference: string,
    @Query('source') source: string,
  ) {
    const card = await this._vcard.findOne(
      user,
      reference,
      source as 'internal' | 'external',
    );
    return {
      status: true,
      message: 'physical card details',
      data: card,
    };
  }

  /**
   * Sends a request to freeze a physical card.
   *
   * @param {Request} req - The request object, containing the authenticated user.
   * @param {number} id - The ID of the physical card to freeze.
   * @returns {Promise<{status: boolean, message: string}>} A promise that resolves to an object containing the status and message of the request.
   *
   * @throws {BadRequestException} If there is an error sending the freeze request.
   */
  @ApiOkResponse()
  @HttpCode(200)
  @ApiParam({ name: 'id', description: 'card id' })
  @Patch('request-freeze/:id')
  async requestFreeze(@Userx() user: User, @Param('id') id: string) {
    await this._vcard.requestFreeze(user, id);
    return {
      status: true,
      message: 'request sent successfully',
    };
  }

  /**
   * Sends a request to activate a physical card.
   *
   * @param {Request} req - The request object, containing the authenticated user.
   * @param {number} id - The ID of the physical card to activate.
   * @returns {Promise<{status: boolean, message: string}>} A promise that resolves to an object containing the status and message of the request.
   *
   * @throws {BadRequestException} If there is an error sending the activation request.
   */
  @ApiOkResponse()
  @HttpCode(200)
  @ApiParam({ name: 'id', description: 'card id' })
  @Patch('request-activate/:id')
  async requestActivate(@Userx() user: User, @Param('id') id: string) {
    await this._vcard.requestActivate(user, id);
    return {
      status: true,
      message: 'request sent successfully',
    };
  }

  /**
   * Funds a physical card.
   *
   * @param {Request} req - The request object, containing the authenticated user.
   * @param {PhysicalCardFundDto} data - The data required to fund the physical card.
   * @param {string} reference - The reference of the physical card to fund.
   * @returns {Promise<{status: boolean, message: string}>} A promise that resolves to an object containing the status and message of the operation.
   *
   * @throws {BadRequestException} If there is an error funding the physical card.
   */
  @Throttle({ default: { ttl: 10000, limit: 1 } })
  @ApiOkResponse()
  @HttpCode(200)
  @ApiParam({ name: 'reference', description: 'card reference' })
  @Post('fund/:reference')
  async fund(
    @Userx() user: User,
    @Body() body: PhysicalCardFundDto,
    @Param('reference') reference: string,
  ) {
    const data = await this._vcard.fund(user, reference, body.amount);
    return {
      status: true,
      message: 'physical card funded successfully',
      data,
    };
  }

  @ApiOkResponse()
  @HttpCode(200)
  @ApiParam({ name: 'reference', description: 'card reference' })
  @Post('charge/:reference')
  async simulateCardCharge(
    @Userx() user: User,
    @Body() body: PhysicalCardChargeDto,
    @Param('reference') reference: string,
  ) {
    const data = await this._vcard.simulateCharge(user, reference, body.amount);
    return {
      status: true,
      message: 'physical card charged successfully',
      data,
    };
  }

  @ApiOkResponse()
  @HttpCode(200)
  @Get('list')
  async listUserCards(@Userx() user: User) {
    const cards = await this._vcard.getCardsForUser(user.id);
    return {
      status: true,
      message: 'user physical cards',
      data: cards,
    };
  }
}
