
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { PlusCircle, Download, Printer, Share2, QrCode, Copy } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import TransactionStatus from '@/components/TransactionStatus';

const QRCodes = () => {
  const { toast } = useToast();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [formStatus, setFormStatus] = useState<'idle' | 'success' | 'failed'>('idle');
  
  const handleCreateQR = (e: React.FormEvent) => {
    e.preventDefault();
    setFormStatus('success');
    setTimeout(() => {
      setShowCreateForm(false);
      setFormStatus('idle');
    }, 2000);
  };
  
  const handleCopyReference = (ref: string) => {
    navigator.clipboard.writeText(ref).then(() => {
      toast({
        title: "Reference copied",
        description: "QR code reference copied to clipboard"
      });
    });
  };
  
  const qrCodes = [
    { id: 1, name: 'Store POS', reference: 'QR-KOJAPAY-S001', amount: 'Dynamic', description: 'For in-store purchases', downloads: 45 },
    { id: 2, name: 'Product Catalog', reference: 'QR-KOJAPAY-P001', amount: '₦12,500', description: 'For product #A123', downloads: 28 },
    { id: 3, name: 'Business Card', reference: 'QR-KOJAPAY-B001', amount: 'Dynamic', description: 'For networking events', downloads: 63 },
  ];
  
  return (
    <BusinessLayout pageTitle="QR Codes">
      <div className="space-y-6">
        <Card>
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Payment QR Codes</CardTitle>
              <CardDescription>Create and manage QR codes for in-person payments</CardDescription>
            </div>
            <Button 
              className="mt-4 sm:mt-0" 
              onClick={() => setShowCreateForm(true)}
              disabled={showCreateForm}
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Create QR Code
            </Button>
          </CardHeader>
          <CardContent>
            {formStatus === 'success' && (
              <div className="mb-6">
                <TransactionStatus 
                  status="success" 
                  message="QR code created successfully"
                />
              </div>
            )}
            
            {formStatus === 'failed' && (
              <div className="mb-6">
                <TransactionStatus 
                  status="failed" 
                  message="Failed to create QR code. Please try again."
                />
              </div>
            )}
            
            {showCreateForm ? (
              <form onSubmit={handleCreateQR} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="qrName">QR Code Name</Label>
                      <Input id="qrName" placeholder="E.g., Store Checkout" className="mt-1" />
                    </div>
                    
                    <div>
                      <Label htmlFor="description">Description (Optional)</Label>
                      <Input id="description" placeholder="What is this QR code for?" className="mt-1" />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch id="fixedAmount" />
                      <Label htmlFor="fixedAmount">Fixed Amount</Label>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="amount">Amount (₦)</Label>
                      <Input id="amount" type="number" placeholder="0.00" className="mt-1" />
                    </div>
                    
                    <div>
                      <Label htmlFor="expiryDate">Expiry Date (Optional)</Label>
                      <Input id="expiryDate" type="date" className="mt-1" />
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Switch id="collectCustomerInfo" defaultChecked />
                      <Label htmlFor="collectCustomerInfo">Collect Customer Information</Label>
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3 mt-6">
                  <Button variant="outline" type="button" onClick={() => setShowCreateForm(false)}>
                    Cancel
                  </Button>
                  <Button type="submit">
                    Generate QR Code
                  </Button>
                </div>
              </form>
            ) : (
              <Tabs defaultValue="all">
                <TabsList className="w-full grid grid-cols-3 mb-6">
                  <TabsTrigger value="all">All QR Codes</TabsTrigger>
                  <TabsTrigger value="fixed">Fixed Amount</TabsTrigger>
                  <TabsTrigger value="dynamic">Dynamic Amount</TabsTrigger>
                </TabsList>
                
                <TabsContent value="all" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Name</th>
                          <th className="pb-3 font-medium">Amount</th>
                          <th className="pb-3 font-medium">Description</th>
                          <th className="pb-3 font-medium">Reference</th>
                          <th className="pb-3 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {qrCodes.map(qr => (
                          <tr key={qr.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{qr.name}</td>
                            <td className="py-4">{qr.amount}</td>
                            <td className="py-4">{qr.description}</td>
                            <td className="py-4">
                              <div className="flex items-center">
                                <span className="text-kojaGray">{qr.reference}</span>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="h-8 w-8 p-0 ml-2"
                                  onClick={() => handleCopyReference(qr.reference)}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                            <td className="py-4">
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Download className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Printer className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Share2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
                
                <TabsContent value="fixed" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Name</th>
                          <th className="pb-3 font-medium">Amount</th>
                          <th className="pb-3 font-medium">Description</th>
                          <th className="pb-3 font-medium">Reference</th>
                          <th className="pb-3 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {qrCodes.filter(qr => qr.amount !== 'Dynamic').map(qr => (
                          <tr key={qr.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{qr.name}</td>
                            <td className="py-4">{qr.amount}</td>
                            <td className="py-4">{qr.description}</td>
                            <td className="py-4">{qr.reference}</td>
                            <td className="py-4">
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Download className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Printer className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Share2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
                
                <TabsContent value="dynamic" className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="text-left border-b">
                          <th className="pb-3 font-medium">Name</th>
                          <th className="pb-3 font-medium">Amount</th>
                          <th className="pb-3 font-medium">Description</th>
                          <th className="pb-3 font-medium">Reference</th>
                          <th className="pb-3 font-medium">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {qrCodes.filter(qr => qr.amount === 'Dynamic').map(qr => (
                          <tr key={qr.id} className="border-b hover:bg-gray-50">
                            <td className="py-4">{qr.name}</td>
                            <td className="py-4">{qr.amount}</td>
                            <td className="py-4">{qr.description}</td>
                            <td className="py-4">{qr.reference}</td>
                            <td className="py-4">
                              <div className="flex space-x-2">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Download className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Printer className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <Share2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </TabsContent>
              </Tabs>
            )}
          </CardContent>
        </Card>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="font-semibold">QR Preview</h3>
                  <p className="text-sm text-kojaGray">Store POS</p>
                </div>
                <QrCode className="h-6 w-6 text-kojaDark" />
              </div>
              <div className="flex justify-center py-4">
                <div className="bg-white p-4 rounded border">
                  <div className="aspect-square w-32 h-32 bg-gray-200 flex items-center justify-center">
                    <QrCode className="h-20 w-20 text-kojaDark" />
                  </div>
                </div>
              </div>
              <div className="flex justify-center space-x-2 mt-4">
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" /> Download
                </Button>
                <Button variant="outline" size="sm">
                  <Printer className="mr-2 h-4 w-4" /> Print
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="mr-2 h-4 w-4" /> Share
                </Button>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-4xl font-bold mb-2">136</div>
              <p className="text-kojaGray">Total Scans</p>
              
              <div className="mt-4">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Store POS</span>
                  <span className="text
                  -sm font-medium">45</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-[#1231b8] h-2 rounded-full" style={{ width: '33%' }}></div>
                </div>
              </div>
              
              <div className="mt-3">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Product Catalog</span>
                  <span className="text-sm font-medium">28</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-[#1231b8] h-2 rounded-full" style={{ width: '21%' }}></div>
                </div>
              </div>
              
              <div className="mt-3">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-sm">Business Card</span>
                  <span className="text-sm font-medium">63</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-[#1231b8] h-2 rounded-full" style={{ width: '46%' }}></div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="text-4xl font-bold mb-2">₦243,500</div>
              <p className="text-kojaGray">Total QR Payments</p>
              
              <div className="mt-6 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Today</span>
                  <span className="text-sm font-medium">₦15,000</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">This Week</span>
                  <span className="text-sm font-medium">₦87,500</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">This Month</span>
                  <span className="text-sm font-medium">₦243,500</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </BusinessLayout>
  );
};

export default QRCodes;
