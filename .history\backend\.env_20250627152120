#app
NODE_ENV=development
FRONTEND_URL=http://localhost:120/
APP_URL=http://localhost:4200/
SECRET=5db16b9d-9bf8-4ba0-8d5d-7e6f847e4066
REFRESH_SECRET=**************************************************
REFRESH_TOKEN_EXPIRATION=20160
APP_NAME='KojaPay'
SUPPORT_EMAIL=<EMAIL>
DOMAIN=kojapay.com
TWO_FACTOR_AUTHENTICATION_APP_NAME=KOJAPAY
APP_ADMIN_URL=http://localhost:4300/
APP_SERVER_URL=http://localhost:3000/
DEFAULT_AVATAR=avatar.png
LOGO=


# BankOne API Configuration
BANKONE_CORE_API_URL=https://staging.mybankone.com/BankOneWebAPI/
BANKONE_CHANNELS_API_URL=https://staging.mybankone.com/thirdpartyapiservice/apiservice/
BANKONE_INSTITUTION_CODE=100575
BANKONE_AUTH_TOKEN=C964E4F0-8F2D-45BB-8457-C2C99B1191A6
BANKONE_API_KEY=a67d9e3f-8982-4c37-ab95-6efe8bb3cd39

#DB
DB_URL=postgres://avnadmin:<EMAIL>:17684/defaultdb
PGSSLROOTCERT=aiven-ca.pem


#application listening port
PORT=8000

#BULL
BULL_NOTIFICATION_QUEUE=notification

# #REDIS
REDIS_URL=redis://default:<EMAIL>:10476



# Email Configuration
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_USER=<EMAIL>
MAIL_SECURE=true
SMTP_PASSWORD=emailPassword123
EMAIL_FROM=<EMAIL>

#Auth
MERCHANT_TOKEN_EXPIRATION=30
CUSTOMER_TOKEN_EXPIRATION=30

SWAGGER_PASSWORD=kojapay@2025

ENCRYPTION_IV=xqGe+zCD/ibCD9yD92sxfA==
ENCRYPTION_KEY=Pc1U/w0HEKS/B6DY3IfVYtCsExDy+BiVkvE70wKxrOU=

TWITTER_HANDLE=
FACEBOOK_HANDLE=
LINKEDIN_HANDLE=
INSTAGRAM_HANDLE=