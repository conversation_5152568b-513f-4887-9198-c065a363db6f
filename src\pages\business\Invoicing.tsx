
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, Download, FileText, Filter, Plus, Search, Tag } from 'lucide-react';
import { Input } from '@/components/ui/input';
import InvoiceForm from '@/components/InvoiceForm';

const InvoicingPage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  
  const mockInvoices = [
    { 
      id: 'INV-2023-001', 
      customer: 'Acme Corporation', 
      amount: 125000, 
      date: '2023-06-15', 
      dueDate: '2023-06-30',
      status: 'paid'
    },
    { 
      id: 'INV-2023-002', 
      customer: 'GlobalTech Inc.', 
      amount: 785000, 
      date: '2023-06-10', 
      dueDate: '2023-07-10',
      status: 'pending'
    },
    { 
      id: 'INV-2023-003', 
      customer: 'Local Shop Ltd.', 
      amount: 45000, 
      date: '2023-06-05', 
      dueDate: '2023-06-20',
      status: 'overdue'
    },
    { 
      id: 'INV-2023-004', 
      customer: 'StartUp Labs', 
      amount: 325000, 
      date: '2023-05-28', 
      dueDate: '2023-06-12',
      status: 'paid'
    },
    { 
      id: 'INV-2023-005', 
      customer: 'Design Agency', 
      amount: 180000, 
      date: '2023-05-20', 
      dueDate: '2023-06-04',
      status: 'paid'
    }
  ];
  
  const stats = {
    total: {
      count: 42,
      amount: 4256000
    },
    paid: {
      count: 35,
      amount: 3842000
    },
    pending: {
      count: 5,
      amount: 295000
    },
    overdue: {
      count: 2,
      amount: 119000
    }
  };
  
  const getStatusBadgeClass = (status: string) => {
    switch(status) {
      case 'paid':
        return 'invoice-status-paid';
      case 'pending':
        return 'invoice-status-pending';
      case 'overdue':
        return 'invoice-status-overdue';
      default:
        return '';
    }
  };

  return (
    <BusinessLayout pageTitle="Invoicing">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">Invoicing</h1>
            <p className="text-muted-foreground">Create and manage your business invoices</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => setActiveTab('create')}>
              <Plus className="h-4 w-4 mr-2" />
              New Invoice
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid grid-cols-3 sm:w-[400px]">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="invoices">Invoices</TabsTrigger>
            <TabsTrigger value="create">Create</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total.count}</div>
                  <p className="text-xs text-muted-foreground">
                    ₦{stats.total.amount.toLocaleString('en-NG')}
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-green-600">Paid</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.paid.count}</div>
                  <p className="text-xs text-muted-foreground">
                    ₦{stats.paid.amount.toLocaleString('en-NG')}
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-amber-600">Pending</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.pending.count}</div>
                  <p className="text-xs text-muted-foreground">
                    ₦{stats.pending.amount.toLocaleString('en-NG')}
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-red-600">Overdue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.overdue.count}</div>
                  <p className="text-xs text-muted-foreground">
                    ₦{stats.overdue.amount.toLocaleString('en-NG')}
                  </p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader>
                <CardTitle>Recent Invoices</CardTitle>
                <CardDescription>Your latest 5 invoices</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockInvoices.map(invoice => (
                    <div key={invoice.id} className="flex justify-between items-center border-b pb-4 last:border-0 last:pb-0">
                      <div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{invoice.id}</span>
                          <span className={getStatusBadgeClass(invoice.status)}>
                            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                          </span>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">{invoice.customer}</div>
                        <div className="flex gap-4 text-xs text-muted-foreground mt-1">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {invoice.date}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Due: {invoice.dueDate}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">₦{invoice.amount.toLocaleString('en-NG')}</div>
                        <Button variant="ghost" size="sm" className="text-xs">View</Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="invoices" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div>
                    <CardTitle>All Invoices</CardTitle>
                    <CardDescription>View and manage all your invoices</CardDescription>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input placeholder="Search invoices..." className="pl-8 w-[200px] md:w-[260px]" />
                    </div>
                    <Button variant="outline" size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="icon">
                      <Tag className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockInvoices.map(invoice => (
                    <div key={invoice.id} className="flex justify-between items-center p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                      <div>
                        <div className="flex items-center gap-2">
                          <FileText className="h-4 w-4 text-muted-foreground" />
                          <span className="font-medium">{invoice.id}</span>
                          <span className={getStatusBadgeClass(invoice.status)}>
                            {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                          </span>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">{invoice.customer}</div>
                        <div className="flex gap-4 text-xs text-muted-foreground mt-1">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {invoice.date}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Due: {invoice.dueDate}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">₦{invoice.amount.toLocaleString('en-NG')}</div>
                        <div className="flex gap-2 mt-2">
                          <Button variant="outline" size="sm">View</Button>
                          <Button variant="outline" size="sm">Download</Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="create">
            <InvoiceForm />
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>
  );
};

export default InvoicingPage;
