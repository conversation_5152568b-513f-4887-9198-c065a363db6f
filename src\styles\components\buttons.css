
/* Button component styles */
.animated-btn {
  @apply transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] font-poppins rounded-xl;
}
  
.koja-primary-btn {
  @apply animated-btn bg-gradient-to-r from-kojaPrimary to-kojaPrimary/90 text-white px-4 py-2.5 sm:px-6 sm:py-3 rounded-xl font-medium shadow-sm hover:shadow-lg font-poppins;
}
  
.koja-secondary-btn {
  @apply animated-btn bg-gradient-to-r from-kojaYellow to-amber-400 text-kojaDark px-4 py-2.5 sm:px-6 sm:py-3 rounded-xl font-medium shadow-sm hover:shadow-lg font-poppins;
}

.action-button {
  @apply px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl bg-gradient-to-r from-kojaPrimary to-blue-600 text-white font-medium transition-all duration-300 shadow-sm hover:shadow-md active:scale-95 focus:ring-2 focus:ring-kojaPrimary/20 focus:outline-none font-poppins;
}
  
.secondary-action-button {
  @apply px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl bg-gradient-to-r from-kojaYellow to-amber-400 text-kojaDark font-medium transition-all duration-300 shadow-sm hover:shadow-md active:scale-95 focus:ring-2 focus:ring-kojaYellow/20 focus:outline-none font-poppins;
}
  
.outline-action-button {
  @apply px-3 py-1.5 sm:px-4 sm:py-2 rounded-xl border border-kojaPrimary/30 backdrop-blur-sm bg-white/50 text-kojaPrimary font-medium transition-all duration-300 hover:bg-kojaPrimary/5 hover:shadow-sm active:scale-95 focus:ring-2 focus:ring-kojaPrimary/20 focus:outline-none font-poppins;
}

/* Business theme buttons */
.business-btn-primary {
  @apply bg-gradient-to-r from-kojaYellow to-amber-400 text-black font-medium px-4 py-2 rounded-xl hover:shadow-md transition-all shadow-sm active:scale-95;
}
  
.business-btn-outline {
  @apply border border-kojaYellow/30 bg-white/50 backdrop-blur-sm text-kojaYellow font-medium px-4 py-2 rounded-xl hover:bg-kojaYellow/10 transition-colors shadow-sm hover:shadow active:scale-95;
}

/* Dark theme buttons */
.dark-button {
  @apply bg-kojaPrimary text-white px-6 py-3 rounded-xl transition-all duration-300 hover:bg-kojaPrimary/90 shadow-lg hover:shadow-xl active:scale-95;
}
  
.dark-outline-button {
  @apply border border-white/20 bg-transparent text-white px-6 py-3 rounded-xl transition-all duration-300 hover:bg-white/10 shadow-sm hover:shadow active:scale-95;
}

/* High-fidelity buttons */
.hi-fi-button {
  @apply rounded-xl px-5 py-2.5 font-medium transition-all duration-300 shadow-sm hover:shadow-md active:scale-[0.98] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white;
}

.hi-fi-primary-button {
  @apply hi-fi-button bg-gradient-to-r from-kojaPrimary to-blue-600 text-white focus:ring-kojaPrimary/30;
}

.hi-fi-secondary-button {
  @apply hi-fi-button bg-gradient-to-r from-kojaYellow to-amber-400 text-kojaDark focus:ring-kojaYellow/30;
}

.hi-fi-outline-button {
  @apply hi-fi-button border border-kojaPrimary/40 bg-white/50 backdrop-blur-sm text-kojaPrimary hover:bg-kojaPrimary/5 focus:ring-kojaPrimary/20;
}

.hi-fi-yellow-outline-button {
  @apply hi-fi-button border border-kojaYellow/40 bg-white/50 backdrop-blur-sm text-kojaYellow hover:bg-kojaYellow/5 focus:ring-kojaYellow/20;
}

/* Auth buttons - enhanced styling for login/signup */
.auth-button {
  @apply w-full flex items-center justify-center gap-2 font-medium py-2 sm:py-2.5 px-3 sm:px-4 rounded-xl
         transition-all duration-300 shadow-sm hover:shadow-md active:scale-[0.98]
         focus:outline-none disabled:opacity-70 disabled:pointer-events-none font-poppins;
}

.auth-button-primary {
  @apply auth-button bg-gradient-to-r from-[#1231B8] to-[#0D2599] text-white hover:from-[#0D2599] hover:to-[#1231B8];
}

.auth-button-secondary {
  @apply auth-button border border-[#1231B8]/20 bg-white/50 backdrop-blur-sm text-[#1231B8] hover:bg-[#1231B8]/5;
}

.auth-button-yellow {
  @apply auth-button bg-gradient-to-r from-[#FDE314] to-[#F9CB11] text-[#1231B8] hover:from-[#F9CB11] hover:to-[#FDE314];
}

.auth-button-destructive {
  @apply auth-button bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 shadow-md;
}

.auth-button-icon {
  @apply flex items-center gap-2;
}

.auth-button-loading {
  @apply flex items-center gap-2 justify-center;
}

/* Admin portal specific buttons */
.admin-action-button {
  @apply flex flex-col items-center justify-center p-4 rounded-xl border border-[#D3E4FD]/50 hover:shadow-md 
         hover:border-[#D3E4FD] transition-all duration-300 bg-white/50 backdrop-blur-sm font-poppins;
}

.admin-icon-container {
  @apply p-3 rounded-full mb-3;
}

/* Responsive button adjustments */
@media (max-width: 640px) {
  .auth-button {
    @apply py-2 text-sm;
  }
  
  .animated-btn {
    @apply text-sm;
  }
}

/* Profile logout button specific styles */
.profile-logout-btn {
  @apply mt-6 w-full rounded-xl bg-gradient-to-r from-red-500 to-red-600 text-white 
         hover:from-red-600 hover:to-red-700 shadow-md hover:shadow-lg transition-all duration-300
         flex items-center justify-center gap-2 py-2.5 font-medium;
}
