
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Link } from 'react-router-dom';
import { 
  PiggyBank, 
  Target, 
  Gift, 
  TrendingUp, 
  Coins, 
  Star,
  Baby,
  Calendar,
  Award,
  Smartphone,
  ArrowLeft
} from 'lucide-react';
import { Helmet } from 'react-helmet-async';

const KidsDashboard = () => {
  const [kidsData] = useState({
    name: 'Young User',
    balance: 250.50,
    savingsGoal: 500,
    weeklyAllowance: 50,
    weeklySpent: 25,
    weeklyLimit: 100,
    rewardPoints: 125,
    achievements: [
      { id: 1, name: 'First Saver', icon: '🏆', earned: true },
      { id: 2, name: 'Smart Spender', icon: '💡', earned: true },
      { id: 3, name: 'Goal Achiever', icon: '🎯', earned: false },
    ]
  });

  const savingsProgress = (kidsData.balance / kidsData.savingsGoal) * 100;
  const weeklySpendingProgress = (kidsData.weeklySpent / kidsData.weeklyLimit) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FDE314]/20 via-blue-50 to-[#1231B8]/20 p-4">
      <Helmet>
        <title>Kids Dashboard | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link to="/" className="inline-flex items-center text-[#1231B8] hover:underline">
            <ArrowLeft size={16} className="mr-1" />
            Back to Home
          </Link>
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              <Baby className="h-8 w-8 text-[#1231B8] mr-2" />
              <h1 className="text-3xl font-bold text-[#1231B8]">
                Hi {kidsData.name}! 👋
              </h1>
            </div>
            <p className="text-gray-600">Welcome to your KojaPay Kids account</p>
          </div>
          <div className="w-24"></div>
        </div>

        {/* Main Stats */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card className="rounded-[40px] bg-gradient-to-r from-[#1231B8] to-[#1231B8]/80 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/80">My Balance</p>
                  <p className="text-3xl font-bold">₦{kidsData.balance.toFixed(2)}</p>
                </div>
                <PiggyBank className="h-12 w-12 text-[#FDE314]" />
              </div>
            </CardContent>
          </Card>

          <Card className="rounded-[40px] bg-gradient-to-r from-[#FDE314] to-[#FDE314]/80">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-[#1231B8]/80">Reward Points</p>
                  <p className="text-3xl font-bold text-[#1231B8]">{kidsData.rewardPoints}</p>
                </div>
                <Star className="h-12 w-12 text-[#1231B8]" />
              </div>
            </CardContent>
          </Card>

          <Card className="rounded-[40px]">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600">Weekly Allowance</p>
                  <p className="text-3xl font-bold text-[#1231B8]">₦{kidsData.weeklyAllowance}</p>
                </div>
                <Calendar className="h-12 w-12 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="rounded-[40px]">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600">Savings Goal</p>
                  <p className="text-3xl font-bold text-[#1231B8]">₦{kidsData.savingsGoal}</p>
                </div>
                <Target className="h-12 w-12 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Progress Cards */}
        <div className="grid gap-6 md:grid-cols-2 mb-8">
          <Card className="rounded-[40px]">
            <CardHeader>
              <CardTitle className="flex items-center text-[#1231B8]">
                <Target className="h-6 w-6 mr-2" />
                Savings Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between text-sm">
                  <span>Current: ₦{kidsData.balance.toFixed(2)}</span>
                  <span>Goal: ₦{kidsData.savingsGoal}</span>
                </div>
                <Progress value={savingsProgress} className="h-3" />
                <p className="text-center text-lg font-semibold text-[#1231B8]">
                  {savingsProgress.toFixed(1)}% Complete!
                </p>
              </div>
            </CardContent>
          </Card>

          <Card className="rounded-[40px]">
            <CardHeader>
              <CardTitle className="flex items-center text-[#1231B8]">
                <Coins className="h-6 w-6 mr-2" />
                Weekly Spending
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between text-sm">
                  <span>Spent: ₦{kidsData.weeklySpent}</span>
                  <span>Limit: ₦{kidsData.weeklyLimit}</span>
                </div>
                <Progress 
                  value={weeklySpendingProgress} 
                  className="h-3"
                />
                <p className="text-center text-lg font-semibold text-[#1231B8]">
                  ₦{kidsData.weeklyLimit - kidsData.weeklySpent} left this week
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Achievements */}
        <Card className="rounded-[40px] mb-8">
          <CardHeader>
            <CardTitle className="flex items-center text-[#1231B8]">
              <Award className="h-6 w-6 mr-2" />
              My Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {kidsData.achievements.map((achievement) => (
                <div 
                  key={achievement.id}
                  className={`p-4 rounded-[20px] text-center transition-all ${
                    achievement.earned 
                      ? 'bg-gradient-to-r from-[#FDE314] to-[#FDE314]/80 border-2 border-[#1231B8]' 
                      : 'bg-gray-100 border border-gray-300'
                  }`}
                >
                  <div className="text-4xl mb-2">{achievement.icon}</div>
                  <h3 className={`font-semibold ${achievement.earned ? 'text-[#1231B8]' : 'text-gray-500'}`}>
                    {achievement.name}
                  </h3>
                  {achievement.earned && (
                    <p className="text-sm text-[#1231B8]/80 mt-1">Earned!</p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="rounded-[40px]">
          <CardHeader>
            <CardTitle className="text-[#1231B8]">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <Button className="p-6 h-auto rounded-[40px] bg-[#1231B8] hover:bg-[#1231B8]/90">
                <div className="text-center">
                  <PiggyBank className="h-8 w-8 mx-auto mb-2" />
                  <span>Save Money</span>
                </div>
              </Button>
              
              <Button className="p-6 h-auto rounded-[40px] bg-[#FDE314] hover:bg-[#FDE314]/90 text-[#1231B8]">
                <div className="text-center">
                  <Gift className="h-8 w-8 mx-auto mb-2" />
                  <span>Redeem Points</span>
                </div>
              </Button>
              
              <Link to="/kids-bill-payment" className="block">
                <Button className="p-6 h-auto rounded-[40px] bg-green-500 hover:bg-green-600 w-full">
                  <div className="text-center">
                    <Smartphone className="h-8 w-8 mx-auto mb-2" />
                    <span>Buy Airtime/Data</span>
                  </div>
                </Button>
              </Link>
              
              <Button className="p-6 h-auto rounded-[40px] bg-purple-500 hover:bg-purple-600">
                <div className="text-center">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2" />
                  <span>View Progress</span>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default KidsDashboard;
