
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3, TrendingUp, ArrowUp, ArrowDown, DollarSign, PieChart, Activity, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart, <PERSON>, <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell, LineChart, Line } from 'recharts';

const Insights = () => {
  // Sample data for charts
  const spendingCategoryData = [
    { name: 'Food', value: 25000, color: '#1231b8' },
    { name: 'Transport', value: 15000, color: '#fde314' },
    { name: 'Shopping', value: 20000, color: '#00A389' },
    { name: 'Bills', value: 30000, color: '#f87171' },
    { name: 'Entertainment', value: 10000, color: '#8b5cf6' },
  ];
  
  const monthlySpendingData = [
    { name: 'Jan', spending: 120000 },
    { name: 'Feb', spending: 140000 },
    { name: 'Mar', spending: 130000 },
    { name: 'Apr', spending: 170000 },
    { name: 'May', spending: 150000 },
    { name: 'Jun', spending: 190000 },
  ];
  
  const incomeVsExpenseData = [
    { name: 'Jan', income: 250000, expense: 120000 },
    { name: 'Feb', income: 260000, expense: 140000 },
    { name: 'Mar', income: 270000, expense: 130000 },
    { name: 'Apr', income: 280000, expense: 170000 },
    { name: 'May', income: 290000, expense: 150000 },
    { name: 'Jun', income: 300000, expense: 190000 },
  ];

  // Insights data
  const smartInsights = [
    {
      title: "Monthly Spending Increased",
      description: "Your spending in May was 12% higher than April.",
      icon: <TrendingUp className="text-red-500" size={20} />,
      type: "warning"
    },
    {
      title: "Unusual Transaction",
      description: "Large transaction of ₦50,000 detected yesterday.",
      icon: <Activity className="text-yellow-500" size={20} />,
      type: "alert"
    },
    {
      title: "Savings Opportunity",
      description: "You could save ₦15,000 by reducing food expenses.",
      icon: <DollarSign className="text-green-500" size={20} />,
      type: "opportunity"
    },
    {
      title: "Recurring Subscription",
      description: "Entertainment subscriptions take 8% of your monthly income.",
      icon: <Calendar className="text-blue-500" size={20} />,
      type: "info"
    },
  ];

  return (
    <DashboardLayout pageTitle="Financial Insights">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-katina">Financial Insights</h2>
          <p className="text-muted-foreground">Understand your spending patterns and financial habits</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Total Spending</p>
                <ArrowUp className="text-red-500 h-4 w-4" />
              </div>
              <div className="text-2xl font-bold">₦450,000</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-red-500 font-medium">+12%</span> from last month
              </p>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Total Income</p>
                <ArrowUp className="text-green-500 h-4 w-4" />
              </div>
              <div className="text-2xl font-bold">₦650,000</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-green-500 font-medium">+5%</span> from last month
              </p>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Savings</p>
                <ArrowUp className="text-green-500 h-4 w-4" />
              </div>
              <div className="text-2xl font-bold">₦200,000</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-green-500 font-medium">+2%</span> from last month
              </p>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Top Category</p>
                <BarChart3 className="text-banklyBlue h-4 w-4" />
              </div>
              <div className="text-2xl font-bold">Bills</div>
              <p className="text-xs text-muted-foreground mt-1">
                <span className="text-red-500 font-medium">₦120,000</span> this month
              </p>
            </CardContent>
          </Card>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 lg:col-span-2 hover:shadow-xl transition-all duration-500">
            <CardHeader>
              <CardTitle>Spending Overview</CardTitle>
              <CardDescription>Track your income vs expenses over time</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="mb-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="trends">Trends</TabsTrigger>
                  <TabsTrigger value="comparison">Comparison</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview">
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={monthlySpendingData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="spending" fill="#1231b8" />
                    </BarChart>
                  </ResponsiveContainer>
                </TabsContent>
                
                <TabsContent value="trends">
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={incomeVsExpenseData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Line type="monotone" dataKey="income" stroke="#00A389" strokeWidth={2} />
                      <Line type="monotone" dataKey="expense" stroke="#f87171" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                </TabsContent>
                
                <TabsContent value="comparison">
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={incomeVsExpenseData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="income" fill="#00A389" />
                      <Bar dataKey="expense" fill="#f87171" />
                    </BarChart>
                  </ResponsiveContainer>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
            <CardHeader>
              <CardTitle>Spending by Category</CardTitle>
              <CardDescription>See where your money is going</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px] flex items-center justify-center">
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={spendingCategoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    >
                      {spendingCategoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
              
              <div className="mt-4 space-y-2">
                {spendingCategoryData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: item.color }}></div>
                      <span className="text-sm">{item.name}</span>
                    </div>
                    <span className="text-sm font-medium">₦{(item.value).toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        
        <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-500">
          <CardHeader>
            <CardTitle>Smart Insights</CardTitle>
            <CardDescription>Personalized financial insights based on your spending habits</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {smartInsights.map((insight, index) => (
                <div 
                  key={index} 
                  className={`p-4 rounded-lg border ${
                    insight.type === 'warning' ? 'border-orange-200 bg-orange-50' :
                    insight.type === 'alert' ? 'border-red-200 bg-red-50' :
                    insight.type === 'opportunity' ? 'border-green-200 bg-green-50' :
                    'border-blue-200 bg-blue-50'
                  }`}
                >
                  <div className="flex gap-3 items-start">
                    <div className="mt-0.5">{insight.icon}</div>
                    <div>
                      <h4 className="font-medium text-gray-900">{insight.title}</h4>
                      <p className="text-xs text-gray-600 mt-1">{insight.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6">
              <Button className="w-full">View Detailed Analysis</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Insights;
