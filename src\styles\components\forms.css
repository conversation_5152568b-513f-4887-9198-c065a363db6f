/* Form component styles */
.form-input {
  @apply w-full px-4 py-2 rounded-[20px] border border-white/50 bg-white/70 backdrop-blur-sm focus:border-kojaPrimary focus:ring-2 focus:ring-kojaPrimary/20 focus:outline-none transition-all duration-300 shadow-sm font-futura;
}
  
.form-select {
  @apply w-full px-4 py-2 rounded-[20px] border border-white/50 bg-white/70 backdrop-blur-sm focus:border-kojaPrimary focus:ring-2 focus:ring-kojaPrimary/20 focus:outline-none transition-all duration-300 shadow-sm font-futura;
}
  
.form-checkbox {
  @apply rounded-[5px] text-kojaPrimary focus:ring-kojaPrimary/20 transition-all duration-300;
}

/* Dark theme inputs */
.dark-input {
  @apply bg-white/10 border border-white/20 text-white rounded-[20px] px-4 py-3 focus:outline-none focus:ring-2 focus:ring-kojaPrimary/30 focus:border-kojaPrimary/50 placeholder-white/50 font-futura;
}

/* Clean modern form elements - inspired by the image */
.clean-input {
  @apply bg-[#f0f8f4] border border-transparent text-gray-800 rounded-xl px-4 py-2.5
         focus:outline-none focus:ring-1 focus:ring-[#1231B8]/30 focus:border-[#1231B8]/50 
         placeholder-gray-400 transition-all duration-200 font-futura;
}

.clean-form-container {
  @apply rounded-xl overflow-hidden;
}

.clean-form-label {
  @apply text-gray-600 text-sm font-medium mb-1 block font-futura;
}

/* Clean styled checkboxes */
.clean-checkbox {
  @apply relative h-5 w-5;
}

.clean-checkbox input {
  @apply absolute opacity-0 h-0 w-0;
}

.clean-checkbox .checkmark {
  @apply absolute top-0 left-0 h-5 w-5 bg-gray-100 border border-gray-300 rounded-md
         after:content-[''] after:absolute after:hidden;
}

.clean-checkbox input:checked ~ .checkmark {
  @apply bg-[#1231B8] border-[#1231B8];
}

.clean-checkbox input:checked ~ .checkmark:after {
  @apply block;
}

.clean-checkbox .checkmark:after {
  @apply left-1.5 top-0.5 w-2 h-3 border-r-2 border-b-2 border-white transform rotate-45;
}

/* Business login form elements */
.business-input {
  @apply bg-white/70 border border-gray-200 text-gray-800 rounded-xl px-4 py-2
         focus:outline-none focus:ring-1 focus:ring-[#1231B8]/30 focus:border-[#1231B8]/50 
         placeholder-gray-400 transition-all duration-200;
}

.business-form-label {
  @apply text-gray-700 text-sm font-medium mb-1 block;
}

.business-form-container {
  @apply rounded-xl overflow-hidden bg-white/90 border border-white/20 backdrop-blur-xl;
}

/* Tech-inspired form elements for the login page */
.tech-input {
  @apply bg-black/40 border border-[#1AFFFF]/30 text-white rounded-md px-4 py-2 
         focus:outline-none focus:ring-2 focus:ring-[#1AFFFF]/50 focus:border-[#1AFFFF]/70 
         placeholder-[#1AFFFF]/50 transition-all duration-300;
}

.tech-form-container {
  @apply relative;
}

.tech-form-container::before {
  @apply content-[''] absolute -inset-0.5 bg-gradient-to-r from-[#1231B8] to-[#1231B8]
         rounded-lg opacity-50 blur-sm transition-opacity duration-300;
}

.tech-form-container:focus-within::before {
  @apply opacity-70;
}

.tech-form-label {
  @apply text-[#1AFFFF] text-sm font-medium mb-1 block;
}

/* Circuit board inspired checkboxes */
.circuit-checkbox {
  @apply relative h-5 w-5;
}

.circuit-checkbox input {
  @apply absolute opacity-0 h-0 w-0;
}

.circuit-checkbox .checkmark {
  @apply absolute top-0 left-0 h-5 w-5 bg-black/60 border border-[#1AFFFF]/50 rounded-sm
         after:content-[''] after:absolute after:hidden;
}

.circuit-checkbox input:checked ~ .checkmark {
  @apply bg-[#1231B8] border-[#1AFFFF];
}

.circuit-checkbox input:checked ~ .checkmark:after {
  @apply block;
}

.circuit-checkbox .checkmark:after {
  @apply left-1.5 top-0.5 w-2 h-3 border-r-2 border-b-2 border-white transform rotate-45;
}

/* Auth buttons - enhanced styling for login/signup */
.auth-button {
  @apply w-full flex items-center justify-center gap-2 font-medium py-2.5 px-4 rounded-xl
         transition-all duration-300 shadow-sm hover:shadow-md active:scale-[0.98]
         focus:outline-none disabled:opacity-70 disabled:pointer-events-none font-futura;
}

.auth-button-primary {
  @apply auth-button bg-gradient-to-r from-[#1231B8] to-[#0D2599] text-white hover:from-[#0D2599] hover:to-[#1231B8];
}

.auth-button-secondary {
  @apply auth-button border border-[#1231B8]/20 bg-white/50 backdrop-blur-sm text-[#1231B8] hover:bg-[#1231B8]/5;
}

.auth-button-yellow {
  @apply auth-button bg-gradient-to-r from-[#FDE314] to-[#F9CB11] text-[#1231B8] hover:from-[#F9CB11] hover:to-[#FDE314];
}

.auth-button-icon {
  @apply flex items-center justify-center gap-2;
}

.auth-button-loading {
  @apply flex items-center gap-2 justify-center;
}

/* Login button styles - for consistency across pages */
.login-button {
  @apply auth-button-primary;
}

.login-button-secondary {
  @apply auth-button-secondary;
}

.login-button-yellow {
  @apply auth-button-yellow;
}

.login-button-loading {
  @apply flex items-center gap-2 justify-center;
}

.login-button-loading .spinner-container {
  @apply relative flex items-center justify-center;
}

.login-button-loading .spinner {
  @apply h-5 w-5 rounded-full border-2 border-t-transparent border-white animate-spin;
}

.login-button-loading .spinner-logo {
  @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-2.5 w-2.5 object-contain;
}

.login-button-icon {
  @apply flex items-center justify-center gap-2;
}

/* Responsive form adjustments */
@media (max-width: 640px) {
  .auth-card {
    @apply mx-auto w-full max-w-[95%] p-4;
  }
  
  .auth-form {
    @apply space-y-3;
  }
}
