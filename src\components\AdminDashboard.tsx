
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  Users, 
  CreditCard, 
  FileText, 
  Shield, 
  Store, 
  Receipt, 
  Landmark, 
  ShoppingBag,
  QrCode,
  Send,
  Lock
} from 'lucide-react';

const AdminDashboard = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  
  // Mock data for admin overview
  const stats = [
    { title: 'Total Users', value: '15,342', icon: <Users size={24} />, color: 'bg-blue-100' },
    { title: 'Business Accounts', value: '4,270', icon: <Store size={24} />, color: 'bg-purple-100' },
    { title: 'Total Transactions', value: '236,512', icon: <FileText size={24} />, color: 'bg-green-100' },
    { title: 'Active Cards', value: '8,925', icon: <CreditCard size={24} />, color: 'bg-yellow-100' },
    { title: 'Pending KYC', value: '142', icon: <Shield size={24} />, color: 'bg-red-100' },
    { title: 'Total Revenue', value: '₦145.3M', icon: <Receipt size={24} />, color: 'bg-indigo-100' }
  ];

  const pendingActions = [
    { id: 1, type: 'KYC Verification', user: 'John Doe', timestamp: '2 hours ago', status: 'pending' },
    { id: 2, type: 'Business Verification', user: 'Acme Corp', timestamp: '5 hours ago', status: 'pending' },
    { id: 3, type: 'Large Transfer', user: 'Jane Smith', timestamp: '1 day ago', status: 'review' },
    { id: 4, type: 'Loan Application', user: 'Tech Solutions Ltd', timestamp: '2 days ago', status: 'pending' }
  ];

  // Navigation handlers for quick access
  const navigateToUsers = () => navigate('/admin/users');
  const navigateToBusiness = () => navigate('/admin/business');
  const navigateToTransactions = () => navigate('/admin/transactions');
  const navigateToCards = () => navigate('/admin/cards');
  const navigateToKYC = () => navigate('/admin/kyc');
  const navigateToEcommerce = () => navigate('/admin/ecommerce');

  return (
    <div className="flex-1">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Admin Dashboard</h1>
          <p className="text-gray-600">Welcome to the KojaPay administration portal</p>
        </div>
        
        <div className="flex flex-wrap gap-3">
          <Button variant="outline" className="gap-2 text-sm">
            <FileText size={16} />
            Export Report
          </Button>
          <Button className="gap-2 text-sm">
            <Shield size={16} />
            System Health
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6">
        {stats.map((stat, index) => (
          <Card 
            key={index} 
            className="border border-gray-200 shadow-sm hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => {
              // Navigate based on stat type
              if (stat.title === 'Total Users') navigateToUsers();
              else if (stat.title === 'Business Accounts') navigateToBusiness();
              else if (stat.title === 'Total Transactions') navigateToTransactions();
              else if (stat.title === 'Active Cards') navigateToCards();
              else if (stat.title === 'Pending KYC') navigateToKYC();
            }}
          >
            <CardContent className="p-4 md:p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">{stat.title}</p>
                  <p className="text-xl md:text-2xl font-bold mt-1">{stat.value}</p>
                </div>
                <div className={`p-2 md:p-3 rounded-full ${stat.color}`}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 w-full max-w-md mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="pending">Pending Actions</TabsTrigger>
          <TabsTrigger value="alerts">System Alerts</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <Users size={18} />
                  User Growth
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-gray-100 rounded-md">
                  <p className="text-gray-500">User Growth Chart</p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <BarChart3 size={18} />
                  Transaction Volume
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center bg-gray-100 rounded-md">
                  <p className="text-gray-500">Transaction Volume Chart</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle>Pending Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-gray-50 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Type</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">User</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600 hidden md:table-cell">Time</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Status</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-600">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pendingActions.map((action) => (
                      <tr key={action.id} className="border-b border-gray-200">
                        <td className="px-4 py-3 text-sm text-gray-800">{action.type}</td>
                        <td className="px-4 py-3 text-sm text-gray-800">{action.user}</td>
                        <td className="px-4 py-3 text-sm text-gray-600 hidden md:table-cell">{action.timestamp}</td>
                        <td className="px-4 py-3 text-sm">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            action.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                            action.status === 'review' ? 'bg-blue-100 text-blue-800' : ''
                          }`}>
                            {action.status}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          <Button variant="outline" size="sm" className="h-8">Review</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-center h-40 bg-gray-50 rounded-md">
                <p className="text-gray-500">No critical alerts at this time</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Access Section */}
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-4">Quick Access</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          <Button
            variant="outline" 
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={navigateToUsers}
          >
            <Users size={24} className="text-blue-600" />
            <span>Users</span>
          </Button>
          
          <Button 
            variant="outline" 
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={navigateToBusiness}
          >
            <Store size={24} className="text-purple-600" />
            <span>Businesses</span>
          </Button>
          
          <Button 
            variant="outline" 
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={navigateToTransactions}
          >
            <FileText size={24} className="text-green-600" />
            <span>Transactions</span>
          </Button>
          
          <Button 
            variant="outline" 
            className="h-auto p-4 flex flex-col items-center gap-2"
            onClick={navigateToEcommerce}
          >
            <ShoppingBag size={24} className="text-amber-600" />
            <span>E-commerce</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
