
import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Bank } from '@/types/bankOne';
import { BankOneApi } from '@/services/bankOneApi';

interface BankSelectorProps {
  onBankSelect: (bank: Bank) => void;
  selectedBankCode?: string;
  disabled?: boolean;
}

const BankSelector: React.FC<BankSelectorProps> = ({ 
  onBankSelect, 
  selectedBankCode,
  disabled = false 
}) => {
  const [banks, setBanks] = useState<Bank[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBanks();
  }, []);

  const fetchBanks = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await BankOneApi.getBanksList();
      
      if (response.success && response.data) {
        // Sort banks alphabetically by name
        const sortedBanks = response.data.banks?.sort((a, b) => 
          a.bankName.localeCompare(b.bankName)
        ) || [];
        
        setBanks(sortedBanks);
      } else {
        setError('Failed to load banks list');
        console.error('Error: Could not load banks list. Please try again.');
      }
    } catch (error) {
      setError('Failed to load banks list');
      console.error('Error: Could not load banks list. Please try again.');
      console.error('Error loading banks list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBankSelect = (value: string) => {
    const selectedBank = banks.find(bank => bank.bankCode === value);
    if (selectedBank) {
      onBankSelect(selectedBank);
    }
  };

  if (isLoading) {
    return (
      <div className="h-10 flex items-center justify-center border rounded-md">
        <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
        <span className="ml-2 text-sm text-muted-foreground">Loading banks...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-10 flex items-center justify-center border border-red-300 bg-red-50 rounded-md">
        <span className="text-sm text-red-500">Error loading banks</span>
        <button 
          onClick={fetchBanks} 
          className="ml-2 text-blue-500 hover:underline text-sm"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <Select onValueChange={handleBankSelect} defaultValue={selectedBankCode} disabled={disabled}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select a bank" />
      </SelectTrigger>
      <SelectContent className="max-h-[300px]">
        {banks.map((bank) => (
          <SelectItem key={bank.bankCode} value={bank.bankCode}>
            {bank.bankName}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default BankSelector;
