import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertTriangle,
  Clock,
  MessageSquare,
  FileText,
  ArrowLeft,
  Send,
  CheckCircle2,
  ShieldCheck,
  Upload,
  User,
  Store,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Types
interface DisputeMessage {
  id: string;
  content: string;
  sender: 'buyer' | 'seller' | 'admin';
  timestamp: string;
  attachments?: { name: string; url: string }[];
}

interface Dispute {
  id: string;
  escrowId: string;
  orderId?: string;
  title: string;
  reason: string;
  status: 'pending' | 'in_review' | 'resolved' | 'closed';
  resolution: string;
  createdBy: 'buyer' | 'seller';
  createdAt: string;
  updatedAt: string;
  messages: DisputeMessage[];
}

interface EscrowParty {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface DisputeEscrow {
  id: string;
  amount: number;
  buyerId: string;
  sellerId: string;
  buyer: EscrowParty;
  seller: EscrowParty;
  status: 'active' | 'disputed' | 'completed' | 'cancelled';
  createdAt: string;
}

// Mock data fetching function
const fetchDisputeDetails = async (disputeId: string): Promise<{ dispute: Dispute; escrow: DisputeEscrow }> => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock data
  const dispute: Dispute = {
    id: disputeId,
    escrowId: 'ESC-12345',
    orderId: 'ORD-67890',
    title: 'Item Not As Described',
    reason: 'The product I received does not match the description. The color is different and there are scratches on the surface.',
    status: 'in_review',
    resolution: '',
    createdBy: 'buyer',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
    messages: [
      {
        id: 'MSG-1',
        content: 'The product I received does not match the description. The color is different and there are scratches on the surface.',
        sender: 'buyer',
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'MSG-2',
        content: 'We apologize for the inconvenience. Could you please provide photos of the item showing the scratches and color difference?',
        sender: 'admin',
        timestamp: new Date(Date.now() - 2.5 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'MSG-3',
        content: 'Here are the photos showing the scratches and color difference.',
        sender: 'buyer',
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        attachments: [
          { name: 'photo1.jpg', url: '#' },
          { name: 'photo2.jpg', url: '#' },
        ],
      },
      {
        id: 'MSG-4',
        content: 'We have shipped the item as described in the listing. The lighting in the photos might make the color appear different.',
        sender: 'seller',
        timestamp: new Date(Date.now() - 1.5 * 24 * 60 * 60 * 1000).toISOString(),
      },
      {
        id: 'MSG-5',
        content: 'We are reviewing this case and will get back to you shortly with our decision.',
        sender: 'admin',
        timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      },
    ],
  };
  
  const escrow: DisputeEscrow = {
    id: 'ESC-12345',
    amount: 25000,
    buyerId: 'user123',
    sellerId: 'seller456',
    buyer: {
      id: 'user123',
      name: 'John Doe',
      email: '<EMAIL>',
    },
    seller: {
      id: 'seller456',
      name: 'TechHub Store',
      email: '<EMAIL>',
    },
    status: 'disputed',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
  };
  
  return { dispute, escrow };
};

const DisputeManagement: React.FC = () => {
  const { disputeId } = useParams<{ disputeId: string }>();
  const [dispute, setDispute] = useState<Dispute | null>(null);
  const [escrow, setEscrow] = useState<DisputeEscrow | null>(null);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [sendingMessage, setSendingMessage] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  
  useEffect(() => {
    const loadDisputeDetails = async () => {
      try {
        setLoading(true);
        if (!disputeId) return;
        
        const { dispute, escrow } = await fetchDisputeDetails(disputeId);
        setDispute(dispute);
        setEscrow(escrow);
      } catch (error) {
        console.error('Error loading dispute details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load dispute details',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    
    loadDisputeDetails();
  }, [disputeId, toast]);
  
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !dispute) return;
    
    try {
      setSendingMessage(true);
      
      // In a real app, this would be an API call
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newMsg: DisputeMessage = {
        id: `MSG-${Date.now()}`,
        content: newMessage,
        sender: 'buyer', // Assuming current user is the buyer
        timestamp: new Date().toISOString(),
      };
      
      setDispute(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          messages: [...prev.messages, newMsg],
          updatedAt: new Date().toISOString(),
        };
      });
      
      setNewMessage('');
      toast({
        title: 'Message Sent',
        description: 'Your message has been sent successfully',
      });
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message',
        variant: 'destructive',
      });
    } finally {
      setSendingMessage(false);
    }
  };
  
  const renderStatusBadge = (status: Dispute['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'in_review':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">In Review</Badge>;
      case 'resolved':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Resolved</Badge>;
      case 'closed':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Closed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  const renderSenderAvatar = (sender: DisputeMessage['sender']) => {
    switch (sender) {
      case 'buyer':
        return (
          <Avatar>
            <AvatarFallback className="bg-blue-100 text-blue-800">JD</AvatarFallback>
          </Avatar>
        );
      case 'seller':
        return (
          <Avatar>
            <AvatarFallback className="bg-green-100 text-green-800">TS</AvatarFallback>
          </Avatar>
        );
      case 'admin':
        return (
          <Avatar>
            <AvatarFallback className="bg-purple-100 text-purple-800">KP</AvatarFallback>
          </Avatar>
        );
    }
  };
  
  const renderSenderName = (sender: DisputeMessage['sender']) => {
    switch (sender) {
      case 'buyer':
        return escrow?.buyer.name || 'Buyer';
      case 'seller':
        return escrow?.seller.name || 'Seller';
      case 'admin':
        return 'KojaPay Support';
    }
  };
  
  if (loading) {
    return (
      <div className="container py-8 max-w-4xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center p-8">
              <Clock className="h-10 w-10 text-muted-foreground animate-pulse mb-4" />
              <p className="text-muted-foreground">Loading dispute details...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  if (!dispute || !escrow) {
    return (
      <div className="container py-8 max-w-4xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col items-center justify-center p-8">
              <AlertTriangle className="h-10 w-10 text-destructive mb-4" />
              <h2 className="text-xl font-bold mb-2">Dispute Not Found</h2>
              <p className="text-muted-foreground mb-4">The dispute you're looking for doesn't exist or has been removed.</p>
              <Button onClick={() => navigate('/account/disputes')}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Disputes
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container py-8 max-w-4xl mx-auto">
      <Button 
        variant="ghost" 
        className="mb-6" 
        onClick={() => navigate('/account/disputes')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Disputes
      </Button>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Dispute #{dispute.id}</CardTitle>
                  <CardDescription>
                    Opened on {new Date(dispute.createdAt).toLocaleDateString()}
                  </CardDescription>
                </div>
                {renderStatusBadge(dispute.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium mb-1">Reason for Dispute</h3>
                  <p className="text-sm text-muted-foreground">{dispute.reason}</p>
                </div>
                
                {dispute.resolution && (
                  <div className="bg-green-50 p-4 rounded-md border border-green-200">
                    <h3 className="text-sm font-medium text-green-800 mb-1">Resolution</h3>
                    <p className="text-sm text-green-800">{dispute.resolution}</p>
                  </div>
                )}
                
                <Separator />
                
                <div>
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Conversation
                  </h3>
                  
                  <div className="space-y-4">
                    {dispute.messages.map((message) => (
                      <div key={message.id} className="flex gap-3">
                        {renderSenderAvatar(message.sender)}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{renderSenderName(message.sender)}</span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(message.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <div className="mt-1 text-sm">{message.content}</div>
                          
                          {message.attachments && message.attachments.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                              {message.attachments.map((attachment, index) => (
                                <div 
                                  key={index} 
                                  className="flex items-center gap-1 text-xs bg-secondary p-1 px-2 rounded-md"
                                >
                                  <FileText className="h-3 w-3" />
                                  <span>{attachment.name}</span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              {['pending', 'in_review'].includes(dispute.status) && (
                <div className="w-full space-y-3">
                  <Textarea 
                    placeholder="Type your message here..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <div className="flex justify-between items-center">
                    <Button variant="outline" size="sm">
                      <Upload className="h-4 w-4 mr-1" />
                      Attach Files
                    </Button>
                    <Button 
                      onClick={handleSendMessage} 
                      disabled={!newMessage.trim() || sendingMessage}
                    >
                      {sendingMessage ? (
                        <>
                          <Clock className="h-4 w-4 mr-2 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Send Message
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              )}
              
              {['resolved', 'closed'].includes(dispute.status) && (
                <div className="w-full bg-muted p-3 rounded-md text-center text-sm text-muted-foreground">
                  This dispute is {dispute.status}. No further messages can be sent.
                </div>
              )}
            </CardFooter>
          </Card>
        </div>
        
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <ShieldCheck className="mr-2 h-5 w-5 text-[#fde314]" />
                Escrow Details
              </CardTitle>
              <CardDescription>
                ID: {escrow.id}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Transaction Amount</h3>
                <p className="text-xl font-bold">u20a6{escrow.amount.toLocaleString()}</p>
              </div>
              
              <Separator />
              
              <div className="space-y-3">
                <div>
                  <h3 className="text-sm font-medium mb-1 flex items-center">
                    <User className="h-4 w-4 mr-1" />
                    Buyer
                  </h3>
                  <p className="text-sm">{escrow.buyer.name}</p>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-1 flex items-center">
                    <Store className="h-4 w-4 mr-1" />
                    Seller
                  </h3>
                  <p className="text-sm">{escrow.seller.name}</p>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="text-sm font-medium mb-1">Dispute Timeline</h3>
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Escrow Created:</span>
                    <span>{new Date(escrow.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Dispute Opened:</span>
                    <span>{new Date(dispute.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Updated:</span>
                    <span>{new Date(dispute.updatedAt).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              {dispute.status === 'resolved' && (
                <Button variant="outline" className="w-full" onClick={() => navigate(`/escrow/${escrow.id}`)}>
                  View Full Escrow Details
                </Button>
              )}
              
              {dispute.status === 'in_review' && (
                <Button variant="destructive" className="w-full">
                  Cancel Dispute
                </Button>
              )}
            </CardFooter>
          </Card>
          
          {dispute.orderId && (
            <Button 
              variant="outline" 
              className="w-full" 
              onClick={() => navigate(`/account/orders/${dispute.orderId}`)}
            >
              View Order Details
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default DisputeManagement;
