import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/card';
import { Button } from './ui/button';
import { MapPin, Locate, Store, Search, Shield, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { Skeleton } from './ui/skeleton';
import { <PERSON><PERSON>, <PERSON><PERSON>ontent, TabsList, TabsTrigger } from './ui/tabs';

// Sample store locations data - in a real app, this would come from your backend
const storeLocations = [
  { id: 1, name: "TechHub", type: "Electronics", coordinates: [3.3792, 6.5244] as [number, number], address: "Lagos, Nigeria", rating: 4.5, verified: true },
  { id: 2, name: "Fashion Palace", type: "Clothing", coordinates: [3.3947, 6.4511] as [number, number], address: "Lagos, Nigeria", rating: 4.2, verified: true },
  { id: 3, name: "Home Essentials", type: "Home & Living", coordinates: [3.3841, 6.4550] as [number, number], address: "Lagos, Nigeria", rating: 3.8, verified: false },
  { id: 4, name: "Gadget World", type: "Electronics", coordinates: [3.3792, 6.4580] as [number, number], address: "Lagos, Nigeria", rating: 4.7, verified: true },
];

interface StoreLocation {
  id: number;
  name: string;
  type: string;
  coordinates: [number, number];
  address: string;
  rating: number;
  verified: boolean;
}

const StoreMap: React.FC = () => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const { toast } = useToast();
  const [userLocation, setUserLocation] = useState<[number, number] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredStores, setFilteredStores] = useState<StoreLocation[]>(storeLocations);
  const [selectedStore, setSelectedStore] = useState<StoreLocation | null>(null);
  const [mapError, setMapError] = useState<string | null>(null);
  const [locationAccess, setLocationAccess] = useState<boolean>(false);

  // Filter stores based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredStores(storeLocations);
      return;
    }

    const filtered = storeLocations.filter(store => 
      store.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      store.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredStores(filtered);
  }, [searchTerm]);

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current) return;

    setIsLoading(true);
    setMapError(null);

    // Get Mapbox token from environment variables
    const mapboxToken = process.env.NEXT_PUBLIC_MAPBOX_TOKEN;
    
    if (!mapboxToken) {
      setMapError("Mapbox token is missing. Please check your environment configuration.");
      setIsLoading(false);
      return;
    }
    
    // Set the Mapbox token from environment variables
    mapboxgl.accessToken = mapboxToken;
    
    try {
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/light-v11',
        center: [3.3792, 6.5244] as [number, number], // Lagos coordinates
        zoom: 12
      });

      // Add navigation controls
      map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

      // Add store markers when map is loaded
      map.current.on('load', () => {
        addStoreMarkers();
        setIsLoading(false);
      });

      // Add error handling for map load failures
      map.current.on('error', (e) => {
        console.error('Map error:', e);
        setMapError("There was an error loading the map. Please try again later.");
        setIsLoading(false);
      });
    } catch (error) {
      console.error('Error initializing map:', error);
      setMapError("Failed to initialize the map. Please check your internet connection and try again.");
      setIsLoading(false);
    }

    return () => {
      map.current?.remove();
    };
  }, []);

  // Add markers for all stores
  const addStoreMarkers = () => {
    if (!map.current) return;
    
    // Clear existing markers if any
    const markers = document.getElementsByClassName('mapboxgl-marker');
    while(markers[0]) {
      markers[0].remove();
    }

    // Add store markers
    filteredStores.forEach(location => {
      const popup = new mapboxgl.Popup({ offset: 25, closeButton: false })
        .setHTML(
          `<div style="padding: 8px;">
            <strong>${location.name}</strong>
            <div>${location.type}</div>
            <div>${location.address}</div>
            ${location.verified ? '<div style="color: green; display: flex; align-items: center; margin-top: 4px; font-size: 12px;"><svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.42 4.58a5.4 5.4 0 0 0-7.65 0l-.77.78-.77-.78a5.4 5.4 0 0 0-7.65 0C1.46 6.7 1.33 10.28 4 13l8 8 8-8c2.67-2.72 2.54-6.3.42-8.42z"></path></svg><span style="margin-left: 4px;">Verified Store</span></div>' : ''}
          </div>`
        );

      const marker = new mapboxgl.Marker({ color: location.verified ? '#10b981' : '#fde314' })
        .setLngLat(location.coordinates)
        .setPopup(popup)
        .addTo(map.current!);

      marker.getElement().addEventListener('click', () => {
        setSelectedStore(location);
      });
    });

    // Add user location marker if available
    if (userLocation) {
      new mapboxgl.Marker({ color: '#ef4444' })
        .setLngLat(userLocation)
        .setPopup(new mapboxgl.Popup().setHTML('<strong>Your Location</strong>'))
        .addTo(map.current);
    }
  };

  // Update markers when filtered stores change
  useEffect(() => {
    if (map.current && map.current.loaded()) {
      addStoreMarkers();
    }
  }, [filteredStores, userLocation]);

  const getUserLocation = () => {
    if (!navigator.geolocation) {
      toast({
        title: "Error",
        description: "Geolocation is not supported by your browser",
        variant: "destructive"
      });
      return;
    }

    // Security check - inform user about location tracking
    toast({
      title: "Security Notice",
      description: "We're requesting your location to find nearby stores. Your location data is only used temporarily and not stored.",
      variant: "default"
    });

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { longitude, latitude } = position.coords;
        const userCoords: [number, number] = [longitude, latitude];
        setUserLocation(userCoords);
        setLocationAccess(true);
        
        if (map.current) {
          map.current.flyTo({
            center: userCoords,
            zoom: 14
          });
        }

        // Find closest stores and highlight them
        const storesWithDistance = storeLocations.map(store => ({
          ...store,
          distance: calculateDistance(latitude, longitude, store.coordinates[1], store.coordinates[0])
        }));

        // Sort by distance
        const sortedStores = [...storesWithDistance].sort((a, b) => a.distance - b.distance);
        setFilteredStores(sortedStores);

        toast({
          title: "Success",
          description: `Found your location! Showing ${sortedStores.length} nearby stores.`,
        });
      },
      (error) => {
        console.error('Geolocation error:', error);
        let errorMessage = "Unable to retrieve your location";
        
        switch(error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = "Location access was denied. Please enable location services to find nearby stores.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable. Please try again later.";
            break;
          case error.TIMEOUT:
            errorMessage = "The request to get your location timed out. Please try again.";
            break;
        }
        
        toast({
          title: "Location Error",
          description: errorMessage,
          variant: "destructive"
        });
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  };

  // Calculate distance between two coordinates in kilometers
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(deg2rad(lat1)) * Math.cos(deg2rad(lat2)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2); 
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a)); 
    const distance = R * c; // Distance in km
    return distance;
  };

  const deg2rad = (deg: number): number => {
    return deg * (Math.PI/180);
  };

  const handleStoreClick = (store: StoreLocation) => {
    setSelectedStore(store);
    
    if (map.current) {
      map.current.flyTo({
        center: store.coordinates,
        zoom: 15
      });
    }
  };

  const renderStoreList = () => {
    if (filteredStores.length === 0) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          <p>No stores found matching your search. Try a different keyword.</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {filteredStores.map(store => {
          const isSelected = selectedStore?.id === store.id;
          return (
            <Card 
              key={store.id} 
              className={`cursor-pointer transition-all ${isSelected ? 'ring-2 ring-primary' : 'hover:shadow-md'}`}
              onClick={() => handleStoreClick(store)}
            >
              <CardContent className="p-3">
                <div className="flex items-start gap-2">
                  <MapPin className={`h-4 w-4 mt-1 ${store.verified ? 'text-emerald-500' : 'text-[#fde314]'}`} />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{store.name}</h3>
                      {store.verified && (
                        <Badge variant="outline" className="text-xs text-emerald-600 border-emerald-200 bg-emerald-50">
                          <Shield className="h-3 w-3 mr-1" /> Verified
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{store.type}</p>
                    <p className="text-sm text-gray-500">{store.address}</p>
                    {userLocation && 'distance' in store && (
                      <p className="text-xs text-primary mt-1">
                        {(store as any).distance.toFixed(1)} km away
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  const renderMapSkeleton = () => (
    <div className="w-full h-[400px] md:h-[500px] rounded-lg overflow-hidden bg-gray-100 animate-pulse flex items-center justify-center">
      <div className="text-gray-400 flex flex-col items-center">
        <Locate className="h-8 w-8 mb-2" />
        <p>Loading map...</p>
      </div>
    </div>
  );

  return (
    <Card className="shadow-md border-0">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Store className="h-5 w-5 text-[#fde314]" />
            <CardTitle>Nearby Stores</CardTitle>
          </div>
          <Button 
            onClick={getUserLocation}
            variant="outline" 
            className="flex items-center gap-2"
          >
            <Locate className="h-4 w-4" />
            Find Nearby Stores
          </Button>
        </div>
        <CardDescription>Discover KojaPay partner stores in your area</CardDescription>

        <div className="mt-4 relative">
          <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search stores by name or category"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
      </CardHeader>

      <CardContent className="p-4">
        {mapError && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Map Error</AlertTitle>
            <AlertDescription>{mapError}</AlertDescription>
          </Alert>
        )}

        <Tabs defaultValue="map" className="mb-6">
          <TabsList className="grid w-full grid-cols-2 mb-4">
            <TabsTrigger value="map">Map View</TabsTrigger>
            <TabsTrigger value="list">List View</TabsTrigger>
          </TabsList>
          
          <TabsContent value="map">
            {isLoading ? renderMapSkeleton() : (
              <div 
                ref={mapContainer} 
                className="w-full h-[400px] md:h-[500px] rounded-lg overflow-hidden"
              />
            )}
          </TabsContent>
          
          <TabsContent value="list">
            {isLoading ? (
              <div className="space-y-3">
                {[1, 2, 3, 4].map(i => (
                  <Skeleton key={i} className="h-24 w-full rounded-lg" />
                ))}
              </div>
            ) : renderStoreList()}
          </TabsContent>
        </Tabs>

        {selectedStore && (
          <Card className="mt-4 bg-muted/50">
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    {selectedStore.name}
                    {selectedStore.verified && (
                      <Badge variant="outline" className="ml-2 text-xs text-emerald-600 border-emerald-200 bg-emerald-50">
                        <Shield className="h-3 w-3 mr-1" /> Verified
                      </Badge>
                    )}
                  </h3>
                  <p className="text-sm text-muted-foreground">{selectedStore.type}</p>
                  <p className="text-sm">{selectedStore.address}</p>
                  <div className="flex items-center mt-2">
                    <div className="flex">
                      {[...Array(5)].map((_, i) => (
                        <svg 
                          key={i} 
                          className={`h-4 w-4 ${i < Math.floor(selectedStore.rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                        </svg>
                      ))}
                    </div>
                    <span className="ml-2 text-sm">{selectedStore.rating} / 5</span>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  Get Directions
                </Button>
              </div>
              
              {!selectedStore.verified && (
                <Alert className="mt-3" variant="warning">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>Unverified Store</AlertTitle>
                  <AlertDescription>
                    This store has not been verified by KojaPay. Please exercise caution when conducting transactions.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};

export default StoreMap;
