
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Helmet } from 'react-helmet-async';
import UserLayout from '@/components/layouts/UserLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useSecurity } from '@/contexts/SecurityContext';
import { toast } from 'sonner';
import { 
  Shield, 
  Lock, 
  Key, 
  RotateCw, 
  Smartphone, 
  FileWarning, 
  Eye, 
  EyeOff, 
  Mail,
  MessageSquare
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const SecuritySettings = () => {
  const { user } = useAuth();
  const { settings, updateSettings } = useSecurity();
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong'>('medium');
  
  // State for all the security settings
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: settings?.twoFactorAuth || false,
    biometricLogin: settings?.biometricLogin || false,
    deviceVerification: settings?.deviceVerification || true,
    loginNotifications: settings?.loginNotifications || true,
    transactionAlerts: settings?.transactionAlerts || true,
    emailAlerts: settings?.emailAlerts || true,
    smsAlerts: settings?.smsAlerts || true,
    requireOtpForAllTransactions: settings?.requireOtpForAllTransactions || true,
    blockInternationalTransactions: settings?.blockInternationalTransactions || false,
    alertSensitivity: settings?.alertSensitivity || 'medium'
  });
  
  const handleToggleChange = async (setting: string, value: boolean) => {
    setSecuritySettings(prev => ({
      ...prev,
      [setting]: value
    }));
    
    try {
      const success = await updateSettings({ [setting]: value } as any);
      if (success) {
        toast.success(`${setting} setting updated successfully`);
      } else {
        toast.error(`Failed to update ${setting} setting`);
        // Revert the change in the UI
        setSecuritySettings(prev => ({
          ...prev,
          [setting]: !value
        }));
      }
    } catch (error) {
      toast.error(`An error occurred: ${error}`);
      // Revert the change in the UI
      setSecuritySettings(prev => ({
        ...prev,
        [setting]: !value
      }));
    }
  };
  
  const handleChangePassword = () => {
    // In a real app, this would show a password change dialog
    toast.info('Password change functionality would open here');
  };
  
  const handleSaveSettings = () => {
    toast.success('Security settings saved successfully');
  };
  
  return (
    <UserLayout pageTitle="Security Settings">
      <Helmet>
        <title>Security Settings | KojaPay</title>
      </Helmet>
      
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">Security Settings</h1>
        <p className="text-muted-foreground">Manage your account security and verification settings</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Two-factor authentication */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone size={18} className="text-amber-500" />
                Two-factor Authentication
              </CardTitle>
              <CardDescription>
                Add an extra layer of security to your account
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">SMS Verification</Label>
                    <p className="text-sm text-muted-foreground">
                      Require an SMS code for all transactions
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.smsAlerts}
                    onCheckedChange={(value) => handleToggleChange('smsAlerts', value)}
                  />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Email Verification</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive email alerts for high-value transactions
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.emailAlerts}
                    onCheckedChange={(value) => handleToggleChange('emailAlerts', value)}
                  />
                </div>
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Authenticator App</Label>
                    <p className="text-sm text-muted-foreground">
                      Use an authenticator app for additional security
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.twoFactorAuth}
                    onCheckedChange={(value) => handleToggleChange('twoFactorAuth', value)}
                  />
                </div>
              </div>
              
              <Button variant="outline" className="w-full mt-4 rounded-[20px]">
                <Key className="mr-2 h-4 w-4" />
                Set Up Authenticator
              </Button>
            </CardContent>
          </Card>
          
          {/* Security tips card */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-100">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield size={18} className="text-blue-600" />
                Security Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="bg-white/70 p-3 rounded-[20px] shadow-sm">
                <h3 className="font-medium text-sm text-blue-800">Use Strong Passwords</h3>
                <p className="text-xs text-slate-600 mt-1">
                  Create unique passwords with letters, numbers, and symbols.
                </p>
              </div>
              <div className="bg-white/70 p-3 rounded-[20px] shadow-sm">
                <h3 className="font-medium text-sm text-blue-800">Monitor Account Activity</h3>
                <p className="text-xs text-slate-600 mt-1">
                  Regularly review transaction history for suspicious activities.
                </p>
              </div>
              <div className="bg-white/70 p-3 rounded-[20px] shadow-sm">
                <h3 className="font-medium text-sm text-blue-800">Update Contact Info</h3>
                <p className="text-xs text-slate-600 mt-1">
                  Keep your email and phone number updated for security alerts.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Security settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock size={18} className="text-purple-500" />
              Account Security
            </CardTitle>
            <CardDescription>
              Configure your account security preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Require OTP for All Transfers</Label>
                    <p className="text-sm text-muted-foreground">
                      Always require OTP verification for transfers
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.requireOtpForAllTransactions}
                    onCheckedChange={(value) => handleToggleChange('requireOtpForAllTransactions', value)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Block International Transactions</Label>
                    <p className="text-sm text-muted-foreground">
                      Block all transactions outside Nigeria
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.blockInternationalTransactions}
                    onCheckedChange={(value) => handleToggleChange('blockInternationalTransactions', value)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Transaction Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Get real-time alerts for all transactions
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.transactionAlerts}
                    onCheckedChange={(value) => handleToggleChange('transactionAlerts', value)}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Login Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Receive alerts when someone logs into your account
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.loginNotifications}
                    onCheckedChange={(value) => handleToggleChange('loginNotifications', value)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Device Verification</Label>
                    <p className="text-sm text-muted-foreground">
                      Verify new devices before allowing access
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.deviceVerification}
                    onCheckedChange={(value) => handleToggleChange('deviceVerification', value)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-base">Biometric Login</Label>
                    <p className="text-sm text-muted-foreground">
                      Use fingerprint or face recognition for login
                    </p>
                  </div>
                  <Switch 
                    checked={securitySettings.biometricLogin}
                    onCheckedChange={(value) => handleToggleChange('biometricLogin', value)}
                  />
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Notification Preferences</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-4">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Mail className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <Label className="text-base">Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive security alerts via email</p>
                  </div>
                  <Switch 
                    checked={securitySettings.emailAlerts}
                    onCheckedChange={(value) => handleToggleChange('emailAlerts', value)}
                  />
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="bg-green-100 p-2 rounded-full">
                    <MessageSquare className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <Label className="text-base">SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">Receive security alerts via SMS</p>
                  </div>
                  <Switch 
                    checked={securitySettings.smsAlerts}
                    onCheckedChange={(value) => handleToggleChange('smsAlerts', value)}
                  />
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Alert Sensitivity</h3>
              <div className="flex flex-col space-y-2">
                <Label htmlFor="sensitivity">Choose how sensitive the system should be to suspicious activity</Label>
                <Select 
                  value={securitySettings.alertSensitivity} 
                  onValueChange={(value: any) => {
                    setSecuritySettings(prev => ({ ...prev, alertSensitivity: value }));
                    updateSettings({ alertSensitivity: value as any });
                    toast.success('Alert sensitivity updated');
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select sensitivity level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low - Only alert for highly suspicious activity</SelectItem>
                    <SelectItem value="medium">Medium - Balance between security and convenience</SelectItem>
                    <SelectItem value="high">High - Alert for any unusual activity</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex flex-wrap gap-2 justify-end">
              <Button variant="outline" className="rounded-[20px]" onClick={handleChangePassword}>
                <RotateCw className="mr-2 h-4 w-4" />
                Reset Password
              </Button>
              <Button variant="outline" className="rounded-[20px]">
                <FileWarning className="mr-2 h-4 w-4" />
                View Security Logs
              </Button>
              <Button className="rounded-[20px]" onClick={handleSaveSettings}>
                <Shield className="mr-2 h-4 w-4" />
                Save Settings
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </UserLayout>
  );
};

export default SecuritySettings;
