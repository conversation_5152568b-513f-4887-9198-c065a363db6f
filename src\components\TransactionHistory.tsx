import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, ArrowUpRight, ArrowDownLeft, Search, Calendar } from "lucide-react";
import { BankOneApi } from '@/services/bankOneApi';
import { TransactionHistoryItem } from '@/types/bankOne';
import { format, subDays } from 'date-fns';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { useToast } from '@/hooks/use-toast';

interface TransactionHistoryProps {
  accountNumber: string;
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ accountNumber }) => {
  const [transactions, setTransactions] = useState<TransactionHistoryItem[]>([]);
  const [filteredTransactions, setFilteredTransactions] = useState<TransactionHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [startDate, setStartDate] = useState<Date>(subDays(new Date(), 30));
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [isStartDateOpen, setIsStartDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (accountNumber) {
      fetchTransactionHistory();
    }
  }, [accountNumber, startDate, endDate]);

  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredTransactions(transactions);
    } else {
      const lowercasedTerm = searchTerm.toLowerCase();
      const filtered = transactions.filter(transaction => 
        transaction.narration.toLowerCase().includes(lowercasedTerm) ||
        transaction.amount.toString().includes(lowercasedTerm) ||
        (transaction.senderName && transaction.senderName.toLowerCase().includes(lowercasedTerm)) ||
        (transaction.receiverName && transaction.receiverName.toLowerCase().includes(lowercasedTerm))
      );
      setFilteredTransactions(filtered);
    }
  }, [searchTerm, transactions]);

  const fetchTransactionHistory = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Format dates for API request
      const formattedStartDate = format(startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(endDate, 'yyyy-MM-dd');
      
      const response = await BankOneApi.getTransactionHistory(
        accountNumber,
        formattedStartDate,
        formattedEndDate
      );

      if (response.success && response.data && response.data.transactions) {
        setTransactions(response.data.transactions);
        setFilteredTransactions(response.data.transactions);
      } else {
        setError('Failed to load transaction history');
        toast({
          title: 'Error',
          description: 'Could not load transaction history. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      setError('Failed to load transaction history');
      toast({
        title: 'Error',
        description: 'Could not load transaction history. Please try again.',
        variant: 'destructive',
      });
      console.error('Error loading transaction history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const formatAmount = (amount: number, type: 'Credit' | 'Debit') => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'MMM dd, yyyy HH:mm');
    } catch (error) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6 flex flex-col items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-kojaPrimary mb-4" />
          <p className="text-muted-foreground">Loading transaction history...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-6 flex flex-col items-center justify-center h-64">
          <p className="text-red-500 mb-2">{error}</p>
          <Button onClick={fetchTransactionHistory} variant="outline">
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!filteredTransactions.length) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="font-futura text-lg">Transaction History</CardTitle>
          <div className="flex flex-col sm:flex-row gap-4 mt-4">
            <div className="relative flex-grow">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search transactions..."
                className="pl-8"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <div className="flex gap-2">
              <Popover open={isStartDateOpen} onOpenChange={setIsStartDateOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                    <Calendar className="h-4 w-4" />
                    <span>{format(startDate, 'MMM dd, yyyy')}</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      if (date) {
                        setStartDate(date);
                        setIsStartDateOpen(false);
                      }
                    }}
                    disabled={(date) => date > endDate || date > new Date()}
                  />
                </PopoverContent>
              </Popover>
              <Popover open={isEndDateOpen} onOpenChange={setIsEndDateOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                    <Calendar className="h-4 w-4" />
                    <span>{format(endDate, 'MMM dd, yyyy')}</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <CalendarComponent
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => {
                      if (date) {
                        setEndDate(date);
                        setIsEndDateOpen(false);
                      }
                    }}
                    disabled={(date) => date < startDate || date > new Date()}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center h-64">
          <p className="text-muted-foreground mb-2">No transactions found</p>
          <Button onClick={fetchTransactionHistory} variant="outline">
            Refresh
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="font-futura text-lg">Transaction History</CardTitle>
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-grow">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search transactions..."
              className="pl-8"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
          <div className="flex gap-2">
            <Popover open={isStartDateOpen} onOpenChange={setIsStartDateOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                  <Calendar className="h-4 w-4" />
                  <span>{format(startDate, 'MMM dd, yyyy')}</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => {
                    if (date) {
                      setStartDate(date);
                      setIsStartDateOpen(false);
                    }
                  }}
                  disabled={(date) => date > endDate || date > new Date()}
                />
              </PopoverContent>
            </Popover>
            <Popover open={isEndDateOpen} onOpenChange={setIsEndDateOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                  <Calendar className="h-4 w-4" />
                  <span>{format(endDate, 'MMM dd, yyyy')}</span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <CalendarComponent
                  mode="single"
                  selected={endDate}
                  onSelect={(date) => {
                    if (date) {
                      setEndDate(date);
                      setIsEndDateOpen(false);
                    }
                  }}
                  disabled={(date) => date < startDate || date > new Date()}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredTransactions.map((transaction) => (
            <div 
              key={transaction.transactionReference} 
              className="flex justify-between items-center p-4 rounded-lg border hover:shadow-sm transition-shadow"
            >
              <div className="flex items-center">
                <div className={`h-10 w-10 rounded-full flex items-center justify-center mr-3 ${
                  transaction.transactionType === 'Credit' 
                    ? 'bg-green-100 text-green-600' 
                    : 'bg-red-100 text-red-600'
                }`}>
                  {transaction.transactionType === 'Credit' 
                    ? <ArrowDownLeft className="h-5 w-5" /> 
                    : <ArrowUpRight className="h-5 w-5" />
                  }
                </div>
                <div>
                  <p className="font-medium">
                    {transaction.transactionType === 'Credit' 
                      ? `From: ${transaction.senderName || 'Unknown'}` 
                      : `To: ${transaction.receiverName || 'Unknown'}`
                    }
                  </p>
                  <p className="text-sm text-muted-foreground truncate max-w-xs">
                    {transaction.narration}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatDate(transaction.transactionDate)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-medium ${
                  transaction.transactionType === 'Credit' 
                    ? 'text-green-600' 
                    : 'text-red-600'
                }`}>
                  {transaction.transactionType === 'Credit' ? '+' : '-'}
                  {formatAmount(transaction.amount, transaction.transactionType)}
                </p>
                <div className={`inline-block px-2 py-1 rounded-full text-xs ${
                  transaction.status === 'Successful' 
                    ? 'bg-green-100 text-green-600' 
                    : transaction.status === 'Failed'
                      ? 'bg-red-100 text-red-600'
                      : 'bg-yellow-100 text-yellow-600'
                }`}>
                  {transaction.status}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionHistory;
