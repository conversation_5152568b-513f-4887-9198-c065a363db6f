
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import { Plus, Pencil } from "lucide-react";
import { useToast } from '@/hooks/use-toast';

interface PlanDetails {
  name: string;
  price: number;
  interval: string;
  accessType: string;
  users?: number;
}

interface PaymentFormProps {
  planDetails: PlanDetails;
  onSubscribe?: (paymentDetails: any) => void;
  onCancel?: () => void;
  onChangePlan?: () => void;
}

const PaymentForm: React.FC<PaymentFormProps> = ({ 
  planDetails, 
  onSubscribe, 
  onCancel,
  onChangePlan 
}) => {
  const [fullName, setFullName] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [billingAddress, setBillingAddress] = useState('');
  const [showAddressField, setShowAddressField] = useState(false);
  const { toast } = useToast();

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    
    if (v.length > 2) {
      return `${v.substring(0, 2)} / ${v.substring(2, 4)}`;
    }
    
    return v;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue);
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatExpiryDate(e.target.value);
    setExpiryDate(formattedValue);
  };

  const handleSubmit = () => {
    if (!fullName || !cardNumber || !expiryDate || !cvv) {
      toast({
        title: "Form Incomplete",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const paymentDetails = {
      fullName,
      cardNumber,
      expiryDate,
      cvv,
      billingAddress: billingAddress || undefined,
    };

    if (onSubscribe) {
      onSubscribe(paymentDetails);
    }

    toast({
      title: "Payment Successful",
      description: "Your subscription has been processed",
    });
  };

  return (
    <div className="grid gap-8 md:grid-cols-2">
      {/* Left side - Payment Form */}
      <Card className="border border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="text-2xl font-semibold">Payment details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="fullName">Full name</Label>
            <EnhancedInput
              id="fullName"
              placeholder="John Smith"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cardNumber">Card Details</Label>
            <EnhancedInput
              id="cardNumber"
              placeholder="0000 0000 0000 0000"
              variant="card"
              value={cardNumber}
              onChange={handleCardNumberChange}
              maxLength={19}
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="expiryDate">MM / YY</Label>
              <EnhancedInput
                id="expiryDate"
                placeholder="MM / YY"
                variant="expiry"
                value={expiryDate}
                onChange={handleExpiryChange}
                maxLength={7}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="cvv">CVV</Label>
              <EnhancedInput
                id="cvv"
                placeholder="123"
                variant="cvv"
                value={cvv}
                onChange={(e) => setCvv(e.target.value)}
                maxLength={4}
                type="password"
              />
            </div>
          </div>
          
          {showAddressField ? (
            <div className="space-y-2">
              <Label htmlFor="billingAddress">Billing address</Label>
              <EnhancedInput
                id="billingAddress"
                placeholder="Enter your billing address"
                value={billingAddress}
                onChange={(e) => setBillingAddress(e.target.value)}
              />
            </div>
          ) : (
            <Button 
              variant="ghost" 
              onClick={() => setShowAddressField(true)}
              className="flex items-center gap-2 text-kojaPrimary hover:text-kojaPrimary"
            >
              <Plus className="h-4 w-4" />
              <span>Add address or tax ID</span>
            </Button>
          )}
        </CardContent>
        
        <div className="px-6 py-4 border-t border-gray-100">
          <p className="text-sm text-gray-500">
            By upgrading, you authorize KojaPay to modify your subscription term, and charge, credit, and/or prorate your account accordingly.
          </p>
        </div>
        
        <CardFooter className="flex justify-between border-t border-gray-100 py-4">
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button className="bg-kojaPrimary" onClick={handleSubmit}>
            Subscribe
          </Button>
        </CardFooter>
      </Card>
      
      {/* Right side - Plan Summary */}
      <div className="space-y-8">
        <div className="flex items-center justify-between">
          <h2 className="text-3xl font-bold">{planDetails.name}</h2>
          <Button 
            variant="outline" 
            className="flex items-center gap-2 border-gray-300"
            onClick={onChangePlan}
          >
            <Pencil className="h-4 w-4" />
            Change plan
          </Button>
        </div>
        
        <div className="flex items-baseline gap-4">
          <span className="text-2xl font-bold">${planDetails.price.toFixed(2)}</span>
          <span className="text-gray-600">{planDetails.accessType}</span>
          <span className="text-gray-600">{planDetails.interval}</span>
        </div>
        
        <Card className="border border-gray-200 shadow-sm">
          <CardContent className="p-0">
            <div className="p-4 border-b border-gray-100">
              <select className="w-full p-2 border border-gray-300 rounded-md bg-white">
                <option>Paid monthly</option>
                <option>Paid yearly</option>
              </select>
            </div>
            
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold">{planDetails.accessType}</span>
                    <span className="text-gray-500">× ${planDetails.price.toFixed(2)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-gray-600 mt-1">
                    <span>{planDetails.users || 1} User</span>
                    <Button variant="link" className="p-0 h-auto text-kojaPrimary">
                      Modify seats
                    </Button>
                  </div>
                </div>
                
                <span className="text-xl font-bold">${planDetails.price.toFixed(2)}</span>
              </div>
            </div>
            
            <div className="p-4">
              <div className="flex items-start justify-between">
                <div>
                  <div className="font-semibold">Updates</div>
                  <p className="text-sm text-gray-600 mt-1">
                    Frequent updates for all current and new products.
                  </p>
                </div>
                
                <span className="font-semibold">Free</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-lg font-bold">Due today</span>
            <div className="text-right">
              <div className="text-2xl font-bold">${planDetails.price.toFixed(2)}</div>
              <div className="text-sm text-gray-500">+ applicable tax</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentForm;
