
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, AlertCircle } from 'lucide-react';
import { BankOneApi } from '@/services/bankOneApi';
import AccountVerifier from './AccountVerifier';
import { useAuth } from '@/contexts/AuthContext';

interface TransferFormProps {
  transferType: 'same-bank' | 'other-bank';
  onTransferSuccess: (reference: string, amount: number) => void;
  onTransferError: (error: string) => void;
  onTransferStart: () => void;
}

const TransferForm: React.FC<TransferFormProps> = ({
  transferType,
  onTransferSuccess,
  onTransferError,
  onTransferStart
}) => {
  const [amount, setAmount] = useState('');
  const [narration, setNarration] = useState('');
  const [isTransferring, setIsTransferring] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Verified account details
  const [receiverAccountNumber, setReceiverAccountNumber] = useState('');
  const [receiverAccountName, setReceiverAccountName] = useState('');
  const [receiverBankName, setReceiverBankName] = useState('');
  const [receiverBankCode, setReceiverBankCode] = useState('');
  const [isAccountVerified, setIsAccountVerified] = useState(false);
  
  const { user } = useAuth();
  
  // Get sender's account number from the user context
  const senderAccountNumber = (user as any)?.accountNumber || '';

  const validateAmount = (value: string) => {
    const numAmount = parseFloat(value);
    if (isNaN(numAmount) || numAmount <= 0) {
      setError('Please enter a valid amount');
      return false;
    }
    if (numAmount > 1000000) {
      setError('Amount cannot exceed ₦1,000,000');
      return false;
    }
    setError(null);
    return true;
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAmount(value);
    setError(null);
  };

  const handleNarrationChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNarration(e.target.value);
  };

  const handleAccountVerificationSuccess = (
    accountNumber: string, 
    accountName: string, 
    bankName: string,
    bankCode?: string
  ) => {
    setReceiverAccountNumber(accountNumber);
    setReceiverAccountName(accountName);
    setReceiverBankName(bankName);
    if (bankCode) setReceiverBankCode(bankCode);
    setIsAccountVerified(true);
    setError(null);
  };

  const handleAccountVerificationError = (error: string) => {
    setIsAccountVerified(false);
    setError(error);
  };

  const handleTransfer = async () => {
    if (!isAccountVerified) {
      setError('Please verify the account number first');
      return;
    }

    if (!validateAmount(amount)) {
      return;
    }

    if (!narration) {
      setError('Please provide a narration/description for this transfer');
      return;
    }

    // Begin transfer process
    setIsTransferring(true);
    onTransferStart();
    setError(null);

    try {
      const amountValue = parseFloat(amount);
      let response;

      if (transferType === 'same-bank') {
        // Transfer within the same bank
        response = await BankOneApi.transferSameBank(
          senderAccountNumber,
          receiverAccountNumber,
          amountValue,
          narration
        );
      } else {
        // Transfer to other bank
        response = await BankOneApi.transferOtherBank(
          senderAccountNumber,
          receiverAccountNumber,
          receiverBankCode,
          amountValue,
          narration,
          receiverAccountName
        );
      }

      if (response.success) {
        console.log(`Transfer Successful: Successfully transferred ₦${amountValue.toLocaleString()} to ${receiverAccountName}`);
        
        // Notify parent component of success
        onTransferSuccess(response.transactionReference || '', amountValue);
      } else {
        setError(response.message || 'Transfer failed. Please try again.');
        onTransferError(response.message || 'Transfer failed. Please try again.');
      }
    } catch (error: any) {
      console.error('Transfer error:', error);
      const errorMessage = 'Failed to complete transfer. Please try again.';
      setError(errorMessage);
      onTransferError(errorMessage);
    } finally {
      setIsTransferring(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">
          {transferType === 'same-bank' ? 'Transfer to KojaPay' : 'Transfer to Other Bank'}
        </h3>
        
        <AccountVerifier
          transferType={transferType}
          onVerificationSuccess={handleAccountVerificationSuccess}
          onVerificationError={handleAccountVerificationError}
        />
      </div>

      {isAccountVerified && (
        <div className="space-y-4 p-4 bg-green-50 rounded-md border border-green-200">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-gray-600">Account Name</p>
              <p className="font-medium">{receiverAccountName}</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Bank</p>
              <p className="font-medium">{receiverBankName}</p>
            </div>
          </div>
        </div>
      )}

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="amount">Amount (₦)</Label>
          <EnhancedInput
            id="amount"
            type="number"
            placeholder="Enter amount"
            value={amount}
            onChange={handleAmountChange}
            disabled={isTransferring}
            icon={error && amount ? <AlertCircle className="h-5 w-5 text-red-500" /> : undefined}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="narration">Narration/Description</Label>
          <Textarea
            id="narration"
            placeholder="What's this transfer for?"
            value={narration}
            onChange={handleNarrationChange}
            disabled={isTransferring}
            className="resize-none"
          />
        </div>

        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}

        <Button
          type="button"
          onClick={handleTransfer}
          disabled={isTransferring || !isAccountVerified || !amount || !narration}
          className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
        >
          {isTransferring ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing Transfer...
            </>
          ) : (
            'Continue'
          )}
        </Button>
      </div>
    </div>
  );
};

export default TransferForm;
