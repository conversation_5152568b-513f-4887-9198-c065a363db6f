
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, FileText, Calendar, CheckCircle2, Loader2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { format, subMonths } from 'date-fns';

interface StatementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  accountNumber: string;
}

const StatementDialog = ({ open, onOpenChange, accountNumber }: StatementDialogProps) => {
  const [periodType, setPeriodType] = useState<string>("predefined");
  const [period, setPeriod] = useState<string>("1month");
  const [format, setFormat] = useState<string>("pdf");
  const [downloading, setDownloading] = useState<boolean>(false);
  const [generatedStatements, setGeneratedStatements] = useState<any[]>([
    {
      id: "stmt-1",
      name: "May 2023 Statement",
      date: "2023-05-01",
      size: "245 KB",
      format: "pdf",
    },
    {
      id: "stmt-2",
      name: "June 2023 Statement",
      date: "2023-06-01",
      size: "312 KB",
      format: "pdf",
    }
  ]);

  const handleGenerateStatement = () => {
    setDownloading(true);
    
    // Simulate statement generation
    setTimeout(() => {
      const currentDate = new Date();
      const newStatement = {
        id: `stmt-${generatedStatements.length + 1}-${Math.random().toString(36).substring(2, 9)}`,
        name: `${currentDate.toLocaleString('default', { month: 'long' })} ${currentDate.getFullYear()} Statement`,
        date: currentDate.toISOString().split('T')[0],
        size: `${Math.floor(Math.random() * 500) + 100} KB`,
        format: format
      };
      
      setGeneratedStatements([newStatement, ...generatedStatements]);
      setDownloading(false);
      
      console.log("Statement Generated: Your account statement has been generated successfully");
    }, 2000);
  };

  const handleDownloadStatement = (statementId: string) => {
    console.log("Statement Downloaded: Your statement has been downloaded to your device");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white/95 backdrop-blur-xl border border-[#D3E4FD] shadow-lg rounded-[20px] sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-unica text-gray-900 flex items-center">
            <FileText className="mr-2 text-kojaPrimary" size={20} /> 
            Account Statement
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            Generate and download your account statements
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="generate" className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="generate" className="text-gray-700 data-[state=active]:text-kojaPrimary">Generate New</TabsTrigger>
            <TabsTrigger value="history" className="text-gray-700 data-[state=active]:text-kojaPrimary">Statement History</TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="space-y-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="period" className="text-gray-700">Period</Label>
                  <Select value={period} onValueChange={setPeriod}>
                    <SelectTrigger id="period" className="border-gray-200">
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1month">Last 1 Month</SelectItem>
                      <SelectItem value="3months">Last 3 Months</SelectItem>
                      <SelectItem value="6months">Last 6 Months</SelectItem>
                      <SelectItem value="1year">Last 1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="format" className="text-gray-700">Format</Label>
                  <Select value={format} onValueChange={setFormat}>
                    <SelectTrigger id="format" className="border-gray-200">
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="excel">Excel</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Card className="bg-gray-50 border-gray-100">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <Calendar className="text-kojaPrimary flex-shrink-0 mt-1" size={20} />
                    <div>
                      <h4 className="text-sm font-medium text-gray-800">Statement Period</h4>
                      <p className="text-xs text-gray-600">
                        {period === "1month" && "Last 30 days"}
                        {period === "3months" && "Last 90 days"}
                        {period === "6months" && "Last 180 days"}
                        {period === "1year" && "Last 365 days"}
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        Account: {accountNumber}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
                onClick={handleGenerateStatement}
                disabled={downloading}
              >
                {downloading ? (
                  <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...</>
                ) : (
                  <><FileText className="mr-2 h-4 w-4" /> Generate Statement</>
                )}
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="history">
            <div className="max-h-[300px] overflow-y-auto pr-1">
              {generatedStatements.length > 0 ? (
                <div className="space-y-2">
                  {generatedStatements.map((statement) => (
                    <Card key={statement.id} className="bg-gray-50 border-gray-100">
                      <CardContent className="p-3 flex justify-between items-center">
                        <div className="flex items-center gap-3">
                          <div className="bg-kojaPrimary/10 p-2 rounded">
                            <FileText className="text-kojaPrimary" size={16} />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-800">{statement.name}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(statement.date).toLocaleDateString()} · {statement.size} · {statement.format.toUpperCase()}
                            </p>
                          </div>
                        </div>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 px-2 text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/10"
                          onClick={() => handleDownloadStatement(statement.id)}
                        >
                          <Download size={16} />
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto mb-2 text-gray-300" size={32} />
                  <p className="text-gray-600">No statements generated yet</p>
                  <p className="text-sm text-gray-500">Generate a statement to see it here</p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default StatementDialog;
