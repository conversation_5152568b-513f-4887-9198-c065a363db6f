
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ooter, <PERSON>Header } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Printer, Share2 } from 'lucide-react';
import { Transaction } from '@/components/TransactionTable';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';

interface EnhancedTransactionReceiptProps {
  transaction: Transaction;
  onClose?: () => void;
  onPrint?: () => void;
  onDownload?: () => void;
}

const EnhancedTransactionReceipt: React.FC<EnhancedTransactionReceiptProps> = ({
  transaction,
  onClose,
  onPrint,
  onDownload
}) => {
  // Generate a reference number if not provided
  const reference = transaction.reference || `KP-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
  
  // Format date
  const formattedDate = format(new Date(transaction.date), 'PPP');
  const formattedTime = format(new Date(transaction.date), 'p');
  
  return (
    <Card className="max-w-md mx-auto backdrop-blur-xl bg-white/80 border border-gray-200/50">
      <CardHeader className="text-center pb-3 border-b">
        <div className="flex flex-col items-center mb-1">
          <img 
            src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" 
            alt="KojaPay Logo" 
            className="h-16 w-16 mb-2"
          />
          <h2 className="text-xl font-bold text-kojaPrimary">KojaPay</h2>
          <p className="text-sm text-gray-500">Transaction Receipt</p>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4 pt-4">
        <div className="bg-gray-50/70 p-3 rounded-[20px] border border-gray-100">
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium text-gray-500">Status</span>
            <span className={`text-sm font-semibold ${
              transaction.status === 'success' 
                ? 'text-green-600' 
                : transaction.status === 'pending'
                  ? 'text-amber-600'
                  : 'text-red-600'
            }`}>
              {transaction.status.toUpperCase()}
            </span>
          </div>
          
          <div className="flex justify-between mb-1">
            <span className="text-sm font-medium text-gray-500">Amount</span>
            <span className="text-xl font-bold">
              ₦{transaction.amount.toLocaleString()}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-500">Type</span>
            <span className="text-sm font-medium">
              {transaction.type === 'incoming' ? 'Credit' : 'Debit'}
            </span>
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Transaction ID</span>
            <span className="text-sm font-medium">{transaction.id}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Reference</span>
            <span className="text-sm font-mono">{reference}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Date</span>
            <span className="text-sm">{formattedDate}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Time</span>
            <span className="text-sm">{formattedTime}</span>
          </div>
          
          <Separator className="my-2" />
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Recipient</span>
            <span className="text-sm font-medium">{transaction.recipient}</span>
          </div>
          
          <div className="flex justify-between">
            <span className="text-sm text-gray-500">Description</span>
            <span className="text-sm">{transaction.description || 'Transaction'}</span>
          </div>
          
          {transaction.fee && (
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Fee</span>
              <span className="text-sm">{transaction.fee}</span>
            </div>
          )}
        </div>
        
        <div className="bg-gray-50/70 p-4 rounded-[20px] text-center border border-gray-100 mt-4">
          <p className="text-sm text-gray-500">Thank you for choosing KojaPay!</p>
          <p className="text-xs text-gray-400 mt-1">This receipt was generated on {format(new Date(), 'PPpp')}</p>
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between pt-2">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1 rounded-[20px]"
          onClick={onPrint}
        >
          <Printer className="h-4 w-4" />
          <span className="hidden sm:inline">Print</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1 rounded-[20px]"
          onClick={onDownload}
        >
          <Download className="h-4 w-4" />
          <span className="hidden sm:inline">Download</span>
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-1 rounded-[20px]"
        >
          <Share2 className="h-4 w-4" />
          <span className="hidden sm:inline">Share</span>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default EnhancedTransactionReceipt;
