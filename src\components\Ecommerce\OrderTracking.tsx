import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Package, 
  Truck, 
  CheckCircle2, 
  ShieldCheck, 
  AlertTriangle, 
  Clock, 
  ArrowLeft,
  MessageSquare,
  FileText,
  ExternalLink,
  ThumbsUp,
  ThumbsDown,
  Info
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Order, ecommerceService } from '@/services/ecommerceService';
import { Escrow, EscrowStatus } from '@/types/escrow';
import { Skeleton } from '@/components/ui/skeleton';

const getEscrowDetails = async (escrowId: string): Promise<Escrow> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    id: escrowId,
    title: `Order Escrow ${escrowId}`,
    description: 'Escrow for product purchase',
    amount: 25000,
    buyerId: 'user123',
    sellerId: 'seller456',
    orderId: 'order-123',
    currency: 'NGN',
    buyer: {
      name: 'John Doe',
      email: '<EMAIL>'
    },
    seller: {
      name: 'TechHub',
      email: '<EMAIL>'
    },
    status: 'active' as EscrowStatus,
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
  };
};

interface OrderTrackingProps {
  orderId?: string;
  onClose?: () => void;
}

const OrderTracking: React.FC<OrderTrackingProps> = ({
  orderId,
  onClose,
}) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [escrow, setEscrow] = useState<Escrow | null>(null);
  const [loading, setLoading] = useState(true);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [disputeDialogOpen, setDisputeDialogOpen] = useState(false);
  const [disputeReason, setDisputeReason] = useState('');
  const navigate = useNavigate();
  const { toast } = useToast();
  const [escrowStatus, setEscrowStatus] = useState<EscrowStatus | null>(null);
  const [deliveryStatus, setDeliveryStatus] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number;
    longitude: number;
    address: string;
    timestamp: Date;
  } | null>(null);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setLoading(true);
        if (!orderId) return;

        const mockOrders = await ecommerceService.getCustomerOrders('user123');
        const orderData = mockOrders.find(o => o.id === orderId);
        
        if (orderData) {
          setOrder(orderData);
          
          if (orderData.escrowId) {
            const escrowData = await getEscrowDetails(orderData.escrowId);
            setEscrow(escrowData);
          }
        }
      } catch (error) {
        console.error('Error fetching order details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load order details',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId, toast]);

  const handleConfirmReceipt = async () => {
    try {
      if (!order) return;
      
      const updatedOrder = await ecommerceService.completeOrder(order.id);
      if ('order' in updatedOrder && 'escrowStatus' in updatedOrder) {
        setOrder(updatedOrder.order);
        
        if (escrow) {
          setEscrow({
            ...escrow,
            status: updatedOrder.escrowStatus as EscrowStatus,
            updatedAt: new Date().toISOString()
          });
        }
        
        toast({
          title: 'Order Completed',
          description: 'You have confirmed receipt of your order. Funds have been released to the seller.',
        });
        
        setConfirmDialogOpen(false);
      } else {
        toast({
          title: 'Error',
          description: 'Failed to confirm order receipt',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error confirming order receipt:', error);
      toast({
        title: 'Error',
        description: 'Failed to confirm order receipt',
        variant: 'destructive',
      });
    }
  };

  const handleRaiseDispute = async () => {
    try {
      if (!order || !disputeReason.trim()) {
        toast({
          title: 'Error',
          description: 'Please provide a reason for the dispute',
          variant: 'destructive',
        });
        return;
      }
      
      if (escrow) {
        setEscrow({
          ...escrow,
          status: 'disputed' as EscrowStatus,
          updatedAt: new Date().toISOString(),
          dispute: {
            id: `DISP-${Date.now().toString().substring(7)}`,
            escrowId: escrow.id,
            orderId: escrow.orderId,
            title: `Dispute for Order ${order.id}`,
            reason: disputeReason,
            details: disputeReason,
            status: 'pending',
            resolution: '',
            createdBy: 'buyer',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            messages: []
          }
        });
      }
      
      toast({
        title: 'Dispute Raised',
        description: 'Your dispute has been submitted and is under review.',
      });
      
      setDisputeDialogOpen(false);
      setDisputeReason('');
    } catch (error) {
      console.error('Error raising dispute:', error);
      toast({
        title: 'Error',
        description: 'Failed to raise dispute',
        variant: 'destructive',
      });
    }
  };

  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'Pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'Processing':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Processing</Badge>;
      case 'Shipped':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 hover:bg-purple-100">Shipped</Badge>;
      case 'Delivered':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Delivered</Badge>;
      case 'Cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const renderEscrowStatusBadge = (status: EscrowStatus) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'active':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Active</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Completed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>;
      case 'disputed':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800 hover:bg-orange-100">Disputed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const renderOrderTimeline = () => {
    const timelineSteps = [
      { status: 'Pending', label: 'Order Placed', icon: <Package className="h-5 w-5" />, completed: true },
      { status: 'Processing', label: 'Processing', icon: <Clock className="h-5 w-5" />, completed: ['Processing', 'Shipped', 'Delivered'].includes(order?.status || '') },
      { status: 'Shipped', label: 'Shipped', icon: <Truck className="h-5 w-5" />, completed: ['Shipped', 'Delivered'].includes(order?.status || '') },
      { status: 'Delivered', label: 'Delivered', icon: <CheckCircle2 className="h-5 w-5" />, completed: order?.status === 'Delivered' }
    ];

    return (
      <div className="relative">
        <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
        <div className="space-y-8">
          {timelineSteps.map((step, index) => (
            <div key={index} className="relative pl-10">
              <div className={`absolute left-0 rounded-full p-2 ${step.completed ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'}`}>
                {step.icon}
              </div>
              <div>
                <h4 className="font-medium">{step.label}</h4>
                <p className="text-sm text-muted-foreground">
                  {step.completed 
                    ? `${step.status === order?.status ? 'Current status' : 'Completed'} - ${new Date(order?.updatedAt || '').toLocaleDateString()}` 
                    : 'Pending'}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container py-8 max-w-4xl mx-auto">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="container py-8 max-w-4xl mx-auto text-center">
        <h2 className="text-2xl font-bold mb-4">Order Not Found</h2>
        <p className="text-muted-foreground mb-6">The order you're looking for doesn't exist or has been removed.</p>
        <Button onClick={() => navigate('/account/orders')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Orders
        </Button>
      </div>
    );
  }

  return (
    <div className="container py-8 max-w-4xl mx-auto">
      <Button 
        variant="ghost" 
        className="mb-6" 
        onClick={() => navigate('/account/orders')}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Orders
      </Button>

      <h1 className="text-2xl font-bold mb-6">Order Details</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Order #{order.id}</span>
              {renderStatusBadge(order.status)}
            </CardTitle>
            <CardDescription>
              Placed on {new Date(order.createdAt).toLocaleDateString()} at {new Date(order.createdAt).toLocaleTimeString()}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-medium mb-2">Items</h3>
              <div className="space-y-3">
                {order.products.map((item, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-md">
                    <div className="w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                      <Package className="h-6 w-6 text-gray-500" />
                    </div>
                    <div className="flex-grow">
                      <div className="font-medium">{item.productName}</div>
                      <div className="text-sm text-muted-foreground">Qty: {item.quantity}</div>
                    </div>
                    <div className="font-bold">
                      ₦{item.totalPrice.toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">Shipping Address</h3>
                <p className="text-sm text-muted-foreground">
                  {order.shippingAddress}
                </p>
              </div>
              <div>
                <h3 className="font-medium mb-2">Payment Information</h3>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Payment Method:</span>
                    <span>KojaPay Wallet</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Payment Status:</span>
                    <Badge variant={order.paymentStatus === 'Completed' ? 'default' : 'destructive'}>
                      {order.paymentStatus}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Subtotal:</span>
                <span>₦{(order.totalAmount * 0.95).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Shipping:</span>
                <span>₦{(order.totalAmount * 0.03).toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Escrow Fee:</span>
                <span>₦{(order.totalAmount * 0.02).toLocaleString()}</span>
              </div>
              <Separator className="my-2" />
              <div className="flex justify-between font-bold">
                <span>Total:</span>
                <span>₦{order.totalAmount.toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            {order.status === 'Shipped' && (
              <Button 
                variant="default"
                className="bg-green-600 hover:bg-green-700"
                onClick={() => setConfirmDialogOpen(true)}
              >
                <ThumbsUp className="mr-2 h-4 w-4" />
                Confirm Receipt
              </Button>
            )}
            
            {['Shipped', 'Delivered'].includes(order.status) && escrow?.status === 'active' && (
              <Button 
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => setDisputeDialogOpen(true)}
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                Report Issue
              </Button>
            )}
          </CardFooter>
        </Card>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Order Status</CardTitle>
            </CardHeader>
            <CardContent>
              {renderOrderTimeline()}
            </CardContent>
          </Card>

          {escrow && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <ShieldCheck className="mr-2 h-5 w-5 text-[#fde314]" />
                  Escrow Protection
                </CardTitle>
                <CardDescription>
                  ID: {escrow.id}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  {renderEscrowStatusBadge(escrow.status)}
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Amount:</span>
                  <span className="font-medium">₦{escrow.amount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Created:</span>
                  <span>{new Date(escrow.createdAt).toLocaleDateString()}</span>
                </div>
                <Separator />
                <div className="text-sm text-muted-foreground">
                  {escrow.status === 'active' && (
                    <div className="bg-blue-50 p-3 rounded-md text-blue-800 flex items-start gap-2">
                      <Info className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <p>
                        Your payment is held securely in escrow. Once you receive the item and confirm it's as described, the funds will be released to the seller.
                      </p>
                    </div>
                  )}
                  {escrow.status === 'completed' && (
                    <div className="bg-green-50 p-3 rounded-md text-green-800 flex items-start gap-2">
                      <CheckCircle2 className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <p>
                        You have confirmed receipt of this order and the funds have been released to the seller. Thank you for using KojaPay Escrow!
                      </p>
                    </div>
                  )}
                  {escrow.status === 'disputed' && (
                    <div className="bg-orange-50 p-3 rounded-md text-orange-800 flex items-start gap-2">
                      <AlertTriangle className="h-5 w-5 mt-0.5 flex-shrink-0" />
                      <p>
                        A dispute has been raised for this transaction. Our team is reviewing the case and will contact you soon.
                      </p>
                    </div>
                  )}
                </div>
                {escrow.status === 'disputed' && (
                  <Button variant="outline" className="w-full" onClick={() => navigate(`/escrow/dispute/${escrow.dispute?.id}`)}>
                    <MessageSquare className="mr-2 h-4 w-4" />
                    View Dispute Details
                  </Button>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" onClick={() => navigate(`/escrow/${escrow.id}`)}>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View Full Escrow Details
                </Button>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>

      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Order Receipt</DialogTitle>
            <DialogDescription>
              By confirming receipt, you acknowledge that you have received the order in good condition and authorize the release of funds from escrow to the seller.
            </DialogDescription>
          </DialogHeader>
          <div className="bg-yellow-50 p-4 rounded-md border border-yellow-200 my-4">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <p className="text-sm text-yellow-800">
                This action cannot be undone. Only confirm receipt if you are satisfied with your order.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
            <Button 
              className="bg-green-600 hover:bg-green-700"
              onClick={handleConfirmReceipt}
            >
              Confirm Receipt
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={disputeDialogOpen} onOpenChange={setDisputeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Report an Issue</DialogTitle>
            <DialogDescription>
              If you've encountered a problem with your order, please provide details below. This will initiate a dispute process through our escrow system.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 my-4">
            <div>
              <label htmlFor="dispute-reason" className="text-sm font-medium block mb-2">
                Reason for Dispute
              </label>
              <Textarea 
                id="dispute-reason"
                placeholder="Please describe the issue in detail..."
                value={disputeReason}
                onChange={(e) => setDisputeReason(e.target.value)}
                className="min-h-[120px]"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDisputeDialogOpen(false)}>Cancel</Button>
            <Button 
              variant="destructive"
              onClick={handleRaiseDispute}
              disabled={!disputeReason.trim()}
            >
              Submit Dispute
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default OrderTracking;
