
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%; /* Softer background with slight blue tint */
    --foreground: 215 25% 27%; /* Softer dark text */
    --card: 0 0% 100%;
    --card-foreground: 215 25% 27%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 27%;
    --primary: 232 83% 40%;
    --primary-foreground: 210 40% 98%;
    --secondary: 51 98% 53%;
    --secondary-foreground: 215 25% 27%;
    --muted: 210 40% 96%; /* Slightly less muted */
    --muted-foreground: 215 16% 47%; /* Softer muted text */
    --accent: 210 40% 96%;
    --accent-foreground: 215 25% 27%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 220 13% 91%; /* Softer borders */
    --input: 220 13% 91%;
    --ring: 232 83% 40%;
    --radius: 20px;
    --sidebar-background: 210 20% 98%; /* Match main background */
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 232 83% 40%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 215 25% 27%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 232 83% 40%;
    --koja-yellow: 50 98% 53%;
  }

  * {
    @apply border-border font-poppins;
  }

  body {
    @apply bg-background text-foreground font-poppins;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-poppins font-bold tracking-tight;
  }

  h1 {
    @apply text-2xl md:text-3xl font-poppins font-bold;
  }

  h2 {
    @apply text-xl md:text-2xl font-poppins font-bold;
  }

  h3 {
    @apply text-lg md:text-xl font-poppins font-bold;
  }

  .caption, caption, figcaption, .text-caption {
    @apply font-poppins font-bold;
  }

  .text-heading {
    @apply font-poppins font-bold;
  }

  p, span, a, button, input, label, div {
    @apply font-poppins;
  }
  
  .nav-item {
    @apply font-poppins;
  }

  .sidebar-text {
    @apply font-poppins;
  }

  .sidebar-heading {
    @apply font-poppins font-bold;
  }
  
  /* Business theme styles with futuristic design */
  .business-theme {
    @apply bg-gradient-to-br from-kojaDark to-black text-white shadow-lg;
  }
  
  .business-theme h1, 
  .business-theme h2, 
  .business-theme h3, 
  .business-theme h4, 
  .business-theme h5, 
  .business-theme h6 {
    @apply text-kojaYellow font-poppins font-bold;
  }
  
  .business-theme .card {
    @apply bg-white/10 backdrop-blur-xl border-kojaYellow/20 shadow-lg hover:shadow-xl transition-all duration-300;
  }
  
  /* Dark theme styles with neon accents */
  .dark-theme {
    @apply bg-[#001235] text-white;
  }
  
  .dark-theme h1,
  .dark-theme h2, 
  .dark-theme h3, 
  .dark-theme h4, 
  .dark-theme h5, 
  .dark-theme h6 {
    @apply text-white font-poppins font-bold;
  }
  
  /* Custom glassmorphism styles */
  .glass-morphism {
    @apply backdrop-blur-xl bg-white/5 border border-white/10 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.3)];
  }
  
  .neo-blur {
    @apply backdrop-blur-2xl bg-black/40 border border-white/10;
  }
  
  /* Text gradient effects */
  .text-gradient {
    @apply bg-gradient-to-br from-white via-white/90 to-white/70 bg-clip-text text-transparent;
  }
  
  .text-gradient-yellow {
    @apply bg-gradient-to-br from-kojaYellow via-amber-400 to-amber-300 bg-clip-text text-transparent;
  }
  
  /* Enhanced card styles */
  .enhanced-card {
    @apply relative overflow-hidden backdrop-blur-xl transition-all duration-300 border border-white/10 rounded-[24px];
  }
  
  .enhanced-card-dark {
    @apply bg-kojaDark/50 text-white;
  }
  
  .enhanced-card-light {
    @apply bg-white/80 text-kojaDark;
  }
  
  /* Button animations */
  .animated-button {
    @apply relative overflow-hidden transition-all duration-300;
  }
  
  .animated-button::after {
    @apply content-[''] absolute top-0 left-[150%] w-[40%] h-full bg-white/30 transform skew-x-[-20deg] transition-all duration-700;
  }
  
  .animated-button:hover::after {
    @apply left-[-50%];
  }
  
  /* Cyberpunk theme - used for login pages */
  .cyberpunk-theme {
    @apply bg-[#0A0E17] text-white;
  }
  
  .cyberpunk-theme h1, 
  .cyberpunk-theme h2, 
  .cyberpunk-theme h3, 
  .cyberpunk-theme h4, 
  .cyberpunk-theme h5, 
  .cyberpunk-theme h6 {
    @apply text-white font-poppins font-bold;
  }
  
  .cyberpunk-card {
    @apply bg-black/20 backdrop-blur-xl border border-white/10 shadow-lg;
  }
  
  .cyberpunk-input {
    @apply bg-black/40 backdrop-blur-md border border-white/10 text-white placeholder:text-white/50;
  }
  
  .cyberpunk-button {
    @apply relative overflow-hidden bg-gradient-to-r from-indigo-500 via-violet-500 to-purple-600 text-white;
  }
  
  /* Futuristic glassmorphism */
  .future-glass {
    @apply backdrop-blur-xl bg-white/5 border border-white/10 shadow-[0_8px_32px_rgba(0,0,0,0.3)];
  }
  
  .future-card {
    @apply relative overflow-hidden bg-black/20 backdrop-blur-xl border border-white/10 rounded-3xl shadow-[0_8px_32px_rgba(0,0,0,0.3)];
  }
  
  .future-glow {
    @apply absolute -inset-3 bg-gradient-to-r from-cyan-500/30 via-blue-500/20 to-purple-600/30 rounded-3xl blur-xl opacity-80;
  }
  
  .future-border {
    @apply absolute inset-0 p-px overflow-hidden rounded-3xl pointer-events-none;
  }
  
  .future-border-inner {
    @apply absolute inset-0 bg-gradient-to-r from-cyan-500 via-blue-500 to-purple-600 animate-slow-spin;
  }
  
  /* Futuristic text styles */
  .future-text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600;
  }
  
  .future-heading {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 font-poppins;
  }
  
  .business-future-heading {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 via-violet-500 to-purple-600 font-poppins;
  }
}
