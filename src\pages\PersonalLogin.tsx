
import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { 
  Eye, 
  EyeOff, 
  LogIn, 
  Sparkles, 
  Shield, 
  Fingerprint, 
  Lock, 
  User, 
  UserPlus 
} from 'lucide-react';
import { AuthButton } from '@/components/ui/auth-button';
import { useIsMobile } from '@/hooks/use-mobile';

const PersonalLogin = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [pin, setPin] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const { login, error } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  
  const [animateCard, setAnimateCard] = useState(false);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimateCard(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phoneNumber || !pin) {
      toast({
        title: 'Validation Error',
        description: 'Please enter both phone number and PIN',
        variant: 'destructive'
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      await login(phoneNumber, pin, 'personal');
      toast({
        title: 'Login Successful',
        description: 'Welcome to KojaPay Personal Banking'
      });
      navigate('/wallet');
    } catch (err) {
      // Error is handled by auth context
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden bg-[#0A0E17] flex items-center justify-center px-4 py-6">
      <div className="absolute inset-0 z-0 opacity-20">
        <div className="h-full w-full grid grid-cols-12 gap-4">
          {Array.from({ length: 12 }).map((_, colIndex) => (
            <div key={`col-${colIndex}`} className="h-full">
              {Array.from({ length: 12 }).map((_, rowIndex) => (
                <div 
                  key={`cell-${colIndex}-${rowIndex}`} 
                  className="h-16 border border-[#1AFFFF]/10"
                />
              ))}
            </div>
          ))}
        </div>
      </div>
      
      <div className="container relative z-10 max-w-6xl mx-auto px-4 py-6 flex flex-col md:flex-row items-center md:items-stretch gap-6">
        <div className="hidden md:flex flex-col justify-between rounded-3xl bg-[#1231B8] text-white p-8 w-full max-w-sm">
          <div>
            <div className="mb-6">
              <img 
                src="/lovable-uploads/6d533269-1146-41c3-ad78-0141d73073a6.png" 
                alt="KojaPay Logo"
                className="h-12 w-12"
              />
            </div>
            <h1 className="text-2xl font-unica mb-4">Welcome Back!</h1>
            <p className="text-white/80 mb-6">
              Access your account to experience next-generation digital banking with enhanced security and innovative features.
            </p>
          </div>
          
          <div className="mt-auto">
            <AuthButton 
              variant="outline"
              size="lg"
              onClick={() => navigate('/create-account')}
              icon={<UserPlus size={18} />}
            >
              SIGN UP
            </AuthButton>
            <p className="text-xs text-white/60 text-center mt-4">
              Don't have an account? Create one today and enjoy seamless banking.
            </p>
          </div>
        </div>
        
        <div 
          className={`w-full max-w-sm md:max-w-md transition-all duration-700 transform ${
            animateCard ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
          }`}
        >
          <Card className="bg-white/90 backdrop-blur-xl border border-white/20 rounded-3xl overflow-hidden shadow-xl">
            <div className="p-6 md:p-8">
              <div className="mb-6 flex items-center justify-between">
                <div>
                  <h2 className="text-xl md:text-2xl font-unica text-[#1231B8]">
                    Login
                  </h2>
                  <p className="text-sm text-gray-500">
                    Sign in to your account
                  </p>
                </div>
                <div className="md:hidden">
                  <img 
                    src="/lovable-uploads/6d533269-1146-41c3-ad78-0141d73073a6.png" 
                    alt="KojaPay Logo"
                    className="h-10 w-10"
                  />
                </div>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-red-400 rounded-xl text-sm animate-fade-in">
                  {error}
                </div>
              )}
              
              <form onSubmit={handleLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber" className="text-sm font-medium text-gray-700">
                    Phone Number
                  </Label>
                  <div className="relative">
                    <div className="relative flex items-center bg-white/70 backdrop-blur-md border border-gray-200 rounded-xl overflow-hidden">
                      <div className="flex items-center justify-center w-10 h-10 text-[#1231B8]">
                        <User size={18} />
                      </div>
                      <Input 
                        id="phoneNumber" 
                        type="tel" 
                        placeholder="Enter your phone number" 
                        value={phoneNumber} 
                        onChange={e => setPhoneNumber(e.target.value)} 
                        className="bg-transparent border-0 focus:ring-0 h-10"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="pin" className="text-sm font-medium text-gray-700">
                      Security PIN
                    </Label>
                    <Link to="/forgot-pin" className="text-xs text-[#1231B8] hover:text-[#1231B8]/80 transition-all">
                      Forgot PIN?
                    </Link>
                  </div>
                  <div className="relative">
                    <div className="relative flex items-center bg-white/70 backdrop-blur-md border border-gray-200 rounded-xl overflow-hidden">
                      <div className="flex items-center justify-center w-10 h-10 text-[#1231B8]">
                        <Lock size={18} />
                      </div>
                      <Input 
                        id="pin" 
                        type={showPin ? 'text' : 'password'} 
                        placeholder="Enter your 6-digit PIN" 
                        value={pin} 
                        onChange={e => setPin(e.target.value)} 
                        maxLength={6} 
                        className="bg-transparent border-0 focus:ring-0 h-10 w-full" 
                      />
                      <button 
                        type="button" 
                        className="absolute right-3 text-gray-500 hover:text-[#1231B8] transition-colors" 
                        onClick={() => setShowPin(!showPin)}
                      >
                        {showPin ? <EyeOff size={18} /> : <Eye size={18} />}
                      </button>
                    </div>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    id="remember-me"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-[#1231B8]"
                    checked={rememberMe}
                    onChange={() => setRememberMe(!rememberMe)}
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-xs text-gray-600">
                    Remember me
                  </label>
                </div>

                <AuthButton 
                  type="submit" 
                  variant="primary"
                  size="lg"
                  loading={isLoading}
                  icon={<LogIn size={18} />}
                >
                  {isLoading ? "Authenticating..." : "Login"}
                </AuthButton>
                
                <div className="mt-6 text-center md:hidden">
                  <p className="text-sm text-gray-600 mb-2">
                    Don't have an account?
                  </p>
                  <AuthButton 
                    variant="secondary"
                    size="lg"
                    onClick={() => navigate('/create-account')}
                    icon={<UserPlus size={18} />}
                  >
                    SIGN UP
                  </AuthButton>
                </div>
              </form>

              <div className="mt-6 hidden md:block">
                <p className="text-xs text-gray-500">
                  Need a business account?{' '}
                  <Link to="/business-login" className="text-[#1231B8] hover:text-[#1231B8]/80 transition-all">
                    Login as Business
                  </Link>
                </p>
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-100">
                <h3 className="text-gray-500 text-xs uppercase tracking-wider mb-2 text-center">Protected By</h3>
                <div className="flex justify-center gap-4">
                  <div className="flex flex-col items-center">
                    <Shield className="h-4 w-4 text-[#1231B8]" />
                    <span className="text-xs text-gray-500 mt-1">SSL</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <Fingerprint className="h-4 w-4 text-[#1231B8]" />
                    <span className="text-xs text-gray-500 mt-1">Biometrics</span>
                  </div>
                  <div className="flex flex-col items-center">
                    <Sparkles className="h-4 w-4 text-[#FDE314]" />
                    <span className="text-xs text-gray-500 mt-1">AI Security</span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PersonalLogin;
