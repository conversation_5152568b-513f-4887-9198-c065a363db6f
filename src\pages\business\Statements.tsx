
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { FileCog, Download, File, Printer, Eye, Mail, Calendar as CalendarIcon, FileText, Filter, ArrowDownToLine } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface Statement {
  id: string;
  period: string;
  dateGenerated: string;
  type: 'monthly' | 'quarterly' | 'annual' | 'custom';
  status: 'available' | 'processing' | 'failed';
  fileSize?: string;
}

const BusinessStatements = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('available');
  const [date, setDate] = useState<Date>();
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [statementType, setStatementType] = useState('monthly');
  const [fileFormat, setFileFormat] = useState('pdf');
  
  // Sample statements data
  const statements: Statement[] = [
    {
      id: 'STMT-2023-12',
      period: 'December 2023',
      dateGenerated: '2024-01-05',
      type: 'monthly',
      status: 'available',
      fileSize: '1.2 MB'
    },
    {
      id: 'STMT-2023-11',
      period: 'November 2023',
      dateGenerated: '2023-12-05',
      type: 'monthly',
      status: 'available',
      fileSize: '0.9 MB'
    },
    {
      id: 'STMT-2023-Q4',
      period: 'Q4 2023',
      dateGenerated: '2024-01-10',
      type: 'quarterly',
      status: 'available',
      fileSize: '3.4 MB'
    },
    {
      id: 'STMT-CUSTOM-1',
      period: 'Oct 15 - Nov 20, 2023',
      dateGenerated: '2023-11-22',
      type: 'custom',
      status: 'available',
      fileSize: '1.8 MB'
    },
    {
      id: 'STMT-2023-01',
      period: 'January 2024',
      dateGenerated: 'In progress',
      type: 'monthly',
      status: 'processing'
    }
  ];
  
  const filteredStatements = statements.filter(statement => {
    if (activeTab === 'all') return true;
    return statement.status === activeTab;
  });
  
  const handleDownload = (statementId: string) => {
    toast({
      title: "Statement Downloaded",
      description: `Statement ${statementId} has been downloaded`,
      variant: "success"
    });
  };
  
  const handlePrint = (statementId: string) => {
    toast({
      title: "Printing Statement",
      description: `Statement ${statementId} has been sent to printer`,
      variant: "success"
    });
  };
  
  const handleEmail = (statementId: string) => {
    toast({
      title: "Statement Emailed",
      description: `Statement ${statementId} has been emailed to your registered address`,
      variant: "success"
    });
  };
  
  const handleGenerateStatement = (e: React.FormEvent) => {
    e.preventDefault();
    
    let description = '';
    if (statementType === 'custom' && startDate && endDate) {
      description = `Custom statement from ${format(startDate, 'MMM dd, yyyy')} to ${format(endDate, 'MMM dd, yyyy')}`;
    } else if (date) {
      const periodText = statementType === 'monthly' 
        ? format(date, 'MMMM yyyy')
        : statementType === 'quarterly'
          ? `Q${Math.ceil(date.getMonth() / 3)} ${date.getFullYear()}`
          : `Annual ${date.getFullYear()}`;
      description = `${periodText} statement in ${fileFormat.toUpperCase()} format`;
    } else {
      description = `Statement request submitted`;
    }
    
    toast({
      title: "Statement Generation Initiated",
      description,
      variant: "success"
    });
  };
  
  const getStatusBadge = (status: Statement['status']) => {
    switch (status) {
      case 'available':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Available</Badge>;
      case 'processing':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Processing</Badge>;
      case 'failed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>;
    }
  };
  
  return (
    <BusinessLayout pageTitle="Business Statements">
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight font-unica">Account Statements</h2>
            <p className="text-muted-foreground">Access and generate your business account statements</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Generate Statement Form */}
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle className="font-unica">Generate Statement</CardTitle>
              <CardDescription>Create a new account statement</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleGenerateStatement} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="statementType">Statement Type</Label>
                  <Select value={statementType} onValueChange={setStatementType}>
                    <SelectTrigger id="statementType">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {statementType !== 'custom' ? (
                  <div className="space-y-2">
                    <Label>Select Period</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !date && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {date ? format(date, statementType === 'annual' ? 'yyyy' : 'MMMM yyyy') : <span>Select date</span>}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={date}
                          onSelect={setDate}
                          initialFocus
                          disabled={(date) => date > new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>Start Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !startDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {startDate ? format(startDate, 'PPP') : <span>Select start date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={startDate}
                            onSelect={setStartDate}
                            initialFocus
                            disabled={(date) => date > new Date()}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    
                    <div className="space-y-2">
                      <Label>End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !endDate && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {endDate ? format(endDate, 'PPP') : <span>Select end date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={endDate}
                            onSelect={setEndDate}
                            initialFocus
                            disabled={(date) => date > new Date() || (startDate ? date < startDate : false)}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label htmlFor="fileFormat">File Format</Label>
                  <Select value={fileFormat} onValueChange={setFileFormat}>
                    <SelectTrigger id="fileFormat">
                      <SelectValue placeholder="Select format" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pdf">PDF</SelectItem>
                      <SelectItem value="csv">CSV</SelectItem>
                      <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <Button type="submit" className="w-full mt-4">
                  <FileCog className="mr-2 h-4 w-4" />
                  Generate Statement
                </Button>
              </form>
            </CardContent>
          </Card>
          
          {/* Statements List */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="font-unica">Available Statements</CardTitle>
              <CardDescription>Access your previously generated statements</CardDescription>
              
              <Tabs defaultValue="available" value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
                <TabsList>
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="available">Available</TabsTrigger>
                  <TabsTrigger value="processing">Processing</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent>
              {filteredStatements.length > 0 ? (
                <div className="space-y-4">
                  {filteredStatements.map((statement) => (
                    <div key={statement.id} className="flex flex-col sm:flex-row justify-between p-4 border rounded-lg">
                      <div className="flex items-start gap-3">
                        <div className="rounded-lg bg-gray-100 p-2">
                          <FileText className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{statement.period} Statement</h3>
                            {getStatusBadge(statement.status)}
                          </div>
                          <p className="text-sm text-muted-foreground">ID: {statement.id}</p>
                          <p className="text-sm text-muted-foreground">
                            Generated: {statement.status === 'processing' ? 'In progress' : new Date(statement.dateGenerated).toLocaleDateString()}
                            {statement.fileSize && ` • ${statement.fileSize}`}
                          </p>
                        </div>
                      </div>
                      
                      {statement.status === 'available' && (
                        <div className="flex mt-4 sm:mt-0 gap-2">
                          <Button variant="outline" size="sm" onClick={() => handleDownload(statement.id)}>
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handlePrint(statement.id)}>
                            <Printer className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleEmail(statement.id)}>
                            <Mail className="h-4 w-4" />
                          </Button>
                          <Button size="sm">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-10">
                  <File className="h-10 w-10 text-muted-foreground opacity-50 mx-auto mb-3" />
                  <h3 className="text-lg font-medium mb-1">No Statements Found</h3>
                  <p className="text-muted-foreground mb-4">There are no statements matching your criteria</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* Download Instructions Card */}
        <Card>
          <CardHeader>
            <CardTitle className="font-unica">Statement Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-gray-100 p-4 mb-4">
                  <ArrowDownToLine className="h-6 w-6 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2">Downloads</h3>
                <p className="text-sm text-muted-foreground">Download your statements in PDF, CSV or Excel format for record keeping</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-gray-100 p-4 mb-4">
                  <CalendarIcon className="h-6 w-6 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2">Custom Periods</h3>
                <p className="text-sm text-muted-foreground">Generate statements for any date range within the last 7 years</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-gray-100 p-4 mb-4">
                  <Mail className="h-6 w-6 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2">Email Service</h3>
                <p className="text-sm text-muted-foreground">Have your statements emailed directly to you or your accounting team</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </BusinessLayout>
  );
};

export default BusinessStatements;
