-- Seed Banks Table
INSERT INTO "banks" ("id", "bankCode", "bankName", "logoUrl", "isActive", "sortCode", "createdAt", "updatedAt")
VALUES
  ('e1a70d4f-c201-4513-9bd5-ff1d9f9ff8a3', '035', 'Wema Bank', 'https://kojapay.com/banklogos/wema.png', true, '000035', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('8e2baf10-37e3-4aad-b9c5-c3b5fe10c6e8', '058', 'GTBank', 'https://kojapay.com/banklogos/gtbank.png', true, '000058', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('f5e29e10-9a4d-4e39-9c9b-2c27a7fe95f0', '044', 'Access Bank', 'https://kojapay.com/banklogos/access.png', true, '000044', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('96430f0f-e1a2-4a36-ba2f-c8c76f22ff61', '033', 'UBA', 'https://kojapay.com/banklogos/uba.png', true, '000033', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('83d06f8a-5f3b-43e5-9f57-8c7d4a0d7b8c', '057', 'Zenith Bank', 'https://kojapay.com/banklogos/zenith.png', true, '000057', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('e3b5c210-a6fa-4e0c-bc8a-ee4c2d21f3e2', '050', 'Ecobank', 'https://kojapay.com/banklogos/ecobank.png', true, '000050', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('fa982b80-3f5c-4d32-a2ed-6e2f6b4ec41c', '063', 'Diamond Bank', 'https://kojapay.com/banklogos/diamond.png', true, '000063', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('c5dfa88d-8a32-4e78-b13a-6a45c99112e3', '070', 'Fidelity Bank', 'https://kojapay.com/banklogos/fidelity.png', true, '000070', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('b7a3d2e9-6e7f-4c8a-9a1b-8c9d0e5f3b2a', '214', 'First City Monument Bank', 'https://kojapay.com/banklogos/fcmb.png', true, '000214', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('a9b8c7d6-e5f4-3g2h-1i0j-k9l8m7n6o5p4', '301', 'KojaBank', 'https://kojapay.com/banklogos/kojabank.png', true, '000301', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Seed Settings Table with default settings
INSERT INTO "settings" ("id", "key", "value", "description", "isPublic", "createdAt", "updatedAt")
VALUES
  ('d1e2f3a4-b5c6-4d7e-8f9a-0b1c2d3e4f5a', 'transfer_limit_per_day', '1000000', 'Maximum amount a user can transfer in a day', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('e5f6g7h8-i9j0-4k5l-6m7n-8o9p0q1r2s3t', 'transfer_fee_same_bank', '0', 'Fee for transfers within the same bank', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('a1b2c3d4-e5f6-4g7h-8i9j-0k1l2m3n4o5p', 'transfer_fee_other_bank', '25', 'Fee for transfers to other banks', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('p5q6r7s8-t9u0-4v1w-2x3y-4z5a6b7c8d9e', 'app_version', '1.0.0', 'Current application version', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('g5h6i7j8-k9l0-4m5n-6o7p-8q9r0s1t2u3v', 'maintenance_mode', 'false', 'Whether the system is in maintenance mode', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('u5v6w7x8-y9z0-4a5b-6c7d-8e9f0g1h2i3j', 'service_status', 'online', 'Current status of the service', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('m5n6o7p8-q9r0-4s5t-6u7v-8w9x0y1z2a3b', 'bankone_api_status', 'online', 'Current status of the BankOne API integration', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('c5d6e7f8-g9h0-4i5j-6k7l-8m9n0o1p2q3r', 'max_loan_amount', '500000', 'Maximum loan amount a user can request', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('i5j6k7l8-m9n0-4o5p-6q7r-8s9t0u1v2w3x', 'min_loan_amount', '5000', 'Minimum loan amount a user can request', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
  ('r5s6t7u8-v9w0-4x5y-6z7a-8b9c0d1e2f3g', 'loan_interest_rate', '15', 'Interest rate for loans (percentage)', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Create Admin User
INSERT INTO "users" ("id", "email", "passwordHash", "fullName", "phone", "accountNumber", "bvn", "role", "status", "createdAt", "updatedAt")
VALUES
  ('a0b1c2d3-e4f5-6g7h-8i9j-0k1l2m3n4o5p', '<EMAIL>', '$2a$12$k8Y1E/mHlGES7lJ2ZbH7vufQPXdF/FpA0v9p6IUfUA9.bJxYuR5Ky', 'KojaPay Admin', '+*************', '**********', '**********1', 'ADMIN', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Create a test user account
INSERT INTO "users" ("id", "email", "passwordHash", "fullName", "phone", "accountNumber", "bvn", "role", "status", "createdAt", "updatedAt")
VALUES
  ('z9y8x7w6-v5u4-3t2s-1r0q-9p8o7n6m5l4k', '<EMAIL>', '$2a$12$k8Y1E/mHlGES7lJ2ZbH7vufQPXdF/FpA0v9p6IUfUA9.bJxYuR5Ky', 'Test User', '+*************', '**********', '**********9', 'USER', 'ACTIVE', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Create accounts for the test user
INSERT INTO "accounts" ("id", "accountNumber", "accountName", "accountType", "balance", "userId", "createdAt", "updatedAt")
VALUES
  ('f1e2d3c4-b5a6-7g8h-9i0j-1k2l3m4n5o6p', '**********', 'Test User', 'SAVINGS', 50000.00, 'z9y8x7w6-v5u4-3t2s-1r0q-9p8o7n6m5l4k', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Create some sample transactions
INSERT INTO "transactions" ("id", "transactionReference", "transactionType", "amount", "narration", "status", "senderAccount", "senderName", "receiverAccount", "receiverName", "transactionDate", "fee", "balanceAfter", "accountId", "userId", "createdAt", "updatedAt")
VALUES
  ('a1b2c3d4-e5f6-g7h8-i9j0-k1l2m3n4o5p6', 'TRF-********', 'CREDIT', 10000.00, 'Salary Payment', 'SUCCESSFUL', '**********', 'ACME Corp', '**********', 'Test User', CURRENT_TIMESTAMP - INTERVAL '5 days', 0.00, 60000.00, 'f1e2d3c4-b5a6-7g8h-9i0j-1k2l3m4n5o6p', 'z9y8x7w6-v5u4-3t2s-1r0q-9p8o7n6m5l4k', CURRENT_TIMESTAMP - INTERVAL '5 days', CURRENT_TIMESTAMP - INTERVAL '5 days'),
  ('q7r8s9t0-u1v2-w3x4-y5z6-a7b8c9d0e1f2', 'TRF-********', 'DEBIT', 5000.00, 'Internet Subscription', 'SUCCESSFUL', '**********', 'Test User', '**********', 'Internet Provider Ltd', CURRENT_TIMESTAMP - INTERVAL '3 days', 25.00, 54975.00, 'f1e2d3c4-b5a6-7g8h-9i0j-1k2l3m4n5o6p', 'z9y8x7w6-v5u4-3t2s-1r0q-9p8o7n6m5l4k', CURRENT_TIMESTAMP - INTERVAL '3 days', CURRENT_TIMESTAMP - INTERVAL '3 days'),
  ('g3h4i5j6-k7l8-m9n0-o1p2-q3r4s5t6u7v8', 'TRF-********', 'DEBIT', 2000.00, 'Grocery Shopping', 'SUCCESSFUL', '**********', 'Test User', '**********', 'Grocery Store Ltd', CURRENT_TIMESTAMP - INTERVAL '1 day', 25.00, 52950.00, 'f1e2d3c4-b5a6-7g8h-9i0j-1k2l3m4n5o6p', 'z9y8x7w6-v5u4-3t2s-1r0q-9p8o7n6m5l4k', CURRENT_TIMESTAMP - INTERVAL '1 day', CURRENT_TIMESTAMP - INTERVAL '1 day'),
  ('w9x0y1z2-a3b4-c5d6-e7f8-g9h0i1j2k3l4', 'TRF-45678901', 'CREDIT', 3000.00, 'Refund from Store', 'SUCCESSFUL', '**********', 'Online Store Ltd', '**********', 'Test User', CURRENT_TIMESTAMP - INTERVAL '6 hours', 0.00, 55950.00, 'f1e2d3c4-b5a6-7g8h-9i0j-1k2l3m4n5o6p', 'z9y8x7w6-v5u4-3t2s-1r0q-9p8o7n6m5l4k', CURRENT_TIMESTAMP - INTERVAL '6 hours', CURRENT_TIMESTAMP - INTERVAL '6 hours');
