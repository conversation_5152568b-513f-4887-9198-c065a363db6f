import React, { useState } from 'react';
import { useN<PERSON><PERSON>, Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Helmet } from 'react-helmet-async';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { User, Phone, Lock, Calendar, Shield, Check, ArrowLeft, ArrowRight, Eye, EyeOff, PartyPopper, Mail } from 'lucide-react';
import OTPVerification from '@/components/OTPVerification';
import { toast } from 'sonner';
import axios from 'axios';

const CreateAccount = () => {
  const [currentPhase, setCurrentPhase] = useState(1);
  const [showPin, setShowPin] = useState(false);
  const [showOTP, setShowOTP] = useState(false);
  const [showCongrats, setShowCongrats] = useState(false);
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    phone: '',
    pin: '',
    dateOfBirth: '',
    address: '',
    bvn: '',
    nin: '',
    email: '',
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const validatePhase1 = () => {
    const { firstName, lastName, phone, pin } = formData;
    
    if (!firstName || !lastName || !phone || !pin) {
      toast.error('Please fill in all required fields');
      return false;
    }
    
    const phoneRegex = /^(\+234|0)[0-9]{10}$/;
    if (!phoneRegex.test(phone)) {
      toast.error('Please enter a valid Nigerian phone number');
      return false;
    }
    
    if (pin.length !== 6 || !/^\d+$/.test(pin)) {
      toast.error('PIN must be 6 digits');
      return false;
    }
    
    return true;
  };

  const validatePhase2 = () => {
    const { dateOfBirth, bvn, nin } = formData;
    
    if (!dateOfBirth || !bvn || !nin) {
      toast.error('Please fill in all required fields');
      return false;
    }
    
    if (bvn.length !== 11 || !/^\d+$/.test(bvn)) {
      toast.error('BVN must be 11 digits');
      return false;
    }
    
    if (nin.length !== 11 || !/^\d+$/.test(nin)) {
      toast.error('NIN must be 11 digits');
      return false;
    }
    
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    
    if (age < 18) {
      toast.error('You must be at least 18 years old');
      return false;
    }
    
    return true;
  };

  const handleNext = () => {
    if (currentPhase === 1 && validatePhase1()) {
      setCurrentPhase(2);
    } else if (currentPhase === 2 && validatePhase2()) {
      setCurrentPhase(3);
      setShowOTP(true);
    }
  };

  const handleBack = () => {
    if (currentPhase > 1) {
      setCurrentPhase(currentPhase - 1);
      if (currentPhase === 3) {
        setShowOTP(false);
      }
    }
  };

  const handleOTPVerify = async (otp) => {
    // Always succeed for testing, but send data to backend
    try {
      const payload = {
        firstName: formData.firstName,
        middleName: formData.middleName || undefined,
        lastName: formData.lastName,
        phone: formData.phone,
        password: formData.pin, // Use password, not pin
        dateOfBirth: formData.dateOfBirth,
        bvn: formData.bvn,
        nin: formData.nin,
        email: formData.email || `${formData.phone}@test.com`,
        address: formData.address || 'N/A',
      };
      const baseUrl = import.meta.env.VITE_BACKEND_URL;
      console.log('Sending payload to backend:', payload);
      const response = await axios.post(
        `${baseUrl}/api/v1/personal/auth/register`,
        payload,
        { headers: { 'x-timezone': 'Africa/Lagos' } }
      );
      console.log('Backend response:', response.data);
      setShowOTP(false);
      setTimeout(() => {
        setShowCongrats(true);
      }, 1000);
    } catch (err) {
      console.error('Account creation error:', err, err?.response?.data);
      toast.error(err.response?.data?.message || 'Account creation failed');
    }
  };

  const handleCongratsContinue = () => {
    setShowCongrats(false);
    navigate('/login');
  };

  const renderProgressBar = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {[1, 2, 3].map((phase) => (
          <div key={phase} className="flex flex-col items-center">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium ${
              currentPhase >= phase
                ? 'bg-[#1231B8] text-white'
                : 'bg-gray-200 text-gray-500'
            }`}>
              {currentPhase > phase ? <Check size={18} /> : phase}
            </div>
            <span className={`text-xs mt-2 ${currentPhase >= phase ? 'text-[#1231B8] font-medium' : 'text-gray-500'}`}>
              {phase === 1 ? 'Personal Details' : phase === 2 ? 'Identity Verification' : 'Account Setup Complete'}
            </span>
          </div>
        ))}
      </div>
      <div className="flex mt-2">
        <div className={`h-1 flex-1 ${currentPhase >= 2 ? 'bg-[#1231B8]' : 'bg-gray-200'}`}></div>
        <div className={`h-1 flex-1 ${currentPhase >= 3 ? 'bg-[#1231B8]' : 'bg-gray-200'}`}></div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white p-4 py-8">
      <Helmet>
        <title>Create Account | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-xl">
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center justify-center mb-6">
            <img 
              src="/lovable-uploads/6d533269-1146-41c3-ad78-0141d73073a6.png" 
              alt="KojaPay Logo"
              className="h-16 w-16"
            />
          </Link>
          <h1 className="text-3xl font-bold text-[#1A1F2C]">Create Your Account</h1>
          <p className="text-gray-600 mt-2">Join thousands enjoying secure banking</p>
        </div>

        <Card className="shadow-xl border-none rounded-2xl overflow-hidden">
          <CardHeader className="bg-[#1231B8] text-white pb-4">
            <CardTitle className="text-xl">New Account Registration</CardTitle>
            <CardDescription className="text-[#fde314] font-medium">Registration Phase {currentPhase} of 3</CardDescription>
          </CardHeader>
          
          <CardContent className="p-6">
            {renderProgressBar()}
            
            <div className="space-y-6">
              {currentPhase === 1 && (
                <div className="space-y-4 animate-fadeIn">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-[#1A1F2C] font-medium flex items-center">
                        <User size={16} className="mr-2 text-[#1231B8]" />
                        First Name
                      </Label>
                      <Input
                        id="firstName"
                        name="firstName"
                        placeholder="First Name"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className="mt-1 rounded-[40px] border-gray-300"
                        required
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="lastName" className="text-[#1A1F2C] font-medium flex items-center">
                        <User size={16} className="mr-2 text-[#1231B8]" />
                        Last Name
                      </Label>
                      <Input
                        id="lastName"
                        name="lastName"
                        placeholder="Last Name"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className="mt-1 rounded-[40px] border-gray-300"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="phone" className="text-[#1A1F2C] font-medium flex items-center">
                      <Phone size={16} className="mr-2 text-[#1231B8]" />
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      name="phone"
                      placeholder="08012345678"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="mt-1 rounded-[40px] border-gray-300"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Format: 08012345678 or +2348012345678</p>
                  </div>
                  
                  <div>
                    <Label htmlFor="pin" className="text-[#1A1F2C] font-medium flex items-center">
                      <Lock size={16} className="mr-2 text-[#1231B8]" />
                      Create 6-Digit PIN
                    </Label>
                    <div className="relative">
                      <Input
                        id="pin"
                        name="pin"
                        type={showPin ? 'text' : 'password'}
                        placeholder="Enter 6-digit PIN"
                        value={formData.pin}
                        onChange={handleInputChange}
                        className="mt-1 rounded-[40px] border-gray-300"
                        maxLength={6}
                        pattern="[0-9]{6}"
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                        onClick={() => setShowPin(!showPin)}
                      >
                        {showPin ? <EyeOff size={18} /> : <Eye size={18} />}
                      </button>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">Used to authorize transactions</p>
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-[#1A1F2C] font-medium flex items-center">
                      <Mail size={16} className="mr-2 text-[#1231B8]" />
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="Enter your email address"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="mt-1 rounded-[40px] border-gray-300"
                      required
                    />
                  </div>
                </div>
              )}
              
              {currentPhase === 2 && (
                <div className="space-y-4 animate-fadeIn">
                  <div>
                    <Label htmlFor="dateOfBirth" className="text-[#1A1F2C] font-medium flex items-center">
                      <Calendar size={16} className="mr-2 text-[#1231B8]" />
                      Date of Birth
                    </Label>
                    <Input
                      id="dateOfBirth"
                      name="dateOfBirth"
                      type="date"
                      value={formData.dateOfBirth}
                      onChange={handleInputChange}
                      className="mt-1 rounded-[40px] border-gray-300"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">You must be at least 18 years old</p>
                  </div>
                  
                  <div>
                    <Label htmlFor="bvn" className="text-[#1A1F2C] font-medium flex items-center">
                      <Shield size={16} className="mr-2 text-[#1231B8]" />
                      BVN (Bank Verification Number)
                    </Label>
                    <Input
                      id="bvn"
                      name="bvn"
                      placeholder="Enter your 11-digit BVN"
                      value={formData.bvn}
                      onChange={handleInputChange}
                      className="mt-1 rounded-[40px] border-gray-300"
                      maxLength={11}
                      pattern="[0-9]{11}"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Required for account verification</p>
                  </div>
                  
                  <div>
                    <Label htmlFor="nin" className="text-[#1A1F2C] font-medium flex items-center">
                      <Shield size={16} className="mr-2 text-[#1231B8]" />
                      NIN (National Identification Number)
                    </Label>
                    <Input
                      id="nin"
                      name="nin"
                      placeholder="Enter your 11-digit NIN"
                      value={formData.nin}
                      onChange={handleInputChange}
                      className="mt-1 rounded-[40px] border-gray-300"
                      maxLength={11}
                      pattern="[0-9]{11}"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">Required for identity verification</p>
                  </div>
                  
                  <div className="bg-blue-50 p-4 rounded-xl border border-blue-100">
                    <div className="flex items-start">
                      <Shield className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
                      <div>
                        <h3 className="font-medium text-blue-800">Secure Verification</h3>
                        <p className="text-sm text-blue-700 mt-1">
                          Your information is encrypted and used only for account verification as required by Nigerian banking regulations.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {currentPhase === 3 && !showOTP && (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#1231B8] mx-auto mb-4"></div>
                  <p className="text-gray-600">Preparing verification...</p>
                </div>
              )}
            </div>
          </CardContent>
          
          {!showOTP && (
            <CardFooter className="flex justify-between p-6 bg-gray-50 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={currentPhase === 1 ? () => navigate('/') : handleBack}
                className="flex items-center rounded-[40px]"
              >
                <ArrowLeft size={16} className="mr-2" />
                {currentPhase === 1 ? 'Home' : 'Back'}
              </Button>
              
              <Button
                type="button"
                onClick={handleNext}
                className="bg-[#1231B8] hover:bg-[#0D2599] text-white flex items-center rounded-[40px]"
                disabled={currentPhase === 3}
              >
                {currentPhase === 3 ? 'Verifying...' : 'Next'}
                {currentPhase < 3 && <ArrowRight size={16} className="ml-2" />}
              </Button>
            </CardFooter>
          )}
        </Card>
        
        <div className="text-center mt-6">
          <p className="text-gray-600">
            Already have an account?{' '}
            <Link to="/login" className="text-[#1231B8] font-medium hover:underline">
              Log In
            </Link>
          </p>
        </div>
      </div>

      {/* OTP Verification Dialog */}
      {showOTP && (
        <Dialog open={showOTP} onOpenChange={() => {}}>
          <DialogContent className="sm:max-w-md">
            <OTPVerification
              onVerify={handleOTPVerify}
              onCancel={() => {
                setShowOTP(false);
                setCurrentPhase(2);
              }}
              phoneNumber={formData.phone}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Congratulations Modal */}
      <Dialog open={showCongrats} onOpenChange={() => {}}>
        <DialogContent className="sm:max-w-md text-center">
          <DialogHeader className="text-center">
            <div className="mx-auto mb-4 h-20 w-20 rounded-full bg-green-100 flex items-center justify-center">
              <PartyPopper className="h-10 w-10 text-green-600" />
            </div>
            <DialogTitle className="text-2xl font-bold text-green-800">
              Congratulations! 🎉
            </DialogTitle>
            <DialogDescription className="text-lg text-gray-600 mt-2">
              Your KojaPay account has been created successfully!
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 mt-6">
            <div className="bg-green-50 p-4 rounded-xl border border-green-100">
              <p className="text-sm text-green-700">
                Welcome to the KojaPay family! You can now enjoy secure banking, fast transfers, and much more.
              </p>
            </div>
            
            <Button
              onClick={handleCongratsContinue}
              className="w-full bg-[#1231B8] hover:bg-[#0D2599] text-white py-3 rounded-[40px]"
            >
              Continue to Login
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CreateAccount;
