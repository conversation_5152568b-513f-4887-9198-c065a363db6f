import axios from 'axios';
import { TransactionHistoryItem, TransactionResponse, AccountVerificationResponse, BankListItem, TransactionStatusResponse } from '@/types/bankOne';

// API Configuration from environment variables
const CORE_BANKING_API_BASE_URL = process.env.BANKONE_CORE_API_URL || 'https://staging.mybankone.com/BankOneWebAPI/';
const CHANNELS_API_BASE_URL = process.env.BANKONE_CHANNELS_API_URL || 'https://staging.mybankone.com/thirdpartyapiservice/apiservice/';
const INSTITUTION_CODE = process.env.BANKONE_INSTITUTION_CODE || '100575';
const AUTH_TOKEN = process.env.BANKONE_AUTH_TOKEN || '';
const API_KEY = process.env.BANKONE_API_KEY || '';

// Log API connection but not sensitive data
console.log(`BankOne API initialized with institution code: ${INSTITUTION_CODE}`);
console.log(`Core Banking API URL: ${CORE_BANKING_API_BASE_URL}`);
console.log(`Channels API URL: ${CHANNELS_API_BASE_URL}`);

// Create axios instances for both API types
const coreBankingApi = axios.create({
  baseURL: CORE_BANKING_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
  }
});

const channelsApi = axios.create({
  baseURL: CHANNELS_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${AUTH_TOKEN}`,
  }
});

// Add request interceptor for error handling
const addRequestInterceptors = () => {
  // Add interceptor to coreBankingApi
  coreBankingApi.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('Core Banking API Error:', error);
      
      // Customize error messages based on status codes
      if (error.response) {
        const { status } = error.response;
        
        if (status === 401) {
          // Handle authentication errors
          console.error('Authentication Error: Please check your API credentials');
        } else if (status === 403) {
          // Handle authorization errors
          console.error('Authorization Error: You do not have permission to perform this action');
        } else if (status === 404) {
          // Handle not found errors
          console.error('Resource Not Found Error');
        } else if (status >= 500) {
          // Handle server errors
          console.error('Server Error: Please try again later');
        }
      } else if (error.request) {
        // Handle network errors
        console.error('Network Error: Please check your internet connection');
      }
      
      return Promise.reject(error);
    }
  );

  // Add interceptor to channelsApi
  channelsApi.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('Channels API Error:', error);
      
      // Customize error messages based on status codes
      if (error.response) {
        const { status } = error.response;
        
        if (status === 401) {
          // Handle authentication errors
          console.error('Authentication Error: Please check your API credentials');
        } else if (status === 403) {
          // Handle authorization errors
          console.error('Authorization Error: You do not have permission to perform this action');
        } else if (status === 404) {
          // Handle not found errors
          console.error('Resource Not Found Error');
        } else if (status >= 500) {
          // Handle server errors
          console.error('Server Error: Please try again later');
        }
      } else if (error.request) {
        // Handle network errors
        console.error('Network Error: Please check your internet connection');
      }
      
      return Promise.reject(error);
    }
  );
};

// Add interceptors to API instances
addRequestInterceptors();

// Error handling utilities
const handleTransferError = (error: any, transferType: string) => {
  console.error(`${transferType} transfer failed:`, error);
  
  // Check if it's an API response error with status and data
  if (error.response) {
    const { status, data } = error.response;
    
    // Handle specific API error codes
    if (status === 400) {
      // Bad request errors - typically validation issues
      const errorMessage = data.responseMessage || 'Invalid transaction details. Please check and try again.';
      return {
        success: false,
        message: errorMessage,
        error,
        errorCode: 'INVALID_REQUEST',
      };
    } else if (status === 401 || status === 403) {
      // Authentication/Authorization errors
      return {
        success: false,
        message: 'Authorization error. Please contact support.',
        error,
        errorCode: 'AUTH_ERROR',
      };
    } else if (status === 404) {
      return {
        success: false,
        message: 'This service is temporarily unavailable. Please try again later.',
        error,
        errorCode: 'SERVICE_UNAVAILABLE',
      };
    } else if (status >= 500) {
      return {
        success: false,
        message: 'Banking service is currently unavailable. Please try again later.',
        error,
        errorCode: 'SERVER_ERROR',
      };
    }
    
    // Custom error messages based on API-specific error codes if available
    if (data.responseCode) {
      switch (data.responseCode) {
        case '99':
          return {
            success: false,
            message: 'Transaction failed. Please try again later.',
            error,
            errorCode: 'GENERAL_ERROR',
          };
        case '85':
        case '86':
          return {
            success: false,
            message: 'Insufficient funds to complete this transaction.',
            error,
            errorCode: 'INSUFFICIENT_FUNDS',
          };
        case '91':
          return {
            success: false,
            message: 'Bank unavailable. Please try again later.',
            error,
            errorCode: 'BANK_UNAVAILABLE',
          };
        default:
          return {
            success: false,
            message: data.responseMessage || 'Transfer failed. Please try again later.',
            error,
            errorCode: data.responseCode,
          };
      }
    }
  } else if (error.request) {
    // Network errors - request made but no response received
    return {
      success: false,
      message: 'Network error. Please check your connection and try again.',
      error,
      errorCode: 'NETWORK_ERROR',
    };
  }
  
  // Default fallback error
  return {
    success: false,
    message: 'Transfer failed. Please try again later.',
    error,
    errorCode: 'UNKNOWN_ERROR',
  };
};

// Account Verification
export const verifyAccount = async (accountNumber: string, bankCode?: string) => {
  try {
    // Validate account number
    if (!accountNumber || accountNumber.length < 10) {
      return {
        success: false,
        message: 'Please provide a valid account number.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    // If bank code is provided but invalid
    if (bankCode && bankCode.trim() === '') {
      return {
        success: false,
        message: 'Please provide a valid bank code.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    // For same bank (KojaPay) verification
    if (!bankCode) {
      const response = await coreBankingApi.post('GetAccountByAccountNumber', {
        accountNumber,
        institutionCode: INSTITUTION_CODE,
      });
      
      // Check for successful response but with error codes in the data
      if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
        return {
          success: false,
          data: response.data,
          message: response.data.responseMessage || 'Account not found or verification failed.',
          errorCode: response.data.responseCode,
        };
      }
      
      // Check if account details were found
      if (!response.data || !response.data.accountName) {
        return {
          success: false,
          data: response.data,
          message: 'Account not found or verification failed.',
          errorCode: 'ACCOUNT_NOT_FOUND',
        };
      }
      
      return {
        success: true,
        data: response.data,
        message: 'Account verification successful',
      };
    } 
    // For other banks verification
    else {
      // Use NIP API for other banks
      const response = await channelsApi.post('NameEnquiry', {
        accountNumber,
        destinationBankCode: bankCode,
        institutionCode: INSTITUTION_CODE,
      });
      
      // Check for successful response but with error codes in the data
      if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
        return {
          success: false,
          data: response.data,
          message: response.data.responseMessage || 'Account not found or verification failed.',
          errorCode: response.data.responseCode,
        };
      }
      
      // Check if account details were found
      if (!response.data || !response.data.accountName) {
        return {
          success: false,
          data: response.data,
          message: 'Account not found or verification failed.',
          errorCode: 'ACCOUNT_NOT_FOUND',
        };
      }
      
      return {
        success: true,
        data: response.data,
        message: 'Account verification successful',
      };
    }
  } catch (error) {
    console.error('Account verification failed:', error);
    
    // Use similar error handling pattern
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 404) {
        return {
          success: false,
          message: 'Account not found. Please check the account number and try again.',
          error,
          errorCode: 'NOT_FOUND',
        };
      } else if (status === 400) {
        return {
          success: false,
          message: 'Invalid account information. Please check and try again.',
          error,
          errorCode: 'INVALID_REQUEST',
        };
      } else if (status >= 500) {
        return {
          success: false,
          message: 'Banking service is currently unavailable. Please try again later.',
          error,
          errorCode: 'SERVER_ERROR',
        };
      }
      
      return {
        success: false,
        message: data?.responseMessage || 'Account verification failed. Please try again later.',
        error,
        errorCode: data?.responseCode || 'API_ERROR',
      };
    } else if (error.request) {
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
        error,
        errorCode: 'NETWORK_ERROR',
      };
    }
    
    return {
      success: false,
      message: 'Account verification failed. Please try again later.',
      error,
      errorCode: 'UNKNOWN_ERROR',
    };
  }
};

// Balance Inquiry
export const getAccountBalance = async (accountNumber: string) => {
  try {
    // Validate account number
    if (!accountNumber || accountNumber.length < 10) {
      return {
        success: false,
        message: 'Please provide a valid account number.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    const response = await coreBankingApi.post('GetBalanceEnquiry', {
      accountNumber,
      institutionCode: INSTITUTION_CODE,
    });
    
    // Check for successful response but with error codes in the data
    if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
      return {
        success: false,
        data: response.data,
        message: response.data.responseMessage || 'Balance inquiry failed.',
        errorCode: response.data.responseCode,
      };
    }
    
    // Check if balance data is missing
    if (!response.data || (response.data.availableBalance === undefined && response.data.balance === undefined)) {
      return {
        success: false,
        data: response.data,
        message: 'Unable to retrieve account balance. The account may not exist or is inactive.',
        errorCode: 'BALANCE_NOT_AVAILABLE',
      };
    }
    
    return {
      success: true,
      data: response.data,
      message: 'Balance inquiry successful',
    };
  } catch (error) {
    console.error('Balance inquiry failed:', error);
    
    // Use similar error handling pattern
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 404) {
        return {
          success: false,
          message: 'Account not found. Please check the account number and try again.',
          error,
          errorCode: 'NOT_FOUND',
        };
      } else if (status === 400) {
        return {
          success: false,
          message: 'Invalid account information. Please check and try again.',
          error,
          errorCode: 'INVALID_REQUEST',
        };
      } else if (status >= 500) {
        return {
          success: false,
          message: 'Banking service is currently unavailable. Please try again later.',
          error,
          errorCode: 'SERVER_ERROR',
        };
      }
      
      return {
        success: false,
        message: data?.responseMessage || 'Balance inquiry failed. Please try again later.',
        error,
        errorCode: data?.responseCode || 'API_ERROR',
      };
    } else if (error.request) {
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
        error,
        errorCode: 'NETWORK_ERROR',
      };
    }
    
    return {
      success: false,
      message: 'Balance inquiry failed. Please try again later.',
      error,
      errorCode: 'UNKNOWN_ERROR',
    };
  }
};

// Transfer within KojaPay (Same Bank)
export const transferSameBank = async (
  senderAccountNumber: string, 
  receiverAccountNumber: string, 
  amount: number,
  narration: string
) => {
  try {
    // Validate inputs before sending to API
    if (!senderAccountNumber || !receiverAccountNumber) {
      return {
        success: false,
        message: 'Both sender and receiver account numbers are required.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    if (amount <= 0) {
      return {
        success: false,
        message: 'Transfer amount must be greater than zero.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    const response = await coreBankingApi.post('IntraBankTransfer', {
      senderAccountNumber,
      receiverAccountNumber,
      amount,
      narration: narration || 'Fund Transfer',
      institutionCode: INSTITUTION_CODE,
    });
    
    // Check for successful response but with error codes in the data
    if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
      return {
        success: false,
        data: response.data,
        message: response.data.responseMessage || 'Transfer was not successful.',
        errorCode: response.data.responseCode,
      };
    }
    
    return {
      success: true,
      data: response.data,
      message: 'Transfer successful',
      transactionReference: response.data.transactionReference || '',
    };
  } catch (error) {
    return handleTransferError(error, 'Intra-bank');
  }
};

// Transfer to other banks
export const transferOtherBank = async (
  senderAccountNumber: string, 
  receiverAccountNumber: string,
  receiverBankCode: string,
  amount: number,
  narration: string,
  receiverName: string
) => {
  try {
    // Validate inputs before sending to API
    if (!senderAccountNumber || !receiverAccountNumber || !receiverBankCode) {
      return {
        success: false,
        message: 'Sender account, receiver account, and receiver bank code are required.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    if (amount <= 0) {
      return {
        success: false,
        message: 'Transfer amount must be greater than zero.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    if (!receiverName) {
      return {
        success: false,
        message: 'Receiver name is required for inter-bank transfers.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    const response = await channelsApi.post('NIPFundsTransfer', {
      senderAccountNumber,
      receiverAccountNumber,
      receiverBankCode,
      amount,
      narration: narration || 'Fund Transfer',
      receiverName,
      institutionCode: INSTITUTION_CODE,
    });
    
    // Check for successful response but with error codes in the data
    if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
      return {
        success: false,
        data: response.data,
        message: response.data.responseMessage || 'Transfer was not successful.',
        errorCode: response.data.responseCode,
      };
    }
    
    return {
      success: true,
      data: response.data,
      message: 'Transfer successful',
      transactionReference: response.data.transactionReference || '',
    };
  } catch (error) {
    return handleTransferError(error, 'Inter-bank');
  }
};

// Get transaction status
export const getTransactionStatus = async (transactionReference: string) => {
  try {
    if (!transactionReference) {
      return {
        success: false,
        message: 'Transaction reference is required to check status.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    const response = await channelsApi.post('GetTransactionStatus', {
      transactionReference,
      institutionCode: INSTITUTION_CODE,
    });
    
    // If we get a response but the status indicates a problem
    if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
      return {
        success: false,
        data: response.data,
        message: response.data.responseMessage || 'Unable to determine transaction status.',
        errorCode: response.data.responseCode,
      };
    }
    
    // Map the transaction status to a more user-friendly status
    let statusMessage = 'Transaction status retrieved successfully';
    if (response.data && response.data.transactionStatus) {
      const status = response.data.transactionStatus.toLowerCase();
      
      if (status.includes('success') || status === 'successful') {
        statusMessage = 'Transaction completed successfully';
      } else if (status.includes('fail') || status === 'failed') {
        statusMessage = 'Transaction failed';
      } else if (status.includes('pend') || status === 'pending') {
        statusMessage = 'Transaction is being processed';
      } else if (status.includes('reject') || status === 'rejected') {
        statusMessage = 'Transaction was rejected';
      } else if (status.includes('reverse') || status === 'reversed') {
        statusMessage = 'Transaction was reversed';
      }
    }
    
    return {
      success: true,
      data: response.data,
      message: statusMessage,
      status: response.data.transactionStatus || 'Unknown',
    };
  } catch (error) {
    console.error('Get transaction status failed:', error);
    
    // Use similar error handling as transfers but simplified
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 404) {
        return {
          success: false,
          message: 'Transaction not found. Please check the reference and try again.',
          error,
          errorCode: 'NOT_FOUND',
        };
      } else if (status >= 500) {
        return {
          success: false,
          message: 'Banking service is currently unavailable. Please try again later.',
          error,
          errorCode: 'SERVER_ERROR',
        };
      }
      
      return {
        success: false,
        message: data.responseMessage || 'Failed to get transaction status.',
        error,
        errorCode: data.responseCode || 'API_ERROR',
      };
    } else if (error.request) {
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
        error,
        errorCode: 'NETWORK_ERROR',
      };
    }
    
    return {
      success: false,
      message: 'Failed to get transaction status. Please try again later.',
      error,
      errorCode: 'UNKNOWN_ERROR',
    };
  }
};

// Get transaction history
export const getTransactionHistory = async (
  accountNumber: string, 
  startDate: string, 
  endDate: string
) => {
  try {
    // Validate inputs
    if (!accountNumber) {
      return {
        success: false,
        message: 'Account number is required to fetch transaction history.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    // Validate date formats (assuming YYYY-MM-DD format)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(startDate) || !dateRegex.test(endDate)) {
      return {
        success: false,
        message: 'Invalid date format. Please use YYYY-MM-DD format.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    // Ensure startDate is not after endDate
    if (new Date(startDate) > new Date(endDate)) {
      return {
        success: false,
        message: 'Start date cannot be after end date.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    // Ensure the date range is not too large (e.g., max 90 days)
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);
    const diffTime = Math.abs(endDateObj.getTime() - startDateObj.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays > 90) {
      return {
        success: false,
        message: 'Date range too large. Please select a maximum of 90 days.',
        errorCode: 'VALIDATION_ERROR',
      };
    }
    
    const response = await coreBankingApi.post('GetCustomerTransactionHistory', {
      accountNumber,
      startDate,
      endDate,
      institutionCode: INSTITUTION_CODE,
    });
    
    // Handle empty transaction list
    if (response.data && Array.isArray(response.data.transactions) && response.data.transactions.length === 0) {
      return {
        success: true,
        data: { transactions: [] },
        message: 'No transactions found for the selected period.',
        isEmpty: true,
      };
    }
    
    // Check for successful response but with error codes in the data
    if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
      return {
        success: false,
        data: response.data,
        message: response.data.responseMessage || 'Failed to retrieve transaction history.',
        errorCode: response.data.responseCode,
      };
    }
    
    return {
      success: true,
      data: response.data,
      message: 'Transaction history retrieved successfully',
    };
  } catch (error) {
    console.error('Get transaction history failed:', error);
    
    // Use similar error handling pattern
    if (error.response) {
      const { status, data } = error.response;
      
      if (status === 404) {
        return {
          success: false,
          message: 'Account not found or no transactions available.',
          error,
          errorCode: 'NOT_FOUND',
        };
      } else if (status === 400) {
        return {
          success: false,
          message: 'Invalid request. Please check your account number and date range.',
          error,
          errorCode: 'INVALID_REQUEST',
        };
      } else if (status >= 500) {
        return {
          success: false,
          message: 'Banking service is currently unavailable. Please try again later.',
          error,
          errorCode: 'SERVER_ERROR',
        };
      }
      
      return {
        success: false,
        message: data.responseMessage || 'Failed to retrieve transaction history.',
        error,
        errorCode: data.responseCode || 'API_ERROR',
      };
    } else if (error.request) {
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
        error,
        errorCode: 'NETWORK_ERROR',
      };
    }
    
    return {
      success: false,
      message: 'Failed to retrieve transaction history. Please try again later.',
      error,
      errorCode: 'UNKNOWN_ERROR',
    };
  }
};

// Get banks list
export const getBanksList = async () => {
  try {
    const response = await channelsApi.post('GetNIPBanks', {
      institutionCode: INSTITUTION_CODE,
    });
    
    // Handle empty banks list
    if (response.data && Array.isArray(response.data.banks) && response.data.banks.length === 0) {
      return {
        success: false,
        data: { banks: [] },
        message: 'No banks were found. This may be a temporary service issue.',
        errorCode: 'EMPTY_BANKS_LIST',
      };
    }
    
    // Check for successful response but with error codes in the data
    if (response.data && response.data.responseCode && response.data.responseCode !== '00') {
      return {
        success: false,
        data: response.data,
        message: response.data.responseMessage || 'Failed to retrieve banks list.',
        errorCode: response.data.responseCode,
      };
    }
    
    return {
      success: true,
      data: response.data,
      message: 'Banks list retrieved successfully',
      count: response.data.banks?.length || 0,
    };
  } catch (error) {
    console.error('Get banks list failed:', error);
    
    // Use similar error handling pattern
    if (error.response) {
      const { status, data } = error.response;
      
      if (status >= 500) {
        return {
          success: false,
          message: 'Banking service is currently unavailable. Please try again later.',
          error,
          errorCode: 'SERVER_ERROR',
        };
      }
      
      return {
        success: false,
        message: data.responseMessage || 'Failed to retrieve banks list.',
        error,
        errorCode: data.responseCode || 'API_ERROR',
      };
    } else if (error.request) {
      return {
        success: false,
        message: 'Network error. Please check your connection and try again.',
        error,
        errorCode: 'NETWORK_ERROR',
      };
    }
    
    return {
      success: false,
      message: 'Failed to retrieve banks list. Please try again later.',
      error,
      errorCode: 'UNKNOWN_ERROR',
    };
  }
};

// Bank object export for easier imports of the API functions
export const BankOneApi = {
  verifyAccount,
  getAccountBalance,
  transferSameBank,
  transferOtherBank,
  getTransactionStatus,
  getTransactionHistory,
  getBanksList,
};

export default BankOneApi;
