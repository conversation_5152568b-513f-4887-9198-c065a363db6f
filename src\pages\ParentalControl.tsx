import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Users, 
  Settings, 
  Bell, 
  DollarSign, 
  Clock,
  Smartphone,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  Plus,
  CreditCard
} from 'lucide-react';
import { Helmet } from 'react-helmet-async';
import { useToast } from '@/hooks/use-toast';
import CreateChildAccount from '@/components/CreateChildAccount';

const ParentalControl = () => {
  const { toast } = useToast();
  const [showCreateChild, setShowCreateChild] = useState(false);
  
  const [settings, setSettings] = useState({
    requireAuthForLogin: true,
    oneTimeDeviceAuth: false,
    weeklySpendingLimit: 100,
    dailySpendingLimit: 25,
    savingsPeriod: 30,
    allowOnlinePayments: false,
    notifyOnSpending: true,
    notifyOnSavings: true,
    emergencyOverride: true
  });

  const [kidsAccounts] = useState([
    {
      id: 1,
      name: 'Alex Johnson',
      age: 12,
      balance: 250.50,
      weeklySpent: 45,
      status: 'active',
      lastActivity: '2 hours ago',
      devices: ['iPhone 13', 'iPad Air']
    },
    {
      id: 2,
      name: 'Emma Johnson',
      age: 9,
      balance: 120.00,
      weeklySpent: 15,
      status: 'active',
      lastActivity: '1 day ago',
      devices: ['Samsung Galaxy']
    }
  ]);

  const [notifications] = useState([
    {
      id: 1,
      type: 'spending',
      message: 'Alex spent ₦15 at Candy Store',
      time: '10 minutes ago',
      severity: 'info'
    },
    {
      id: 2,
      type: 'savings',
      message: 'Emma reached 50% of savings goal!',
      time: '2 hours ago',
      severity: 'success'
    },
    {
      id: 3,
      type: 'limit',
      message: 'Alex approaching weekly spending limit',
      time: '1 day ago',
      severity: 'warning'
    }
  ]);

  const handleSettingChange = (key: string, value: boolean | number) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    toast({
      title: 'Settings Updated',
      description: 'Your parental control settings have been saved.',
    });
  };

  const handleChildCreated = () => {
    toast({
      title: 'Child Account Created',
      description: 'Your child account has been successfully created!',
    });
    // Refresh the children list in a real app
  };

  const handleFundAccount = (childId: number, childName: string) => {
    toast({
      title: 'Fund Account',
      description: `Funding ${childName}'s account...`,
    });
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'success': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-blue-600 bg-blue-100';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'success': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <XCircle className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <Helmet>
        <title>Parental Controls | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-6xl">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Shield className="h-12 w-12 text-[#1231B8] mr-3" />
            <h1 className="text-4xl font-bold text-[#1231B8]">Parental Controls</h1>
          </div>
          <p className="text-gray-600 text-lg">Manage and monitor your children's banking activities</p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 rounded-[40px]">
            <TabsTrigger value="overview" className="rounded-[40px]">Overview</TabsTrigger>
            <TabsTrigger value="settings" className="rounded-[40px]">Settings</TabsTrigger>
            <TabsTrigger value="accounts" className="rounded-[40px]">Kids Accounts</TabsTrigger>
            <TabsTrigger value="notifications" className="rounded-[40px]">Notifications</TabsTrigger>
            <TabsTrigger value="reports" className="rounded-[40px]">Reports</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-3">
              <Card className="rounded-[40px]">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600">Total Kids</p>
                      <p className="text-3xl font-bold text-[#1231B8]">{kidsAccounts.length}</p>
                    </div>
                    <Users className="h-12 w-12 text-[#1231B8]" />
                  </div>
                </CardContent>
              </Card>

              <Card className="rounded-[40px]">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600">Total Balance</p>
                      <p className="text-3xl font-bold text-[#1231B8]">
                        ₦{kidsAccounts.reduce((sum, acc) => sum + acc.balance, 0).toFixed(2)}
                      </p>
                    </div>
                    <DollarSign className="h-12 w-12 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="rounded-[40px]">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-600">Active Alerts</p>
                      <p className="text-3xl font-bold text-[#1231B8]">
                        {notifications.filter(n => n.severity === 'warning').length}
                      </p>
                    </div>
                    <AlertTriangle className="h-12 w-12 text-yellow-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card className="rounded-[40px]">
              <CardHeader>
                <CardTitle className="flex items-center text-[#1231B8]">
                  <Clock className="h-6 w-6 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notifications.slice(0, 3).map((notification) => (
                    <div key={notification.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-[20px]">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${getSeverityColor(notification.severity)}`}>
                          {getSeverityIcon(notification.severity)}
                        </div>
                        <div>
                          <p className="font-medium">{notification.message}</p>
                          <p className="text-sm text-gray-500">{notification.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Authentication Settings */}
              <Card className="rounded-[40px]">
                <CardHeader>
                  <CardTitle className="flex items-center text-[#1231B8]">
                    <Shield className="h-6 w-6 mr-2" />
                    Authentication Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Require parent authentication for login</Label>
                      <p className="text-sm text-gray-600">Kids must get parent approval every time they log in</p>
                    </div>
                    <Switch
                      checked={settings.requireAuthForLogin}
                      onCheckedChange={(checked) => handleSettingChange('requireAuthForLogin', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">One-time device authentication</Label>
                      <p className="text-sm text-gray-600">Approve devices once, then allow automatic login</p>
                    </div>
                    <Switch
                      checked={settings.oneTimeDeviceAuth}
                      onCheckedChange={(checked) => handleSettingChange('oneTimeDeviceAuth', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Emergency override</Label>
                      <p className="text-sm text-gray-600">Allow kids to bypass restrictions in emergencies</p>
                    </div>
                    <Switch
                      checked={settings.emergencyOverride}
                      onCheckedChange={(checked) => handleSettingChange('emergencyOverride', checked)}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Spending Limits */}
              <Card className="rounded-[40px]">
                <CardHeader>
                  <CardTitle className="flex items-center text-[#1231B8]">
                    <DollarSign className="h-6 w-6 mr-2" />
                    Spending Limits
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label htmlFor="weeklyLimit">Weekly Spending Limit (₦)</Label>
                    <Input
                      id="weeklyLimit"
                      type="number"
                      value={settings.weeklySpendingLimit}
                      onChange={(e) => handleSettingChange('weeklySpendingLimit', Number(e.target.value))}
                      className="rounded-[40px] mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="dailyLimit">Daily Spending Limit (₦)</Label>
                    <Input
                      id="dailyLimit"
                      type="number"
                      value={settings.dailySpendingLimit}
                      onChange={(e) => handleSettingChange('dailySpendingLimit', Number(e.target.value))}
                      className="rounded-[40px] mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="savingsPeriod">Savings Lock Period (days)</Label>
                    <Input
                      id="savingsPeriod"
                      type="number"
                      value={settings.savingsPeriod}
                      onChange={(e) => handleSettingChange('savingsPeriod', Number(e.target.value))}
                      className="rounded-[40px] mt-2"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Allow online payments</Label>
                      <p className="text-sm text-gray-600">Let kids make purchases online</p>
                    </div>
                    <Switch
                      checked={settings.allowOnlinePayments}
                      onCheckedChange={(checked) => handleSettingChange('allowOnlinePayments', checked)}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Notification Settings */}
              <Card className="rounded-[40px] md:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center text-[#1231B8]">
                    <Bell className="h-6 w-6 mr-2" />
                    Notification Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid gap-6 md:grid-cols-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Spending notifications</Label>
                      <p className="text-sm text-gray-600">Get notified when kids make purchases</p>
                    </div>
                    <Switch
                      checked={settings.notifyOnSpending}
                      onCheckedChange={(checked) => handleSettingChange('notifyOnSpending', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">Savings notifications</Label>
                      <p className="text-sm text-gray-600">Get notified about savings milestones</p>
                    </div>
                    <Switch
                      checked={settings.notifyOnSavings}
                      onCheckedChange={(checked) => handleSettingChange('notifyOnSavings', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Kids Accounts Tab */}
          <TabsContent value="accounts" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-[#1231B8]">Manage Kids Accounts</h2>
              <Button 
                onClick={() => setShowCreateChild(true)}
                className="bg-[#1231B8] hover:bg-[#1231B8]/90 rounded-[40px]"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Child Account
              </Button>
            </div>

            <div className="grid gap-6">
              {kidsAccounts.map((account) => (
                <Card key={account.id} className="rounded-[40px]">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center text-[#1231B8]">
                        <Users className="h-6 w-6 mr-2" />
                        {account.name} ({account.age} years old)
                      </CardTitle>
                      <Badge variant={account.status === 'active' ? 'default' : 'secondary'}>
                        {account.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-4">
                      <div>
                        <p className="text-sm text-gray-600">Current Balance</p>
                        <p className="text-xl font-bold text-[#1231B8]">₦{account.balance.toFixed(2)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Weekly Spent</p>
                        <p className="text-xl font-bold">₦{account.weeklySpent}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Last Activity</p>
                        <p className="text-sm font-medium">{account.lastActivity}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Authorized Devices</p>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {account.devices.map((device, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <Smartphone className="h-3 w-3 mr-1" />
                              {device}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2 mt-4">
                      <Button size="sm" className="rounded-[40px] bg-[#1231B8]">
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="rounded-[40px]"
                        onClick={() => handleFundAccount(account.id, account.name)}
                      >
                        <CreditCard className="h-4 w-4 mr-1" />
                        Fund Account
                      </Button>
                      <Button size="sm" variant="outline" className="rounded-[40px]">
                        <Settings className="h-4 w-4 mr-1" />
                        Manage
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="space-y-6">
            <Card className="rounded-[40px]">
              <CardHeader>
                <CardTitle className="flex items-center text-[#1231B8]">
                  <Bell className="h-6 w-6 mr-2" />
                  Activity Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {notifications.map((notification) => (
                    <div key={notification.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-[20px]">
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-full ${getSeverityColor(notification.severity)}`}>
                          {getSeverityIcon(notification.severity)}
                        </div>
                        <div>
                          <p className="font-medium">{notification.message}</p>
                          <p className="text-sm text-gray-500">{notification.time}</p>
                        </div>
                      </div>
                      <Badge variant="outline">{notification.type}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Reports Tab */}
          <TabsContent value="reports" className="space-y-6">
            <Card className="rounded-[40px]">
              <CardHeader>
                <CardTitle className="flex items-center text-[#1231B8]">
                  <Calendar className="h-6 w-6 mr-2" />
                  Monthly Reports
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Alex's Activity</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total Spent This Month:</span>
                        <span className="font-semibold">₦180</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Savings Added:</span>
                        <span className="font-semibold text-green-600">₦120</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Goals Achieved:</span>
                        <span className="font-semibold">2</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="font-semibold text-lg">Emma's Activity</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Total Spent This Month:</span>
                        <span className="font-semibold">₦65</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Savings Added:</span>
                        <span className="font-semibold text-green-600">₦80</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Goals Achieved:</span>
                        <span className="font-semibold">1</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <CreateChildAccount
          isOpen={showCreateChild}
          onClose={() => setShowCreateChild(false)}
          onChildCreated={handleChildCreated}
        />
      </div>
    </div>
  );
};

export default ParentalControl;
