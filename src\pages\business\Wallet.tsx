
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion } from 'framer-motion';
import { 
  ArrowDown, ArrowUp, ChevronRight, CreditCard, Download, 
  FileText, History, Landmark, Plus, QrCode, Receipt, Send, 
  Smartphone, Wallet as WalletIcon, Shield, Settings,
  Eye, EyeOff, Building
} from "lucide-react";
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import EnhancedVirtualCard from '@/components/EnhancedVirtualCard';
import BillPaymentDialog from '@/components/BillPaymentDialog';
import LoansDialog from '@/components/LoansDialog';
import CardsDialog from '@/components/CardsDialog';

const BusinessWallet = () => {
  // Move all useState calls inside the component function
  const [showCardDetails, setShowCardDetails] = useState(false);
  const [billPaymentOpen, setBillPaymentOpen] = useState(false);
  const [loansOpen, setLoansOpen] = useState(false);
  const [cardsOpen, setCardsOpen] = useState(false);
  
  const { user } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const { toast } = useToast();
  
  const businessUser = user as any;
  const businessName = businessUser?.businessName || 'Your Business';
  const accountNumber = businessUser?.accountNumber || '**********';
  
  const walletData = {
    balance: `₦${businessUser?.balance?.toLocaleString() || '250,000'}`,
    income: "₦450,000",
    expenses: "₦200,000",
    accountNumber: accountNumber
  };
  
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };
  
  const toggleCardDetails = () => {
    setShowCardDetails(!showCardDetails);
  };
  
  const handleQuickActions = (action: string) => {
    switch (action) {
      case 'send':
        navigate('/business/transfer/same-bank');
        break;
      case 'billpayment':
        setBillPaymentOpen(true);
        break;
      case 'loans':
        setLoansOpen(true);
        break;
      case 'cards':
        setCardsOpen(true);
        break;
      default:
        break;
    }
  };
  
  return (
    <BusinessLayout pageTitle="Business Wallet">
      <div className="py-6 space-y-8">
        <motion.div 
          className="glass-morph p-6 rounded-[20px]" 
          initial="hidden" 
          animate="visible" 
          variants={fadeInUp}
        >
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold text-neutral-800 font-unica">Welcome, {businessName}!</h1>
              <p className="text-neutral-600 font-unica">Manage your business finances</p>
            </div>
            <div className="mt-4 md:mt-0">
              <Button 
                className="bg-kojaPrimary hover:bg-kojaPrimary/90 font-unica"
                onClick={() => navigate('/business/transfer/same-bank')}
              >
                <Send className="mr-2 h-4 w-4" />
                Transfer Funds
              </Button>
            </div>
          </div>
        </motion.div>
        
        <motion.div 
          variants={fadeInUp} 
          initial="hidden" 
          animate="visible" 
          transition={{ delay: 0.1 }}
        >
          <Card className="wallet-balance-card border-none shadow-xl bg-gradient-to-br from-[#1231b8] to-[#09125a] text-white rounded-[20px] overflow-hidden">
            <CardContent className="p-6">
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full blur-3xl -mr-32 -mt-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full blur-2xl -ml-24 -mb-24"></div>
              
              <div className="flex flex-col lg:flex-row lg:items-center justify-between relative z-10">
                <div className="space-y-4 mb-6 lg:mb-0">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-white/20 rounded-full">
                      <WalletIcon className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-white font-unica">Business Wallet</h3>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-white/80 text-sm font-unica">Available Balance</p>
                    <h2 className="text-4xl font-bold text-white font-unica">{walletData.balance}</h2>
                  </div>
                  
                  <div className="space-y-1">
                    <p className="text-white/80 text-sm font-unica">Account Number</p>
                    <p className="font-medium text-white font-unica">{walletData.accountNumber}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-2 gap-3 w-full lg:w-auto">
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" 
                    onClick={() => handleQuickActions('send')}
                  >
                    <Send className="h-5 w-5 mb-2" />
                    <span className="text-sm">Send</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" 
                    onClick={() => handleQuickActions('billpayment')}
                  >
                    <Receipt className="h-5 w-5 mb-2" />
                    <span className="text-sm">Bill Payment</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" 
                    onClick={() => handleQuickActions('loans')}
                  >
                    <Landmark className="h-5 w-5 mb-2" />
                    <span className="text-sm">Loans</span>
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    className="flex flex-col items-center justify-center h-24 w-full sm:w-28 bg-white/10 border-white/20 text-white hover:bg-white/20 font-unica" 
                    onClick={() => handleQuickActions('cards')}
                  >
                    <CreditCard className="h-5 w-5 mb-2" />
                    <span className="text-sm">Cards</span>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
        
        {/* Business Virtual Card Section */}
        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.15 }}>
          <h3 className="text-lg font-medium mb-3 sm:mb-4 font-unica">Business Virtual Card</h3>
          <Card className="wallet-balance-card glass-morph border-kojaPrimary/20 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
            <CardContent className="p-4 sm:p-6">
              <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                <div className="w-full md:w-auto">
                  <EnhancedVirtualCard
                    cardNumber="**** **** **** 4532"
                    name={businessUser?.businessName?.toUpperCase() || "YOUR BUSINESS"}
                    expiryDate="05/26"
                    cardType="business"
                    variant="virtual"
                    className="mx-auto shadow-lg hover:shadow-xl transition-all duration-300"
                  />
                </div>
                <div className="w-full md:w-auto space-y-4">
                  <div className="space-y-2">
                    <p className="text-kojaGray text-sm font-unica">Card Balance</p>
                    <h3 className="text-2xl font-bold font-unica">₦120,000</h3>
                    <p className="text-sm text-kojaGray font-unica">Limit: ₦500,000</p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button 
                      variant="outline" 
                      className="flex items-center gap-2 border-kojaPrimary/20 text-kojaPrimary shadow-sm hover:shadow-md font-unica"
                      onClick={toggleCardDetails}
                    >
                      {showCardDetails ? <EyeOff size={16} /> : <Eye size={16} />}
                      <span>{showCardDetails ? 'Hide Details' : 'Card Details'}</span>
                    </Button>
                    <Button 
                      className="flex items-center gap-2 bg-[#9b87f5] hover:bg-[#9b87f5]/90 text-white shadow-sm hover:shadow-md font-unica"
                      onClick={() => navigate('/business/cards')}
                    >
                      <Settings size={16} />
                      <span>Manage Card</span>
                    </Button>
                  </div>
                  
                  {showCardDetails && (
                    <div className="mt-4 p-4 bg-gray-50 rounded-lg space-y-3 border border-gray-100 shadow-sm">
                      <div className="flex justify-between">
                        <span className="text-sm text-kojaGray font-unica">Card Number</span>
                        <span className="text-sm font-medium font-unica">4539 **** **** 4532</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-kojaGray font-unica">CVV</span>
                        <span className="text-sm font-medium font-unica">***</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-kojaGray font-unica">Expiry Date</span>
                        <span className="text-sm font-medium font-unica">05/26</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-kojaGray font-unica">Status</span>
                        <span className="text-sm font-medium text-green-600 font-unica">Active</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          <div className="md:col-span-8 space-y-6">
            <Card className="wallet-balance-card modern-card shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-lg font-medium text-[#1231B8] font-unica">Financial Overview</CardTitle>
                <Button 
                  variant="ghost" 
                  className="text-[#1231B8] hover:text-[#1231B8]/80 hover:bg-[#1231B8]/5 flex items-center gap-1 font-unica" 
                  onClick={() => navigate('/business/analysis')}
                >
                  View Analysis
                  <ChevronRight size={16} />
                </Button>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="bg-[#1231B8]/5 p-4 rounded-xl">
                    <div className="flex items-center gap-2">
                      <div className="bg-[#1231B8]/10 p-2 rounded-lg">
                        <ArrowUp className="text-[#1231B8]" size={18} />
                      </div>
                      <span className="text-sm font-medium text-[#1231B8] font-unica">Income</span>
                    </div>
                    <p className="mt-2 text-2xl font-semibold text-[#000000] font-unica">{walletData.income}</p>
                    <p className="text-xs text-green-600 mt-1 font-unica">+12% from last month</p>
                  </div>
                  
                  <div className="bg-[#1231B8]/5 p-4 rounded-xl">
                    <div className="flex items-center gap-2">
                      <div className="bg-[#1231B8]/10 p-2 rounded-lg">
                        <ArrowDown className="text-[#1231B8]" size={18} />
                      </div>
                      <span className="text-sm font-medium text-[#1231B8] font-unica">Expenses</span>
                    </div>
                    <p className="mt-2 text-2xl font-semibold text-[#000000] font-unica">{walletData.expenses}</p>
                    <p className="text-xs text-red-600 mt-1 font-unica">-8% from last month</p>
                  </div>
                </div>
                
                <div className="mt-6">
                  <h4 className="text-sm font-medium mb-3 text-[#1231B8] font-unica">Recent Transactions</h4>
                  <div className="space-y-3">
                    {[
                      { name: 'Office Supplies', date: 'May 15, 2023', amount: '₦45,600', type: 'expense' },
                      { name: 'Client Payment', date: 'May 12, 2023', amount: '₦120,000', type: 'income' },
                      { name: 'Internet Bill', date: 'May 10, 2023', amount: '₦25,000', type: 'expense' },
                    ].map((transaction, index) => (
                      <div key={index} className="flex justify-between items-center p-3 border-b border-gray-100 last:border-0">
                        <div className="flex items-center gap-3">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center ${transaction.type === 'income' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                            {transaction.type === 'income' ? <ArrowDown className="h-5 w-5" /> : <ArrowUp className="h-5 w-5" />}
                          </div>
                          <div>
                            <p className="font-medium text-sm font-unica">{transaction.name}</p>
                            <p className="text-xs text-gray-500 font-unica">{transaction.date}</p>
                          </div>
                        </div>
                        <p className={`font-medium ${transaction.type === 'income' ? 'text-green-600' : 'text-red-600'} font-unica`}>
                          {transaction.type === 'income' ? '+' : '-'} {transaction.amount}
                        </p>
                      </div>
                    ))}
                  </div>
                  <Button 
                    variant="outline" 
                    className="w-full mt-4 font-unica" 
                    onClick={() => navigate('/business/transactions')}
                  >
                    View All Transactions
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="md:col-span-4 space-y-6">
            <Card className="wallet-balance-card modern-card shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
              <CardHeader>
                <CardTitle className="font-unica">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 gap-3">
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-20 p-2 text-xs font-unica" 
                  onClick={() => navigate('/business/pos-management')}
                >
                  <Smartphone className="h-5 w-5 mb-1" />
                  <span>POS Terminal</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-20 p-2 text-xs font-unica" 
                  onClick={() => navigate('/business/loans')}
                >
                  <Landmark className="h-5 w-5 mb-1" />
                  <span>Apply for Loan</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-20 p-2 text-xs font-unica" 
                  onClick={() => navigate('/business/bill-payment')}
                >
                  <Receipt className="h-5 w-5 mb-1" />
                  <span>Pay Bills</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  className="flex flex-col items-center justify-center h-20 p-2 text-xs font-unica" 
                  onClick={() => navigate('/business/payment-links')}
                >
                  <Plus className="h-5 w-5 mb-1" />
                  <span>Payment Link</span>
                </Button>
              </CardContent>
            </Card>
            
            <Card className="wallet-balance-card modern-card shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
              <CardHeader>
                <CardTitle className="font-unica">Your Business Accounts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="bg-[#1231B8]/5 p-3 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Building className="h-4 w-4 text-[#1231B8]" />
                    <p className="font-medium text-sm font-unica">KojaPay Business</p>
                  </div>
                  <p className="text-xs text-gray-500 font-unica">Account Number</p>
                  <p className="font-medium font-unica">{walletData.accountNumber}</p>
                </div>
                
                <Button variant="outline" className="w-full font-unica">
                  <Plus className="mr-2 h-4 w-4" />
                  Link Another Account
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Bill Payment Dialog */}
      <BillPaymentDialog 
        open={billPaymentOpen}
        onOpenChange={setBillPaymentOpen}
        accountNumber={accountNumber}
      />

      {/* Loans Dialog */}
      <LoansDialog
        open={loansOpen}
        onOpenChange={setLoansOpen}
        accountNumber={accountNumber}
      />

      {/* Cards Dialog */}
      <CardsDialog
        open={cardsOpen}
        onOpenChange={setCardsOpen}
        userName={businessName}
      />
    </BusinessLayout>
  );
};

// Make sure to properly export the component
export default BusinessWallet;
