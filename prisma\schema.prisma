// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and profile information
model User {
  id                String            @id @default(uuid())
  email             String            @unique
  passwordHash      String
  fullName          String
  phone             String?
  accountNumber     String            @unique
  bvn               String?           @unique
  dateOfBirth       DateTime?
  address           String?
  profilePicture    String?
  role              UserRole          @default(USER)
  status            UserStatus        @default(ACTIVE)
  tier              AccountTier       @default(TIER1)
  lastLogin         DateTime?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  accounts          Account[]
  transactions      Transaction[]
  cards             Card[]
  loans             Loan[]
  notifications     Notification[]
  beneficiaries     Beneficiary[]
  notificationPreferences NotificationPreferences?

  // Savings Relations
  personalSavingsPlans PersonalSavingsPlan[]
  groupSavingsMembers GroupSavingsMember[]
  savingsContributions SavingsContribution[]
  createdGroupSavings GroupSavings[]

  @@map("users")
}

// User roles for permission management
enum UserRole {
  USER
  ADMIN
  SUPPORT
}

// User account status
enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

// Account tiers for transaction limits
enum AccountTier {
  TIER1
  TIER2
}

// Account model for storing bank account information
model Account {
  id                String            @id @default(uuid())
  accountNumber     String            @unique
  accountName       String
  accountType       AccountType
  balance           Float             @default(0.0)
  currencyCode      String            @default("NGN")
  isActive          Boolean           @default(true)
  dailyLimit        Float?            // Daily transaction limit based on tier
  withdrawalLimit   Float?            // Daily withdrawal limit based on tier
  userId            String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id])
  transactions      Transaction[]

  @@map("accounts")
}

// Account types
enum AccountType {
  SAVINGS
  CURRENT
  FIXED_DEPOSIT
}

// Transaction model for storing transaction history
model Transaction {
  id                String            @id @default(uuid())
  transactionReference String         @unique
  transactionType   TransactionType
  amount            Float
  narration         String?
  status            TransactionStatus
  senderAccount     String?
  senderName        String?
  senderBankCode    String?
  receiverAccount   String?
  receiverName      String?
  receiverBankCode  String?
  transactionDate   DateTime          @default(now())
  fee               Float             @default(0.0)
  balanceAfter      Float?
  metadata          Json?
  accountId         String
  userId            String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  account           Account           @relation(fields: [accountId], references: [id])
  user              User              @relation(fields: [userId], references: [id])

  @@map("transactions")
}

// Transaction types
enum TransactionType {
  CREDIT
  DEBIT
}

// Transaction status
enum TransactionStatus {
  PENDING
  SUCCESSFUL
  FAILED
  REVERSED
}

// Card model for storing virtual and physical card information
model Card {
  id                String            @id @default(uuid())
  cardNumber        String            @unique
  cardType          CardType
  cardName          String
  expiryMonth       Int
  expiryYear        Int
  cvv               String
  isActive          Boolean           @default(true)
  dailyLimit        Float             @default(100000.0)
  userId            String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id])

  @@map("cards")
}

// Card types
enum CardType {
  VIRTUAL
  PHYSICAL
}

// Loan model for storing loan information
model Loan {
  id                String            @id @default(uuid())
  loanReference     String            @unique
  amount            Float
  interestRate      Float
  tenure            Int               // In months
  startDate         DateTime
  endDate           DateTime
  purpose           String?
  status            LoanStatus
  repaymentAmount   Float
  amountPaid        Float             @default(0.0)
  nextPaymentDate   DateTime?
  userId            String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id])

  @@map("loans")
}

// Loan status
enum LoanStatus {
  PENDING
  APPROVED
  DISBURSED
  REPAYING
  COMPLETED
  DEFAULTED
  REJECTED
}

// Notification model for storing user notifications
model Notification {
  id                String            @id @default(uuid())
  title             String
  message           String
  isRead            Boolean           @default(false)
  type              NotificationType
  metadata          Json?
  userId            String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id])

  @@map("notifications")
}

// Notification types
enum NotificationType {
  TRANSACTION
  LOAN
  SYSTEM
  SECURITY
}

// Bank model for storing supported bank information
model Bank {
  id                String            @id @default(uuid())
  bankCode          String            @unique
  bankName          String
  logoUrl           String?
  isActive          Boolean           @default(true)
  sortCode          String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("banks")
}

// Beneficiary model for storing frequently used transfer recipients
model Beneficiary {
  id                String            @id @default(uuid())
  accountNumber     String
  accountName       String
  bankCode          String?
  bankName          String?
  nickname          String?
  userId            String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  
  // Relations
  user              User              @relation(fields: [userId], references: [id])

  @@unique([userId, accountNumber, bankCode])
  @@map("beneficiaries")
}

// Bill payment model for storing bill payment information
model BillPayment {
  id                String            @id @default(uuid())
  referenceNumber   String            @unique
  amount            Float
  billType          BillType
  billReference     String            // Customer ID or reference number
  billName          String            // Service provider name
  status            TransactionStatus
  metadata          Json?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("bill_payments")
}

// Bill types
enum BillType {
  ELECTRICITY
  WATER
  TELEVISION
  INTERNET
  EDUCATION
  GOVERNMENT
  OTHER
}

// Fee Settings model for managing transaction fees and charges
model FeeSettings {
  id                String            @id @default(uuid())
  feeType           FeeType
  name              String
  description       String?
  percentage        Float?
  fixedAmount       Float?
  minAmount         Float?
  maxAmount         Float?
  currencyCode      String            @default("NGN")
  applicableToUserTypes UserRole[]
  applicableToAccountTypes AccountType[]
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("fee_settings")
}

// Fee types
enum FeeType {
  TRANSFER
  WITHDRAWAL
  DEPOSIT
  LOAN_PROCESSING
  CARD_ISSUANCE
  ACCOUNT_MAINTENANCE
  POS_TRANSACTION
  ESCROW
  BILL_PAYMENT
  INTERNATIONAL_TRANSFER
}

// Settings model for application settings
model Settings {
  id                String            @id @default(uuid())
  key               String            @unique
  value             String
  description       String?
  isPublic          Boolean           @default(false)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("settings")
}

// Audit log for tracking system actions
model AuditLog {
  id                String            @id @default(uuid())
  action            String
  entityType        String
  entityId          String?
  oldValues         Json?
  newValues         Json?
  ipAddress         String?
  userAgent         String?
  userId            String?
  createdAt         DateTime          @default(now())

  @@map("audit_logs")
}

// BankOneTransaction model for storing raw BankOne API transaction responses
model BankOneTransaction {
  id                String            @id @default(uuid())
  transactionReference String         @unique
  rawResponse       Json
  processedAt       DateTime          @default(now())

  @@map("bankone_transactions")
}

// ApiKey model for managing API keys
model ApiKey {
  id                String            @id @default(uuid())
  key               String            @unique
  name              String
  isActive          Boolean           @default(true)
  lastUsed          DateTime?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("api_keys")
}

// Session model for user sessions
model Session {
  id                String            @id @default(uuid())
  token             String            @unique
  userId            String
  expiresAt         DateTime
  ipAddress         String?
  userAgent         String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  @@map("sessions")
}

// NotificationPreferences model for storing user notification settings
model NotificationPreferences {
  id                String            @id @default(uuid())
  userId            String            @unique
  emailNotifications Boolean          @default(true)
  pushNotifications Boolean          @default(true)
  smsNotifications  Boolean          @default(false)
  inAppNotifications Boolean          @default(true)
  marketingNotifications Boolean     @default(false)
  securityNotifications Boolean      @default(true)
  transactionNotifications Boolean   @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  user              User              @relation(fields: [userId], references: [id])

  @@map("notification_preferences")
}

// Personal Savings Plan model (GoFundMe-style savings)
model PersonalSavingsPlan {
  id                String            @id @default(uuid())
  title             String
  description       String?
  targetAmount      Float
  currentAmount     Float             @default(0.0)
  targetDate        DateTime?
  isPublic          Boolean           @default(true)
  allowContributions Boolean          @default(true)
  status            SavingsPlanStatus @default(ACTIVE)
  creatorId         String
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  creator           User              @relation(fields: [creatorId], references: [id])
  contributions     SavingsContribution[]

  @@map("personal_savings_plans")
}

// Group Savings model (Individual target motivation system)
model GroupSavings {
  id                String            @id @default(uuid())
  name              String
  description       String?
  targetAmount      Float
  targetDate        DateTime?
  creatorId         String
  isActive          Boolean           @default(true)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt

  // Relations
  creator           User              @relation(fields: [creatorId], references: [id])
  members           GroupSavingsMember[]

  @@map("group_savings")
}

// Group Savings Member model (Individual savings within group)
model GroupSavingsMember {
  id                String            @id @default(uuid())
  groupId           String
  userId            String
  individualTarget  Float
  currentAmount     Float             @default(0.0)
  joinedAt          DateTime          @default(now())
  isActive          Boolean           @default(true)

  // Relations
  group             GroupSavings      @relation(fields: [groupId], references: [id])
  user              User              @relation(fields: [userId], references: [id])

  @@unique([groupId, userId])
  @@map("group_savings_members")
}

// Savings Contribution model (For GoFundMe-style contributions)
model SavingsContribution {
  id                String            @id @default(uuid())
  planId            String
  contributorId     String
  amount            Float
  message           String?
  isAnonymous       Boolean           @default(false)
  createdAt         DateTime          @default(now())

  // Relations
  plan              PersonalSavingsPlan @relation(fields: [planId], references: [id])
  contributor       User              @relation(fields: [contributorId], references: [id])

  @@map("savings_contributions")
}

// Savings Plan Status enum
enum SavingsPlanStatus {
  ACTIVE
  COMPLETED
  PAUSED
  CANCELLED
}
