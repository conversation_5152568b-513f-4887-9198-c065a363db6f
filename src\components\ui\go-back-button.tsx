
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from './button';
import { ArrowLeft } from 'lucide-react';

interface GoBackButtonProps {
  className?: string;
}

const GoBackButton: React.FC<GoBackButtonProps> = ({ className }) => {
  const navigate = useNavigate();

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleGoBack}
      className={`rounded-[20px] hover:bg-gray-100 transition-colors mb-4 ${className}`}
    >
      <ArrowLeft size={16} className="mr-2" />
      <span>Back</span>
    </Button>
  );
};

export { GoBackButton };
