
import React, { ReactNode, useState } from 'react';
import BusinessSidebar from './BusinessSidebar';
import { Bell, Menu, CreditCard, Store, Search, ShoppingBag } from 'lucide-react';
import { Button } from './ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { Sheet, SheetContent, SheetTrigger } from './ui/sheet';
import { useIsMobile } from '@/hooks/use-mobile';
import { Avatar } from './ui/avatar';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { MicroButton } from './ui/micro-button';
import { Badge } from './ui/badge';
import OTPVerification from './OTPVerification';

interface BusinessLayoutProps {
  children: ReactNode;
  pageTitle?: string;
}

const BusinessLayout: React.FC<BusinessLayoutProps> = ({
  children,
  pageTitle = "Business Dashboard"
}) => {
  const {
    user,
  } = useAuth();
  const isMobile = useIsMobile();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const {
    toast
  } = useToast();
  const navigate = useNavigate();
  const businessUser = user as any;
  const businessName = businessUser?.businessName || 'Business';
  const accountNumber = businessUser?.accountNumber || '**********';
  const firstName = businessUser?.fullName?.split(' ')[0] || 'User';

  return (
    <div className="flex min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 text-kojaDark relative">
      <div className="logo-container fixed top-3 left-4 z-50 w-12 h-12">
        <img src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" alt="KojaPay Logo" className="logo-image w-9 h-9" />
      </div>
      
      {!isMobile && (
        <aside className="fixed left-0 top-16 z-10 h-[calc(100%-4rem)]">
          <BusinessSidebar firstName={firstName} accountNumber={accountNumber} businessName={businessName} logoUrl="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" />
        </aside>
      )}
      
      <div className="w-full flex flex-col min-h-screen">
        <header className="backdrop-blur-xl bg-white/70 shadow-md fixed top-0 left-0 right-0 z-30">
          <div className="grid grid-cols-3 md:grid-cols-12 items-center w-full h-16 px-4">
            {/* Left section - Mobile menu or Logo area */}
            <div className="col-span-1 md:col-span-3 flex items-center">
              {isMobile && (
                <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
                  <SheetTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Menu />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="p-0 bg-banklyBlue/95 text-white">
                    <BusinessSidebar firstName={firstName} accountNumber={accountNumber} businessName={businessName} logoUrl="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" />
                  </SheetContent>
                </Sheet>
              )}
              <div className="ml-10 md:ml-14 flex items-center gap-2 font-unica font-bold">
                {/* Brand name or additional content can go here */}
              </div>
            </div>
            
            {/* Middle section - Search bar */}
            <div className="col-span-1 md:col-span-5 flex justify-center">
              {!isMobile && (
                <div className="w-full max-w-md relative">
                  <Search className="absolute left-3 top-2.5 h-4 w-4 text-kojaGray" />
                  <input type="text" placeholder="Search..." className="form-input pl-9 w-full bg-white/80 text-sm rounded-[20px] font-unica" />
                </div>
              )}
            </div>
            
            {/* Right section - Actions and profile - IMPROVED SPACING */}
            <div className="col-span-1 md:col-span-4 flex justify-end items-center gap-4 md:gap-6">
              {!isMobile && (
                <>
                  <Button variant="outline" size="sm" className="hidden lg:flex items-center gap-1 border-purple-200 hover:bg-purple-50 text-xs font-unica" onClick={() => navigate('/business/pos')}>
                    <CreditCard size={14} className="text-purple-600" />
                    <span>POS Terminal</span>
                  </Button>
                  
                  <Button variant="outline" size="sm" className="hidden lg:flex items-center gap-1 border-blue-200 hover:bg-blue-50 text-xs font-unica" onClick={() => navigate('/business/ecommerce')}>
                    <ShoppingBag size={14} className="text-blue-600" />
                    <span>E-commerce</span>
                  </Button>
                </>
              )}
              
              {/* Notifications with improved spacing */}
              <MicroButton variant="ghost" size="xs" className="relative" onClick={() => navigate('/business/notifications')}>
                <Bell size={15} />
                <Badge className="absolute -top-1 -right-1 h-4 w-4 p-0 flex items-center justify-center text-[10px] bg-red-500 font-unica">
                  3
                </Badge>
              </MicroButton>
              
              {/* Mobile search button */}
              {isMobile && (
                <Button variant="ghost" size="icon" className="text-gray-600" onClick={() => toast({
                  title: "Search",
                  description: "Search functionality coming soon for mobile",
                })}>
                  <Search size={15} />
                </Button>
              )}
              
              {/* Profile avatar with improved spacing */}
              <Avatar 
                className={`${isMobile ? 'h-6 w-6' : 'h-7 w-7'} cursor-pointer`} 
                onClick={() => navigate('/business/profile')}
              >
                <div className="bg-gradient-to-br from-purple-500 to-indigo-600 h-full w-full flex items-center justify-center text-white">
                  {firstName.charAt(0)}
                </div>
              </Avatar>
            </div>
          </div>
        </header>
        
        <main className={`p-3 pt-20 flex-grow ${!isMobile ? 'ml-64' : 'ml-0'}`}>
          <h1 className="text-2xl font-bold mb-4 font-unica">{pageTitle}</h1>
          {children}
        </main>
        
        <footer className={`border-t border-gray-200/30 p-2 text-center text-xs text-kojaGray backdrop-blur-sm bg-white/30 ${!isMobile ? 'ml-64' : 'ml-0'} mt-auto rounded-t-[20px]`}>
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center">
            <div className="text-xs font-unica">© {new Date().getFullYear()} KojaPay Business. All rights reserved.</div>
            <div className="flex gap-3 mt-1 md:mt-0">
              <a href="/legal/privacy-policy" className="animated-underline hover:text-kojaPrimary transition-colors text-xs font-unica">Privacy</a>
              <a href="/legal/terms-of-service" className="animated-underline hover:text-kojaPrimary transition-colors text-xs font-unica">Terms</a>
              <a href="#" className="animated-underline hover:text-kojaPrimary transition-colors text-xs font-unica">Security</a>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default BusinessLayout;
