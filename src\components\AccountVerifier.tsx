
import React, { useState } from 'react';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Loader2, Check, AlertCircle } from 'lucide-react';
import { BankOneApi } from '@/services/bankOneApi';
import BankSelector from './BankSelector';
import { Bank } from '@/types/bankOne';

interface AccountVerifierProps {
  transferType: 'same-bank' | 'other-bank';
  onVerificationSuccess: (accountNumber: string, accountName: string, bankName: string, bankCode?: string) => void;
  onVerificationError: (error: string) => void;
}

const AccountVerifier: React.FC<AccountVerifierProps> = ({
  transferType,
  onVerificationSuccess,
  onVerificationError
}) => {
  const [accountNumber, setAccountNumber] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [selectedBank, setSelectedBank] = useState<Bank | null>(null);
  const [error, setError] = useState<string | null>(null);

  const validateAccountNumber = (value: string) => {
    if (value.length !== 10) {
      setError('Account number must be 10 digits');
      return false;
    }
    if (!/^\d+$/.test(value)) {
      setError('Account number must contain only digits');
      return false;
    }
    setError(null);
    return true;
  };

  const handleAccountNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAccountNumber(value);
    setIsVerified(false);
    setError(null);
  };

  const handleBankSelect = (bank: Bank) => {
    setSelectedBank(bank);
    setIsVerified(false);
  };

  const handleVerify = async () => {
    if (!accountNumber) {
      setError('Please enter an account number');
      onVerificationError('Please enter an account number');
      return;
    }
    
    if (!validateAccountNumber(accountNumber)) {
      onVerificationError(error || 'Invalid account number');
      return;
    }

    if (transferType === 'other-bank' && !selectedBank) {
      setError('Please select a bank');
      onVerificationError('Please select a bank');
      return;
    }
    
    setIsVerifying(true);
    setError(null);
    
    try {
      let response;
      
      if (transferType === 'same-bank') {
        // Verify account within KojaPay
        response = await BankOneApi.verifyAccount(accountNumber);
      } else {
        // Verify account in other bank
        response = await BankOneApi.verifyAccount(accountNumber, selectedBank?.bankCode);
      }
      
      if (response.success && response.data) {
        setIsVerified(true);
        const bankName = transferType === 'same-bank' ? 'KojaPay' : (selectedBank?.bankName || '');
        onVerificationSuccess(
          accountNumber, 
          response.data.accountName, 
          bankName,
          selectedBank?.bankCode
        );
        
        console.log(`Account Verified: Successfully verified account: ${response.data.accountName}`);
      } else {
        setError(response.message || 'Account verification failed');
        setIsVerified(false);
        onVerificationError(response.message || 'Account verification failed');
      }
    } catch (error: any) {
      console.error('Account verification error:', error);
      const errorMessage = 'Failed to verify account. Please try again.';
      setError(errorMessage);
      setIsVerified(false);
      onVerificationError(errorMessage);
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className="space-y-4">
      {transferType === 'other-bank' && (
        <div className="space-y-2">
          <Label htmlFor="bank">Select Bank</Label>
          <BankSelector
            onBankSelect={handleBankSelect}
            selectedBankCode={selectedBank?.bankCode}
            disabled={isVerifying || isVerified}
          />
        </div>
      )}
      
      <div className="space-y-2">
        <Label htmlFor="accountNumber">Account Number</Label>
        <div className="relative">
          <EnhancedInput
            id="accountNumber"
            placeholder="Enter 10-digit account number"
            value={accountNumber}
            onChange={handleAccountNumberChange}
            disabled={isVerifying || isVerified}
            icon={
              isVerified ? (
                <Check className="h-5 w-5 text-green-500" />
              ) : error ? (
                <AlertCircle className="h-5 w-5 text-red-500" />
              ) : null
            }
          />
          
          <Button
            type="button"
            variant={isVerified ? "outline" : "default"}
            size="sm"
            className={`absolute right-2 top-1.5 ${isVerified ? 'border-green-500 text-green-600' : ''}`}
            onClick={handleVerify}
            disabled={isVerifying || !accountNumber || accountNumber.length !== 10}
          >
            {isVerifying ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying
              </>
            ) : isVerified ? (
              <>
                <Check className="mr-2 h-4 w-4" />
                Verified
              </>
            ) : (
              'Verify'
            )}
          </Button>
        </div>
        
        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
      </div>
    </div>
  );
};

export default AccountVerifier;
