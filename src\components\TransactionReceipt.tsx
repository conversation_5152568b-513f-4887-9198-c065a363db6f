
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Printer, Share2 } from 'lucide-react';
import TransactionStatus from './TransactionStatus';
import { formatDate } from '@/lib/utils';

interface TransactionReceiptProps {
  transactionId: string;
  date: Date;
  amount: number;
  recipient: string;
  recipientAccount?: string;
  recipientBank?: string;
  description?: string;
  status: 'success' | 'failed' | 'pending';
  reference?: string;
  fee?: number;
  senderName?: string;
  senderAccount?: string;
}

const TransactionReceipt: React.FC<TransactionReceiptProps> = ({
  transactionId,
  date,
  amount,
  recipient,
  recipientAccount,
  recipientBank,
  description,
  status,
  reference,
  fee = 0,
  senderName,
  senderAccount
}) => {
  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // In a real implementation, this would generate a PDF
    console.log('Downloading receipt...');
  };

  const handleShare = () => {
    // In a real implementation, this would open share options
    console.log('Sharing receipt...');
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader className="border-b text-center pb-6">
        <div className="flex justify-center mb-4">
          <img 
            src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" 
            alt="KojaPay Logo" 
            className="h-12 w-12"
          />
        </div>
        <CardTitle className="text-xl">Transaction Receipt</CardTitle>
      </CardHeader>
      
      <CardContent className="pt-6">
        <div className="mb-6">
          <TransactionStatus status={status} />
        </div>
        
        <div className="space-y-4">
          <div className="text-center">
            <p className="text-sm text-gray-500">Amount</p>
            <p className="text-3xl font-bold">₦{amount.toLocaleString()}</p>
          </div>
          
          <div className="border-t border-b border-dashed py-4 space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Date & Time</span>
              <span className="text-sm font-medium">{formatDate(date)}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Transaction ID</span>
              <span className="text-sm font-medium">{transactionId}</span>
            </div>
            
            {reference && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Reference</span>
                <span className="text-sm font-medium">{reference}</span>
              </div>
            )}
            
            {fee > 0 && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Fee</span>
                <span className="text-sm font-medium">₦{fee.toLocaleString()}</span>
              </div>
            )}
          </div>
          
          <div className="border-b border-dashed py-4 space-y-3">
            <h3 className="font-medium mb-2">Recipient Details</h3>
            
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Name</span>
              <span className="text-sm font-medium">{recipient}</span>
            </div>
            
            {recipientAccount && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Account Number</span>
                <span className="text-sm font-medium">{recipientAccount}</span>
              </div>
            )}
            
            {recipientBank && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Bank</span>
                <span className="text-sm font-medium">{recipientBank}</span>
              </div>
            )}
          </div>
          
          {senderName && (
            <div className="border-b border-dashed py-4 space-y-3">
              <h3 className="font-medium mb-2">Sender Details</h3>
              
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">Name</span>
                <span className="text-sm font-medium">{senderName}</span>
              </div>
              
              {senderAccount && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Account Number</span>
                  <span className="text-sm font-medium">{senderAccount}</span>
                </div>
              )}
            </div>
          )}
          
          {description && (
            <div className="py-4">
              <h3 className="font-medium mb-2">Description</h3>
              <p className="text-sm">{description}</p>
            </div>
          )}
          
          <div className="flex justify-between pt-4">
            <Button 
              variant="outline" 
              className="flex items-center gap-1" 
              onClick={handlePrint}
            >
              <Printer size={16} />
              Print
            </Button>
            
            <Button 
              variant="outline" 
              className="flex items-center gap-1"
              onClick={handleDownload}
            >
              <Download size={16} />
              Download
            </Button>
            
            <Button 
              variant="outline" 
              className="flex items-center gap-1"
              onClick={handleShare}
            >
              <Share2 size={16} />
              Share
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TransactionReceipt;
