
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Bell, CheckCircle, AlertTriangle, Info, Clock, CreditCard, DollarSign, Users, Store, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import TransactionStatus from '@/components/TransactionStatus';

interface BusinessNotification {
  id: string;
  type: 'transaction' | 'security' | 'system' | 'staff';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: 'high' | 'medium' | 'low';
  icon: React.ReactNode;
}

const BusinessNotifications = () => {
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<BusinessNotification[]>([
    {
      id: '1',
      type: 'transaction',
      title: 'Large Transaction Alert',
      message: 'A transaction of ₦500,000 has been processed for XYZ Enterprises.',
      timestamp: '15 minutes ago',
      read: false,
      priority: 'high',
      icon: <DollarSign className="h-5 w-5 text-kojaYellow" />
    },
    {
      id: '2',
      type: 'security',
      title: 'Admin Login Detected',
      message: 'A new admin login was detected from an unrecognized device.',
      timestamp: '1 hour ago',
      read: false,
      priority: 'high',
      icon: <AlertTriangle className="h-5 w-5 text-red-400" />
    },
    {
      id: '3',
      type: 'staff',
      title: 'Staff Card Issued',
      message: 'A new staff card has been issued to Adesola James.',
      timestamp: '3 hours ago',
      read: true,
      priority: 'medium',
      icon: <CreditCard className="h-5 w-5 text-kojaYellow" />
    },
    {
      id: '4',
      type: 'system',
      title: 'API Usage Alert',
      message: 'Your API usage is at 85% of your monthly limit.',
      timestamp: '1 day ago',
      read: false,
      priority: 'medium',
      icon: <Info className="h-5 w-5 text-blue-400" />
    },
    {
      id: '5',
      type: 'staff',
      title: 'Payroll Processing',
      message: 'Monthly payroll has been processed successfully for 12 staff members.',
      timestamp: '2 days ago',
      read: true,
      priority: 'medium',
      icon: <Users className="h-5 w-5 text-kojaYellow" />
    },
    {
      id: '6',
      type: 'transaction',
      title: 'POS Terminal Offline',
      message: 'POS Terminal #003 at Victoria Island branch has been offline for 3 hours.',
      timestamp: '4 hours ago',
      read: false,
      priority: 'high',
      icon: <Store className="h-5 w-5 text-red-400" />
    }
  ]);

  // Filter notifications by type
  const transactionNotifications = notifications.filter(notif => notif.type === 'transaction');
  const securityNotifications = notifications.filter(notif => notif.type === 'security');
  const systemNotifications = notifications.filter(notif => notif.type === 'system');
  const staffNotifications = notifications.filter(notif => notif.type === 'staff');
  
  // Count unread notifications
  const unreadCount = notifications.filter(notif => !notif.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notif => ({ ...notif, read: true }))
    );
    toast({
      title: "All notifications marked as read",
      description: "You have no new notifications",
    });
  };

  const deleteNotification = (id: string) => {
    setNotifications(prevNotifications =>
      prevNotifications.filter(notif => notif.id !== id)
    );
    toast({
      title: "Notification deleted",
      description: "The notification has been removed",
    });
  };

  const getNotificationCard = (notification: BusinessNotification) => (
    <Card 
      key={notification.id} 
      className={`mb-4 transition-all duration-300 hover:shadow-lg animate-fade-in ${!notification.read ? 'border-l-4 border-kojaYellow' : ''}`}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="mt-1 bg-kojaYellow/10 p-2 rounded-full">
            {notification.icon}
          </div>
          <div className="flex-1">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold text-kojaDark">{notification.title}</h3>
                <p className="text-sm text-kojaGray mt-1">{notification.message}</p>
                <p className="text-xs text-kojaGray mt-2">{notification.timestamp}</p>
              </div>
              <div className="flex items-center gap-2">
                {!notification.read && (
                  <Badge variant="outline" className="bg-kojaYellow/10 text-kojaYellow border-kojaYellow/20">
                    New
                  </Badge>
                )}
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7 rounded-full hover:bg-red-100 hover:text-red-500"
                  onClick={() => deleteNotification(notification.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {!notification.read && (
              <Button 
                variant="link" 
                className="text-sm p-0 h-auto mt-2 text-kojaYellow"
                onClick={() => markAsRead(notification.id)}
              >
                Mark as read
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <BusinessLayout pageTitle="Business Notifications">
      <div className="max-w-4xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-2xl font-semibold text-kojaYellow flex items-center gap-2">
              <Bell className="h-6 w-6 text-kojaYellow" />
              Business Notifications
              {unreadCount > 0 && (
                <Badge className="ml-2 bg-kojaYellow text-kojaDark">
                  {unreadCount} new
                </Badge>
              )}
            </h1>
            <p className="text-kojaGray mt-1">Stay updated with your business account activities</p>
          </div>
          
          <Button 
            variant="outline" 
            className="text-kojaYellow border-kojaYellow/20 hover:bg-kojaYellow/5 shadow-sm"
            onClick={markAllAsRead}
          >
            Mark all as read
          </Button>
        </div>

        <div className="glass-card p-4 md:p-6 mb-6">
          <TransactionStatus 
            status={unreadCount > 0 ? "pending" : "success"} 
            message={unreadCount > 0 
              ? `You have ${unreadCount} unread notification${unreadCount > 1 ? 's' : ''}` 
              : "You're all caught up!"
            } 
            className="mb-4" 
          />
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList className="mb-6 grid grid-cols-2 md:grid-cols-5 bg-gray-100">
            <TabsTrigger value="all" className="data-[state=active]:bg-kojaYellow data-[state=active]:text-kojaDark">
              All
              {unreadCount > 0 && <Badge className="ml-2 bg-kojaYellow text-kojaDark">{unreadCount}</Badge>}
            </TabsTrigger>
            <TabsTrigger value="transactions" className="data-[state=active]:bg-kojaYellow data-[state=active]:text-kojaDark">
              Transactions
              {transactionNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-kojaYellow text-kojaDark">
                  {transactionNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="security" className="data-[state=active]:bg-kojaYellow data-[state=active]:text-kojaDark">
              Security
              {securityNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-red-400 text-white">
                  {securityNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="system" className="data-[state=active]:bg-kojaYellow data-[state=active]:text-kojaDark">
              System
              {systemNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-blue-400 text-white">
                  {systemNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="staff" className="data-[state=active]:bg-kojaYellow data-[state=active]:text-kojaDark">
              Staff
              {staffNotifications.filter(n => !n.read).length > 0 && (
                <Badge className="ml-2 bg-kojaYellow text-kojaDark">
                  {staffNotifications.filter(n => !n.read).length}
                </Badge>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="all" className="animate-fade-in">
            {notifications.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {notifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12 modern-card p-8 animate-fade-in">
                <Bell className="h-12 w-12 text-kojaYellow/30 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No notifications</h3>
                <p className="text-kojaGray">You don't have any notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="transactions" className="animate-fade-in">
            {transactionNotifications.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {transactionNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12 modern-card p-8 animate-fade-in">
                <DollarSign className="h-12 w-12 text-kojaYellow/30 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No transaction notifications</h3>
                <p className="text-kojaGray">You don't have any transaction notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="security" className="animate-fade-in">
            {securityNotifications.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {securityNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12 modern-card p-8 animate-fade-in">
                <AlertTriangle className="h-12 w-12 text-red-300/30 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No security notifications</h3>
                <p className="text-kojaGray">You don't have any security notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="system" className="animate-fade-in">
            {systemNotifications.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {systemNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12 modern-card p-8 animate-fade-in">
                <Info className="h-12 w-12 text-blue-300/30 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No system notifications</h3>
                <p className="text-kojaGray">You don't have any system notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="staff" className="animate-fade-in">
            {staffNotifications.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {staffNotifications.map(notification => getNotificationCard(notification))}
              </div>
            ) : (
              <div className="text-center py-12 modern-card p-8 animate-fade-in">
                <Users className="h-12 w-12 text-kojaYellow/30 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-kojaDark">No staff notifications</h3>
                <p className="text-kojaGray">You don't have any staff notifications at the moment.</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </BusinessLayout>
  );
};

export default BusinessNotifications;
