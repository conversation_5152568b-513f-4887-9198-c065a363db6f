
import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

const authButtonVariants = cva(
  "w-full flex items-center justify-center gap-2 font-medium transition-all duration-300 shadow-sm hover:shadow-md active:scale-[0.98] focus:outline-none disabled:opacity-70 disabled:pointer-events-none font-poppins",
  {
    variants: {
      variant: {
        primary: "bg-[#1231B8] hover:bg-[#1231B8]/90 text-white",
        secondary: "border border-[#1231B8]/20 bg-white/50 backdrop-blur-sm text-[#1231B8] hover:bg-[#1231B8]/5",
        yellow: "bg-[#FDE314] hover:bg-[#FDE314]/90 text-[#1231B8]",
        outline: "bg-transparent border border-white/20 text-white hover:bg-white/5",
        destructive: "bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 border border-red-400/20",
      },
      size: {
        default: "py-2.5 rounded-[40px] text-sm",
        sm: "py-2 rounded-[40px] text-xs",
        lg: "py-3 rounded-[40px] text-base",
        icon: "p-2 rounded-full aspect-square",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
);

export interface AuthButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof authButtonVariants> {
  loading?: boolean;
  icon?: React.ReactNode;
  fullWidth?: boolean;
}

const AuthButton = React.forwardRef<HTMLButtonElement, AuthButtonProps>(
  ({ className, variant, size, loading, icon, children, fullWidth = true, ...props }, ref) => {
    return (
      <button
        className={cn(authButtonVariants({ variant, size, className }), !fullWidth && "w-auto")}
        ref={ref}
        disabled={props.disabled || loading}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Processing...</span>
          </div>
        ) : (
          <>
            {icon && <span className="flex-shrink-0">{icon}</span>}
            {children}
          </>
        )}
      </button>
    );
  }
);

AuthButton.displayName = "AuthButton";

export { AuthButton, authButtonVariants };
