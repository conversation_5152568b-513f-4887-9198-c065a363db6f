
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { useAuth } from '@/contexts/AuthContext';
import { 
  Users, 
  Crown, 
  ArrowUpRight, 
  ArrowDownLeft, 
  MoreVertical,
  Settings,
  Eye,
  TrendingUp
} from 'lucide-react';
import { motion } from 'framer-motion';

const JointAccountDashboard = () => {
  const { user, switchToJointAccount } = useAuth();
  const [selectedJointAccount, setSelectedJointAccount] = useState<string | null>(null);

  const jointAccounts = (user as any)?.jointAccounts || [];

  const handleJointAccountSelect = (accountId: string) => {
    setSelectedJointAccount(accountId);
    if (switchToJointAccount) {
      switchToJointAccount(accountId);
    }
  };

  if (!jointAccounts.length) {
    return (
      <Card className="modern-card">
        <CardContent className="p-6 text-center">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">No Joint Accounts</h3>
          <p className="text-gray-600 mb-4">You don't have any joint accounts yet.</p>
          <Button variant="outline" className="text-kojaPrimary border-kojaPrimary/20">
            Create Joint Account
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <motion.div 
        className="flex items-center justify-between"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div className="flex items-center gap-3">
          <div className="bg-kojaPrimary/10 p-2 rounded-lg">
            <Users className="h-6 w-6 text-kojaPrimary" />
          </div>
          <div>
            <h2 className="text-xl font-bold">Joint Accounts</h2>
            <p className="text-gray-600">Manage your shared financial accounts</p>
          </div>
        </div>
        <Button variant="outline" size="sm" className="text-kojaPrimary border-kojaPrimary/20">
          <Settings className="h-4 w-4 mr-2" />
          Manage
        </Button>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {jointAccounts.map((account: any, index: number) => (
          <motion.div
            key={account.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="modern-card hover:shadow-lg transition-all duration-300 cursor-pointer"
                  onClick={() => handleJointAccountSelect(account.id)}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="bg-gradient-to-r from-kojaPrimary to-blue-600 p-2 rounded-full">
                        <Users className="h-5 w-5 text-white" />
                      </div>
                      {account.primaryHolder === user?.fullName && (
                        <Crown className="h-3 w-3 text-yellow-500 absolute -top-1 -right-1" />
                      )}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{account.name}</CardTitle>
                      <p className="text-sm text-gray-600">
                        {account.jointHolders.length} account holders
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Account Balance</span>
                    <Badge variant="secondary" className="text-xs">
                      {account.primaryHolder === user?.fullName ? 'Primary' : 'Secondary'}
                    </Badge>
                  </div>
                  <h3 className="text-2xl font-bold text-kojaDark">
                    ₦{account.balance.toLocaleString()}
                  </h3>
                  <div className="flex items-center text-sm text-green-600">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    <span>+2.5% this month</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Account Holders</span>
                    <span className="font-medium">{account.jointHolders.length}</span>
                  </div>
                  
                  <div className="flex -space-x-2">
                    {account.jointHolders.map((holder: string, holderIndex: number) => (
                      <Avatar key={holderIndex} className="h-8 w-8 border-2 border-white">
                        <AvatarFallback className="text-xs bg-kojaPrimary/10 text-kojaPrimary">
                          {holder.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2 pt-2">
                  <Button 
                    size="sm" 
                    className="flex-1 bg-kojaPrimary hover:bg-kojaPrimary/90"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleJointAccountSelect(account.id);
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="flex-1 text-kojaPrimary border-kojaPrimary/20"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ArrowUpRight className="h-4 w-4 mr-2" />
                    Transfer
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <Card className="modern-card">
        <CardHeader>
          <CardTitle className="text-lg">Recent Joint Account Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              { type: 'in', amount: 50000, description: 'Salary deposit - Jane Doe', time: '2 hours ago' },
              { type: 'out', amount: 15000, description: 'Grocery expenses', time: '1 day ago' },
              { type: 'in', amount: 25000, description: 'Freelance payment - John Smith', time: '2 days ago' }
            ].map((transaction, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50/80 rounded-xl">
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-full ${
                    transaction.type === 'in' ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    {transaction.type === 'in' ? (
                      <ArrowDownLeft className={`h-4 w-4 ${
                        transaction.type === 'in' ? 'text-green-600' : 'text-red-600'
                      }`} />
                    ) : (
                      <ArrowUpRight className={`h-4 w-4 ${
                        transaction.type === 'in' ? 'text-green-600' : 'text-red-600'
                      }`} />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-sm">{transaction.description}</p>
                    <p className="text-xs text-gray-600">{transaction.time}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-semibold ${
                    transaction.type === 'in' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.type === 'in' ? '+' : '-'}₦{transaction.amount.toLocaleString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default JointAccountDashboard;
