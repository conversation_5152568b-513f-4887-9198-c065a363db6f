
import * as React from 'react';
import { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Eye, EyeOff, LogIn, KeyRound, Shield } from 'lucide-react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { motion } from 'framer-motion';

const AdminLogin = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username || !password) {
      toast({
        title: "Missing Information",
        description: "Please enter your username and password",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    // Simulating API call
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // For demo purposes, admin/admin123 is the credentials
      if (username === 'admin' && password === 'admin123') {
        toast({
          title: "Login Successful",
          description: "Welcome to the admin portal",
        });
        navigate('/admin/dashboard');
      } else {
        toast({
          title: "Login Failed",
          description: "Invalid username or password",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Login Error",
        description: "An error occurred during login",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };
  
  return (
    <>
      <Helmet>
        <title>Admin Login | KojaPay</title>
      </Helmet>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#1231B8]/5 to-[#FDE314]/5 p-4">
        <div className="absolute top-6 left-6">
          <img 
            src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png"
            alt="KojaPay Logo"
            className="h-12 w-12"
          />
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md"
        >
          <Card className="shadow-[0_15px_30px_rgba(0,0,0,0.1)] backdrop-blur-xl">
            <CardHeader className="space-y-1">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-[#1231B8]/10 rounded-full">
                  <Shield className="h-6 w-6 text-[#1231B8]" />
                </div>
              </div>
              <CardTitle className="text-2xl text-center font-bold text-[#1231B8]">Admin Portal</CardTitle>
              <CardDescription className="text-center">
                Enter your credentials to log in to the admin panel
              </CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="username" className="text-sm font-medium">
                    Username
                  </label>
                  <div className="relative">
                    <Input
                      id="username"
                      type="text"
                      placeholder="admin"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="bg-white/70 backdrop-blur-sm pl-10 rounded-[20px]"
                      disabled={isLoading}
                    />
                    <KeyRound className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                  </div>
                </div>
                <div className="space-y-2">
                  <label htmlFor="password" className="text-sm font-medium">
                    Password
                  </label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="bg-white/70 backdrop-blur-sm pl-10 pr-10 rounded-[20px]"
                      disabled={isLoading}
                    />
                    <KeyRound className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 text-white"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <LogIn className="mr-2 h-5 w-5" />
                      Log In
                    </div>
                  )}
                </Button>
              </CardFooter>
            </form>
            <div className="px-6 pb-6 text-center">
              <Button 
                variant="link" 
                onClick={() => navigate('/')}
                className="text-[#1231B8]"
              >
                Back to KojaPay
              </Button>
            </div>
          </Card>
          <div className="text-center mt-6 text-xs text-gray-500">
            <p>© {new Date().getFullYear()} KojaPay Admin Portal</p>
            <p className="mt-1">For authorized personnel only</p>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default AdminLogin;
