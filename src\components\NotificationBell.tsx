
import React, { useState, useEffect, useRef } from 'react';
import { Bell, CheckCircle2, AlertTriangle, CreditCard, Info, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import notificationService, { Notification, NotificationType } from '@/services/notificationService';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { useAuth } from '@/contexts/AuthContext';

interface ComponentNotification extends Omit<Notification, 'isRead'> {
  read: boolean;
}

const NotificationBell: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<ComponentNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const popoverRef = useRef<HTMLDivElement>(null);

  // Fetch notifications and unread count
  const fetchNotifications = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const [notificationsResponse, unreadCountResponse] = await Promise.all([
        notificationService.getNotifications(user.id),
        notificationService.getUnreadCount(user.id)
      ]);

      if (notificationsResponse.success && notificationsResponse.data) {
        // Map server notifications to component notifications (isRead → read)
        const componentNotifications = notificationsResponse.data.map(notification => ({
          ...notification,
          read: notification.isRead
        }));
        
        setNotifications(componentNotifications);
      }

      if (unreadCountResponse.success && unreadCountResponse.data) {
        setUnreadCount(unreadCountResponse.data.count);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch notifications on mount
  useEffect(() => {
    if (user?.id) {
      fetchNotifications();

      // Set up polling for new notifications (every 30 seconds)
      const intervalId = setInterval(fetchNotifications, 30000);
      return () => clearInterval(intervalId);
    }
  }, [user]);

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user?.id) return;

    try {
      const response = await notificationService.markAllAsRead(user.id);
      if (response.success) {
        setNotifications(prev => prev.map(notification => ({ ...notification, read: true })));
        setUnreadCount(0);
        toast({
          title: 'Success',
          description: 'All notifications marked as read',
        });
      }
    } catch (error) {
      console.error('Error marking notifications as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark notifications as read',
        variant: 'destructive'
      });
    }
  };

  // Mark a single notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      const response = await notificationService.markAsRead(notificationId);
      if (response.success) {
        setNotifications(prev =>
          prev.map(notification =>
            notification.id === notificationId ? { ...notification, read: true } : notification
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Handle notification click
  const handleNotificationClick = (notification: ComponentNotification) => {
    // Mark as read if not already read
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Handle navigation based on notification type and metadata
    if (notification.metadata?.url) {
      navigate(notification.metadata.url as string);
      setIsOpen(false);
    } else {
      // Default navigation based on type
      switch (notification.type) {
        case "transaction":
          navigate('/transactions');
          break;
        case "security":
          navigate('/security');
          break;
        case "loan":
          navigate('/loans');
          break;
        case "system":
        case "marketing":
        default:
          // Just close the popover for general notifications
          break;
      }
      setIsOpen(false);
    }
  };

  // Get filtered notifications based on active tab
  const getFilteredNotifications = () => {
    if (activeTab === 'all') {
      return notifications;
    }
    return notifications.filter(notification => notification.type.toLowerCase() === activeTab);
  };

  // Get icon based on notification type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "security":
        return <Shield className="h-4 w-4 text-red-500" />;
      case "transaction":
        return <CreditCard className="h-4 w-4 text-blue-500" />;
      case "system":
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      case "loan":
        return <CreditCard className="h-4 w-4 text-green-500" />;
      case "marketing":
        return <Info className="h-4 w-4 text-purple-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  // Handle click outside to close popover
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Render notification skeleton loaders
  const renderSkeletons = () => {
    return Array(3)
      .fill(0)
      .map((_, index) => (
        <div key={index} className="flex items-start gap-3 p-3 border-b last:border-0">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-full" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      ));
  };

  // Render empty state when no notifications
  const renderEmptyState = () => {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <Bell className="h-10 w-10 text-muted-foreground mb-3 opacity-20" />
        <p className="text-muted-foreground font-medium">No notifications</p>
        <p className="text-sm text-muted-foreground">
          You don't have any {activeTab !== 'all' ? activeTab : ''} notifications yet
        </p>
      </div>
    );
  };

  // Render notification item
  const renderNotificationItem = (notification: ComponentNotification) => {
    const timeAgo = formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true });

    return (
      <div
        key={notification.id}
        className={`flex items-start gap-3 p-3 border-b last:border-0 cursor-pointer hover:bg-muted/50 transition-colors ${!notification.read ? 'bg-muted/30' : ''}`}
        onClick={() => handleNotificationClick(notification)}
      >
        <div className="mt-1">{getNotificationIcon(notification.type)}</div>
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <h4 className={`font-medium text-sm ${!notification.read ? 'text-primary' : ''}`}>
              {notification.title}
            </h4>
            <span className="text-xs text-muted-foreground ml-2">{timeAgo}</span>
          </div>
          <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
          {!notification.read && (
            <div className="mt-1 flex">
              <Badge variant="outline" className="text-xs bg-primary/10 text-primary border-0">
                New
              </Badge>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div ref={popoverRef}>
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="relative"
            onClick={() => setIsOpen(!isOpen)}
          >
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <Badge
                className="absolute -top-1 -right-1 h-5 min-w-[20px] flex items-center justify-center bg-red-500 text-white text-xs p-0"
                variant="destructive"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[380px] p-0" align="end">
          <Card className="border-0 shadow-none">
            <CardHeader className="pb-2 pt-4 px-4">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Notifications</CardTitle>
                {unreadCount > 0 && (
                  <Button variant="ghost" size="sm" onClick={markAllAsRead}>
                    <CheckCircle2 className="h-4 w-4 mr-1" />
                    Mark all as read
                  </Button>
                )}
              </div>
              <Tabs
                defaultValue="all"
                value={activeTab}
                onValueChange={setActiveTab}
                className="mt-2"
              >
                <TabsList className="grid grid-cols-5 h-8">
                  <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
                  <TabsTrigger value="security" className="text-xs">Security</TabsTrigger>
                  <TabsTrigger value="transaction" className="text-xs">Transaction</TabsTrigger>
                  <TabsTrigger value="system" className="text-xs">System</TabsTrigger>
                  <TabsTrigger value="marketing" className="text-xs">Marketing</TabsTrigger>
                </TabsList>
                <TabsContent value={activeTab}>
                  <ScrollArea className="h-[300px]">
                    {isLoading ? (
                      renderSkeletons()
                    ) : getFilteredNotifications().length > 0 ? (
                      getFilteredNotifications().map(renderNotificationItem)
                    ) : (
                      renderEmptyState()
                    )}
                  </ScrollArea>
                </TabsContent>
              </Tabs>
            </CardHeader>
            <CardFooter className="p-3 border-t flex justify-center">
              <Button
                variant="link"
                size="sm"
                className="text-xs"
                onClick={() => {
                  navigate('/notifications');
                  setIsOpen(false);
                }}
              >
                View all notifications
              </Button>
            </CardFooter>
          </Card>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default NotificationBell;
