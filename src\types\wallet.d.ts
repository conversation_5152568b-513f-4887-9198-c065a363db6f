
declare module "@/types/wallet" {
  // Add missing props for dialog components
  export interface BillPaymentDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    accountNumber: string;
    isDrawer?: boolean; // Added to fix EnhancedWallet errors
  }

  export interface LoansDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    accountNumber: string;
    isDrawer?: boolean; // Added to fix EnhancedWallet errors
  }

  export interface CardsDialogProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    userName: string;
    isDrawer?: boolean; // Added to fix EnhancedWallet errors
  }

  // Update enhanced virtual card props to match how it's being used across components
  export interface EnhancedVirtualCardProps {
    name: string;
    onManageCard?: () => void;
    cardNumber?: string;
    expiryDate?: string;
    cardType?: 'personal' | 'business';
    variant?: 'physical' | 'virtual';
    className?: string;
    showDetails?: boolean;
    toggleDetails?: () => void;
  }
}
