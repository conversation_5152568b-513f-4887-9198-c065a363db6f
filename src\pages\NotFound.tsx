
import * as React from 'react';
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import SEO from "@/components/SEO";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-kojaDark to-[#001235] text-white">
      <SEO 
        title="Page Not Found | KojaPay"
        description="The page you are looking for doesn't exist or has been moved."
        ogType="website"
      />
      <div className="flex-grow flex items-center justify-center">
        <div className="text-center p-8 bg-white/5 backdrop-blur-lg rounded-xl border border-white/10">
          <h1 className="text-6xl font-unica mb-4 text-kojaYellow">404</h1>
          <p className="text-xl text-white/80 mb-6">Oops! Page not found</p>
          <p className="mb-8 text-white/60 max-w-md">
            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
          </p>
          <Link to="/" className="inline-block bg-kojaPrimary hover:bg-kojaPrimary/90 text-white px-6 py-3 rounded-xl">
            Return to Home
          </Link>
        </div>
      </div>
      <footer className="w-full p-2 text-center text-xs text-white/60 backdrop-blur-sm bg-white/5 mt-auto">
        <div className="max-w-7xl mx-auto">
          © {new Date().getFullYear()} KojaPay. All rights reserved.
        </div>
      </footer>
    </div>
  );
};

export default NotFound;
