
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { <PERSON>, CardContent, CardFooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, Store, Heart, ArrowRight, Star, Search, Filter, ShoppingBag, Package } from "lucide-react";
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import StoreMap from '@/components/StoreMap';
import { useCart } from '@/contexts/CartContext';
import { Product } from '@/services/ecommerceService';
import { useToast } from '@/hooks/use-toast';

const Ecommerce = () => {
  const navigate = useNavigate();
  const { addToCart, setIsCartOpen } = useCart();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  // Sample product data with updated images from public folder
  const featuredProducts = [
    {
      id: "1",
      name: "Smart Bluetooth Speaker",
      price: 25000,
      rating: 4.5,
      image: "/lovable-uploads/c6ef850a-0def-4097-aff9-b8dfc8bb22e6.png",
      category: "electronics",
      inventory: 20,
      description: "High quality bluetooth speaker with amazing sound",
      status: "In Stock",
      stock: 20,
      storeName: "TechHub"
    },
    {
      id: "2",
      name: "Premium Leather Wallet",
      price: 15000,
      rating: 4.8,
      image: "/lovable-uploads/7efdf088-98ad-45d6-b51d-a15cdcbef2c6.png",
      category: "fashion",
      inventory: 35,
      description: "Handcrafted genuine leather wallet",
      status: "In Stock",
      stock: 35,
      storeName: "Fashion Palace"
    },
    {
      id: "3",
      name: "Wireless Earbuds",
      price: 18500,
      rating: 4.3,
      image: "/lovable-uploads/f9c0ea5d-a465-49fb-8b2f-ccac2b858bad.png",
      category: "electronics",
      inventory: 15,
      description: "True wireless earbuds with noise cancellation",
      status: "In Stock",
      stock: 15,
      storeName: "Gadget World"
    },
    {
      id: "4",
      name: "Smartphone Stand",
      price: 3500,
      rating: 4.2,
      image: "/lovable-uploads/2abc36d0-01c9-4395-b926-976d7adb4088.png",
      category: "accessories",
      inventory: 50,
      description: "Adjustable smartphone stand for desk or bedside",
      status: "In Stock",
      stock: 50,
      storeName: "Home Essentials"
    }
  ];

  // Sample stores with updated images
  const popularStores = [
    { id: 1, name: "TechHub", type: "Electronics", logo: "/lovable-uploads/e299ea68-3e4d-4384-ba76-77e94d63296e.png" },
    { id: 2, name: "Fashion Palace", type: "Clothing", logo: "/lovable-uploads/bf9beaec-2b17-43c9-ad47-3873c06bdb2c.png" },
    { id: 3, name: "Home Essentials", type: "Home & Living", logo: "/lovable-uploads/9cf7be1b-0753-4e6e-8d1f-86279205dd91.png" },
    { id: 4, name: "Gadget World", type: "Electronics", logo: "/lovable-uploads/cacdd1f9-7979-45c9-b3ba-298996dc82cf.png" },
  ];

  const categories = [
    { name: "Electronics", icon: <Package className="h-4 w-4" /> },
    { name: "Fashion", icon: <ShoppingBag className="h-4 w-4" /> },
    { name: "Home", icon: <Store className="h-4 w-4" /> },
    { name: "Health", icon: <Package className="h-4 w-4" /> }
  ];

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleAddToCart = (product: Product) => {
    addToCart(product);
    toast({
      title: "Added to Cart",
      description: `${product.name} has been added to your cart`,
    });
  };

  const handleViewCart = () => {
    setIsCartOpen(true);
  };

  const handleProductClick = (productId: string) => {
    navigate(`/ecommerce/product/${productId}`);
  };

  const handleStoreClick = (storeId: number) => {
    navigate(`/ecommerce/store/${storeId}`);
  };

  return (
    <DashboardLayout pageTitle="Shop">
      <div className="py-6 space-y-8">
        {/* Header Section */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4"
        >
          <div>
            <h1 className="text-2xl font-bold text-gray-900">KojaPay Marketplace</h1>
            <p className="text-gray-600">Shop with ease using your KojaPay wallet</p>
          </div>
          
          <div className="flex space-x-3">
            <Button 
              variant="outline" 
              className="flex items-center border-[#fde314] text-gray-700"
              onClick={handleViewCart}
            >
              <ShoppingCart className="mr-2 h-4 w-4 text-[#fde314]" />
              <span>Cart</span>
            </Button>
            <Button 
              className="bg-[#fde314] hover:bg-[#fde314]/90 text-gray-900 border-none"
              onClick={() => navigate('/ecommerce/stores')}
            >
              <Store className="mr-2 h-4 w-4" />
              Stores
            </Button>
          </div>
        </motion.div>

        {/* Search and Filters */}
        <motion.div 
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          transition={{ delay: 0.1 }}
          className="flex flex-col sm:flex-row gap-4"
        >
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input 
              placeholder="Search products..." 
              className="pl-10 border-[#fde314]/30 focus:border-[#fde314]"
              value={searchQuery}
              onChange={handleSearchChange}
            />
          </div>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </Button>
        </motion.div>

        {/* Store Map */}
        <StoreMap />

        {/* Categories */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          transition={{ delay: 0.2 }}
        >
          <h2 className="text-lg font-medium mb-4">Categories</h2>
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant="outline"
                className="flex flex-col items-center justify-center h-24 hover:border-[#fde314] hover:bg-[#fde314]/5"
                onClick={() => navigate(`/ecommerce/category/${category.name.toLowerCase()}`)}
              >
                <div className="bg-gray-100 p-3 rounded-full mb-2">
                  {category.icon}
                </div>
                <span>{category.name}</span>
              </Button>
            ))}
          </div>
        </motion.div>

        {/* Popular Stores */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          transition={{ delay: 0.3 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Popular Stores</h2>
            <Button 
              variant="link" 
              className="text-[#fde314]"
              onClick={() => navigate('/ecommerce/stores')}
            >
              View All <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {popularStores.map((store) => (
              <Card 
                key={store.id} 
                className="overflow-hidden hover:shadow-md transition-all cursor-pointer"
                onClick={() => handleStoreClick(store.id)}
              >
                <CardContent className="p-4 flex flex-col items-center text-center">
                  <div className="w-16 h-16 rounded-full bg-gray-100 mb-3 overflow-hidden flex items-center justify-center">
                    <img src={store.logo} alt={store.name} className="object-cover" />
                  </div>
                  <h3 className="font-medium">{store.name}</h3>
                  <p className="text-sm text-gray-500">{store.type}</p>
                </CardContent>
                <CardFooter className="p-3 bg-gray-50 flex justify-center">
                  <Button variant="link" className="text-[#fde314] p-0 h-auto">
                    Visit Store
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </motion.div>

        {/* Featured Products */}
        <motion.div
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
          transition={{ delay: 0.4 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium">Featured Products</h2>
            <Button 
              variant="link" 
              className="text-[#fde314]"
              onClick={() => navigate('/ecommerce/products')}
            >
              View All <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProducts
              .filter(product => 
                searchQuery ? product.name.toLowerCase().includes(searchQuery.toLowerCase()) : true
              )
              .map((product) => (
                <Card key={product.id} className="overflow-hidden hover:shadow-md transition-all">
                  <div 
                    className="aspect-video overflow-hidden relative cursor-pointer"
                    onClick={() => handleProductClick(product.id)}
                  >
                    <img 
                      src={product.image} 
                      alt={product.name} 
                      className="object-cover w-full h-full"
                    />
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="absolute top-2 right-2 bg-white/80 hover:bg-white text-gray-700 rounded-full h-8 w-8"
                      onClick={(e) => {
                        e.stopPropagation();
                        toast({
                          title: "Added to Wishlist",
                          description: `${product.name} has been added to your wishlist`,
                        });
                      }}
                    >
                      <Heart className="h-4 w-4" />
                    </Button>
                    <Badge className="absolute top-2 left-2 bg-[#fde314] text-gray-900">
                      New
                    </Badge>
                  </div>
                  <CardContent 
                    className="p-4 cursor-pointer" 
                    onClick={() => handleProductClick(product.id)}
                  >
                    <div className="flex items-center text-yellow-500 mb-2">
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 fill-current" />
                      <Star className="h-4 w-4 text-gray-300" />
                      <span className="text-xs text-gray-600 ml-1">{product.rating}</span>
                    </div>
                    <h3 className="font-medium mb-1">{product.name}</h3>
                    <p className="text-lg font-bold text-gray-900">₦{product.price.toLocaleString()}</p>
                  </CardContent>
                  <CardFooter className="p-4 bg-gray-50">
                    <Button 
                      className="w-full bg-[#fde314] hover:bg-[#fde314]/90 text-gray-900"
                      onClick={() => handleAddToCart(product as Product)}
                    >
                      Add to Cart
                    </Button>
                  </CardFooter>
                </Card>
              ))}
          </div>
          
          {/* No Results */}
          {searchQuery && featuredProducts.filter(product => 
            product.name.toLowerCase().includes(searchQuery.toLowerCase())
          ).length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No products found matching "{searchQuery}"</p>
              <Button 
                variant="link" 
                className="text-[#fde314] mt-2"
                onClick={() => setSearchQuery('')}
              >
                Clear search
              </Button>
            </div>
          )}
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default Ecommerce;
