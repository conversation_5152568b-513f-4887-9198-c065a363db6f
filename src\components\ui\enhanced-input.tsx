
import * as React from "react";
import { cn } from "@/lib/utils";
import { CreditCard, Calendar, LockKeyhole, Plus, CornerDownRight } from "lucide-react";

export type InputVariant = "default" | "card" | "expiry" | "cvv" | "address";

interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  icon?: React.ReactNode;
  variant?: InputVariant;
  error?: string;
  optional?: boolean;
}

const getInputIcon = (variant: InputVariant) => {
  switch (variant) {
    case "card":
      return <CreditCard className="h-5 w-5 text-gray-400" />;
    case "expiry":
      return <Calendar className="h-5 w-5 text-gray-400" />;
    case "cvv":
      return <LockKeyhole className="h-5 w-5 text-gray-400" />;
    case "address":
      return <Plus className="h-5 w-5 text-gray-400" />;
    default:
      return null;
  }
};

const EnhancedInput = React.forwardRef<HTMLInputElement, EnhancedInputProps>(
  ({ className, icon, variant = "default", error, optional = false, ...props }, ref) => {
    // Determine if we should show the branded card icon
    const showBrandedCardIcon = variant === "card" && props.value && String(props.value).length >= 14;
    
    return (
      <div className="space-y-1 w-full">
        <div className={cn(
          "flex items-center w-full rounded-2xl border bg-white/90 backdrop-blur-sm px-3 py-2 text-base focus-within:ring-2 focus-within:ring-kojaPrimary/20 focus-within:border-kojaPrimary/70 focus-within:shadow-[0_0_0_3px_rgba(18,49,184,0.1)] transition-all duration-300",
          error ? "border-red-300" : "border-gray-300/60",
          className
        )}>
          {icon || getInputIcon(variant)}
          
          <input
            ref={ref}
            className="w-full bg-transparent px-2 py-1 outline-none placeholder:text-gray-400 disabled:cursor-not-allowed disabled:opacity-50 rounded-xl"
            {...props}
          />
          
          {variant === "card" && showBrandedCardIcon && (
            <div className="flex-shrink-0">
              <div className="w-8 h-5 rounded-xl overflow-hidden">
                <img src="https://brand.mastercard.com/content/dam/mccom/global/logos/logo-mastercard-mobile.svg" alt="Mastercard" />
              </div>
            </div>
          )}
          
          {optional && (
            <span className="text-sm text-gray-400 ml-2">Optional</span>
          )}
        </div>
        
        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
      </div>
    );
  }
);

EnhancedInput.displayName = "EnhancedInput";

export { EnhancedInput };
