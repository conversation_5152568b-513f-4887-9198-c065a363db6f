import * as React from 'react';
import { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import { PersonalAccount, BusinessAccount, AccountType } from '@/types/account';
import { useToast } from '@/hooks/use-toast';
import { login as apiLogin } from '@/services/authApi';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string, type?: string) => Promise<void>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  accountType: AccountType | null;
  register: (userData: PersonalAccount | BusinessAccount, accountType?: AccountType) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  userAccounts: {[key: string]: any};
  switchAccount: (newAccountType: AccountType) => Promise<void>;
  switchToJointAccount: (jointAccountId: string) => Promise<void>;
}

export interface User {
  id: string;
  name?: string;
  email?: string;
  role?: string;
  fullName?: string;
  balance?: number;
  businessName?: string;
  hasKidsAccounts?: boolean;
  hasJointAccounts?: boolean;
  jointAccounts?: JointAccount[];
}

interface JointAccount {
  id: string;
  name: string;
  balance: number;
  jointHolders: string[];
  primaryHolder: string;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accountType, setAccountType] = useState<AccountType | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [userAccounts, setUserAccounts] = useState<{[key: string]: any}>({});
  const { toast } = useToast();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const savedUser = localStorage.getItem('user');
        const savedAccountType = localStorage.getItem('accountType');
        
        if (savedUser) {
          setUser(JSON.parse(savedUser));
          if (savedAccountType) {
            setAccountType(savedAccountType as AccountType);
          }
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        setError('Authentication check failed');
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (phoneNumber: string, pin: string, type: string = 'auto-detect') => {
    setIsLoading(true);
    setError(null);
    try {
      // Call real backend API
      const response = await apiLogin({ phoneNumber, pin, role: 'USER' });
      const user = response.user;
      setUser(user);
      setAccountType(type as AccountType);
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('accountType', type);
      toast({
        title: 'Login successful',
        description: 'Welcome to KojaPay Banking!',
      });
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message || 'Login failed. Please check your credentials.');
      toast({
        title: 'Login failed',
        description: error.message || 'Please check your credentials and try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      setUser(null);
      setAccountType(null);
      localStorage.removeItem('user');
      localStorage.removeItem('accountType');
      toast({
        title: "Logged out successfully",
      });
    } catch (error) {
      console.error('Logout error:', error);
      setError('Logout failed');
      toast({
        title: "Logout failed",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const register = async (userData: PersonalAccount | BusinessAccount, type: AccountType = 'personal') => {
    setIsLoading(true);
    setError(null);
    
    try {
      const mockUser: User = {
        id: Math.random().toString(36).substr(2, 9),
        name: userData.fullName,
        fullName: userData.fullName,
        email: userData.email,
        role: 'USER',
        balance: 0,
      };
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setUser(mockUser);
      setAccountType(type);
      localStorage.setItem('user', JSON.stringify(mockUser));
      localStorage.setItem('accountType', type);
      
      toast({
        title: "Registration successful",
        description: "Your account has been created successfully!",
      });
      
      return Promise.resolve();
    } catch (error) {
      console.error('Registration error:', error);
      setError('Registration failed. Please try again.');
      toast({
        title: "Registration failed",
        description: "Please try again.",
        variant: "destructive",
      });
      return Promise.reject(error);
    } finally {
      setIsLoading(false);
    }
  };

  const switchAccount = async (newAccountType: AccountType) => {
    if (userAccounts[newAccountType]) {
      setUser(userAccounts[newAccountType]);
      setAccountType(newAccountType);
      localStorage.setItem('user', JSON.stringify(userAccounts[newAccountType]));
      localStorage.setItem('accountType', newAccountType);
      
      toast({
        title: "Account switched",
        description: `Switched to ${newAccountType} account`,
      });
    }
  };

  const switchToJointAccount = async (jointAccountId: string) => {
    const currentUser = user as User;
    if (currentUser?.jointAccounts) {
      const jointAccount = currentUser.jointAccounts.find(acc => acc.id === jointAccountId);
      if (jointAccount) {
        const jointUser = {
          ...currentUser,
          balance: jointAccount.balance,
          name: jointAccount.name,
          fullName: jointAccount.name,
          id: jointAccount.id
        };
        
        setUser(jointUser);
        localStorage.setItem('user', JSON.stringify(jointUser));
        
        toast({
          title: "Switched to joint account",
          description: `Now viewing ${jointAccount.name}`,
        });
      }
    }
  };

  const value = {
    user,
    loading,
    login,
    logout,
    isAuthenticated: !!user,
    accountType,
    register,
    isLoading,
    error,
    userAccounts,
    switchAccount,
    switchToJointAccount,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthProvider;
