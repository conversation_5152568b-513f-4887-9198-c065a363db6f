import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, Shield, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';

interface FraudAlertBannerProps {
  alert?: {
    id: string;
    title: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    timestamp: string;
  };
  onDismiss?: (id: string) => void;
  onViewDetails?: (id: string) => void;
}

const FraudAlertBanner: React.FC<FraudAlertBannerProps> = ({ 
  alert, 
  onDismiss, 
  onViewDetails 
}) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (alert) {
      setIsVisible(true);
    }
  }, [alert]);

  if (!alert || !isVisible) {
    return null;
  }

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss && alert) {
      onDismiss(alert.id);
    }
  };

  const handleViewDetails = () => {
    if (onViewDetails && alert) {
      onViewDetails(alert.id);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'high':
        return 'bg-amber-50 border-amber-200 text-amber-800';
      case 'medium':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'low':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getSeverityIcon = (severity: string) => {
    if (severity === 'critical' || severity === 'high') {
      return <AlertTriangle className="h-5 w-5 text-red-600" />;
    }
    return <Shield className="h-5 w-5 text-amber-600" />;
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
          className="mb-4"
        >
          <Alert className={`${getSeverityColor(alert.severity)} border`}>
            <div className="flex justify-between items-start">
              <div className="flex items-start">
                {getSeverityIcon(alert.severity)}
                <div className="ml-3">
                  <AlertTitle className="font-semibold">{alert.title}</AlertTitle>
                  <AlertDescription className="mt-1">
                    {alert.description}
                  </AlertDescription>
                  <div className="mt-2 flex items-center space-x-3">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="bg-white hover:bg-gray-50"
                      onClick={handleViewDetails}
                    >
                      <ExternalLink className="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                    <span className="text-xs text-gray-500">
                      {new Date(alert.timestamp).toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
              <Button variant="ghost" size="icon" onClick={handleDismiss} className="-mt-1 -mr-1">
                <X className="h-4 w-4" />
              </Button>
            </div>
          </Alert>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FraudAlertBanner;
