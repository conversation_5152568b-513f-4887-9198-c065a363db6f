import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { EnhancedInput } from '@/components/ui/enhanced-input';
import { MicroButton } from '@/components/ui/micro-button';
import { Loader2, Send, BuildingIcon, Users, ArrowRight, X, AlertCircle } from 'lucide-react';
import TransactionStatus from '@/components/TransactionStatus';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { BankOneApi } from '@/services/bankOneApi';
import { 
  Bank, 
  ApiResponse, 
  AccountVerificationResponse, 
  TransferResponse, 
  TransactionStatusResponse 
} from '@/types/bankOne';
import BankSelector from './BankSelector';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose
} from '@/components/ui/drawer';

interface TransferDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  isDrawer?: boolean;
}

type TransferStep = 'select-type' | 'enter-details' | 'confirm' | 'processing' | 'complete';
type TransferType = 'same-bank' | 'other-bank';

const TransferDialog: React.FC<TransferDialogProps> = ({ open, onOpenChange, isDrawer }) => {
  const [step, setStep] = useState<TransferStep>('select-type');
  const [transferType, setTransferType] = useState<TransferType>('same-bank');
  const [accountNumber, setAccountNumber] = useState('');
  const [amount, setAmount] = useState('');
  const [accountName, setAccountName] = useState('');
  const [bankName, setBankName] = useState('');
  const [isAccountVerified, setIsAccountVerified] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [transferStatus, setTransferStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const isMobile = useIsMobile();
  const { toast } = useToast();

  const resetForm = () => {
    setStep('select-type');
    setTransferType('same-bank');
    setAccountNumber('');
    setAmount('');
    setAccountName('');
    setBankName('');
    setIsAccountVerified(false);
    setTransferStatus('idle');
    setProgress(0);
    setError(null);
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const validateAccountNumber = (value: string) => {
    if (value.length !== 10) {
      setError('Account number must be 10 digits');
      return false;
    }
    if (!/^\d+$/.test(value)) {
      setError('Account number must contain only digits');
      return false;
    }
    setError(null);
    return true;
  };

  const handleVerifyAccount = () => {
    if (!accountNumber) {
      setError('Please enter an account number');
      return;
    }
    
    if (!validateAccountNumber(accountNumber)) {
      return;
    }
    
    setIsVerifying(true);
    setError(null);
    
    setTimeout(() => {
      if (transferType === 'same-bank') {
        setAccountName('John Doe');
        setBankName('KojaPay');
      } else {
        setAccountName('Jane Smith');
        setBankName('Other Bank');
      }
      setIsAccountVerified(true);
      setIsVerifying(false);
    }, 1500);
  };

  const validateAmount = (value: string) => {
    const numAmount = parseFloat(value);
    if (isNaN(numAmount) || numAmount <= 0) {
      setError('Please enter a valid amount');
      return false;
    }
    if (numAmount > 1000000) {
      setError('Amount cannot exceed ₦1,000,000');
      return false;
    }
    setError(null);
    return true;
  };

  const handleSubmitTransfer = () => {
    if (!validateAmount(amount)) {
      return;
    }

    setStep('processing');
    setTransferStatus('processing');
    setError(null);
    
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 10;
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setTransferStatus('success');
            setStep('complete');
          }, 500);
        }
        return newProgress > 100 ? 100 : newProgress;
      });
    }, 500);
  };

  const renderContent = () => {
    switch (step) {
      case 'select-type':
        return (
          <div className="space-y-4 py-4">
            <p className="text-center text-muted-foreground mb-4 font-futura">
              Choose where you want to send money
            </p>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <MicroButton
                variant="glass"
                onClick={() => {
                  setTransferType('same-bank');
                  setStep('enter-details');
                }}
                className="h-24 flex flex-col items-center justify-center gap-2 shadow-sm border border-kojaYellow/20 font-futura"
              >
                <Users className="h-8 w-8 text-kojaPrimary" />
                <span>Same Bank</span>
              </MicroButton>
              
              <MicroButton
                variant="glass"
                onClick={() => {
                  setTransferType('other-bank');
                  setStep('enter-details');
                }}
                className="h-24 flex flex-col items-center justify-center gap-2 shadow-sm border border-kojaYellow/20 font-futura"
              >
                <BuildingIcon className="h-8 w-8 text-kojaYellow" />
                <span>Other Banks</span>
              </MicroButton>
            </div>
          </div>
        );
        
      case 'enter-details':
        return (
          <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium font-futura">
                {transferType === 'same-bank' ? 'Same Bank Transfer' : 'Other Bank Transfer'}
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStep('select-type')}
                className="border-kojaYellow/20 font-futura"
              >
                Change
              </Button>
            </div>
            
            {transferType === 'other-bank' && (
              <div className="space-y-2">
                <Label htmlFor="bank" className="font-futura">Select Bank</Label>
                <div className="relative">
                  <select 
                    id="bank"
                    className="flex h-10 w-full rounded-md border border-kojaYellow/20 bg-white px-3 py-2 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-kojaPrimary/20 font-futura"
                    onChange={(e) => setBankName(e.target.value)}
                  >
                    <option value="">Select Bank</option>
                    <option value="First Bank">First Bank</option>
                    <option value="GTBank">GTBank</option>
                    <option value="Zenith Bank">Zenith Bank</option>
                    <option value="UBA">UBA</option>
                  </select>
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="account-number" className="font-futura">Account Number</Label>
              <div className="flex space-x-2">
                <EnhancedInput
                  id="account-number"
                  placeholder="Enter account number"
                  value={accountNumber}
                  onChange={(e) => {
                    setAccountNumber(e.target.value);
                    setIsAccountVerified(false);
                  }}
                  error={error}
                  className="flex-1 font-futura"
                  maxLength={10}
                />
                <Button 
                  onClick={handleVerifyAccount} 
                  disabled={!accountNumber || accountNumber.length < 10 || isVerifying}
                  className="bg-kojaPrimary hover:bg-kojaPrimary/90 font-futura whitespace-nowrap"
                >
                  {isVerifying ? <Loader2 className="h-4 w-4 animate-spin" /> : "Verify"}
                </Button>
              </div>
            </div>
            
            {isAccountVerified && (
              <motion.div 
                initial={{ opacity: 0, y: 10 }} 
                animate={{ opacity: 1, y: 0 }}
                className="p-3 border rounded-md bg-gray-50 border-kojaYellow/20 shadow-sm"
              >
                <p className="text-sm font-medium font-futura">{accountName}</p>
                <p className="text-xs text-muted-foreground font-futura">{bankName}</p>
              </motion.div>
            )}
            
            <div className="space-y-2">
              <Label htmlFor="amount" className="font-futura">Amount</Label>
              <EnhancedInput
                id="amount"
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                type="number"
                error={error}
                className="font-futura"
              />
            </div>
            
            <div className="pt-4">
              <Button 
                className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90 font-futura"
                disabled={!isAccountVerified || !amount}
                onClick={() => {
                  if (validateAmount(amount)) {
                    setStep('confirm');
                  }
                }}
              >
                Continue
              </Button>
            </div>
          </div>
        );
        
      case 'confirm':
        return (
          <div className="space-y-4 py-4">
            <h3 className="text-lg font-medium text-center font-futura">Confirm Transfer</h3>
            
            <Card className="border border-kojaYellow/20 shadow-sm">
              <CardContent className="space-y-4 p-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground font-futura">To</p>
                    <p className="font-medium font-futura">{accountName}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground font-futura">Account</p>
                    <p className="font-medium font-futura">{accountNumber}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground font-futura">Bank</p>
                  <p className="font-medium font-futura">{bankName}</p>
                </div>
                
                <div className="pt-2 border-t border-kojaYellow/20">
                  <p className="text-sm text-muted-foreground font-futura">Amount</p>
                  <p className="text-xl font-bold font-futura">₦{parseFloat(amount).toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>
            
            <div className="bg-amber-50 p-3 rounded-md">
              <p className="text-sm text-amber-700 font-futura">
                Please confirm that all details are correct. This transaction cannot be reversed once completed.
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-3 pt-4">
              <Button 
                variant="outline"
                onClick={() => setStep('enter-details')}
                className="border-kojaYellow/20 font-futura"
              >
                Edit
              </Button>
              <Button 
                className="bg-kojaPrimary hover:bg-kojaPrimary/90 font-futura"
                onClick={handleSubmitTransfer}
              >
                Confirm <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        );
        
      case 'processing':
      case 'complete':
        return (
          <div className="space-y-6 py-8 flex flex-col items-center justify-center">
            <TransactionStatus 
              status={transferStatus}
              progress={progress}
              message={
                transferStatus === 'success' 
                  ? `Successfully sent ₦${parseFloat(amount).toLocaleString()} to ${accountName}`
                  : undefined
              }
            />
            
            {transferStatus === 'success' && (
              <div className="w-full pt-4 space-y-3">
                <Button 
                  variant="outline"
                  className="w-full border-kojaYellow/20 font-futura"
                  onClick={handleClose}
                >
                  Close
                </Button>
                <Button 
                  className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90 font-futura"
                  onClick={() => {
                    resetForm();
                    setStep('select-type');
                  }}
                >
                  Make Another Transfer
                </Button>
              </div>
            )}
          </div>
        );
    }
  };

  if (isMobile || isDrawer) {
    return (
      <Drawer open={open} onOpenChange={onOpenChange}>
        <DrawerContent className="px-0 pt-0">
          <div className="mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted" />
          <DrawerHeader className="text-center pb-0">
            <DrawerTitle className="font-futura">
              {step === 'select-type' && 'Send Money'}
              {step === 'enter-details' && (transferType === 'same-bank' ? 'Same Bank Transfer' : 'Other Bank Transfer')}
              {step === 'confirm' && 'Confirm Transfer'}
              {step === 'processing' && 'Processing Transfer'}
              {step === 'complete' && 'Transfer Complete'}
            </DrawerTitle>
          </DrawerHeader>
          <div className="px-4 pb-8">
            {renderContent()}
          </div>
          <DrawerClose asChild>
            <Button 
              variant="outline" 
              className="absolute right-4 top-4 border-kojaYellow/20 font-futura"
              onClick={resetForm}
            >
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </DrawerClose>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="font-futura">
            {step === 'select-type' && 'Send Money'}
            {step === 'enter-details' && (transferType === 'same-bank' ? 'Same Bank Transfer' : 'Other Bank Transfer')}
            {step === 'confirm' && 'Confirm Transfer'}
            {step === 'processing' && 'Processing Transfer'}
            {step === 'complete' && 'Transfer Complete'}
          </DialogTitle>
        </DialogHeader>
        <Button 
          variant="outline" 
          className="absolute right-4 top-4 rounded-full h-8 w-8 p-0 border-kojaYellow/20"
          onClick={handleClose}
        >
          <X className="h-4 w-4" />
        </Button>
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};

export default TransferDialog;
