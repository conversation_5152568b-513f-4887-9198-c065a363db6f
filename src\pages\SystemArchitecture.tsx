import React, { use<PERSON><PERSON>back, useState } from 'react';
import {
  <PERSON>act<PERSON>low,
  MiniMap,
  Controls,
  Background,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  MarkerType,
  Position,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  Shield, 
  CreditCard, 
  Users, 
  ArrowRight, 
  Server, 
  Smartphone,
  Globe,
  Lock,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Zap,
  Monitor,
  Cloud,
  Terminal,
  HardDrive,
  Network,
  Cpu
} from 'lucide-react';

// Custom Node Components with Black Theme
const DatabaseNode = ({ data }: { data: any }) => (
  <div className="px-4 py-3 shadow-lg rounded-lg bg-black border-2 border-gray-400 min-w-[140px] text-white">
    <div className="flex items-center gap-2">
      <Database className="w-5 h-5 text-gray-300" />
      <div className="font-semibold text-sm">{data.label}</div>
    </div>
    {data.details && (
      <div className="text-xs text-gray-400 mt-1">{data.details}</div>
    )}
    {data.connections && (
      <div className="text-xs text-gray-500 mt-1">Connections: {data.connections}</div>
    )}
  </div>
);

const ServiceNode = ({ data }: { data: any }) => (
  <div className="px-4 py-3 shadow-lg rounded-lg bg-gray-900 border-2 border-gray-600 text-white min-w-[160px]">
    <div className="flex items-center gap-2">
      <Server className="w-5 h-5 text-gray-300" />
      <div className="font-semibold text-sm">{data.label}</div>
    </div>
    {data.details && (
      <div className="text-xs text-gray-400 mt-1">{data.details}</div>
    )}
    {data.port && (
      <div className="text-xs text-gray-500 mt-1">Port: {data.port}</div>
    )}
  </div>
);

const SecurityNode = ({ data }: { data: any }) => (
  <div className="px-4 py-3 shadow-lg rounded-lg bg-gray-800 border-2 border-gray-500 text-white min-w-[140px]">
    <div className="flex items-center gap-2">
      <Shield className="w-5 h-5 text-gray-300" />
      <div className="font-semibold text-sm">{data.label}</div>
    </div>
    {data.details && (
      <div className="text-xs text-gray-400 mt-1">{data.details}</div>
    )}
    {data.level && (
      <div className="text-xs text-gray-500 mt-1">Level: {data.level}</div>
    )}
  </div>
);

const ClientNode = ({ data }: { data: any }) => (
  <div className="px-4 py-3 shadow-lg rounded-lg bg-gray-700 border-2 border-gray-500 text-white min-w-[140px]">
    <div className="flex items-center gap-2">
      <Smartphone className="w-5 h-5 text-gray-300" />
      <div className="font-semibold text-sm">{data.label}</div>
    </div>
    {data.details && (
      <div className="text-xs text-gray-400 mt-1">{data.details}</div>
    )}
    {data.platform && (
      <div className="text-xs text-gray-500 mt-1">Platform: {data.platform}</div>
    )}
  </div>
);

const GatewayNode = ({ data }: { data: any }) => (
  <div className="px-6 py-4 shadow-lg rounded-lg bg-black border-3 border-white text-white min-w-[180px]">
    <div className="flex items-center gap-2">
      <Network className="w-6 h-6 text-white" />
      <div className="font-bold text-base">{data.label}</div>
    </div>
    {data.details && (
      <div className="text-xs text-gray-300 mt-1">{data.details}</div>
    )}
    <div className="text-xs text-gray-400 mt-1">Gateway Layer</div>
  </div>
);

const nodeTypes = {
  database: DatabaseNode,
  service: ServiceNode,
  security: SecurityNode,
  client: ClientNode,
  gateway: GatewayNode,
};

// Database Schema Visualization Component
const DatabaseSchema = () => (
  <div className="bg-black p-6 rounded-lg border border-gray-600 text-white font-mono">
    <h3 className="text-lg font-bold mb-4 text-gray-200">Database Schema Design</h3>
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div className="space-y-4">
        <div className="border border-gray-600 rounded p-3">
          <div className="font-bold text-gray-200 mb-2">users</div>
          <div className="text-xs space-y-1 text-gray-400">
            <div>├── id: UUID (PK)</div>
            <div>├── email: VARCHAR(255) UNIQUE</div>
            <div>├── fullName: VARCHAR(255)</div>
            <div>├── role: ENUM(USER, ADMIN, MANAGER)</div>
            <div>├── status: ENUM(ACTIVE, SUSPENDED)</div>
            <div>├── createdAt: TIMESTAMP</div>
            <div>└── updatedAt: TIMESTAMP</div>
          </div>
        </div>
        
        <div className="border border-gray-600 rounded p-3">
          <div className="font-bold text-gray-200 mb-2">accounts</div>
          <div className="text-xs space-y-1 text-gray-400">
            <div>├── id: UUID (PK)</div>
            <div>├── userId: UUID (FK → users.id)</div>
            <div>├── accountType: ENUM(PERSONAL, BUSINESS, KIDS)</div>
            <div>├── balance: DECIMAL(15,2)</div>
            <div>├── currency: VARCHAR(3) DEFAULT 'NGN'</div>
            <div>├── status: ENUM(ACTIVE, FROZEN, CLOSED)</div>
            <div>└── limits: JSONB</div>
          </div>
        </div>

        <div className="border border-gray-600 rounded p-3">
          <div className="font-bold text-gray-200 mb-2">transactions</div>
          <div className="text-xs space-y-1 text-gray-400">
            <div>├── id: UUID (PK)</div>
            <div>├── fromAccountId: UUID (FK → accounts.id)</div>
            <div>├── toAccountId: UUID (FK → accounts.id)</div>
            <div>├── amount: DECIMAL(15,2)</div>
            <div>├── type: ENUM(TRANSFER, PAYMENT, WITHDRAWAL)</div>
            <div>├── status: ENUM(PENDING, COMPLETED, FAILED)</div>
            <div>├── reference: VARCHAR(255) UNIQUE</div>
            <div>├── metadata: JSONB</div>
            <div>└── createdAt: TIMESTAMP</div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="border border-gray-600 rounded p-3">
          <div className="font-bold text-gray-200 mb-2">cards</div>
          <div className="text-xs space-y-1 text-gray-400">
            <div>├── id: UUID (PK)</div>
            <div>├── accountId: UUID (FK → accounts.id)</div>
            <div>├── cardType: ENUM(DEBIT, CREDIT, VIRTUAL)</div>
            <div>├── maskedNumber: VARCHAR(19)</div>
            <div>├── expiryDate: DATE</div>
            <div>├── status: ENUM(ACTIVE, BLOCKED, EXPIRED)</div>
            <div>├── limits: JSONB</div>
            <div>└── createdAt: TIMESTAMP</div>
          </div>
        </div>

        <div className="border border-gray-600 rounded p-3">
          <div className="font-bold text-gray-200 mb-2">escrows</div>
          <div className="text-xs space-y-1 text-gray-400">
            <div>├── id: UUID (PK)</div>
            <div>├── buyerId: UUID (FK → users.id)</div>
            <div>├── sellerId: UUID (FK → users.id)</div>
            <div>├── amount: DECIMAL(15,2)</div>
            <div>├── status: ENUM(ACTIVE, RELEASED, DISPUTED)</div>
            <div>├── terms: JSONB</div>
            <div>├── releaseDate: TIMESTAMP</div>
            <div>└── createdAt: TIMESTAMP</div>
          </div>
        </div>

        <div className="border border-gray-600 rounded p-3">
          <div className="font-bold text-gray-200 mb-2">audit_logs</div>
          <div className="text-xs space-y-1 text-gray-400">
            <div>├── id: UUID (PK)</div>
            <div>├── userId: UUID (FK → users.id)</div>
            <div>├── action: VARCHAR(255)</div>
            <div>├── details: JSONB</div>
            <div>├── ipAddress: INET</div>
            <div>├── userAgent: TEXT</div>
            <div>└── timestamp: TIMESTAMP</div>
          </div>
        </div>
      </div>
    </div>
    
    <div className="mt-6 border-t border-gray-600 pt-4">
      <h4 className="font-bold text-gray-200 mb-2">Relationships</h4>
      <div className="text-xs text-gray-400 space-y-1">
        <div>users.id ──(1:N)──→ accounts.userId</div>
        <div>accounts.id ──(1:N)──→ transactions.fromAccountId</div>
        <div>accounts.id ──(1:N)──→ transactions.toAccountId</div>
        <div>accounts.id ──(1:N)──→ cards.accountId</div>
        <div>users.id ──(1:N)──→ escrows.buyerId</div>
        <div>users.id ──(1:N)──→ escrows.sellerId</div>
        <div>users.id ──(1:N)──→ audit_logs.userId</div>
      </div>
    </div>
  </div>
);

// Enhanced nodes and edges with more technical details
const initialNodes: Node[] = [
  // Client Layer
  {
    id: 'web-app',
    type: 'client',
    position: { x: 50, y: 50 },
    data: { label: 'Web Application', details: 'React 18 + TypeScript', platform: 'Browser' },
  },
  {
    id: 'mobile-app',
    type: 'client',
    position: { x: 300, y: 50 },
    data: { label: 'Mobile App', details: 'PWA + Capacitor', platform: 'iOS/Android' },
  },
  {
    id: 'admin-portal',
    type: 'client',
    position: { x: 550, y: 50 },
    data: { label: 'Admin Portal', details: 'Management Interface', platform: 'Web' },
  },
  
  // API Gateway - Central hub
  {
    id: 'api-gateway',
    type: 'gateway',
    position: { x: 300, y: 200 },
    data: { label: 'API Gateway', details: 'Load Balancer, Rate Limiting, Auth' },
  },
  
  // Microservices Layer
  {
    id: 'auth-service',
    type: 'security',
    position: { x: 50, y: 380 },
    data: { label: 'Auth Service', details: 'JWT + MFA + OAuth', level: 'High', port: '3001' },
  },
  {
    id: 'user-service',
    type: 'service',
    position: { x: 220, y: 380 },
    data: { label: 'User Service', details: 'Account Management', port: '3002' },
  },
  {
    id: 'transaction-service',
    type: 'service',
    position: { x: 390, y: 380 },
    data: { label: 'Transaction Engine', details: 'Payment Processing', port: '3003' },
  },
  {
    id: 'card-service',
    type: 'service',
    position: { x: 560, y: 380 },
    data: { label: 'Card Service', details: 'Card Lifecycle Management', port: '3004' },
  },
  {
    id: 'escrow-service',
    type: 'service',
    position: { x: 730, y: 380 },
    data: { label: 'Escrow Engine', details: 'Smart Contract Logic', port: '3005' },
  },
  
  // Security & Compliance Layer
  {
    id: 'fraud-detection',
    type: 'security',
    position: { x: 100, y: 530 },
    data: { label: 'Fraud Detection', details: 'ML Risk Analysis', level: 'Critical' },
  },
  {
    id: 'aml-engine',
    type: 'security',
    position: { x: 320, y: 530 },
    data: { label: 'AML Compliance', details: 'Anti-Money Laundering', level: 'Critical' },
  },
  {
    id: 'kyc-service',
    type: 'security',
    position: { x: 540, y: 530 },
    data: { label: 'KYC Service', details: 'Identity Verification', level: 'High' },
  },
  
  // Database Layer
  {
    id: 'postgresql-master',
    type: 'database',
    position: { x: 150, y: 680 },
    data: { label: 'PostgreSQL Master', details: 'Primary Database', connections: '1000' },
  },
  {
    id: 'postgresql-replica',
    type: 'database',
    position: { x: 350, y: 680 },
    data: { label: 'PostgreSQL Replica', details: 'Read Replica', connections: '500' },
  },
  {
    id: 'redis-cluster',
    type: 'database',
    position: { x: 550, y: 680 },
    data: { label: 'Redis Cluster', details: 'Cache & Sessions', connections: '2000' },
  },
  {
    id: 'audit-db',
    type: 'database',
    position: { x: 750, y: 680 },
    data: { label: 'Audit Database', details: 'Immutable Logs', connections: '100' },
  },
];

const initialEdges: Edge[] = [
  // Client to API Gateway - HTTPS connections
  { 
    id: 'e1', 
    source: 'web-app', 
    target: 'api-gateway', 
    type: 'straight',
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#ffffff' },
    label: 'HTTPS',
    labelStyle: { fill: '#ffffff', fontSize: 10 }
  },
  { 
    id: 'e2', 
    source: 'mobile-app', 
    target: 'api-gateway', 
    type: 'straight',
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#ffffff' },
    label: 'HTTPS',
    labelStyle: { fill: '#ffffff', fontSize: 10 }
  },
  { 
    id: 'e3', 
    source: 'admin-portal', 
    target: 'api-gateway', 
    type: 'straight',
    style: { stroke: '#ffffff', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#ffffff' },
    label: 'HTTPS',
    labelStyle: { fill: '#ffffff', fontSize: 10 }
  },
  
  // API Gateway to Services - Internal TCP
  { 
    id: 'e4', 
    source: 'api-gateway', 
    target: 'auth-service', 
    type: 'straight',
    style: { stroke: '#cccccc', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#cccccc' },
    label: 'TCP:3001',
    labelStyle: { fill: '#cccccc', fontSize: 9 }
  },
  { 
    id: 'e5', 
    source: 'api-gateway', 
    target: 'user-service', 
    type: 'straight',
    style: { stroke: '#cccccc', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#cccccc' },
    label: 'TCP:3002',
    labelStyle: { fill: '#cccccc', fontSize: 9 }
  },
  { 
    id: 'e6', 
    source: 'api-gateway', 
    target: 'transaction-service', 
    type: 'straight',
    style: { stroke: '#cccccc', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#cccccc' },
    label: 'TCP:3003',
    labelStyle: { fill: '#cccccc', fontSize: 9 }
  },
  { 
    id: 'e7', 
    source: 'api-gateway', 
    target: 'card-service', 
    type: 'straight',
    style: { stroke: '#cccccc', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#cccccc' },
    label: 'TCP:3004',
    labelStyle: { fill: '#cccccc', fontSize: 9 }
  },
  { 
    id: 'e8', 
    source: 'api-gateway', 
    target: 'escrow-service', 
    type: 'straight',
    style: { stroke: '#cccccc', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#cccccc' },
    label: 'TCP:3005',
    labelStyle: { fill: '#cccccc', fontSize: 9 }
  },
  
  // Services to Security Layer
  { 
    id: 'e9', 
    source: 'transaction-service', 
    target: 'fraud-detection', 
    type: 'straight',
    style: { stroke: '#ff6b6b', strokeWidth: 2, strokeDasharray: '5,5' },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#ff6b6b' },
    label: 'Risk Check',
    labelStyle: { fill: '#ff6b6b', fontSize: 9 }
  },
  { 
    id: 'e10', 
    source: 'user-service', 
    target: 'aml-engine', 
    type: 'straight',
    style: { stroke: '#ff6b6b', strokeWidth: 2, strokeDasharray: '5,5' },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#ff6b6b' },
    label: 'AML Check',
    labelStyle: { fill: '#ff6b6b', fontSize: 9 }
  },
  { 
    id: 'e11', 
    source: 'auth-service', 
    target: 'kyc-service', 
    type: 'straight',
    style: { stroke: '#ff6b6b', strokeWidth: 2, strokeDasharray: '5,5' },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#ff6b6b' },
    label: 'Identity Verify',
    labelStyle: { fill: '#ff6b6b', fontSize: 9 }
  },
  
  // Services to Database Layer
  { 
    id: 'e12', 
    source: 'user-service', 
    target: 'postgresql-master', 
    type: 'straight',
    style: { stroke: '#888888', strokeWidth: 3 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#888888' },
    label: 'Write/Read',
    labelStyle: { fill: '#888888', fontSize: 9 }
  },
  { 
    id: 'e13', 
    source: 'transaction-service', 
    target: 'postgresql-master', 
    type: 'straight',
    style: { stroke: '#888888', strokeWidth: 3 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#888888' },
    label: 'Write/Read',
    labelStyle: { fill: '#888888', fontSize: 9 }
  },
  { 
    id: 'e14', 
    source: 'auth-service', 
    target: 'redis-cluster', 
    type: 'straight',
    style: { stroke: '#888888', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#888888' },
    label: 'Sessions',
    labelStyle: { fill: '#888888', fontSize: 9 }
  },
  { 
    id: 'e15', 
    source: 'fraud-detection', 
    target: 'audit-db', 
    type: 'straight',
    style: { stroke: '#888888', strokeWidth: 2 },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#888888' },
    label: 'Audit Logs',
    labelStyle: { fill: '#888888', fontSize: 9 }
  },
  // Database replication
  { 
    id: 'e16', 
    source: 'postgresql-master', 
    target: 'postgresql-replica', 
    type: 'straight',
    style: { stroke: '#666666', strokeWidth: 2, strokeDasharray: '3,3' },
    markerEnd: { type: MarkerType.ArrowClosed, color: '#666666' },
    label: 'Replication',
    labelStyle: { fill: '#666666', fontSize: 9 }
  },
];

const SystemArchitecture = () => {
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [activeTab, setActiveTab] = useState('architecture');

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges],
  );

  return (
    <div className="min-h-screen bg-black text-white p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-6 md:mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            KojaPay Banking System Architecture
          </h1>
          <p className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto">
            Technical system architecture with microservices, security layers, and database infrastructure
          </p>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 md:grid-cols-5 bg-gray-900 mb-6">
            <TabsTrigger value="architecture" className="data-[state=active]:bg-gray-700 text-xs md:text-sm">Architecture</TabsTrigger>
            <TabsTrigger value="database" className="data-[state=active]:bg-gray-700 text-xs md:text-sm">Database</TabsTrigger>
            <TabsTrigger value="security" className="data-[state=active]:bg-gray-700 text-xs md:text-sm">Security</TabsTrigger>
            <TabsTrigger value="tech-stack" className="data-[state=active]:bg-gray-700 text-xs md:text-sm">Tech Stack</TabsTrigger>
            <TabsTrigger value="performance" className="data-[state=active]:bg-gray-700 text-xs md:text-sm">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="architecture" className="mt-6">
            <Card className="h-[600px] md:h-[800px] bg-black border-gray-600">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-white">
                  <Monitor className="w-6 h-6" />
                  System Architecture Diagram
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Interactive technical architecture with line connections and service details
                </CardDescription>
              </CardHeader>
              <CardContent className="h-full p-0">
                <ReactFlow
                  nodes={nodes}
                  edges={edges}
                  onNodesChange={onNodesChange}
                  onEdgesChange={onEdgesChange}
                  onConnect={onConnect}
                  nodeTypes={nodeTypes}
                  fitView
                  style={{ backgroundColor: '#000000' }}
                  className="bg-black"
                >
                  <Controls className="bg-gray-800 border-gray-600" />
                  <MiniMap 
                    className="bg-gray-900 border-gray-600" 
                    nodeColor="#666666"
                    maskColor="rgba(0,0,0,0.8)"
                  />
                  <Background gap={20} size={1} color="#333333" />
                </ReactFlow>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="database" className="mt-6">
            <div className="space-y-6">
              <DatabaseSchema />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                <Card className="bg-gray-900 border-gray-600">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-white">
                      <HardDrive className="w-6 h-6" />
                      Performance Metrics
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-white">
                    <div className="space-y-3 font-mono text-sm">
                      <div className="flex justify-between border-b border-gray-700 pb-2">
                        <span className="text-gray-400">Max Connections:</span>
                        <span>1000</span>
                      </div>
                      <div className="flex justify-between border-b border-gray-700 pb-2">
                        <span className="text-gray-400">Query Timeout:</span>
                        <span>30s</span>
                      </div>
                      <div className="flex justify-between border-b border-gray-700 pb-2">
                        <span className="text-gray-400">Cache Hit Ratio:</span>
                        <span>95%</span>
                      </div>
                      <div className="flex justify-between border-b border-gray-700 pb-2">
                        <span className="text-gray-400">Backup Frequency:</span>
                        <span>Every 6h</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Replication Lag:</span>
                        <span>&lt;100ms</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-gray-900 border-gray-600">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-white">
                      <Database className="w-6 h-6" />
                      Index Strategy
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-white">
                    <div className="space-y-2 font-mono text-sm">
                      <div className="text-gray-400 text-xs">PRIMARY INDEXES:</div>
                      <div className="ml-4 text-gray-300">• users(id) - BTREE</div>
                      <div className="ml-4 text-gray-300">• accounts(id) - BTREE</div>
                      <div className="ml-4 text-gray-300">• transactions(id) - BTREE</div>
                      
                      <div className="text-gray-400 text-xs mt-3">SECONDARY INDEXES:</div>
                      <div className="ml-4 text-gray-300">• users(email) - UNIQUE</div>
                      <div className="ml-4 text-gray-300">• transactions(reference) - UNIQUE</div>
                      <div className="ml-4 text-gray-300">• transactions(created_at) - BTREE</div>
                      <div className="ml-4 text-gray-300">• audit_logs(timestamp) - BTREE</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="security" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Shield className="w-6 h-6" />
                    Authentication Layer
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white">
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center gap-2">
                      <Terminal className="w-3 h-3 text-gray-400" />
                      <span>JWT RSA-256 Tokens</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Terminal className="w-3 h-3 text-gray-400" />
                      <span>Multi-Factor Authentication</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Terminal className="w-3 h-3 text-gray-400" />
                      <span>Biometric Verification</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Terminal className="w-3 h-3 text-gray-400" />
                      <span>Session Management</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Terminal className="w-3 h-3 text-gray-400" />
                      <span>Rate Limiting: 100/min</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <AlertTriangle className="w-6 h-6" />
                    Fraud Detection Engine
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white">
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center gap-2">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span>Real-time ML Analysis</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span>Risk Score Calculation</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span>Behavioral Pattern Detection</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span>Geolocation Verification</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Cpu className="w-3 h-3 text-gray-400" />
                      <span>Response Time: &lt;50ms</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Lock className="w-6 h-6" />
                    Data Protection
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white">
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center gap-2">
                      <Shield className="w-3 h-3 text-gray-400" />
                      <span>AES-256 Encryption</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-3 h-3 text-gray-400" />
                      <span>TLS 1.3 in Transit</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-3 h-3 text-gray-400" />
                      <span>PCI DSS Level 1</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-3 h-3 text-gray-400" />
                      <span>GDPR Compliant</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Shield className="w-3 h-3 text-gray-400" />
                      <span>Key Rotation: 90 days</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="tech-stack" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Monitor className="w-6 h-6" />
                    Frontend Stack
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white">
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center justify-between">
                      <span>React 18</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v18.2.0</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>TypeScript</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v5.1.6</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Tailwind CSS</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v3.3.0</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Vite</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v4.4.0</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>React Flow</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v12.0.0</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Server className="w-6 h-6" />
                    Backend Stack
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white">
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center justify-between">
                      <span>NestJS</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v10.1.0</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>PostgreSQL</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v15.3</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Redis</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v7.0</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Prisma ORM</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v5.1.1</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Node.js</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v18.17.0</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Cloud className="w-6 h-6" />
                    Infrastructure
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white">
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center justify-between">
                      <span>Docker</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v24.0</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Kubernetes</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">v1.27</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>AWS EKS</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">Latest</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>GitHub Actions</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">CI/CD</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Cloudflare</span>
                      <Badge variant="outline" className="text-xs border-gray-600 text-gray-300">CDN</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="performance" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <TrendingUp className="w-6 h-6" />
                    System Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white font-mono">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 border border-gray-700 rounded">
                        <div className="text-2xl font-bold">99.9%</div>
                        <div className="text-xs text-gray-400">Uptime SLA</div>
                      </div>
                      <div className="text-center p-3 border border-gray-700 rounded">
                        <div className="text-2xl font-bold">&lt;100ms</div>
                        <div className="text-xs text-gray-400">API Latency</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 border border-gray-700 rounded">
                        <div className="text-2xl font-bold">10K</div>
                        <div className="text-xs text-gray-400">Concurrent Users</div>
                      </div>
                      <div className="text-center p-3 border border-gray-700 rounded">
                        <div className="text-2xl font-bold">1M</div>
                        <div className="text-xs text-gray-400">TPS Capacity</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-900 border-gray-600">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Zap className="w-6 h-6" />
                    Load Balancing
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-white font-mono">
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between border-b border-gray-700 pb-2">
                      <span className="text-gray-400">Algorithm:</span>
                      <span>Round Robin</span>
                    </div>
                    <div className="flex justify-between border-b border-gray-700 pb-2">
                      <span className="text-gray-400">Health Checks:</span>
                      <span>30s interval</span>
                    </div>
                    <div className="flex justify-between border-b border-gray-700 pb-2">
                      <span className="text-gray-400">Auto Scaling:</span>
                      <span>CPU &gt; 70%</span>
                    </div>
                    <div className="flex justify-between border-b border-gray-700 pb-2">
                      <span className="text-gray-400">Min Instances:</span>
                      <span>3</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Max Instances:</span>
                      <span>20</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SystemArchitecture;