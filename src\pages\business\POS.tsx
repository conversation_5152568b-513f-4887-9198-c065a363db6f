import React, { useState, useEffect } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Receipt, 
  Plus, 
  ShoppingCart, 
  Trash2, 
  DollarSign, 
  CreditCard,
  Check,
  AlertTriangle,
  Wifi,
  RotateCw,
  X,
  Shield,
  Smartphone,
  Wallet,
  ArrowRightLeft,
  ChevronRight,
  ArrowLeft,
  BatteryFull,
  Printer,
  Users,
  BarChart3,
  RefreshCcw,
  Download,
  Signal,
  ScanLine
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import TransactionStatus from '@/components/TransactionStatus';
import { generateTransactionReceipt } from '@/utils/receipt-generator';
import { format } from 'date-fns';

const BusinessPOS = () => {
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const [showCardReaderWarning, setShowCardReaderWarning] = useState(true);
  const [showCardReader, setShowCardReader] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState("pos");
  const [isConnecting, setIsConnecting] = useState(false);
  const [isCardReaderConnected, setIsCardReaderConnected] = useState(false);
  const [showPaymentMethods, setShowPaymentMethods] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState("");
  const [pinEntered, setPinEntered] = useState('');
  const [showVirtualKeypad, setShowVirtualKeypad] = useState(false);
  const [showReceiptDialog, setShowReceiptDialog] = useState(false);
  const [lastTransaction, setLastTransaction] = useState<any>(null);
  const [sessionExpiration, setSessionExpiration] = useState<Date | null>(null);
  
  const [cartItems, setCartItems] = useState([
    { id: 1, name: 'Product 1', price: 2500, quantity: 1 },
    { id: 2, name: 'Product 2', price: 1800, quantity: 1 },
    { id: 3, name: 'Product 3', price: 3200, quantity: 1 }
  ]);

  useEffect(() => {
    const savedSession = localStorage.getItem('cardReaderSession');
    
    if (savedSession) {
      try {
        const session = JSON.parse(savedSession);
        const expirationDate = new Date(session.expiration);
        
        if (expirationDate > new Date()) {
          setIsCardReaderConnected(true);
          setSessionExpiration(expirationDate);
        } else {
          localStorage.removeItem('cardReaderSession');
        }
      } catch (e) {
        localStorage.removeItem('cardReaderSession');
      }
    }
  }, []);
  
  const calculateSubtotal = () => {
    return cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };
  
  const calculateTax = () => {
    return calculateSubtotal() * 0.075;
  };
  
  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };
  
  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setCartItems(cartItems.map(item => 
      item.id === id ? { ...item, quantity: newQuantity } : item
    ));
  };
  
  const removeItem = (id: number) => {
    setCartItems(cartItems.filter(item => item.id !== id));
  };
  
  const connectCardReader = () => {
    setIsConnecting(true);
    
    setTimeout(() => {
      setIsConnecting(false);
      setIsCardReaderConnected(true);
      
      const expiration = new Date();
      expiration.setHours(expiration.getHours() + 24);
      setSessionExpiration(expiration);
      
      localStorage.setItem('cardReaderSession', JSON.stringify({
        connected: true,
        expiration: expiration.toISOString()
      }));
      
      toast({
        title: "Card Reader Connected",
        description: "Your KojaPay card reader is now connected and ready to use",
        variant: "success",
      });
    }, 2000);
  };
  
  const processCardPayment = () => {
    if (selectedPaymentMethod === "") {
      setShowPaymentMethods(true);
      return;
    }
    
    if (selectedPaymentMethod === "card" && !isCardReaderConnected) {
      toast({
        title: "Card Reader Not Connected",
        description: "Please connect your KojaPay card reader first",
        variant: "destructive",
      });
      return;
    }
    
    setShowPaymentMethods(false);
    
    if (selectedPaymentMethod === "card") {
      setShowVirtualKeypad(true);
    } else {
      startPaymentProcessing();
    }
  };
  
  const selectPaymentMethod = (method: string) => {
    setSelectedPaymentMethod(method);
    setShowPaymentMethods(false);
    
    if (method === "card" && !isCardReaderConnected) {
      connectCardReader();
    } else {
      processCardPayment();
    }
  };
  
  const startPaymentProcessing = () => {
    setShowCardReader(true);
    setProcessingPayment(true);
    setShowVirtualKeypad(false);
    
    setTimeout(() => {
      setProcessingPayment(false);
      setPaymentSuccess(true);
      
      const newTransaction = {
        id: `KP${Math.floor(Math.random() * 1000000)}`,
        date: new Date().toISOString(),
        amount: calculateTotal(),
        recipient: 'Customer Purchase',
        status: 'success',
        description: `POS Sale - ${cartItems.length} items`,
        type: 'sale'
      };
      
      setLastTransaction(newTransaction);
      
      toast({
        title: "Payment Successful",
        description: `Payment of ₦${calculateTotal().toLocaleString()} completed successfully`,
        variant: "success",
      });
    }, 2000);
  };
  
  const handlePinInput = (digit: string) => {
    if (pinEntered.length < 4) {
      setPinEntered(prev => prev + digit);
    }
    
    if (pinEntered.length === 3) {
      setTimeout(() => {
        startPaymentProcessing();
      }, 500);
    }
  };
  
  const clearPin = () => {
    setPinEntered('');
  };
  
  const addRandomProduct = () => {
    const productNames = ["Coffee", "Sandwich", "Snack", "Water", "Juice", "Tea", "Pastry", "Soda"];
    const randomName = productNames[Math.floor(Math.random() * productNames.length)];
    const randomPrice = Math.floor(Math.random() * 5000) + 500;
    
    const newProduct = {
      id: cartItems.length > 0 ? Math.max(...cartItems.map(item => item.id)) + 1 : 1,
      name: randomName,
      price: randomPrice,
      quantity: 1
    };
    
    setCartItems([...cartItems, newProduct]);
  };
  
  const newSale = () => {
    setCartItems([]);
    setSelectedPaymentMethod("");
    setShowCardReader(false);
    setPaymentSuccess(false);
    setPinEntered('');
  };
  
  const printReceipt = () => {
    if (!lastTransaction) return;
    
    generateTransactionReceipt(lastTransaction, true);
    
    toast({
      title: "Receipt Generated",
      description: "Receipt has been downloaded to your device. You can also print directly using the Print button.",
      variant: "default",
    });
  };
  
  const printReceiptToPrinter = () => {
    if (!lastTransaction) return;
    
    const receipt = {
      id: lastTransaction.id || `KP${Math.floor(Math.random() * 1000000)}`,
      transactionDate: format(new Date(lastTransaction.date || new Date()), 'PPP'),
      transactionTime: format(new Date(lastTransaction.date || new Date()), 'p'),
      recipient: lastTransaction.recipient || 'Customer Purchase',
      amount: lastTransaction.amount,
      status: lastTransaction.status || 'success',
      description: lastTransaction.description || `POS Sale - ${cartItems.length} items`,
      reference: lastTransaction.reference || `REF-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
      type: lastTransaction.type || 'sale',
      fee: '₦0.00',
      totalAmount: lastTransaction.amount,
      items: cartItems.map(item => ({
        name: item.name,
        price: item.price, 
        quantity: item.quantity
      })),
      paymentMethod: selectedPaymentMethod.charAt(0).toUpperCase() + selectedPaymentMethod.slice(1) || 'Card',
      tax: calculateTax(),
      subtotal: calculateSubtotal(),
      merchantName: 'KojaPay',
      merchantAddress: '123 Business Avenue, Lagos',
      customerName: 'Valued Customer'
    };
    
    import('@/utils/receipt-generator').then(module => {
      module.printPOSReceipt(receipt);
    });
    
    toast({
      title: "Printing Receipt",
      description: "The receipt is being sent to your printer",
      variant: "default",
    });
  };
  
  const viewReceipt = () => {
    setShowReceiptDialog(true);
  };

  return (
    <BusinessLayout pageTitle="POS Terminal">
      <Dialog open={showCardReaderWarning} onOpenChange={setShowCardReaderWarning}>
        <DialogContent className="rounded-[20px] bg-white/90 backdrop-blur-xl border border-white/40 max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center text-red-500 font-semibold text-xl">
              <AlertTriangle className="mr-2" size={24} />
              Security Warning
            </DialogTitle>
            <DialogDescription className="pt-4">
              <div className="space-y-4">
                <div className="flex items-start">
                  <Shield className="mr-2 text-purple-600 flex-shrink-0 mt-1" size={20} />
                  <p>For your security, only use <span className="font-semibold">official KojaPay card readers</span> for payment processing.</p>
                </div>
                
                <div className="flex items-start">
                  <AlertTriangle className="mr-2 text-amber-500 flex-shrink-0 mt-1" size={20} />
                  <p>Unauthorized card readers may compromise your customers' data and business security.</p>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-xl">
                  <h4 className="font-medium text-blue-700 mb-2 flex items-center">
                    <CreditCard className="mr-2" size={18} />
                    Connect Your Card Reader
                  </h4>
                  <p className="text-blue-600 text-sm">Connect your KojaPay card reader to process card payments securely.</p>
                  
                  <div className="mt-4 flex justify-end">
                    <Button 
                      className="bg-purple-600 hover:bg-purple-700 text-white"
                      onClick={() => setShowCardReaderWarning(false)}
                    >
                      I Understand
                    </Button>
                  </div>
                </div>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
      
      <Dialog open={showVirtualKeypad} onOpenChange={setShowVirtualKeypad}>
        <DialogContent className="rounded-[20px] bg-gray-900 text-white max-w-sm">
          <div className="py-4">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium mb-1">Enter PIN</h3>
              <p className="text-sm text-gray-400">Amount: ₦{calculateTotal().toLocaleString()}</p>
              
              <div className="flex justify-center space-x-4 my-6">
                {[...Array(4)].map((_, i) => (
                  <div
                    key={i}
                    className="w-3 h-3 rounded-full border border-gray-500 flex items-center justify-center"
                  >
                    {pinEntered.length > i ? <div className="w-2 h-2 bg-white rounded-full"></div> : null}
                  </div>
                ))}
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-3">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 'clear', 0, 'enter'].map((key, index) => (
                <Button
                  key={index}
                  variant={typeof key === 'number' ? "outline" : key === 'enter' ? "default" : "destructive"}
                  className={`h-14 text-lg font-medium ${typeof key === 'number' ? 'bg-gray-800 hover:bg-gray-700 text-white border-gray-700' : ''} ${key === 'enter' ? 'bg-green-600 hover:bg-green-700' : ''}`}
                  onClick={() => {
                    if (key === 'clear') {
                      clearPin();
                    } else if (key === 'enter') {
                      startPaymentProcessing();
                    } else {
                      handlePinInput(key.toString());
                    }
                  }}
                >
                  {key === 'clear' ? <X size={20} /> : key === 'enter' ? <Check size={20} /> : key}
                </Button>
              ))}
            </div>
            
            <p className="text-xs text-center text-gray-500 mt-4">
              Customer PIN is never stored and securely processed
            </p>
          </div>
        </DialogContent>
      </Dialog>
      
      <Dialog open={showCardReader} onOpenChange={setShowCardReader}>
        <DialogContent className="rounded-[20px] bg-gradient-to-br from-gray-900 to-purple-900 backdrop-blur-xl border border-white/10 text-white max-w-sm">
          <div className="flex flex-col items-center justify-center py-8">
            {processingPayment ? (
              <>
                <div className="w-24 h-24 rounded-full bg-gray-800 flex items-center justify-center mb-6">
                  <RotateCw size={40} className="text-white animate-spin" />
                </div>
                <h3 className="text-xl font-bold mb-2">Processing Payment</h3>
                <p className="text-gray-300 text-center mb-4">Please do not remove card until transaction is complete</p>
                <div className="w-full bg-gray-700 h-2 rounded-full overflow-hidden">
                  <div className="bg-gradient-to-r from-purple-400 to-pink-500 h-full rounded-full animate-pulse" style={{width: '70%'}}></div>
                </div>
                <p className="text-gray-400 mt-4 text-sm">Communicating with bank...</p>
              </>
            ) : paymentSuccess ? (
              <>
                <div className="w-24 h-24 rounded-full bg-green-500/20 flex items-center justify-center mb-6 animate-scale-in">
                  <Check size={40} className="text-green-500" />
                </div>
                <h3 className="text-xl font-bold mb-2">Payment Successful</h3>
                <p className="text-gray-300 text-center">Transaction completed successfully</p>
                <div className="mt-6 bg-gray-800/50 rounded-xl p-4 w-full">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-400">Amount:</span>
                    <span>₦{calculateTotal().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-400">Transaction ID:</span>
                    <span>{lastTransaction?.id || `KP${Math.floor(Math.random() * 1000000)}`}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Date:</span>
                    <span>{new Date().toLocaleDateString()}</span>
                  </div>
                </div>
                
                <div className="flex gap-2 mt-6">
                  <Button variant="outline" className="border-gray-600 text-white hover:bg-gray-800" onClick={viewReceipt}>
                    <Receipt size={16} className="mr-2" /> View
                  </Button>
                  <Button variant="outline" className="border-gray-600 text-white hover:bg-gray-800" onClick={printReceiptToPrinter}>
                    <Printer size={16} className="mr-2" /> Print
                  </Button>
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white" onClick={newSale}>
                    <Plus size={16} className="mr-2" /> New Sale
                  </Button>
                </div>
              </>
            ) : null}
          </div>
        </DialogContent>
      </Dialog>
      
      <Dialog open={showReceiptDialog} onOpenChange={setShowReceiptDialog}>
        <DialogContent className="receipt-paper max-w-sm">
          <DialogHeader>
            <DialogTitle className="text-center">Payment Receipt</DialogTitle>
          </DialogHeader>
          
          <div className="border-t border-b py-4 my-2">
            <div className="text-center mb-4">
              <h3 className="font-bold text-xl">KojaPay</h3>
              <p className="text-sm text-gray-500">123 Business Avenue, Lagos</p>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Transaction ID:</span>
                <span>{lastTransaction?.id || `KP${Math.floor(Math.random() * 1000000)}`}</span>
              </div>
              <div className="flex justify-between">
                <span>Date:</span>
                <span>{new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Card Type:</span>
                <span>Mastercard ****1234</span>
              </div>
            </div>
            
            <div className="receipt-divider my-4"></div>
            
            <p className="font-semibold mb-2">Items:</p>
            {cartItems.map((item) => (
              <div key={item.id} className="flex justify-between text-sm mb-1">
                <span>{item.name} x{item.quantity}</span>
                <span>₦{(item.price * item.quantity).toLocaleString()}</span>
              </div>
            ))}
            
            <div className="receipt-divider my-4"></div>
            
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>Subtotal:</span>
                <span>₦{calculateSubtotal().toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Tax (7.5%):</span>
                <span>₦{calculateTax().toLocaleString()}</span>
              </div>
              <div className="flex justify-between font-bold mt-2">
                <span>Total:</span>
                <span>₦{calculateTotal().toLocaleString()}</span>
              </div>
            </div>
            
            <div className="receipt-divider my-4"></div>
            
            <div className="text-center">
              <TransactionStatus 
                status="success" 
                message="Transaction Approved" 
                compact={true}
              />
            </div>
          </div>
          
          <div className="text-center text-sm text-gray-500">
            <p>Thank you for your business!</p>
            <p className="mt-1">www.kojapay.com</p>
          </div>
          
          <div className="flex justify-between mt-4">
            <Button variant="outline" className="w-1/3" onClick={() => setShowReceiptDialog(false)}>
              Close
            </Button>
            <Button className="w-1/3 bg-blue-600 hover:bg-blue-700" onClick={printReceiptToPrinter}>
              <Printer size={16} className="mr-1" /> Print
            </Button>
            <Button className="w-1/3 bg-purple-600 hover:bg-purple-700" onClick={printReceipt}>
              <Download size={16} className="mr-1" /> Download
            </Button>
          </div>
        </DialogContent>
      </Dialog>
      
      <Dialog open={showPaymentMethods} onOpenChange={setShowPaymentMethods}>
        <DialogContent className="rounded-[20px] bg-white max-w-sm">
          <DialogHeader>
            <DialogTitle>Select Payment Method</DialogTitle>
            <DialogDescription>Choose how you want to process this payment</DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-2 gap-3 py-4">
            <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => selectPaymentMethod("card")}>
              <CardContent className="p-6 flex flex-col items-center justify-center">
                <CreditCard size={32} className="text-purple-600 mb-3" />
                <span className="font-medium">Card Payment</span>
                <span className="text-xs text-gray-500 mt-1">Debit/Credit</span>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => selectPaymentMethod("cash")}>
              <CardContent className="p-6 flex flex-col items-center justify-center">
                <Wallet size={32} className="text-green-600 mb-3" />
                <span className="font-medium">Cash</span>
                <span className="text-xs text-gray-500 mt-1">Physical currency</span>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => selectPaymentMethod("transfer")}>
              <CardContent className="p-6 flex flex-col items-center justify-center">
                <ArrowRightLeft size={32} className="text-blue-600 mb-3" />
                <span className="font-medium">Transfer</span>
                <span className="text-xs text-gray-500 mt-1">Bank transfer</span>
              </CardContent>
            </Card>
            
            <Card className="cursor-pointer hover:bg-gray-50 transition-colors" onClick={() => selectPaymentMethod("qr")}>
              <CardContent className="p-6 flex flex-col items-center justify-center">
                <ScanLine size={32} className="text-amber-600 mb-3" />
                <span className="font-medium">QR Payment</span>
                <span className="text-xs text-gray-500 mt-1">Scan to pay</span>
              </CardContent>
            </Card>
          </div>
        </DialogContent>
      </Dialog>
      
      <Tabs defaultValue="pos" className="w-full" onValueChange={setActiveTab}>
        <div className="flex items-center justify-between mb-4">
          <TabsList className="bg-gray-100">
            <TabsTrigger value="pos" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <ShoppingCart size={16} className="mr-2" /> POS
            </TabsTrigger>
            <TabsTrigger value="transactions" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <Receipt size={16} className="mr-2" /> Transactions
            </TabsTrigger>
            <TabsTrigger value="reports" className="data-[state=active]:bg-purple-600 data-[state=active]:text-white">
              <BarChart3 size={16} className="mr-2" /> Reports
            </TabsTrigger>
          </TabsList>
          
          {activeTab === "pos" && (
            <div className="flex items-center">
              <Badge variant={isCardReaderConnected ? "outline" : "outline"} className={isCardReaderConnected ? "bg-green-50 text-green-700 border-green-200" : ""}>
                {isCardReaderConnected ? (
                  <><BatteryFull size={12} className="mr-1" /> Reader Connected</>
                ) : (
                  <><AlertTriangle size={12} className="mr-1" /> Not Connected</>
                )}
              </Badge>
              <Button 
                size="sm" 
                variant="outline"
                onClick={connectCardReader}
                disabled={isCardReaderConnected || isConnecting}
                className="ml-2"
              >
                {isConnecting ? (
                  <><RotateCw size={14} className="mr-1 animate-spin" /> Connecting...</>
                ) : (
                  <><Wifi size={14} className="mr-1" /> {isCardReaderConnected ? "Connected" : "Connect"}</>
                )}
              </Button>
            </div>
          )}
        </div>
        
        <TabsContent value="pos" className="mt-0">
          <div className="terminal-container">
            <div className="bg-gray-100 p-3 flex items-center justify-between">
              <div className="flex items-center">
                <div className={`card-reader-indicator ${isCardReaderConnected ? 'reader-connected' : 'reader-disconnected'}`}></div>
                <span className="text-xs">{isCardReaderConnected ? 'Reader Connected' : 'Reader Disconnected'}</span>
              </div>
              <div className="text-xs text-gray-500">
                {sessionExpiration && isCardReaderConnected && (
                  `Session expires: ${sessionExpiration.toLocaleTimeString()}`
                )}
              </div>
            </div>
            
            <Card className="border-0 rounded-none shadow-none">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Sales Terminal</CardTitle>
                    <CardDescription>Add items to cart</CardDescription>
                  </div>
                  
                  <Button size="sm" onClick={addRandomProduct}>
                    <Plus size={16} className="mr-1" /> Add Item
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="mb-3">
                  <div className="relative">
                    <Input 
                      type="text" 
                      placeholder="Search products..." 
                      className="pl-10 bg-white border-gray-200"
                    />
                    <ShoppingCart className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  </div>
                </div>
                
                <div className="bg-white rounded-xl border border-gray-100 mb-4 max-h-[240px] overflow-y-auto">
                  <div className="p-2 space-y-2">
                    {cartItems.length > 0 ? (
                      cartItems.map((item) => (
                        <div key={item.id} className="flex items-center justify-between p-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors">
                          <div className="flex-1">
                            <div className="font-medium text-sm">{item.name}</div>
                            <div className="text-xs text-gray-500">₦{item.price.toLocaleString()} × {item.quantity}</div>
                          </div>
                          <div className="flex items-center gap-1">
                            <Button 
                              variant="outline" 
                              size="icon" 
                              className="h-6 w-6 rounded-full"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                            >
                              -
                            </Button>
                            <span className="w-5 text-center text-sm">{item.quantity}</span>
                            <Button 
                              variant="outline" 
                              size="icon" 
                              className="h-6 w-6 rounded-full"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                            >
                              +
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-6 w-6 text-red-500 rounded-full"
                              onClick={() => removeItem(item.id)}
                            >
                              <Trash2 size={14} />
                            </Button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-400">
                        <ShoppingCart size={24} className="mx-auto mb-2 opacity-30" />
                        <p className="text-sm">Cart is empty</p>
                        <p className="text-xs">Add products to begin</p>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-1 border-t pt-3 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal</span>
                    <span>₦{calculateSubtotal().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax (7.5%)</span>
                    <span>₦{calculateTax().toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-base">
                    <span>Total</span>
                    <span>₦{calculateTotal().toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="border-t bg-gray-50/50 pt-4 flex-col gap-2">
                <div className="w-full grid grid-cols-2 gap-2">
                  {["card", "cash", "transfer", "qr"].map((method) => (
                    <Button 
                      key={method}
                      variant="outline" 
                      className={`justify-start text-xs h-10 ${selectedPaymentMethod === method ? "border-purple-500 bg-purple-50" : ""}`}
                      onClick={() => selectPaymentMethod(method)}
                    >
                      {method === "card" && <CreditCard className="h-3 w-3 text-purple-600 mr-1" />}
                      {method === "cash" && <Wallet className="h-3 w-3 text-green-600 mr-1" />}
                      {method === "transfer" && <ArrowRightLeft className="h-3 w-3 text-blue-600 mr-1" />}
                      {method === "qr" && <ScanLine className="h-3 w-3 text-amber-600 mr-1" />}
                      <span className="capitalize">{method}</span>
                    </Button>
                  ))}
                </div>
                
                <div className="w-full flex gap-2 mt-2">
                  <Button 
                    variant="outline" 
                    className="w-1/3" 
                    onClick={() => setCartItems([])}
                    disabled={cartItems.length === 0}
                  >
                    <X size={14} className="mr-1" /> Clear
                  </Button>
                  <Button 
                    className="w-2/3 bg-purple-600 hover:bg-purple-700"
                    onClick={processCardPayment}
                    disabled={cartItems.length === 0}
                  >
                    <Check size={14} className="mr-1" /> Pay ₦{calculateTotal().toLocaleString()}
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="transactions" className="mt-0">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Transaction History</CardTitle>
                  <CardDescription>View and manage your recent transactions</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <RefreshCcw size={14} className="mr-1" /> Refresh
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download size={14} className="mr-1" /> Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg border overflow-hidden">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left font-medium text-gray-500">Transaction ID</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-500">Date & Time</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-500">Amount</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-500">Method</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-500">Status</th>
                      <th className="px-4 py-3 text-left font-medium text-gray-500">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {[
                      { id: "KP123456", date: "2023-05-10 10:45 AM", amount: 4500, method: "Card", status: "success" },
                      { id: "KP123455", date: "2023-05-10 10:32 AM", amount: 3000, method: "Cash", status: "success" },
                      { id: "KP123454", date: "2023-05-10 10:15 AM", amount: 1500, method: "Transfer", status: "success" },
                      { id: "KP123453", date: "2023-05-09 16:45 PM", amount: 5500, method: "Card", status: "success" },
                      { id: "KP123452", date: "2023-05-09 15:20 PM", amount: 2000, method: "Cash", status: "success" },
                      { id: "KP123451", date: "2023-05-09 14:45 PM", amount: 1000, method: "Card", status: "failed" }
                    ].map((tx, i) => (
                      <tr key={i} className="hover:bg-gray-50">
                        <td className="px-4 py-3">{tx.id}</td>
                        <td className="px-4 py-3">{tx.date}</td>
                        <td className="px-4 py-3 font-medium">₦{tx.amount.toLocaleString()}</td>
                        <td className="px-4 py-3">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium 
                            ${tx.method === 'Card' ? 'bg-purple-100 text-purple-700' : 
                              tx.method === 'Cash' ? 'bg-green-100 text-green-700' : 
                              'bg-blue-100 text-blue-700'}`}>
                            {tx.method}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          <TransactionStatus 
                            status={tx.status as any} 
                            compact={true}
                          />
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" className="h-7 px-2 text-xs">
                              <Receipt size={12} className="mr-1" /> 
                              View
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm" 
                              className="h-7 px-2 text-xs"
                              onClick={() => {
                                const tempTransaction = {
                                  id: tx.id,
                                  date: new Date().toISOString(),
                                  amount: tx.amount,
                                  recipient: 'Customer Purchase',
                                  status: tx.status as any,
                                  description: 'POS Transaction',
                                  reference: `REF-${Math.random().toString(36).substring(2, 10).toUpperCase()}`,
                                  type: 'sale',
                                  paymentMethod: tx.method
                                };
                                generateTransactionReceipt(tempTransaction as any, true);
                              }}
                            >
                              <Printer size={12} className="mr-1" /> 
                              Print
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="reports" className="mt-0">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Sales Reports</CardTitle>
                  <CardDescription>Analyze your business performance</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    Today
                  </Button>
                  <Button variant="outline" size="sm">
                    This Week
                  </Button>
                  <Button variant="outline" size="sm">
                    This Month
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">Total Sales</p>
                        <h3 className="text-2xl font-bold mt-1">₦145800</h3>
                        <div className="text-xs text-green-500 flex items-center mt-1">
                          <ArrowRightLeft size={12} className="mr-1" /> +23% increase
                        </div>
                      </div>
                      <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                        <BarChart3 size={20} className="text-purple-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">Transactions</p>
                        <h3 className="text-2xl font-bold mt-1">86</h3>
                        <div className="text-xs text-amber-500 flex items-center mt-1">
                          <ArrowRightLeft size={12} className="mr-1" /> -5% decrease
                        </div>
                      </div>
                      <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                        <Receipt size={20} className="text-amber-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm text-gray-500">Average Sale</p>
                        <h3 className="text-2xl font-bold mt-1">₦1695</h3>
                        <div className="text-xs text-green-500 flex items-center mt-1">
                          <ArrowRightLeft size={12} className="mr-1" /> +12% increase
                        </div>
                      </div>
                      <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                        <Users size={20} className="text-green-600" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Payment Methods</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { method: "Card", percentage: 68, amount: 99144 },
                        { method: "Cash", percentage: 22, amount: 32076 },
                        { method: "Transfer", percentage: 10, amount: 14580 }
                      ].map((item, i) => (
                        <div key={i} className="space-y-1">
                          <div className="flex justify-between items-center">
                            <span className="text-sm">{item.method}</span>
                            <span className="text-sm font-medium">{item.percentage}%</span>
                          </div>
                          <Progress value={item.percentage} className="h-2" />
                          <div className="text-xs text-gray-500">₦{item.amount.toLocaleString()}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Top Selling Products</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {[
                        { product: "Product 1", quantity: 54, amount: 27000 },
                        { product: "Product 2", quantity: 32, amount: 19200 },
                        { product: "Product 3", quantity: 28, amount: 22400 }
                      ].map((item, i) => (
                        <div key={i} className="flex justify-between items-center py-1">
                          <div>
                            <div className="font-medium">{item.product}</div>
                            <div className="text-xs text-gray-500">{item.quantity} units sold</div>
                          </div>
                          <div className="text-sm font-semibold">₦{item.amount.toLocaleString()}</div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </BusinessLayout>
  );
};

export default BusinessPOS;
