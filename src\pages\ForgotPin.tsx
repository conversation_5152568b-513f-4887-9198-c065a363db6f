
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';

const ForgotPin = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phoneNumber) {
      toast({
        title: 'Error',
        description: 'Please enter your phone number',
        variant: 'destructive',
      });
      return;
    }
    
    // In a real app, this would call an API to send a reset PIN OTP
    setIsSubmitted(true);
    toast({
      title: 'OTP Sent',
      description: 'A verification code has been sent to your phone number',
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-kojaLight p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 rounded-full bg-kojaPrimary text-white font-bold text-2xl">K</div>
          </div>
          <h1 className="text-2xl font-bold text-kojaDark">Reset Your PIN</h1>
          <p className="text-kojaGray mt-2">We'll help you recover access to your account</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 animate-fade-in">
          {!isSubmitted ? (
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <Label htmlFor="phoneNumber" className="block text-kojaDark font-medium mb-2">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  placeholder="Enter your registered phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="w-full"
                />
                <p className="text-sm text-kojaGray mt-2">
                  We'll send a verification code to this number
                </p>
              </div>

              <Button
                type="submit"
                className="w-full py-3 bg-kojaPrimary text-white rounded-lg hover:bg-kojaPrimary/90 transition-colors duration-300"
              >
                Send Verification Code
              </Button>
            </form>
          ) : (
            <div className="space-y-6">
              <div className="p-4 bg-green-50 border border-green-200 text-green-700 rounded-lg">
                Verification code sent! Please check your phone.
              </div>
              
              <div className="mb-6">
                <Label htmlFor="otp" className="block text-kojaDark font-medium mb-2">Enter Verification Code</Label>
                <Input
                  id="otp"
                  type="text"
                  placeholder="Enter 6-digit code"
                  maxLength={6}
                  className="w-full text-center text-lg font-medium tracking-widest"
                />
              </div>
              
              <div className="mb-6">
                <Label htmlFor="newPin" className="block text-kojaDark font-medium mb-2">New PIN</Label>
                <Input
                  id="newPin"
                  type="password"
                  placeholder="Enter new 6-digit PIN"
                  maxLength={6}
                  className="w-full"
                />
              </div>
              
              <div className="mb-6">
                <Label htmlFor="confirmPin" className="block text-kojaDark font-medium mb-2">Confirm New PIN</Label>
                <Input
                  id="confirmPin"
                  type="password"
                  placeholder="Confirm new 6-digit PIN"
                  maxLength={6}
                  className="w-full"
                />
              </div>
              
              <Button
                type="button"
                className="w-full py-3 bg-kojaPrimary text-white rounded-lg hover:bg-kojaPrimary/90 transition-colors duration-300"
              >
                Reset PIN
              </Button>
            </div>
          )}

          <div className="mt-6">
            <Link to="/login" className="inline-flex items-center text-kojaGray hover:text-kojaPrimary">
              <ArrowLeft size={16} className="mr-2" />
              Back to Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPin;
