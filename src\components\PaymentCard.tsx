
import React from 'react';

interface PaymentCardProps {
  cardNumber: string;
  name: string;
  expiryDate: string;
}

const PaymentCard: React.FC<PaymentCardProps> = ({ cardNumber, name, expiryDate }) => {
  return (
    <div className="koja-card w-full">
      <div className="card-wave"></div>
      <div className="card-chip"></div>
      
      <div className="flex flex-col justify-between h-full">
        <div className="card-number">{cardNumber}</div>
        
        <div className="card-name-date">
          <div className="text-xs font-medium">{name}</div>
          <div className="text-xs">{expiryDate}</div>
        </div>
      </div>
      
      <div className="mastercard-logo">
        <div className="mastercard-circle mastercard-red"></div>
        <div className="mastercard-circle mastercard-yellow"></div>
      </div>
      
      <div className="absolute top-3 right-3 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
        Debit
      </div>
    </div>
  );
};

export default PaymentCard;
