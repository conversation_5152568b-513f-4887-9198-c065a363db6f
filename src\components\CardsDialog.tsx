
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { CreditCard, Plus, Clock, ChevronRight, Shield, Settings } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import EnhancedVirtualCard from '@/components/EnhancedVirtualCard';

interface CardsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userName: string;
}

const CardsDialog = ({ open, onOpenChange, userName }: CardsDialogProps) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>("virtual");
  
  const virtualCards = [
    {
      id: "card-1",
      cardNumber: "**** **** **** 7690",
      name: userName.toUpperCase() || "JOHN DOE",
      expiryDate: "08/26",
      cardType: "personal",
      variant: "virtual",
      balance: "₦85,000",
      limit: "₦200,000",
      status: "active"
    }
  ];
  
  const physicalCards = [
    {
      id: "card-2",
      cardNumber: "**** **** **** 9876",
      name: userName.toUpperCase() || "JOHN DOE",
      expiryDate: "05/27",
      cardType: "personal",
      variant: "physical",
      status: "pending"
    }
  ];
  
  const handleCardAction = (actionType: string, cardId?: string) => {
    switch (actionType) {
      case 'manage':
        navigate('/cards');
        onOpenChange(false);
        break;
      case 'request':
        toast({
          title: "Card Request Submitted",
          description: "Your physical card request has been submitted successfully",
          variant: "default"
        });
        break;
      case 'virtual':
        navigate('/cards/create-virtual');
        onOpenChange(false);
        break;
      default:
        break;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="bg-white/95 backdrop-blur-xl border border-[#D3E4FD] shadow-lg rounded-[20px] sm:max-w-[520px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-unica text-gray-900 flex items-center">
            <CreditCard className="mr-2 text-kojaPrimary" size={20} /> 
            Manage Cards
          </DialogTitle>
          <DialogDescription className="text-gray-600">
            View and manage your cards
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="virtual" onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="virtual" className="text-gray-700 data-[state=active]:text-kojaPrimary">Virtual Cards</TabsTrigger>
            <TabsTrigger value="physical" className="text-gray-700 data-[state=active]:text-kojaPrimary">Physical Cards</TabsTrigger>
          </TabsList>

          <TabsContent value="virtual" className="space-y-4">
            {virtualCards.length > 0 ? (
              <div className="space-y-4">
                {virtualCards.map((card) => (
                  <Card key={card.id} className="border-gray-100">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div className="w-full sm:w-auto">
                          <EnhancedVirtualCard
                            cardNumber={card.cardNumber}
                            name={card.name}
                            expiryDate={card.expiryDate}
                            cardType={card.cardType as 'personal' | 'business'}
                            variant={card.variant as 'physical' | 'virtual'}
                            className="mx-auto sm:mx-0"
                          />
                        </div>
                        <div className="w-full sm:w-auto space-y-3">
                          <div>
                            <p className="text-sm text-gray-600">Balance</p>
                            <p className="text-lg font-bold text-gray-900">{card.balance}</p>
                            <p className="text-xs text-gray-500">Limit: {card.limit}</p>
                          </div>
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="border-kojaPrimary/20 text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5"
                              onClick={() => handleCardAction('manage', card.id)}
                            >
                              <Settings size={16} className="mr-1" />
                              Manage
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                <Button
                  variant="outline"
                  className="w-full border-dashed border-2 border-gray-300 hover:border-kojaPrimary hover:bg-kojaPrimary/5 py-6"
                  onClick={() => handleCardAction('virtual')}
                >
                  <Plus size={20} className="mr-2 text-kojaPrimary" />
                  Create New Virtual Card
                </Button>
              </div>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="mx-auto mb-3 text-gray-300" size={40} />
                <p className="text-gray-700 font-medium">No Virtual Cards Yet</p>
                <p className="text-sm text-gray-500 mb-4">Create your first virtual card now</p>
                <Button 
                  onClick={() => handleCardAction('virtual')}
                  className="bg-kojaPrimary hover:bg-kojaPrimary/90"
                >
                  <Plus size={16} className="mr-2" />
                  Create Virtual Card
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="physical" className="space-y-4">
            {physicalCards.length > 0 ? (
              <div className="space-y-4">
                {physicalCards.map((card) => (
                  <Card key={card.id} className="border-gray-100">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                        <div className="w-full sm:w-auto">
                          <EnhancedVirtualCard
                            cardNumber={card.cardNumber}
                            name={card.name}
                            expiryDate={card.expiryDate}
                            cardType={card.cardType as 'personal' | 'business'}
                            variant={card.variant as 'physical' | 'virtual'}
                            className="mx-auto sm:mx-0"
                          />
                        </div>
                        <div className="w-full sm:w-auto space-y-3">
                          <div>
                            <p className="text-sm text-gray-600">Status</p>
                            <div className="flex items-center">
                              <p className="text-amber-600 font-medium flex items-center">
                                <Clock size={14} className="mr-1" /> Pending
                              </p>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Delivery in 3-5 business days</p>
                          </div>
                          <div className="flex flex-col sm:flex-row gap-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              className="border-kojaPrimary/20 text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5"
                              onClick={() => handleCardAction('track', card.id)}
                            >
                              Track Card
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <CreditCard className="mx-auto mb-3 text-gray-300" size={40} />
                <p className="text-gray-700 font-medium">No Physical Cards Yet</p>
                <p className="text-sm text-gray-500 mb-4">Request a physical card to be delivered to you</p>
                <Button 
                  onClick={() => handleCardAction('request')}
                  className="bg-kojaPrimary hover:bg-kojaPrimary/90"
                >
                  <Plus size={16} className="mr-2" />
                  Request Physical Card
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
        
        <Button 
          variant="outline" 
          className="w-full text-kojaPrimary border-kojaPrimary/20 hover:bg-kojaPrimary/5"
          onClick={() => navigate('/cards')}
        >
          View All Cards
          <ChevronRight size={16} className="ml-1" />
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default CardsDialog;
