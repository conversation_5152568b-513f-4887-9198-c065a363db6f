
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Lock, ShieldCheck, Clock, CheckCircle, AlertTriangle, CornerDownRight, Search, Filter, HandCoins } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import TransactionStatus from '@/components/TransactionStatus';
import { EnhancedInput } from '@/components/ui/enhanced-input';

// Define escrow transaction types
type EscrowStatus = 'pending' | 'active' | 'completed' | 'disputed' | 'cancelled';

interface EscrowTransaction {
  id: string;
  title: string;
  amount: number;
  seller: string;
  buyer: string;
  status: EscrowStatus;
  date: string;
  description: string;
  releaseDate?: string;
}

const BusinessEscrowPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  
  // Mock escrow transactions data
  const transactions: EscrowTransaction[] = [
    {
      id: 'ESC-B001',
      title: 'Product Shipment to ABC Enterprises',
      amount: 1250000,
      seller: 'Your Business',
      buyer: 'ABC Enterprises',
      status: 'active',
      date: '2023-05-15',
      description: 'Shipment of 200 units of premium products',
    },
    {
      id: 'ESC-B002',
      title: 'Web Development Project',
      amount: 750000,
      seller: 'DevPartners',
      buyer: 'Your Business',
      status: 'completed',
      date: '2023-05-10',
      releaseDate: '2023-05-20',
      description: 'Custom e-commerce website development',
    },
    {
      id: 'ESC-B003',
      title: 'Equipment Purchase',
      amount: 3450000,
      seller: 'Industrial Solutions',
      buyer: 'Your Business',
      status: 'pending',
      date: '2023-05-20',
      description: 'Industrial manufacturing equipment',
    },
    {
      id: 'ESC-B004',
      title: 'Consulting Services',
      amount: 650000,
      seller: 'Your Business',
      buyer: 'Global Corp',
      status: 'disputed',
      date: '2023-05-08',
      description: 'Business strategy consulting package',
    }
  ];

  // Function to handle escrow action (release, dispute, etc.)
  const handleEscrowAction = (transactionId: string, action: string) => {
    toast({
      title: `Escrow ${action}`,
      description: `Transaction ${transactionId} has been ${action}`,
      variant: "success"
    });
  };

  // Function to create new escrow
  const handleCreateEscrow = (e: React.FormEvent) => {
    e.preventDefault();
    setShowCreateForm(false);
    toast({
      title: "Escrow Created",
      description: "Your escrow transaction has been created successfully",
      variant: "success"
    });
  };

  // Filter transactions based on active tab and search query
  const filteredTransactions = transactions.filter(transaction => {
    const matchesTab = activeTab === 'all' || transaction.status === activeTab;
    const matchesSearch = transaction.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          transaction.id.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesTab && matchesSearch;
  });

  // Function to get status badge styling
  const getStatusBadge = (status: EscrowStatus) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">Pending</Badge>;
      case 'active':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Active</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      case 'disputed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Disputed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Cancelled</Badge>;
    }
  };

  // Function to render transaction status component
  const getTransactionStatusComponent = (status: EscrowStatus) => {
    switch (status) {
      case 'pending':
        return <TransactionStatus status="pending" message="Awaiting payment" compact />;
      case 'active':
        return <TransactionStatus status="processing" message="Funds in escrow" compact />;
      case 'completed':
        return <TransactionStatus status="success" message="Completed" compact />;
      case 'disputed':
        return <TransactionStatus status="failed" message="Disputed" compact />;
      case 'cancelled':
        return <TransactionStatus status="idle" message="Cancelled" compact />;
    }
  };

  return (
    <BusinessLayout pageTitle="Business Escrow Service">
      <div className="space-y-6">
        {/* Header with search */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight font-unica">Business Escrow Service</h2>
            <p className="text-muted-foreground">Manage secure business transactions with our escrow service</p>
          </div>
          <div className="w-full sm:w-auto flex gap-2">
            <div className="relative flex-1 sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search transactions..."
                className="pl-8 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
            <Button onClick={() => setShowCreateForm(true)}>Create Escrow</Button>
          </div>
        </div>

        {/* Information card */}
        <Card className="bg-gradient-to-r from-kojaPrimary/10 to-white border border-kojaPrimary/20">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="flex gap-4 items-center">
                <ShieldCheck className="h-10 w-10 text-kojaPrimary" />
                <div>
                  <h3 className="text-lg font-semibold text-kojaPrimary mb-1 font-unica">Business Escrow Protection</h3>
                  <p className="text-sm text-muted-foreground">Secure your high-value business transactions with our enterprise escrow service</p>
                </div>
              </div>
              <Button 
                variant="outline" 
                className="whitespace-nowrap text-kojaPrimary border-kojaPrimary/30 hover:bg-kojaPrimary/5"
                onClick={() => navigate('/escrow/terms')}
              >
                View Escrow Terms
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Create Escrow Form */}
        {showCreateForm && (
          <Card>
            <CardHeader>
              <CardTitle className="font-unica">Create New Business Escrow</CardTitle>
              <CardDescription>Enter the details of the transaction</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateEscrow} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Transaction Title</Label>
                    <Input id="title" placeholder="e.g. Product Shipment" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="amount">Amount (₦)</Label>
                    <EnhancedInput
                      id="amount"
                      type="number"
                      placeholder="0.00"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="seller">Seller Information</Label>
                    <Input id="seller" placeholder="Seller name or business" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="buyer">Buyer Information</Label>
                    <Input id="buyer" placeholder="Buyer name or business" required />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="description">Transaction Description</Label>
                    <Input id="description" placeholder="Describe the goods or services" required />
                  </div>
                </div>
                <div className="flex justify-end gap-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>Cancel</Button>
                  <Button type="submit">Create Escrow</Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Tabs for filtering transactions */}
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="all">All Transactions</TabsTrigger>
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="active">Active</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
            <TabsTrigger value="disputed">Disputed</TabsTrigger>
          </TabsList>
          
          <TabsContent value={activeTab} className="space-y-4">
            {filteredTransactions.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {filteredTransactions.map((transaction) => (
                  <Card key={transaction.id} className="overflow-hidden">
                    <CardContent className="p-0">
                      <div className="flex flex-col md:flex-row">
                        <div className="p-6 flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-medium text-lg font-unica">{transaction.title}</h3>
                              <p className="text-sm text-muted-foreground">{transaction.description}</p>
                            </div>
                            {getStatusBadge(transaction.status)}
                          </div>
                          
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                            <div>
                              <p className="text-sm text-muted-foreground">Transaction ID</p>
                              <p className="font-medium">{transaction.id}</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Amount</p>
                              <p className="font-bold text-lg">₦{transaction.amount.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Seller</p>
                              <p>{transaction.seller}</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Buyer</p>
                              <p>{transaction.buyer}</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground">Date Created</p>
                              <p>{new Date(transaction.date).toLocaleDateString()}</p>
                            </div>
                            {transaction.releaseDate && (
                              <div>
                                <p className="text-sm text-muted-foreground">Date Completed</p>
                                <p>{new Date(transaction.releaseDate).toLocaleDateString()}</p>
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 p-6 flex flex-col justify-between md:w-64">
                          <div className="mb-4">
                            <h4 className="font-medium mb-2 font-unica">Status</h4>
                            {getTransactionStatusComponent(transaction.status)}
                          </div>
                          
                          <div className="space-y-2 mt-auto">
                            {transaction.status === 'active' && (
                              <>
                                <Button 
                                  className="w-full"
                                  onClick={() => handleEscrowAction(transaction.id, 'released')}
                                >
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Release Funds
                                </Button>
                                <Button 
                                  variant="outline" 
                                  className="w-full"
                                  onClick={() => handleEscrowAction(transaction.id, 'disputed')}
                                >
                                  <AlertTriangle className="mr-2 h-4 w-4" />
                                  Dispute
                                </Button>
                              </>
                            )}
                            {transaction.status === 'pending' && (
                              <Button 
                                className="w-full"
                                onClick={() => handleEscrowAction(transaction.id, 'activated')}
                              >
                                <CornerDownRight className="mr-2 h-4 w-4" />
                                Pay to Escrow
                              </Button>
                            )}
                            {transaction.status === 'disputed' && (
                              <Button 
                                className="w-full"
                                onClick={() => handleEscrowAction(transaction.id, 'resolved')}
                              >
                                Resolve Dispute
                              </Button>
                            )}
                            <Button 
                              variant="ghost" 
                              className="w-full"
                              onClick={() => navigate(`/escrow/${transaction.id}`)}
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="p-8 text-center">
                <HandCoins className="mx-auto h-12 w-12 text-muted-foreground opacity-50 mb-3" />
                <h3 className="text-lg font-medium mb-1 font-unica">No Transactions Found</h3>
                <p className="text-muted-foreground mb-4">There are no escrow transactions matching your criteria</p>
                <Button onClick={() => setShowCreateForm(true)}>Create New Escrow</Button>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* Escrow Process Explanation */}
        <Card>
          <CardHeader>
            <CardTitle className="font-unica">How Business Escrow Works</CardTitle>
            <CardDescription>Our enterprise escrow process in four simple steps</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-2">
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-kojaPrimary/10 p-4 mb-4">
                  <HandCoins className="h-8 w-8 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2 font-unica">Step 1: Agreement</h3>
                <p className="text-sm text-muted-foreground">Buyer and seller agree on terms</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-kojaPrimary/10 p-4 mb-4">
                  <ShieldCheck className="h-8 w-8 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2 font-unica">Step 2: Payment</h3>
                <p className="text-sm text-muted-foreground">Buyer sends payment to escrow</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-kojaPrimary/10 p-4 mb-4">
                  <Clock className="h-8 w-8 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2 font-unica">Step 3: Delivery</h3>
                <p className="text-sm text-muted-foreground">Seller delivers goods or services</p>
              </div>
              
              <div className="flex flex-col items-center text-center">
                <div className="rounded-full bg-kojaPrimary/10 p-4 mb-4">
                  <CheckCircle className="h-8 w-8 text-kojaPrimary" />
                </div>
                <h3 className="font-medium mb-2 font-unica">Step 4: Release</h3>
                <p className="text-sm text-muted-foreground">Funds are released to the seller</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </BusinessLayout>
  );
};

export default BusinessEscrowPage;
