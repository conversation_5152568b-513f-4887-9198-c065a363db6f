import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash,
  StoreIcon,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';

const BusinessAccountManagement = () => {
  const businesses = [
    { 
      id: 'BUS001', 
      name: 'Tech Solutions Ltd', 
      email: '<EMAIL>',
      phone: '+234 ************',
      owner: '<PERSON>',
      industry: 'Technology',
      location: 'Lagos, Nigeria',
      dateJoined: '2023-01-15',
      status: 'Active'
    },
    { 
      id: 'BUS002', 
      name: 'Green Fashion Store', 
      email: '<EMAIL>',
      phone: '+234 ************',
      owner: 'Alice Smith',
      industry: 'Fashion',
      location: 'Abuja, Nigeria',
      dateJoined: '2023-02-20',
      status: 'Pending Verification'
    },
    { 
      id: 'BUS003', 
      name: 'Foodie Delights', 
      email: '<EMAIL>',
      phone: '+234 ************',
      owner: 'Robert Wilson',
      industry: 'Food & Beverage',
      location: 'Port Harcourt, Nigeria',
      dateJoined: '2023-03-05',
      status: 'Active'
    },
    { 
      id: 'BUS004', 
      name: 'Build-It Construction', 
      email: '<EMAIL>',
      phone: '+234 ************',
      owner: 'Grace Okoro',
      industry: 'Construction',
      location: 'Kano, Nigeria',
      dateJoined: '2023-04-12',
      status: 'Inactive'
    },
    { 
      id: 'BUS005', 
      name: 'MediCare Pharmacy', 
      email: '<EMAIL>',
      phone: '+234 ************',
      owner: 'David Musa',
      industry: 'Healthcare',
      location: 'Ibadan, Nigeria',
      dateJoined: '2023-05-08',
      status: 'Active'
    },
  ];

  return (
    <AdminLayout pageTitle="Business Account Management">
      <Helmet>
        <title>Business Account Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Businesses</CardTitle>
              <CardDescription>Businesses currently active</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">325</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Pending Verification</CardTitle>
              <CardDescription>Accounts awaiting approval</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">15</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Revenue</CardTitle>
              <CardDescription>From business accounts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">₦45.8M</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Inactive Accounts</CardTitle>
              <CardDescription>Accounts needing attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">8</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Business Account Management</CardTitle>
              <CardDescription>Manage business accounts and merchant stores on the platform</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Add New Business
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search businesses..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <StoreIcon size={12} />
                  <span>Retail</span>
                  <XCircle size={12} className="ml-1" />
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <CheckCircle size={12} />
                  <span>Active</span>
                  <XCircle size={12} className="ml-1" />
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>List of business accounts on the platform</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Business ID</TableHead>
                    <TableHead>Business Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Owner</TableHead>
                    <TableHead>Industry</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Date Joined</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {businesses.map((business) => (
                    <TableRow key={business.id}>
                      <TableCell className="font-medium">{business.id}</TableCell>
                      <TableCell>{business.name}</TableCell>
                      <TableCell>{business.email}</TableCell>
                      <TableCell>{business.phone}</TableCell>
                      <TableCell>{business.owner}</TableCell>
                      <TableCell>{business.industry}</TableCell>
                      <TableCell>{business.location}</TableCell>
                      <TableCell>{business.dateJoined}</TableCell>
                      <TableCell>
                        <Badge variant={
                          business.status === 'Active' ? 'outline' : 
                          business.status === 'Inactive' ? 'destructive' : 
                          'secondary'
                        }>
                          {business.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                          {business.status === 'Pending Verification' && <Clock size={12} className="mr-1" />}
                          {business.status === 'Inactive' && <XCircle size={12} className="mr-1" />}
                          {business.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Edit Account</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Deactivate Account</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default BusinessAccountManagement;
