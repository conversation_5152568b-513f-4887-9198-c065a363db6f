
// Notification utilities for frontend
export interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  timestamp: string;
  isRead: boolean;
}

// Comment out backend import - focusing on frontend only
// import { notificationController } from '../backend/src/controllers/notificationController';

export class NotificationUtils {
  private static notifications: NotificationData[] = [];

  static addNotification(notification: Omit<NotificationData, 'id' | 'timestamp' | 'isRead'>): NotificationData {
    const newNotification: NotificationData = {
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      isRead: false,
      ...notification
    };
    
    this.notifications.unshift(newNotification);
    return newNotification;
  }

  static getNotifications(): NotificationData[] {
    return [...this.notifications];
  }

  static markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.isRead = true;
    }
  }

  static clearAll(): void {
    this.notifications = [];
  }
}

export const notificationUtils = NotificationUtils;
