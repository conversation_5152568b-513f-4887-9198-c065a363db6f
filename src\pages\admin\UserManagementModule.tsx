import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  User, 
  Users, 
  UserPlus, 
  UserCheck, 
  Filter, 
  Search, 
  Store, 
  Trash2, 
  Edit, 
  Shield, 
  Lock, 
  Download
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const UserManagementModule = () => {
  const [activeTab, setActiveTab] = useState('personal');
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const handleUserAction = (action: string, userId: string) => {
    toast({
      title: `${action} User`,
      description: `Successfully ${action.toLowerCase()}ed user with ID: ${userId}`,
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // Mock data
  const personalUsers = [
    { id: 'P001', name: 'John Doe', email: '<EMAIL>', status: 'active', tier: 'tier2', joinDate: '2023-05-15' },
    { id: 'P002', name: 'Jane Smith', email: '<EMAIL>', status: 'inactive', tier: 'tier1', joinDate: '2023-06-20' },
    { id: 'P003', name: 'Michael Johnson', email: '<EMAIL>', status: 'active', tier: 'tier3', joinDate: '2023-04-10' },
    { id: 'P004', name: 'Sarah Williams', email: '<EMAIL>', status: 'pending', tier: 'tier1', joinDate: '2023-07-05' },
  ];
  
  const businessUsers = [
    { id: 'B001', name: 'Acme Corp', email: '<EMAIL>', status: 'active', type: 'Corporate', joinDate: '2023-03-12' },
    { id: 'B002', name: 'TechSolutions LLC', email: '<EMAIL>', status: 'active', type: 'SME', joinDate: '2023-05-18' },
    { id: 'B003', name: 'Global Enterprises', email: '<EMAIL>', status: 'inactive', type: 'Corporate', joinDate: '2023-02-25' },
    { id: 'B004', name: 'Local Shop', email: '<EMAIL>', status: 'pending', type: 'Merchant', joinDate: '2023-07-30' },
  ];

  const adminUsers = [
    { id: 'A001', name: 'Admin User', email: '<EMAIL>', role: 'admin', lastLogin: '2023-08-15 14:30' },
    { id: 'A002', name: 'Super Admin', email: '<EMAIL>', role: 'superadmin', lastLogin: '2023-08-15 10:15' },
  ];

  const renderUserStatusBadge = (status: string) => {
    let color = '';
    switch(status) {
      case 'active':
        color = 'bg-green-100 text-green-800';
        break;
      case 'inactive':
        color = 'bg-gray-100 text-gray-800';
        break;
      case 'pending':
        color = 'bg-yellow-100 text-yellow-800';
        break;
      default:
        color = 'bg-blue-100 text-blue-800';
    }
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
        {status}
      </span>
    );
  };

  return (
    <>
      <Helmet>
        <title>User Management | KojaPay Admin Portal</title>
      </Helmet>
      <AdminLayout pageTitle="User Management">
        <div className="space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold">User Management</h1>
              <p className="text-gray-600">Manage all user accounts and permissions</p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search users..." 
                  className="pl-10"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
              
              <Button className="bg-[#1231B8]">
                <UserPlus className="mr-2 h-4 w-4" />
                Add User
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="personal" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Personal Accounts
              </TabsTrigger>
              <TabsTrigger value="business" className="flex items-center gap-2">
                <Store className="h-4 w-4" />
                Business Accounts
              </TabsTrigger>
              <TabsTrigger value="admin" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Admin Accounts
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="personal">
              <Card>
                <CardHeader className="pb-2 flex flex-col sm:flex-row justify-between">
                  <CardTitle>Personal User Accounts</CardTitle>
                  <div className="flex gap-2 mt-2 sm:mt-0">
                    <Button variant="outline" size="sm">
                      <Filter className="mr-2 h-4 w-4" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">User ID</th>
                          <th className="text-left py-3 px-4">Name</th>
                          <th className="text-left py-3 px-4">Email</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-left py-3 px-4">Tier</th>
                          <th className="text-left py-3 px-4">Join Date</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {personalUsers
                          .filter(user => 
                            searchQuery ? user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                                          user.email.toLowerCase().includes(searchQuery.toLowerCase()) : true
                          )
                          .map(user => (
                          <tr key={user.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4">{user.id}</td>
                            <td className="py-3 px-4">{user.name}</td>
                            <td className="py-3 px-4">{user.email}</td>
                            <td className="py-3 px-4">{renderUserStatusBadge(user.status)}</td>
                            <td className="py-3 px-4">
                              <span className="capitalize">{user.tier}</span>
                            </td>
                            <td className="py-3 px-4">{user.joinDate}</td>
                            <td className="py-3 px-4 text-right">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-blue-600"
                                onClick={() => handleUserAction('View', user.id)}
                              >
                                <User className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-yellow-600"
                                onClick={() => handleUserAction('Edit', user.id)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-red-600"
                                onClick={() => handleUserAction('Delete', user.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    
                    {personalUsers.filter(user => 
                      searchQuery ? user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                                    user.email.toLowerCase().includes(searchQuery.toLowerCase()) : true
                    ).length === 0 && (
                      <div className="text-center py-10">
                        <p className="text-gray-500">No users found matching "{searchQuery}"</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="business">
              <Card>
                <CardHeader className="pb-2 flex flex-col sm:flex-row justify-between">
                  <CardTitle>Business User Accounts</CardTitle>
                  <div className="flex gap-2 mt-2 sm:mt-0">
                    <Button variant="outline" size="sm">
                      <Filter className="mr-2 h-4 w-4" />
                      Filter
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="mr-2 h-4 w-4" />
                      Export
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Business ID</th>
                          <th className="text-left py-3 px-4">Business Name</th>
                          <th className="text-left py-3 px-4">Email</th>
                          <th className="text-left py-3 px-4">Status</th>
                          <th className="text-left py-3 px-4">Type</th>
                          <th className="text-left py-3 px-4">Join Date</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {businessUsers
                          .filter(user => 
                            searchQuery ? user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                                          user.email.toLowerCase().includes(searchQuery.toLowerCase()) : true
                          )
                          .map(user => (
                          <tr key={user.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4">{user.id}</td>
                            <td className="py-3 px-4">{user.name}</td>
                            <td className="py-3 px-4">{user.email}</td>
                            <td className="py-3 px-4">{renderUserStatusBadge(user.status)}</td>
                            <td className="py-3 px-4">{user.type}</td>
                            <td className="py-3 px-4">{user.joinDate}</td>
                            <td className="py-3 px-4 text-right">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-blue-600"
                                onClick={() => handleUserAction('View', user.id)}
                              >
                                <User className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-yellow-600"
                                onClick={() => handleUserAction('Edit', user.id)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-red-600"
                                onClick={() => handleUserAction('Delete', user.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    
                    {businessUsers.filter(user => 
                      searchQuery ? user.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
                                    user.email.toLowerCase().includes(searchQuery.toLowerCase()) : true
                    ).length === 0 && (
                      <div className="text-center py-10">
                        <p className="text-gray-500">No businesses found matching "{searchQuery}"</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="admin">
              <Card>
                <CardHeader className="pb-2 flex flex-col sm:flex-row justify-between">
                  <CardTitle>Admin User Accounts</CardTitle>
                  <Button className="mt-2 sm:mt-0 bg-[#1231B8]">
                    <Shield className="mr-2 h-4 w-4" />
                    Add Admin
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-3 px-4">Admin ID</th>
                          <th className="text-left py-3 px-4">Name</th>
                          <th className="text-left py-3 px-4">Email</th>
                          <th className="text-left py-3 px-4">Role</th>
                          <th className="text-left py-3 px-4">Last Login</th>
                          <th className="text-right py-3 px-4">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {adminUsers.map(admin => (
                          <tr key={admin.id} className="border-b hover:bg-gray-50">
                            <td className="py-3 px-4">{admin.id}</td>
                            <td className="py-3 px-4">{admin.name}</td>
                            <td className="py-3 px-4">{admin.email}</td>
                            <td className="py-3 px-4">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                admin.role === 'superadmin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                              }`}>
                                {admin.role}
                              </span>
                            </td>
                            <td className="py-3 px-4">{admin.lastLogin}</td>
                            <td className="py-3 px-4 text-right">
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-yellow-600"
                                onClick={() => handleUserAction('Edit', admin.id)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-blue-600"
                                onClick={() => handleUserAction('Permissions', admin.id)}
                              >
                                <Lock className="h-4 w-4" />
                              </Button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </AdminLayout>
    </>
  );
};

export default UserManagementModule;
