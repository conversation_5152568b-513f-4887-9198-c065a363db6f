import * as React from 'react';
import { Link } from 'react-router-dom';
import { User, Building2, Moon, Sun, HelpCircle, UserPlus, Building, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';

const Index: React.FC = () => {
  const [darkMode, setDarkMode] = React.useState(false);
  
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };
  
  return (
    <div className={`min-h-screen bg-gradient-to-b from-blue-50 to-white dark:from-[#09125a] dark:to-[#1231B8] text-slate-900 dark:text-white relative overflow-hidden ${darkMode ? 'dark' : ''}`}>
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,transparent)] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,transparent,white)]" />
      
      <header className="container mx-auto px-4 py-6 flex justify-between items-center relative z-10">
        <div className="flex items-center gap-2">
          <img 
            alt="KojaPay Logo" 
            className="w-8 h-8 object-contain" 
            src="/lovable-uploads/037922b1-235b-42f7-86e4-7a09b957d9ee.png" 
          />
          <span className="font-bold text-xl">KojaPay</span>
        </div>
        <div className="flex items-center gap-4">
          <button 
            onClick={toggleDarkMode} 
            className="p-2 rounded-full hover:bg-slate-100 dark:hover:bg-white/10 transition-colors"
            aria-label="Toggle dark mode"
          >
            {darkMode ? 
              <Sun className="h-5 w-5 text-yellow-400" /> : 
              <Moon className="h-5 w-5 text-slate-700" />
            }
          </button>
          <Button variant="ghost" size="icon">
            <HelpCircle className="h-5 w-5" />
          </Button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-12 md:py-20 relative z-10">
        <div className="max-w-2xl mx-auto text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 tracking-tight">
            Banking Made Simple for Everyone
          </h1>
          <p className="text-lg text-slate-600 dark:text-slate-300">
            Experience seamless digital banking with enhanced security and innovative features.
          </p>
        </div>

        <div className="max-w-md mx-auto">
          <div className="bg-white/80 dark:bg-white/10 backdrop-blur-xl p-8 rounded-2xl shadow-xl border border-slate-200 dark:border-white/20">
            <div className="space-y-6">
              <div>
                <h2 className="text-xl font-semibold mb-4">Choose your account type</h2>
                <div className="grid grid-cols-2 gap-4">
                  <Link to="/personal-login" className="group">
                    <div className="h-full p-4 rounded-xl bg-slate-50 dark:bg-white/5 border border-slate-200 dark:border-white/10 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                      <div className="flex flex-col items-center">
                        <User className="h-6 w-6 mb-2 text-blue-600 dark:text-blue-400" />
                        <span className="font-medium">Personal</span>
                      </div>
                    </div>
                  </Link>
                  <Link to="/business-login" className="group">
                    <div className="h-full p-4 rounded-xl bg-slate-50 dark:bg-white/5 border border-slate-200 dark:border-white/10 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                      <div className="flex flex-col items-center">
                        <Building2 className="h-6 w-6 mb-2 text-blue-600 dark:text-blue-400" />
                        <span className="font-medium">Business</span>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>

              <div>
                <h2 className="text-xl font-semibold mb-4">New to KojaPay?</h2>
                <div className="grid grid-cols-2 gap-4">
                  <Link to="/create-account" className="group">
                    <div className="h-full p-4 rounded-xl bg-slate-50 dark:bg-white/5 border border-slate-200 dark:border-white/10 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                      <div className="flex flex-col items-center">
                        <UserPlus className="h-6 w-6 mb-2 text-blue-600 dark:text-blue-400" />
                        <span className="font-medium">Personal</span>
                      </div>
                    </div>
                  </Link>
                  <Link to="/business-signup" className="group">
                    <div className="h-full p-4 rounded-xl bg-slate-50 dark:bg-white/5 border border-slate-200 dark:border-white/10 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300">
                      <div className="flex flex-col items-center">
                        <Building className="h-6 w-6 mb-2 text-blue-600 dark:text-blue-400" />
                        <span className="font-medium">Business</span>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-center space-x-4 mt-8">
            <a 
              href="https://apps.apple.com/app/kojapay/id1234567890" 
              target="_blank" 
              rel="noopener noreferrer"
              className="transform transition-transform hover:scale-105"
            >
              <Button variant="outline" className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                App Store
              </Button>
            </a>
            <a 
              href="https://play.google.com/store/apps/details?id=com.kojapay.mobile" 
              target="_blank" 
              rel="noopener noreferrer"
              className="transform transition-transform hover:scale-105"
            >
              <Button variant="outline" className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Google Play
              </Button>
            </a>
          </div>

          <p className="text-center text-sm text-slate-600 dark:text-slate-400 mt-6">
            By proceeding, you agree to our{' '}
            <Link to="/terms" className="text-blue-600 dark:text-blue-400 hover:underline">Terms</Link>
            {' '}and{' '}
            <Link to="/privacy" className="text-blue-600 dark:text-blue-400 hover:underline">Privacy Policy</Link>
          </p>
        </div>
      </main>
    </div>
  );
};

export default Index;
