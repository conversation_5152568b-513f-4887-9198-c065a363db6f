
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Settings, 
  Globe, 
  Clock, 
  Mail, 
  Bell, 
  Shield,
  Database,
  Server,
  Lock,
  CreditCard,
  Banknote,
  AlertCircle,
  CheckCircle,
  HardDrive,
  FileCode,
  RefreshCcw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const SystemConfiguration = () => {
  const { toast } = useToast();

  const handleSaveChanges = () => {
    toast({
      title: "Settings Saved",
      description: "System configuration has been updated successfully",
    });
  };

  const handleRestart = () => {
    toast({
      title: "System Restarting",
      description: "System services are being restarted...",
    });
  };

  return (
    <AdminLayout pageTitle="System Configuration">
      <Helmet>
        <title>System Configuration | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">System Status</CardTitle>
              <CardDescription>Overall health</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                <CheckCircle className="text-green-500 h-6 w-6" />
                <div className="text-2xl font-bold text-green-500">Healthy</div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Uptime</CardTitle>
              <CardDescription>System availability</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">99.98%</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">API Requests</CardTitle>
              <CardDescription>Last 24 hours</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">1.2M</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Server Load</CardTitle>
              <CardDescription>Current load average</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">42%</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="apis">API & Services</TabsTrigger>
            <TabsTrigger value="maintenance">Maintenance</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold">General System Settings</CardTitle>
                <CardDescription>Configure core system settings</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Application Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="app-name">Application Name</Label>
                      <Input id="app-name" defaultValue="KojaPay Platform" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="app-url">Application URL</Label>
                      <Input id="app-url" defaultValue="https://platform.kojapay.com" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="app-timezone">Default Timezone</Label>
                      <Input id="app-timezone" defaultValue="Africa/Lagos" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="app-currency">Default Currency</Label>
                      <Input id="app-currency" defaultValue="NGN" />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Notification Configuration</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="email-notifications" className="flex items-center space-x-2">
                        <Mail className="h-4 w-4" />
                        <span>Email Notifications</span>
                      </Label>
                      <Switch id="email-notifications" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="sms-notifications" className="flex items-center space-x-2">
                        <Bell className="h-4 w-4" />
                        <span>SMS Notifications</span>
                      </Label>
                      <Switch id="sms-notifications" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="push-notifications" className="flex items-center space-x-2">
                        <Bell className="h-4 w-4" />
                        <span>Push Notifications</span>
                      </Label>
                      <Switch id="push-notifications" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="maintenance-notifications" className="flex items-center space-x-2">
                        <Settings className="h-4 w-4" />
                        <span>Maintenance Alerts</span>
                      </Label>
                      <Switch id="maintenance-notifications" defaultChecked />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Regional Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="date-format">Date Format</Label>
                      <Input id="date-format" defaultValue="DD/MM/YYYY" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="time-format">Time Format</Label>
                      <Input id="time-format" defaultValue="HH:mm:ss" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="language">Default Language</Label>
                      <Input id="language" defaultValue="English" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="number-format">Number Format</Label>
                      <Input id="number-format" defaultValue="1,234.56" />
                    </div>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="flex justify-end space-x-4">
                <Button variant="outline">Reset to Defaults</Button>
                <Button onClick={handleSaveChanges} className="bg-[#1231B8] hover:bg-[#09125a]">
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="security">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold">Security Configuration</CardTitle>
                <CardDescription>System security and access control settings</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Authentication Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="two-factor" className="flex items-center space-x-2">
                        <Shield className="h-4 w-4" />
                        <span>Require Two-Factor Authentication</span>
                      </Label>
                      <Switch id="two-factor" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="session-timeout" className="flex items-center space-x-2">
                        <Clock className="h-4 w-4" />
                        <span>Auto Session Timeout</span>
                      </Label>
                      <Switch id="session-timeout" defaultChecked />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="session-length">Session Length (minutes)</Label>
                      <Input id="session-length" type="number" defaultValue="30" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="max-login-attempts">Max Login Attempts</Label>
                      <Input id="max-login-attempts" type="number" defaultValue="5" />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Password Policy</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="min-password-length">Minimum Password Length</Label>
                      <Input id="min-password-length" type="number" defaultValue="8" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="password-expiry">Password Expiry (days)</Label>
                      <Input id="password-expiry" type="number" defaultValue="90" />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="require-uppercase" className="flex items-center space-x-2">
                        <span>Require Uppercase Letters</span>
                      </Label>
                      <Switch id="require-uppercase" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="require-special" className="flex items-center space-x-2">
                        <span>Require Special Characters</span>
                      </Label>
                      <Switch id="require-special" defaultChecked />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Encryption & Compliance</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="data-encryption" className="flex items-center space-x-2">
                        <Lock className="h-4 w-4" />
                        <span>Data At Rest Encryption</span>
                      </Label>
                      <Switch id="data-encryption" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="ssl-requirement" className="flex items-center space-x-2">
                        <Globe className="h-4 w-4" />
                        <span>Require SSL For All Connections</span>
                      </Label>
                      <Switch id="ssl-requirement" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="pci-compliance" className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4" />
                        <span>PCI Compliance Mode</span>
                      </Label>
                      <Switch id="pci-compliance" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="audit-logging" className="flex items-center space-x-2">
                        <FileCode className="h-4 w-4" />
                        <span>Enhanced Audit Logging</span>
                      </Label>
                      <Switch id="audit-logging" defaultChecked />
                    </div>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="flex justify-end space-x-4">
                <Button variant="outline">Reset to Defaults</Button>
                <Button onClick={handleSaveChanges} className="bg-[#1231B8] hover:bg-[#09125a]">
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="apis">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold">API & Services Configuration</CardTitle>
                <CardDescription>Configure APIs, integrations and services</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">API Settings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="api-enabled" className="flex items-center space-x-2">
                        <Globe className="h-4 w-4" />
                        <span>API Access Enabled</span>
                      </Label>
                      <Switch id="api-enabled" defaultChecked />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="api-rate-limit">API Rate Limit (requests/min)</Label>
                      <Input id="api-rate-limit" type="number" defaultValue="100" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="api-timeout">API Timeout (seconds)</Label>
                      <Input id="api-timeout" type="number" defaultValue="30" />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="api-logging" className="flex items-center space-x-2">
                        <FileCode className="h-4 w-4" />
                        <span>API Request Logging</span>
                      </Label>
                      <Switch id="api-logging" defaultChecked />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Payment Integration</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="payment-gateway" className="flex items-center space-x-2">
                        <CreditCard className="h-4 w-4" />
                        <span>Payment Gateway</span>
                      </Label>
                      <Switch id="payment-gateway" defaultChecked />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="gateway-name">Gateway Provider</Label>
                      <Input id="gateway-name" defaultValue="Paystack" />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="test-mode" className="flex items-center space-x-2">
                        <Banknote className="h-4 w-4" />
                        <span>Test Mode</span>
                      </Label>
                      <Switch id="test-mode" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="webhook-url">Webhook URL</Label>
                      <Input id="webhook-url" defaultValue="https://platform.kojapay.com/api/webhooks/payment" />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">External Services</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="email-service" className="flex items-center space-x-2">
                        <Mail className="h-4 w-4" />
                        <span>Email Service</span>
                      </Label>
                      <Switch id="email-service" defaultChecked />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email-provider">Email Provider</Label>
                      <Input id="email-provider" defaultValue="SendGrid" />
                    </div>
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="sms-service" className="flex items-center space-x-2">
                        <Bell className="h-4 w-4" />
                        <span>SMS Service</span>
                      </Label>
                      <Switch id="sms-service" defaultChecked />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="sms-provider">SMS Provider</Label>
                      <Input id="sms-provider" defaultValue="Twilio" />
                    </div>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="flex justify-end space-x-4">
                <Button variant="outline">Reset to Defaults</Button>
                <Button onClick={handleSaveChanges} className="bg-[#1231B8] hover:bg-[#09125a]">
                  Save Changes
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="maintenance">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl font-bold">System Maintenance</CardTitle>
                <CardDescription>System maintenance and operations</CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">System Operations</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="flex flex-col p-4 border rounded-md">
                      <div className="flex items-center gap-2 mb-2">
                        <RefreshCcw className="h-5 w-5 text-blue-600" />
                        <h4 className="font-semibold">Restart Application Services</h4>
                      </div>
                      <p className="text-sm text-gray-500 mb-4">Restart all application services and workers</p>
                      <Button 
                        variant="outline" 
                        className="self-start"
                        onClick={handleRestart}
                      >
                        Restart Services
                      </Button>
                    </div>
                    
                    <div className="flex flex-col p-4 border rounded-md">
                      <div className="flex items-center gap-2 mb-2">
                        <Database className="h-5 w-5 text-green-600" />
                        <h4 className="font-semibold">Database Maintenance</h4>
                      </div>
                      <p className="text-sm text-gray-500 mb-4">Run database optimization and cleanup tasks</p>
                      <Button 
                        variant="outline" 
                        className="self-start"
                      >
                        Run Maintenance
                      </Button>
                    </div>
                    
                    <div className="flex flex-col p-4 border rounded-md">
                      <div className="flex items-center gap-2 mb-2">
                        <HardDrive className="h-5 w-5 text-purple-600" />
                        <h4 className="font-semibold">Cache Management</h4>
                      </div>
                      <p className="text-sm text-gray-500 mb-4">Clear system caches and temporary files</p>
                      <Button 
                        variant="outline" 
                        className="self-start"
                      >
                        Clear Cache
                      </Button>
                    </div>
                    
                    <div className="flex flex-col p-4 border rounded-md">
                      <div className="flex items-center gap-2 mb-2">
                        <FileCode className="h-5 w-5 text-amber-600" />
                        <h4 className="font-semibold">Log Management</h4>
                      </div>
                      <p className="text-sm text-gray-500 mb-4">Download or clear system logs</p>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                        >
                          Download Logs
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                        >
                          Clear Logs
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Scheduled Maintenance</h3>
                  <div className="grid grid-cols-1 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="maintenance-window">Maintenance Window</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <Input id="maintenance-start" type="time" defaultValue="02:00" />
                        <Input id="maintenance-end" type="time" defaultValue="04:00" />
                      </div>
                      <p className="text-xs text-gray-500">Scheduled maintenance window (UTC)</p>
                    </div>
                    
                    <div className="flex items-center justify-between space-x-2">
                      <Label htmlFor="auto-backup" className="flex items-center space-x-2">
                        <Database className="h-4 w-4" />
                        <span>Automatic Daily Backups</span>
                      </Label>
                      <Switch id="auto-backup" defaultChecked />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="backup-retention">Backup Retention (days)</Label>
                      <Input id="backup-retention" type="number" defaultValue="30" />
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-semibold mb-4">Maintenance Mode</h3>
                  <div className="flex flex-col p-6 border rounded-md bg-amber-50">
                    <div className="flex items-center gap-2 mb-4">
                      <AlertCircle className="h-6 w-6 text-amber-600" />
                      <div>
                        <h4 className="font-semibold text-amber-800">Enable Maintenance Mode</h4>
                        <p className="text-sm text-amber-700">Puts the application in maintenance mode, blocking user access</p>
                      </div>
                    </div>
                    
                    <div className="space-y-3 mb-4">
                      <Label htmlFor="maintenance-message">Maintenance Message</Label>
                      <Input 
                        id="maintenance-message" 
                        defaultValue="KojaPay is currently undergoing scheduled maintenance. We'll be back shortly." 
                      />
                    </div>
                    
                    <div className="flex justify-end">
                      <Button 
                        variant="outline" 
                        className="bg-amber-600 text-white hover:bg-amber-700 hover:text-white border-amber-600 hover:border-amber-700"
                      >
                        Enable Maintenance Mode
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default SystemConfiguration;
