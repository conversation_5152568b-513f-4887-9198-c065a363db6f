
import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import UserLayout from '@/components/UserLayout';
import { AlertCircle, AlertTriangle, CheckCircle, Clock, Shield } from 'lucide-react';
import { useSecurity } from '@/contexts/SecurityContext';
import { FraudAlert } from '@/services/securityService';
import { toast } from 'sonner';

const FraudAlertDashboard = () => {
  const { alerts, refreshAlerts, updateAlertStatus } = useSecurity();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadAlerts();
  }, []);

  const loadAlerts = async () => {
    setIsLoading(true);
    try {
      await refreshAlerts();
    } catch (error) {
      console.error('Error loading alerts', error);
      toast.error('Failed to load security alerts');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async (alertId: string, status: 'pending' | 'reviewing' | 'resolved' | 'blocked') => {
    try {
      const success = await updateAlertStatus(alertId, status, 'user');
      if (success) {
        toast.success(`Alert status updated to ${status}`);
      } else {
        toast.error('Failed to update alert status');
      }
    } catch (error) {
      console.error('Error updating alert status', error);
      toast.error('An error occurred while updating the alert');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-amber-600 bg-amber-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'resolved':
      case 'blocked':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'reviewing':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short'
    }).format(date);
  };

  return (
    <UserLayout pageTitle="Fraud Alert Dashboard">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold">Fraud & Security Alerts</h1>
            <p className="text-muted-foreground">
              Monitor and manage security alerts related to your account
            </p>
          </div>
          <Button onClick={loadAlerts} disabled={isLoading}>
            <Shield className="mr-2 h-4 w-4" />
            Refresh Alerts
          </Button>
        </div>

        {alerts.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center p-6">
              <div className="rounded-full bg-green-100 p-3 mb-4">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Security Alerts</h3>
              <p className="text-center text-muted-foreground">
                Your account is currently secure with no active fraud alerts.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {alerts.map((alert) => (
              <Card key={alert.id} className="overflow-hidden">
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-2">
                      <div className={`rounded-full p-1 ${getSeverityColor(alert.severity)}`}>
                        <AlertTriangle className="h-4 w-4" />
                      </div>
                      <CardTitle className="text-lg">{alert.title}</CardTitle>
                    </div>
                    <Badge
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      {getStatusIcon(alert.status)}
                      <span className="capitalize">{alert.status}</span>
                    </Badge>
                  </div>
                  <CardDescription>
                    {formatDate(alert.timestamp)} • {alert.alertType.replace('_', ' ')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="mb-4">{alert.description}</p>
                  
                  {alert.location && (
                    <div className="text-sm text-muted-foreground mb-2">
                      <span className="font-medium">Location:</span> {alert.location}
                    </div>
                  )}
                  
                  {alert.deviceInfo && (
                    <div className="text-sm text-muted-foreground mb-2">
                      <span className="font-medium">Device:</span> {alert.deviceInfo}
                    </div>
                  )}
                  
                  {alert.ipAddress && (
                    <div className="text-sm text-muted-foreground mb-2">
                      <span className="font-medium">IP Address:</span> {alert.ipAddress}
                    </div>
                  )}
                  
                  {alert.riskFactors && alert.riskFactors.length > 0 && (
                    <>
                      <div className="font-medium text-sm mt-2 mb-1">Risk Factors:</div>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {alert.riskFactors.map((factor, index) => (
                          <Badge key={index} variant="secondary">
                            {factor.replace('_', ' ')}
                          </Badge>
                        ))}
                      </div>
                    </>
                  )}
                  
                  <Separator className="my-3" />
                  
                  <div className="flex justify-end space-x-2">
                    {alert.status !== 'resolved' && alert.status !== 'blocked' && (
                      <>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleStatusUpdate(alert.id, 'resolved')}
                        >
                          Mark as Resolved
                        </Button>
                        <Button 
                          variant="destructive" 
                          size="sm"
                          onClick={() => handleStatusUpdate(alert.id, 'blocked')}
                        >
                          Block Activity
                        </Button>
                      </>
                    )}
                    {alert.status === 'resolved' || alert.status === 'blocked' ? (
                      <Button variant="outline" size="sm">View Details</Button>
                    ) : (
                      <Button variant="default" size="sm">Investigate</Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </UserLayout>
  );
};

export default FraudAlertDashboard;
