
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check, ChevronRight, Shield, Clock, CreditCard, ArrowUp, BadgeDollarSign, User } from "lucide-react";
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { motion } from 'framer-motion';
import { AccountTier } from '@/types/account';

const AccountUpgrade = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const currentTier = (user as any)?.tier || 'tier1';
  
  const tiers = [
    {
      id: 'tier1',
      name: 'Basic',
      description: 'Essential features for everyday banking',
      limits: {
        daily: '₦50,000',
        monthly: '₦500,000',
        perTransaction: '₦20,000'
      },
      features: [
        'Basic KYC verification',
        'Standard transaction fees',
        'Single savings wallet',
        'Basic customer support'
      ],
      buttonText: currentTier === 'tier1' ? 'Current Plan' : 'Downgrade',
      variant: 'tier1' as const,
      upgradeRequirements: []
    },
    {
      id: 'tier2',
      name: 'Standard',
      description: 'Enhanced features for growing needs',
      limits: {
        daily: '₦200,000',
        monthly: '₦2,000,000',
        perTransaction: '₦100,000'
      },
      features: [
        'Advanced KYC verification',
        'Reduced transaction fees',
        'Multiple savings goals',
        'Priority customer support',
        'Virtual card access'
      ],
      buttonText: currentTier === 'tier2' ? 'Current Plan' : 'Upgrade',
      variant: 'tier2' as const,
      upgradeRequirements: [
        'Valid government ID',
        'Utility bill',
        'Additional contact details'
      ]
    },
    {
      id: 'tier3',
      name: 'Premium',
      description: 'Maximum limits for serious banking',
      limits: {
        daily: '₦1,000,000',
        monthly: '₦10,000,000', 
        perTransaction: '₦500,000'
      },
      features: [
        'Full KYC verification',
        'Lowest transaction fees',
        'Unlimited savings goals',
        'VIP customer support',
        'Physical and virtual cards',
        'Investment opportunities',
        'Free international transfers',
        'Exclusive rewards program'
      ],
      buttonText: currentTier === 'tier3' ? 'Current Plan' : 'Upgrade',
      variant: 'tier3' as const,
      upgradeRequirements: [
        'Valid government ID',
        'Utility bill',
        'Proof of income',
        'Tax identification',
        'In-person verification'
      ]
    }
  ];

  const handleUpgrade = (tierId: AccountTier) => {
    if (tierId === currentTier) {
      toast({
        title: "Current Plan",
        description: "You are already on this plan",
      });
      return;
    }
    
    const tierLevel = parseInt(tierId.replace('tier', ''));
    const currentLevel = parseInt(currentTier.replace('tier', ''));
    
    if (tierLevel < currentLevel) {
      toast({
        title: "Downgrade Confirmation",
        description: "Are you sure you want to downgrade your account? This will reduce your transaction limits.",
        variant: "destructive",
        action: (
          <Button variant="outline" size="sm" onClick={() => confirmTierChange(tierId)}>
            Confirm
          </Button>
        ),
      });
    } else {
      toast({
        title: "Upgrade Process Started",
        description: "Follow the verification steps to complete your upgrade",
      });
      
      // In a real app, navigate to upgrade verification flow
    }
  };
  
  const confirmTierChange = (tierId: AccountTier) => {
    toast({
      title: "Account Updated",
      description: `Your account has been switched to ${tierId.replace('tier', 'Tier ')}`,
    });
  };
  
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };
  
  return (
    <DashboardLayout pageTitle="Account Upgrade">
      <div className="py-6 space-y-8">
        <motion.div 
          className="glass-morph p-6 rounded-[20px]"
          initial="hidden" 
          animate="visible" 
          variants={fadeInUp}
        >
          <div className="flex flex-col space-y-2">
            <h1 className="text-2xl font-bold text-[#1231B8]">Account Tiers</h1>
            <p className="text-neutral-600">Upgrade your account to enjoy higher transaction limits and premium features</p>
          </div>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {tiers.map((tier, index) => (
            <motion.div 
              key={tier.id}
              initial="hidden" 
              animate="visible" 
              variants={fadeInUp}
              transition={{ delay: index * 0.1 }}
            >
              <Card variant={tier.variant} className={`h-full ${tier.id === currentTier ? 'ring-2 ring-[#1231B8]' : ''}`}>
                <CardHeader>
                  <CardTitle className={`text-xl ${tier.id === 'tier3' ? 'text-[#1231B8]' : ''}`}>
                    {tier.name} {tier.id === currentTier && <span className="ml-2 text-xs bg-[#1231B8]/10 text-[#1231B8] px-2 py-1 rounded-full">Current</span>}
                  </CardTitle>
                  <CardDescription>{tier.description}</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold flex items-center">
                      <CreditCard className="mr-2 h-4 w-4 text-[#1231B8]" />
                      Transaction Limits
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Daily Limit:</span>
                        <span className="font-medium">{tier.limits.daily}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Monthly Limit:</span>
                        <span className="font-medium">{tier.limits.monthly}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Per Transaction:</span>
                        <span className="font-medium">{tier.limits.perTransaction}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h4 className="text-sm font-semibold flex items-center">
                      <BadgeDollarSign className="mr-2 h-4 w-4 text-[#1231B8]" />
                      Features
                    </h4>
                    <ul className="space-y-2">
                      {tier.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <Check className="mr-2 h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
                          <span className="text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    variant={tier.id === currentTier ? 'outline' : tier.variant} 
                    className="w-full"
                    onClick={() => handleUpgrade(tier.id as AccountTier)}
                    disabled={tier.id === currentTier}
                  >
                    {tier.buttonText}
                    {tier.id !== currentTier && <ChevronRight className="ml-2 h-4 w-4" />}
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
        
        <motion.div 
          className="glass-morph p-6 rounded-[20px] mt-6"
          initial="hidden" 
          animate="visible" 
          variants={fadeInUp}
          transition={{ delay: 0.3 }}
        >
          <h2 className="text-xl font-bold text-[#1231B8] mb-4">Upgrade Requirements</h2>
          <div className="space-y-4">
            <p className="text-neutral-600">To upgrade your account tier, you will need to complete additional verification steps:</p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
              <Card className="shadow-md hover:shadow-xl">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <div className="p-3 bg-[#1231B8]/10 rounded-full">
                      <Shield className="h-6 w-6 text-[#1231B8]" />
                    </div>
                    <h3 className="font-medium">Identity Verification</h3>
                    <p className="text-sm text-gray-500">Upload government-issued ID and take a selfie for verification</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="shadow-md hover:shadow-xl">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <div className="p-3 bg-[#1231B8]/10 rounded-full">
                      <User className="h-6 w-6 text-[#1231B8]" />
                    </div>
                    <h3 className="font-medium">Address Verification</h3>
                    <p className="text-sm text-gray-500">Provide proof of address with a recent utility bill</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="shadow-md hover:shadow-xl">
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center space-y-2">
                    <div className="p-3 bg-[#1231B8]/10 rounded-full">
                      <Clock className="h-6 w-6 text-[#1231B8]" />
                    </div>
                    <h3 className="font-medium">Processing Time</h3>
                    <p className="text-sm text-gray-500">Account upgrades are processed within 24-48 hours</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>
    </DashboardLayout>
  );
};

export default AccountUpgrade;
