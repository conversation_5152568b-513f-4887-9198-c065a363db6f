
import React from 'react';
import { cn } from '@/lib/utils';
import { ArrowDownLeft, ArrowUpRight, MoreHorizontal } from 'lucide-react';
import { Avatar } from './ui/avatar';
import { MicroButton } from './ui/micro-button';

interface GlassTransactionItemProps {
  recipient: string;
  date: string;
  amount: string;
  status: 'pending' | 'success' | 'failed';
  type: 'incoming' | 'outgoing';
  initials?: string;
  image?: string;
  className?: string;
}

const GlassTransactionItem: React.FC<GlassTransactionItemProps> = ({
  recipient,
  date,
  amount,
  status,
  type,
  initials,
  image,
  className,
}) => {
  const isIncoming = type === 'incoming';
  
  const getStatusColor = () => {
    switch (status) {
      case 'pending':
        return 'bg-amber-100 text-amber-800';
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <div className={cn(
      'bg-white/80 backdrop-blur-sm rounded-xl p-3 border border-white/30 shadow-sm hover:shadow-md transition-all duration-300 transform hover:-translate-y-1',
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="relative">
            {image ? (
              <Avatar className="h-10 w-10">
                <img src={image} alt={recipient} />
              </Avatar>
            ) : (
              <Avatar className="h-10 w-10 bg-gray-100 text-kojaPrimary">
                <span>{initials}</span>
              </Avatar>
            )}
            <div className={cn(
              "absolute -bottom-1 -right-1 rounded-full h-5 w-5 flex items-center justify-center text-white",
              isIncoming ? "bg-green-500" : "bg-red-500"
            )}>
              {isIncoming ? (
                <ArrowDownLeft className="h-3 w-3" />
              ) : (
                <ArrowUpRight className="h-3 w-3" />
              )}
            </div>
          </div>
          
          <div>
            <p className="font-medium text-kojaDark text-sm">{recipient}</p>
            <div className="flex items-center gap-2">
              <p className="text-xs text-kojaGray">{date}</p>
              <span className={cn("text-[10px] px-1.5 py-0.5 rounded-full", getStatusColor())}>
                {status}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <p className={cn(
            "font-semibold",
            isIncoming ? "text-green-600" : "text-red-600"
          )}>
            {isIncoming ? '+' : '-'} {amount}
          </p>
          <MicroButton variant="ghost" size="icon" className="text-kojaGray hover:text-kojaDark">
            <MoreHorizontal className="h-4 w-4" />
          </MicroButton>
        </div>
      </div>
    </div>
  );
};

export default GlassTransactionItem;
