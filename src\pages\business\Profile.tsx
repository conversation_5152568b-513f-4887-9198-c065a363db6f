
import React, { useState } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MicroButton } from '@/components/ui/micro-button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Avatar } from '@/components/ui/avatar';
import { 
  Store, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  FileText, 
  Shield, 
  Edit, 
  Upload, 
  CheckCircle2, 
  AlertCircle, 
  Globe,
  Plus,
  CreditCard,
  Lock,
  Bell,
  Share2,
  HelpCircle,
  ChevronRight,
  LogOut,
  Camera
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import LogoutButton from '@/components/LogoutButton';

const BusinessProfile = () => {
  const { user, logout } = useAuth();
  const business = user as any;
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const [activeTab, setActiveTab] = useState("business");
  const [isEditing, setIsEditing] = useState(false);
  
  const businessData = {
    businessName: business?.businessName || 'Koja Enterprises',
    businessType: business?.businessType || 'Technology',
    registrationNumber: business?.cacNumber || 'RC123456',
    dateEstablished: 'January 15, 2020',
    businessEmail: business?.email || '<EMAIL>',
    businessPhone: business?.phoneNumber || '+234 ************',
    businessAddress: business?.businessAddress || '123 Marina Street, Lagos Island, Lagos',
    website: 'www.kojaenterprises.com',
    industry: 'Financial Technology',
    ownerName: business?.fullName || 'John Doe',
    ownerEmail: business?.email || '<EMAIL>',
    ownerPhone: business?.phoneNumber || '+234 ************',
    completionPercentage: 85
  };
  
  const handleLogout = () => {
    logout();
    toast({
      title: 'Logged Out',
      description: 'You have been successfully logged out',
    });
    navigate('/login');
  };
  
  const handleSaveChanges = () => {
    toast({
      title: 'Changes Saved',
      description: 'Your business profile has been updated successfully',
    });
    setIsEditing(false);
  };
  
  const handleUploadLogo = () => {
    // Mock upload functionality
    toast({
      title: 'Logo Updated',
      description: 'Your business logo has been updated successfully',
    });
  };
  
  const renderSidebar = () => (
    <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100 h-full">
      <CardContent className="p-4">
        <div className="mb-6 flex flex-col items-center">
          <div className="relative mb-4 mt-4">
            <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-kojaDark to-kojaPrimary flex items-center justify-center overflow-hidden border-2 border-white shadow-sm">
              {business?.businessLogo ? (
                <img 
                  src={business.businessLogo} 
                  alt={businessData.businessName} 
                  className="w-full h-full object-cover"
                />
              ) : (
                <Store className="h-8 w-8 text-white" />
              )}
            </div>
            <Button 
              size="icon" 
              variant="outline"
              className="absolute -bottom-2 -right-2 h-7 w-7 rounded-full bg-kojaYellow hover:bg-kojaYellow/90 text-black border-none shadow-sm"
              onClick={handleUploadLogo}
            >
              <Camera className="h-3.5 w-3.5" />
            </Button>
          </div>
          <div className="text-center">
            <h3 className="text-lg font-bold text-kojaDark font-futura">{businessData.businessName}</h3>
            <p className="text-kojaGray text-xs font-futura">{business?.accountNumber || '**********'}</p>
          </div>
          
          <div className="mt-5 w-full">
            <div className="progress-circle bg-kojaYellow/10">
              <div className="progress-circle-text text-kojaYellow">{businessData.completionPercentage}%</div>
            </div>
            <p className="text-xs text-center mt-2 text-kojaGray font-futura">Profile Completion</p>
          </div>
        </div>
        
        <div className="profile-sidebar-nav">
          <div 
            className={`profile-nav-item ${activeTab === "business" ? "profile-nav-item-active" : ""}`}
            onClick={() => setActiveTab("business")}
          >
            <Store className="profile-nav-icon" />
            <span className="profile-nav-text">Business Account</span>
            <ChevronRight size={16} className={activeTab === "business" ? "text-kojaDark" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "payment" ? "profile-nav-item-active" : ""}`}
            onClick={() => setActiveTab("payment")}
          >
            <CreditCard className="profile-nav-icon" />
            <span className="profile-nav-text">Payment Limits</span>
            <ChevronRight size={16} className={activeTab === "payment" ? "text-kojaDark" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "security" ? "profile-nav-item-active" : ""}`}
            onClick={() => setActiveTab("security")}
          >
            <Lock className="profile-nav-icon" />
            <span className="profile-nav-text">Account & Security</span>
            <ChevronRight size={16} className={activeTab === "security" ? "text-kojaDark" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "social" ? "profile-nav-item-active" : ""}`}
            onClick={() => setActiveTab("social")}
          >
            <Share2 className="profile-nav-icon" />
            <span className="profile-nav-text">Social Networks</span>
            <ChevronRight size={16} className={activeTab === "social" ? "text-kojaDark" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "notifications" ? "profile-nav-item-active" : ""}`}
            onClick={() => setActiveTab("notifications")}
          >
            <Bell className="profile-nav-icon" />
            <span className="profile-nav-text">Notifications</span>
            <ChevronRight size={16} className={activeTab === "notifications" ? "text-kojaDark" : "text-kojaGray"} />
          </div>
          
          <div 
            className={`profile-nav-item ${activeTab === "help" ? "profile-nav-item-active" : ""}`}
            onClick={() => setActiveTab("help")}
          >
            <HelpCircle className="profile-nav-icon" />
            <span className="profile-nav-text">Get Help</span>
            <ChevronRight size={16} className={activeTab === "help" ? "text-kojaDark" : "text-kojaGray"} />
          </div>
        </div>
        
        <div className="mt-6">
          <LogoutButton 
            asAuthButton 
            variant="destructive" 
            className="w-full font-futura"
          />
        </div>
      </CardContent>
    </Card>
  );
  
  return (
    <BusinessLayout pageTitle="Business Profile">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Left Sidebar */}
        <div className="lg:col-span-1">
          {renderSidebar()}
        </div>
        
        {/* Right Content */}
        <div className="lg:col-span-3">
          <Card className="shadow-md bg-white/90 backdrop-blur-sm border border-gray-100">
            <CardHeader className="border-b border-gray-100 pb-4">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl font-bold font-futura text-gray-800">Business Information</CardTitle>
                  <CardDescription className="font-futura text-gray-600">Manage your business details and settings</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <MicroButton 
                    variant="pill" 
                    size="sm"
                    className="text-kojaYellow border border-kojaYellow/30 bg-transparent hover:bg-kojaYellow/5 font-futura"
                    onClick={() => setIsEditing(!isEditing)}
                  >
                    {isEditing ? "Cancel" : "Edit Profile"}
                  </MicroButton>
                  <MicroButton 
                    variant="pill" 
                    size="sm"
                    className="bg-kojaYellow text-black font-futura font-medium"
                    onClick={() => navigate('/contact-us')}
                  >
                    Connect CS
                  </MicroButton>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-6">
              {/* Wrapping all TabsContent components in a Tabs container */}
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsContent value="business" className="mt-0">
                  <div>
                    <div className="flex items-center mb-6">
                      <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-kojaDark to-kojaPrimary flex items-center justify-center overflow-hidden border-2 border-white shadow-sm mr-4">
                        {business?.businessLogo ? (
                          <img 
                            src={business.businessLogo} 
                            alt={businessData.businessName} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Store className="h-8 w-8 text-white" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex gap-2">
                          <MicroButton 
                            variant="outline" 
                            size="sm"
                            onClick={handleUploadLogo}
                            className="font-futura"
                          >
                            <Upload className="h-3.5 w-3.5 mr-1" />
                            Upload Logo
                          </MicroButton>
                        </div>
                      </div>
                    </div>
                    
                    <h3 className="font-bold text-indigo-700 mb-4 font-futura">Business Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <Label htmlFor="businessName" className="text-xs text-indigo-600 font-futura font-medium">Business Name*</Label>
                        <Input 
                          id="businessName" 
                          value={businessData.businessName} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="businessType" className="text-xs text-indigo-600 font-futura font-medium">Business Type*</Label>
                        <Input 
                          id="businessType" 
                          value={businessData.businessType} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="registrationNumber" className="text-xs text-indigo-600 font-futura font-medium">Registration Number*</Label>
                        <Input 
                          id="registrationNumber" 
                          value={businessData.registrationNumber} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="dateEstablished" className="text-xs text-indigo-600 font-futura font-medium">Date Established</Label>
                        <Input 
                          id="dateEstablished" 
                          value={businessData.dateEstablished} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="industry" className="text-xs text-indigo-600 font-futura font-medium">Industry</Label>
                        <Input 
                          id="industry" 
                          value={businessData.industry} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="website" className="text-xs text-indigo-600 font-futura font-medium">Website</Label>
                        <Input 
                          id="website" 
                          value={businessData.website} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                    </div>
                    
                    <h3 className="font-bold text-indigo-700 mt-8 mb-4 font-futura">Contact Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <Label htmlFor="businessEmail" className="text-xs text-indigo-600 font-futura font-medium">Business Email*</Label>
                        <Input 
                          id="businessEmail" 
                          value={businessData.businessEmail} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="businessPhone" className="text-xs text-indigo-600 font-futura font-medium">Business Phone*</Label>
                        <Input 
                          id="businessPhone" 
                          value={businessData.businessPhone} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div className="md:col-span-3">
                        <Label htmlFor="businessAddress" className="text-xs text-indigo-600 font-futura font-medium">Business Address*</Label>
                        <Input 
                          id="businessAddress" 
                          value={businessData.businessAddress} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                    </div>
                    
                    <h3 className="font-bold text-indigo-700 mt-8 mb-4 font-futura">Business Owner</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <Label htmlFor="ownerName" className="text-xs text-indigo-600 font-futura font-medium">Owner Name*</Label>
                        <Input 
                          id="ownerName" 
                          value={businessData.ownerName} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="ownerEmail" className="text-xs text-indigo-600 font-futura font-medium">Owner Email*</Label>
                        <Input 
                          id="ownerEmail" 
                          value={businessData.ownerEmail} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                      
                      <div>
                        <Label htmlFor="ownerPhone" className="text-xs text-indigo-600 font-futura font-medium">Owner Phone*</Label>
                        <Input 
                          id="ownerPhone" 
                          value={businessData.ownerPhone} 
                          disabled={!isEditing} 
                          className="mt-1 rounded-[20px] font-futura text-gray-800 border-indigo-100 focus:border-indigo-300 focus:ring-1 focus:ring-indigo-200"
                        />
                      </div>
                    </div>
                    
                    <h3 className="font-bold text-indigo-700 mt-8 mb-4 font-futura">Business Documents</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-white rounded-lg hover:shadow-md transition-all duration-300 group">
                        <div className="flex items-center gap-3">
                          <div className="bg-kojaYellow/10 p-2 rounded-md">
                            <FileText className="h-5 w-5 text-kojaYellow" />
                          </div>
                          <div>
                            <div className="font-medium font-futura text-indigo-800 group-hover:text-indigo-600 transition-colors">Certificate of Incorporation</div>
                            <div className="text-xs text-green-600 flex items-center gap-1 font-futura">
                              <CheckCircle2 className="h-3 w-3" /> Verified
                            </div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" className="rounded-[20px] font-futura text-indigo-600 border-indigo-200 hover:bg-indigo-50 hover:border-indigo-300 transition-all">View</Button>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-white rounded-lg hover:shadow-md transition-all duration-300 group">
                        <div className="flex items-center gap-3">
                          <div className="bg-kojaYellow/10 p-2 rounded-md">
                            <Building className="h-5 w-5 text-kojaYellow" />
                          </div>
                          <div>
                            <div className="font-medium font-futura text-indigo-800 group-hover:text-indigo-600 transition-colors">Tax Identification</div>
                            <div className="text-xs text-green-600 flex items-center gap-1 font-futura">
                              <CheckCircle2 className="h-3 w-3" /> Verified
                            </div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" className="rounded-[20px] font-futura text-indigo-600 border-indigo-200 hover:bg-indigo-50 hover:border-indigo-300 transition-all">View</Button>
                      </div>
                      
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-indigo-50 to-white rounded-lg hover:shadow-md transition-all duration-300 group">
                        <div className="flex items-center gap-3">
                          <div className="bg-kojaYellow/10 p-2 rounded-md">
                            <Shield className="h-5 w-5 text-kojaYellow" />
                          </div>
                          <div>
                            <div className="font-medium font-futura text-indigo-800 group-hover:text-indigo-600 transition-colors">Business Permit</div>
                            <div className="text-xs text-yellow-600 flex items-center gap-1 font-futura">
                              <AlertCircle className="h-3 w-3" /> Pending
                            </div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm" className="rounded-[20px] font-futura text-indigo-600 border-indigo-200 hover:bg-indigo-50 hover:border-indigo-300 transition-all">Upload</Button>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="payment" className="mt-0">
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-bold mb-2 font-futura text-gray-800">Payment Limits</h3>
                    <p className="text-gray-600 font-futura">Manage spending limits for business transactions</p>
                  </div>
                </TabsContent>
                
                <TabsContent value="security" className="mt-0">
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-bold mb-2 font-futura text-gray-800">Account & Security</h3>
                    <p className="text-gray-600 font-futura">Protect your business account with enhanced security</p>
                  </div>
                </TabsContent>
                
                <TabsContent value="social" className="mt-0">
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-bold mb-2 font-futura text-gray-800">Social Networks</h3>
                    <p className="text-gray-600 font-futura">Connect your business social media accounts</p>
                  </div>
                </TabsContent>
                
                <TabsContent value="notifications" className="mt-0">
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-bold mb-2 font-futura text-gray-800">Notifications</h3>
                    <p className="text-gray-600 font-futura">Set up business alerts for transactions and account activity</p>
                  </div>
                </TabsContent>
                
                <TabsContent value="help" className="mt-0">
                  <div className="p-6 text-center">
                    <h3 className="text-lg font-bold mb-2 font-futura text-gray-800">Get Help</h3>
                    <p className="text-gray-600 font-futura">Access business support and resources</p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            
            <CardFooter className="border-t border-gray-100 pt-4 flex justify-end">
              {isEditing && (
                <Button 
                  onClick={handleSaveChanges}
                  className="bg-kojaYellow text-black hover:bg-kojaYellow/90 font-futura font-medium rounded-full px-6 shadow-md hover:shadow-lg transition-all"
                >
                  Save Changes
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      </div>
    </BusinessLayout>
  );
};

export default BusinessProfile;
