
import React from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Switch 
} from '@/components/ui/switch';
import { 
  Lock, 
  Bell, 
  CreditCard, 
  User, 
  Shield, 
  LogOut,
  Share2,
  Mail,
  Phone,
  Globe,
  Home
} from 'lucide-react';

const Settings = () => {
  return (
    <DashboardLayout pageTitle="Settings">
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold tracking-tight font-katina">Account Settings</h2>
          <p className="text-muted-foreground">Manage your personal account settings</p>
        </div>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="general">Profile</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="cards">Cards</TabsTrigger>
            <TabsTrigger value="privacy">Privacy</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general">
            <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
                <CardDescription>Update your account details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input id="name" defaultValue="John Doe" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input id="username" defaultValue="johndoe" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" defaultValue="+234 ************" />
                  </div>
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="address">Address</Label>
                    <Input id="address" defaultValue="123 Marina Street, Lagos Island, Lagos" />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-2">
                <Button variant="outline">Cancel</Button>
                <Button>Save Changes</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="notifications">
            <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>Choose how you want to be notified</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { id: 'txn-notif', title: 'Transaction Notifications', description: 'Receive notifications for all transactions' },
                    { id: 'login-notif', title: 'Login Alerts', description: 'Get notified about new login activities' },
                    { id: 'product-notif', title: 'Product Updates', description: 'Receive product and feature update information' },
                    { id: 'promo-notif', title: 'Promotional Offers', description: 'Get notified about special offers and promotions' },
                    { id: 'app-notif', title: 'App Notifications', description: 'Enable push notifications in the mobile app' },
                  ].map((item) => (
                    <div key={item.id} className="flex items-center justify-between space-x-2">
                      <div className="flex-1 space-y-1">
                        <p className="font-medium">{item.title}</p>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                      <Switch id={item.id} defaultChecked={item.id !== 'promo-notif'} />
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-end">
                <Button>Save Preferences</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="security">
            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>Manage your account security</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="currentPin">Current PIN</Label>
                    <Input id="currentPin" type="password" placeholder="Enter current PIN" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="newPin">New PIN</Label>
                    <Input id="newPin" type="password" placeholder="Enter new PIN" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirmPin">Confirm New PIN</Label>
                    <Input id="confirmPin" type="password" placeholder="Confirm new PIN" />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full">Change PIN</Button>
                </CardFooter>
              </Card>
              
              <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
                <CardHeader>
                  <CardTitle>Two-Factor Authentication</CardTitle>
                  <CardDescription>Add an extra layer of security</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between space-x-2">
                    <div className="flex-1 space-y-1">
                      <p className="font-medium">Enable Two-Factor Authentication</p>
                      <p className="text-sm text-muted-foreground">Protect your account with an additional security layer</p>
                    </div>
                    <Switch id="2fa" />
                  </div>
                  
                  <div>
                    <p className="text-sm text-muted-foreground">Two-factor authentication adds an extra layer of security to your account by requiring more than just a password to log in.</p>
                  </div>
                  
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="flex items-center gap-3">
                      <div className="rounded-full p-2 bg-primary/10">
                        <Shield className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h4 className="text-sm font-medium">Your account is protected</h4>
                        <p className="text-xs text-muted-foreground">Fraud protection and monitoring is enabled</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button className="w-full" variant="outline">Setup 2FA</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="cards">
            <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
              <CardHeader>
                <CardTitle>Card Management</CardTitle>
                <CardDescription>Manage your debit and virtual cards</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="bg-kojaPrimary/10 p-2 rounded-md">
                        <CreditCard className="h-5 w-5 text-kojaPrimary" />
                      </div>
                      <div>
                        <p className="font-medium text-kojaDark">Change Card PIN</p>
                        <p className="text-xs text-kojaGray">Update the PIN for your physical card</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Change</Button>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="bg-kojaPrimary/10 p-2 rounded-md">
                        <CreditCard className="h-5 w-5 text-kojaPrimary" />
                      </div>
                      <div>
                        <p className="font-medium text-kojaDark">Request New Card</p>
                        <p className="text-xs text-kojaGray">Order a replacement for your physical card</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Request</Button>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="bg-kojaPrimary/10 p-2 rounded-md">
                        <Lock className="h-5 w-5 text-kojaPrimary" />
                      </div>
                      <div>
                        <p className="font-medium text-kojaDark">Block Card</p>
                        <p className="text-xs text-kojaGray">Temporarily block your card</p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">Block</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="privacy">
            <Card className="backdrop-blur-sm bg-white/90 shadow-lg border border-gray-100">
              <CardHeader>
                <CardTitle>Privacy Settings</CardTitle>
                <CardDescription>Control your data and privacy preferences</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { id: 'data-collection', title: 'Data Collection', description: 'Allow collection of usage data to improve services' },
                    { id: 'marketing', title: 'Marketing Communications', description: 'Receive marketing and promotional emails' },
                    { id: 'third-party', title: 'Third-Party Sharing', description: 'Share data with trusted partners' },
                    { id: 'location', title: 'Location Services', description: 'Enable location-based features and services' },
                  ].map((item) => (
                    <div key={item.id} className="flex items-center justify-between space-x-2">
                      <div className="flex-1 space-y-1">
                        <p className="font-medium">{item.title}</p>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                      <Switch id={item.id} defaultChecked={item.id === 'data-collection'} />
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline" className="text-red-500">Delete Account</Button>
                <Button>Save Preferences</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
