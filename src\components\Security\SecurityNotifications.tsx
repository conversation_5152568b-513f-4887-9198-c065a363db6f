import React, { useState, useEffect } from 'react';
import { Bell, BellOff, Shield, AlertTriangle, Info, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';

export interface SecurityNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'critical' | 'success';
  timestamp: string;
  isRead: boolean;
  actionRequired?: boolean;
  actionText?: string;
  onAction?: () => void;
}

interface SecurityNotificationsProps {
  notifications?: SecurityNotification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
  onDismiss?: (id: string) => void;
  onAction?: (id: string) => void;
  className?: string;
}

const SecurityNotifications: React.FC<SecurityNotificationsProps> = ({
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead,
  onDismiss,
  onAction,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Count unread notifications
    setUnreadCount(notifications.filter(notification => !notification.isRead).length);
  }, [notifications]);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  const handleMarkAsRead = (id: string) => {
    if (onMarkAsRead) {
      onMarkAsRead(id);
    }
  };

  const handleMarkAllAsRead = () => {
    if (onMarkAllAsRead) {
      onMarkAllAsRead();
    }
  };

  const handleDismiss = (id: string) => {
    if (onDismiss) {
      onDismiss(id);
    }
  };

  const handleAction = (id: string) => {
    if (onAction) {
      onAction(id);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-amber-500" />;
      case 'critical':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'success':
        return <Check className="h-5 w-5 text-green-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className={`relative ${className}`}>
      <Button
        variant="ghost"
        size="icon"
        onClick={toggleOpen}
        className="relative h-10 w-10 rounded-full"
      >
        {isOpen ? (
          <BellOff className="h-5 w-5" />
        ) : (
          <>
            <Bell className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute top-0 right-0 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium text-white flex items-center justify-center">
                {unreadCount > 9 ? '9+' : unreadCount}
              </span>
            )}
          </>
        )}
      </Button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 mt-2 w-80 sm:w-96 z-50"
          >
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-lg flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-blue-500" />
                    Security Alerts
                  </CardTitle>
                  {unreadCount > 0 && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={handleMarkAllAsRead}
                      className="text-xs h-8"
                    >
                      Mark all as read
                    </Button>
                  )}
                </div>
                <CardDescription>
                  {unreadCount > 0 
                    ? `You have ${unreadCount} unread security ${unreadCount === 1 ? 'alert' : 'alerts'}`
                    : 'No new security alerts'}
                </CardDescription>
              </CardHeader>
              <Separator />
              <CardContent className="p-0">
                <ScrollArea className="h-[300px] px-4">
                  {notifications.length > 0 ? (
                    <div className="py-2 space-y-2">
                      {notifications.map((notification) => (
                        <div 
                          key={notification.id} 
                          className={`p-3 rounded-lg ${notification.isRead ? 'bg-gray-50' : 'bg-blue-50 border-l-4 border-blue-500'}`}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex items-start">
                              {getNotificationIcon(notification.type)}
                              <div className="ml-3">
                                <h4 className={`text-sm font-medium ${notification.isRead ? 'text-gray-700' : 'text-gray-900'}`}>
                                  {notification.title}
                                </h4>
                                <p className="text-xs text-gray-500 mt-1">
                                  {notification.message}
                                </p>
                                <div className="flex items-center mt-2">
                                  <span className="text-[10px] text-gray-400">
                                    {new Date(notification.timestamp).toLocaleString()}
                                  </span>
                                  {notification.actionRequired && (
                                    <Badge variant="outline" className="ml-2 text-[10px] py-0 h-4 bg-amber-50 border-amber-200 text-amber-700">
                                      Action Required
                                    </Badge>
                                  )}
                                </div>
                                {notification.actionRequired && notification.actionText && (
                                  <Button 
                                    variant="outline" 
                                    size="sm" 
                                    className="mt-2 h-7 text-xs bg-white"
                                    onClick={() => handleAction(notification.id)}
                                  >
                                    {notification.actionText}
                                  </Button>
                                )}
                              </div>
                            </div>
                            <div className="flex space-x-1">
                              {!notification.isRead && (
                                <Button 
                                  variant="ghost" 
                                  size="icon" 
                                  className="h-6 w-6" 
                                  onClick={() => handleMarkAsRead(notification.id)}
                                >
                                  <Check className="h-3 w-3" />
                                </Button>
                              )}
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-6 w-6" 
                                onClick={() => handleDismiss(notification.id)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full py-8">
                      <Shield className="h-12 w-12 text-gray-300 mb-2" />
                      <p className="text-gray-500 text-sm">No security notifications</p>
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
              <Separator />
              <CardFooter className="flex justify-between py-3">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs"
                  onClick={() => toast.success('Navigating to security settings')}
                >
                  Security Settings
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-xs"
                  onClick={() => setIsOpen(false)}
                >
                  Close
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SecurityNotifications;
