
import React, { ReactNode } from 'react';
import { Helmet } from 'react-helmet-async';
import DashboardSidebar from '@/components/DashboardSidebar';
import { useAuth } from '@/contexts/AuthContext';

interface UserLayoutProps {
  children: ReactNode;
  pageTitle?: string;
}

const UserLayout: React.FC<UserLayoutProps> = ({ children, pageTitle = 'User Dashboard' }) => {
  const { user } = useAuth();

  return (
    <div className="flex min-h-screen bg-gray-50">
      <Helmet>
        <title>{pageTitle} | KojaPay</title>
      </Helmet>

      <DashboardSidebar />
      
      <div className="flex-1 p-6 md:p-10 overflow-auto">
        <div className="max-w-7xl mx-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default UserLayout;
