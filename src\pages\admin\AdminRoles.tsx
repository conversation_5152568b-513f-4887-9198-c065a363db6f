
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  MoreHorizontal, 
  ShieldCheck, 
  UserCheck, 
  Eye, 
  Edit, 
  Trash,
  Plus,
  Filter,
  AlertCircle,
  CheckCircle,
  ShieldAlert,
  UserCog
} from 'lucide-react';

const AdminRoles = () => {
  const roles = [
    { 
      id: 'ROLE001', 
      name: 'Super Admin', 
      permissions: 'Full Access',
      users: 3,
      created: '2023-01-15',
      lastModified: '2023-05-22',
      status: 'Active'
    },
    { 
      id: 'ROLE002', 
      name: 'Finance Manager', 
      permissions: 'Finance Module',
      users: 8,
      created: '2023-02-20',
      lastModified: '2023-06-10',
      status: 'Active'
    },
    { 
      id: 'ROLE003', 
      name: 'Customer Support', 
      permissions: 'Support Module',
      users: 12,
      created: '2023-03-05',
      lastModified: '2023-06-18',
      status: 'Active'
    },
    { 
      id: 'ROLE004', 
      name: 'Security Analyst', 
      permissions: 'Security Module',
      users: 5,
      created: '2023-04-12',
      lastModified: '2023-05-30',
      status: 'Active'
    },
    { 
      id: 'ROLE005', 
      name: 'Read Only', 
      permissions: 'View Only',
      users: 7,
      created: '2023-05-08',
      lastModified: '2023-06-15',
      status: 'Disabled'
    },
  ];

  return (
    <AdminLayout pageTitle="Admin Roles Management">
      <Helmet>
        <title>Admin Roles Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Roles</CardTitle>
              <CardDescription>Active admin roles</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">5</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Admin Users</CardTitle>
              <CardDescription>Total admin users</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">35</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Permission Groups</CardTitle>
              <CardDescription>Configured groups</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">12</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Pending Approvals</CardTitle>
              <CardDescription>Access requests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">3</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="roles" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="roles">Role Management</TabsTrigger>
            <TabsTrigger value="users">Role Assignments</TabsTrigger>
            <TabsTrigger value="permissions">Permission Sets</TabsTrigger>
          </TabsList>
          
          <TabsContent value="roles">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Admin Roles</CardTitle>
                  <CardDescription>Manage administrative roles and permissions</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    <Plus size={16} className="mr-2" />
                    Create New Role
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search roles..." 
                      className="pl-9"
                    />
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>List of admin roles</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Role ID</TableHead>
                        <TableHead>Role Name</TableHead>
                        <TableHead>Permissions</TableHead>
                        <TableHead>Users</TableHead>
                        <TableHead>Created Date</TableHead>
                        <TableHead>Last Modified</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {roles.map((role) => (
                        <TableRow key={role.id}>
                          <TableCell className="font-medium">{role.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {role.name === 'Super Admin' && <ShieldCheck size={16} className="text-red-600" />}
                              {role.name === 'Finance Manager' && <UserCog size={16} className="text-blue-600" />}
                              {role.name === 'Customer Support' && <UserCheck size={16} className="text-green-600" />}
                              {role.name === 'Security Analyst' && <ShieldAlert size={16} className="text-amber-600" />}
                              {role.name === 'Read Only' && <Eye size={16} className="text-purple-600" />}
                              {role.name}
                            </div>
                          </TableCell>
                          <TableCell>{role.permissions}</TableCell>
                          <TableCell>{role.users}</TableCell>
                          <TableCell>{role.created}</TableCell>
                          <TableCell>{role.lastModified}</TableCell>
                          <TableCell>
                            <Badge variant={role.status === 'Active' ? 'outline' : 'secondary'}>
                              {role.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                              {role.status === 'Disabled' && <AlertCircle size={12} className="mr-1" />}
                              {role.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Edit Role</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Delete Role</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>Role Assignments</CardTitle>
                <CardDescription>Manage user role assignments</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <UserCheck size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Assign Roles to Users</h3>
                  <p className="text-gray-500 mb-6">Manage which users have access to which roles</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Manage Role Assignments</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="permissions">
            <Card>
              <CardHeader>
                <CardTitle>Permission Sets</CardTitle>
                <CardDescription>Configure detailed permission sets</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <ShieldCheck size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Configure Permissions</h3>
                  <p className="text-gray-500 mb-6">Set up granular permissions for each role in the system</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Configure Permissions</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminRoles;
