export type AccountType = 'personal' | 'business' | 'admin' | 'staff' | 'manager';
export type AccountTier = 'tier1' | 'tier2'; // Updated to only have 2 tiers as requested
export type UserRole = 'STAFF' | 'MANAGER' | 'ACCOUNT' | 'MANAGEMENT' | 'ADMIN' | 'admin' | 'superadmin';

export interface TransactionLimits {
  daily: number;
  weekly: number;
  monthly: number;
  perTransaction: number;
}

export interface BaseUser {
  id?: string;
  fullName: string;
  email: string;
  phoneNumber: string;
  role?: UserRole;
  isActive?: boolean;
  lastLogin?: Date;
  department?: string;
  permissions?: string[];
}

export interface PersonalAccount extends BaseUser {
  dateOfBirth: string;
  address: string;
  bvnNumber: string;
  governmentId: string;
  pin: string;
  accountNumber: string;
  balance: number;
  isVerified: boolean;
  tier: AccountTier;
  transactionLimits: TransactionLimits;
}

export interface BusinessAccount extends Omit<PersonalAccount, 'dateOfBirth'> {
  businessName: string;
  cacNumber: string;
  businessAddress: string;
  businessType: string;
}

export interface StaffAccount extends BaseUser {
  staffId: string;
  department: string;
  position: string;
  reportingTo?: string;
  accessLevel: number;
}

export interface AdminAccount extends BaseUser {
  adminId: string;
  role: UserRole; // Updated to use UserRole type which now includes 'admin' and 'superadmin'
  accessLevel: number;
  canManageUsers: boolean;
  canManageRoles: boolean;
  canViewAuditLogs: boolean;
  canManageSettings: boolean;
}

export interface AuthState {
  isAuthenticated: boolean;
  accountType: AccountType | null;
  user: PersonalAccount | BusinessAccount | StaffAccount | AdminAccount | null;
  isLoading: boolean;
  error: string | null;
}

export interface ActivityLog {
  id: string;
  userId: string;
  action: string;
  module: string;
  description: string;
  metadata?: Record<string, any>;
  timestamp: Date;
  ipAddress?: string;
  status: 'SUCCESS' | 'FAILURE' | 'PENDING';
}

export interface Department {
  id: string;
  name: string;
  code: string;
  managerId: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
  actions: string[];
}

export interface JointAccount {
  id: string;
  name: string;
  balance: number;
  jointHolders: string[];
  primaryHolder: string;
  accountType: 'savings' | 'checking' | 'investment';
  permissions: JointAccountPermissions;
}

export interface JointAccountPermissions {
  canTransfer: boolean;
  canViewStatements: boolean;
  canManageSettings: boolean;
  dailyTransferLimit: number;
}
