
import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { Product } from '@/services/ecommerceService';
import { CartItem } from '@/services/cartService';
import { useToast } from '@/hooks/use-toast';

// Define the cart interface
interface Cart {
  items: CartItem[];
  subtotal: number;
  itemCount: number;
}

// Define the cart context interface
interface CartContextType {
  cart: Cart;
  addToCart: (product: Product, quantity?: number) => void;
  removeFromCart: (productId: number | string, productName?: string) => void;
  updateCartItem: (productId: number | string, quantity: number) => void;
  clearCart: () => void;
  isInCart: (productId: number | string) => boolean;
  isCartOpen: boolean;
  setIsCartOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

// Create the context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Create the cart provider component
export const CartProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { toast } = useToast();
  const [cart, setCart] = useState<Cart>({
    items: [],
    subtotal: 0,
    itemCount: 0
  });
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Load cart from localStorage on mount
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem('kojapay_cart');
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        
        // Calculate subtotal and itemCount
        const subtotal = parsedCart.items.reduce(
          (sum: number, item: CartItem) => sum + (item.product.price * item.quantity), 
          0
        );
        
        const itemCount = parsedCart.items.reduce(
          (count: number, item: CartItem) => count + item.quantity, 
          0
        );
        
        setCart({
          items: parsedCart.items || [],
          subtotal,
          itemCount
        });
      }
    } catch (error) {
      console.error('Error loading cart from localStorage:', error);
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('kojapay_cart', JSON.stringify(cart));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
    }
  }, [cart]);

  // Add a product to the cart
  const addToCart = (product: Product, quantity = 1) => {
    setCart(prevCart => {
      const existingItemIndex = prevCart.items.findIndex(
        item => item.productId === product.id
      );
      
      let newItems;
      
      if (existingItemIndex >= 0) {
        // Product already in cart, update quantity
        newItems = [...prevCart.items];
        newItems[existingItemIndex] = {
          ...newItems[existingItemIndex],
          quantity: newItems[existingItemIndex].quantity + quantity
        };
      } else {
        // Product not in cart, add it
        newItems = [
          ...prevCart.items,
          {
            productId: product.id,
            product,
            quantity
          }
        ];
      }
      
      // Calculate new subtotal and itemCount
      const subtotal = newItems.reduce(
        (sum, item) => sum + (item.product.price * item.quantity), 
        0
      );
      
      const itemCount = newItems.reduce(
        (count, item) => count + item.quantity, 
        0
      );
      
      return {
        items: newItems,
        subtotal,
        itemCount
      };
    });
  };

  // Remove a product from the cart
  const removeFromCart = (productId: number | string, productName?: string) => {
    setCart(prevCart => {
      const newItems = prevCart.items.filter(
        item => item.productId !== productId
      );
      
      // Calculate new subtotal and itemCount
      const subtotal = newItems.reduce(
        (sum, item) => sum + (item.product.price * item.quantity), 
        0
      );
      
      const itemCount = newItems.reduce(
        (count, item) => count + item.quantity, 
        0
      );
      
      if (productName) {
        toast({
          title: "Item removed",
          description: `${productName} has been removed from your cart.`
        });
      }
      
      return {
        items: newItems,
        subtotal,
        itemCount
      };
    });
  };

  // Update the quantity of a cart item
  const updateCartItem = (productId: number | string, quantity: number) => {
    setCart(prevCart => {
      const newItems = prevCart.items.map(item => 
        item.productId === productId
          ? { ...item, quantity }
          : item
      );
      
      // Calculate new subtotal and itemCount
      const subtotal = newItems.reduce(
        (sum, item) => sum + (item.product.price * item.quantity), 
        0
      );
      
      const itemCount = newItems.reduce(
        (count, item) => count + item.quantity, 
        0
      );
      
      return {
        items: newItems,
        subtotal,
        itemCount
      };
    });
  };

  // Clear the cart
  const clearCart = () => {
    setCart({
      items: [],
      subtotal: 0,
      itemCount: 0
    });
  };

  // Check if a product is already in the cart
  const isInCart = (productId: number | string) => {
    return cart.items.some(item => item.productId === productId);
  };

  return (
    <CartContext.Provider 
      value={{ 
        cart, 
        addToCart, 
        removeFromCart, 
        updateCartItem, 
        clearCart, 
        isInCart,
        isCartOpen,
        setIsCartOpen
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

// Create a hook to use the cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
