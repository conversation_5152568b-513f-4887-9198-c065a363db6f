
/* Card component styles */
.glass-card {
  @apply bg-white/70 backdrop-blur-2xl border border-[#D3E4FD] shadow-md rounded-[20px] transition-all duration-300 hover:shadow-xl transform hover:translate-y-[-5px] font-poppins relative overflow-hidden;
}

.glass-card::before {
  content: "";
  @apply absolute -top-16 -right-16 w-32 h-32 bg-[#FDE314]/15 rounded-full blur-3xl opacity-50 z-0;
}
  
.stats-card {
  @apply bg-white/80 backdrop-blur-md p-5 border border-[#D3E4FD] transition-all duration-300 animate-fade-in rounded-[20px] shadow-md hover:shadow-xl font-poppins relative overflow-hidden;
}

.stats-card::before {
  content: "";
  @apply absolute top-5 right-5 w-20 h-20 bg-[#FDE314]/20 rounded-full blur-xl opacity-40 z-0;
}
  
.dashboard-card {
  @apply bg-white/80 backdrop-blur-md p-6 border border-[#D3E4FD] transition-transform duration-200 hover:translate-y-[-5px] rounded-[20px] shadow-md hover:shadow-xl font-poppins relative overflow-hidden;
}

.dashboard-card::before {
  content: "";
  @apply absolute -bottom-10 -left-10 w-32 h-32 bg-[#FDE314]/15 rounded-full blur-3xl opacity-40 z-0;
}

.koja-gradient-card {
  @apply relative overflow-hidden rounded-[20px] transition-all duration-300 bg-gradient-to-br from-white/80 to-white/60 backdrop-blur-lg border border-[#D3E4FD] shadow-md hover:shadow-xl font-poppins;
}

.koja-gradient-card::before {
  content: "";
  @apply absolute -top-10 -right-10 w-32 h-32 bg-[#FDE314]/20 rounded-full blur-3xl opacity-40 z-0;
}
  
.balance-card {
  @apply bg-white/80 backdrop-blur-md rounded-[20px] p-5 flex flex-col gap-2 border border-[#D3E4FD] animate-fade-in transition-all duration-300 shadow-md hover:shadow-xl font-poppins relative overflow-hidden;
}

.balance-card::before {
  content: "";
  @apply absolute top-0 right-0 w-32 h-32 bg-[#FDE314]/20 rounded-full blur-3xl opacity-40 z-0;
}

/* Add the new blur circle effect for wallet balance cards */
.wallet-balance-card {
  @apply relative overflow-hidden;
}

.wallet-balance-card::after {
  content: "";
  @apply absolute -right-8 top-10 w-40 h-40 bg-[#FDE314]/25 rounded-full blur-3xl opacity-70 z-0 animate-float-subtle;
}

/* Keep existing styles for other cards */
.insight-card {
  @apply bg-white/80 backdrop-blur-md p-5 border border-[#D3E4FD] transition-all hover:translate-y-[-5px] duration-300 animate-float rounded-[20px] shadow-md hover:shadow-xl font-poppins;
}

.modern-card {
  @apply bg-white/80 backdrop-blur-2xl rounded-[20px] border border-[#D3E4FD] transition-all duration-300 hover:translate-y-[-2px] shadow-md hover:shadow-xl font-poppins;
}

.modern-glass-card {
  @apply bg-white/60 backdrop-blur-2xl rounded-[20px] border border-[#D3E4FD] transition-all duration-300 hover:translate-y-[-2px] shadow-md hover:shadow-xl font-poppins;
}

.floating-card {
  @apply modern-card animate-float-subtle shadow-md hover:shadow-xl;
}

.notification-card {
  @apply bg-white/90 backdrop-blur-sm rounded-[20px] border border-[#D3E4FD] p-4 transition-all duration-300 animate-scale-in shadow-md hover:shadow-xl font-poppins;
}
  
.transaction-item {
  @apply bg-white/90 backdrop-blur-sm rounded-[20px] p-3 transition-all duration-300 border border-[#D3E4FD] animate-slide-up-fade shadow-md hover:shadow-xl font-poppins;
}

/* Product cards */
.product-card {
  @apply bg-white/90 backdrop-blur-sm rounded-[20px] border border-[#D3E4FD] overflow-hidden transition-all duration-300 shadow-md hover:shadow-xl;
}

/* 3D Card Effects */
.card-3d {
  @apply shadow-[0_10px_20px_rgba(0,0,0,0.1)] hover:shadow-[0_15px_30px_rgba(0,0,0,0.15)] transform hover:translate-y-[-5px] transition-all duration-300;
}

.card-3d-blue {
  @apply shadow-[0_10px_20px_rgba(18,49,184,0.1)] hover:shadow-[0_15px_30px_rgba(18,49,184,0.15)] transform hover:translate-y-[-5px] transition-all duration-300;
}

.card-3d-yellow {
  @apply shadow-[0_10px_20px_rgba(253,227,20,0.1)] hover:shadow-[0_15px_30px_rgba(253,227,20,0.15)] transform hover:translate-y-[-5px] transition-all duration-300;
}

/* Account tier cards */
.account-tier-card {
  @apply relative overflow-hidden rounded-[20px] p-6 border border-[#D3E4FD] shadow-md hover:shadow-xl transition-all duration-300 transform hover:translate-y-[-5px];
}

.tier-basic {
  @apply bg-gradient-to-br from-gray-50 to-gray-100 backdrop-blur-xl;
}

.tier-silver {
  @apply bg-gradient-to-br from-gray-200 to-white backdrop-blur-xl;
}

.tier-gold {
  @apply bg-gradient-to-br from-amber-100 to-yellow-50 backdrop-blur-xl;
}

.tier-premium {
  @apply bg-gradient-to-br from-[#1231B8]/10 to-[#1231B8]/5 backdrop-blur-xl;
}

/* Dialog/Sheet card alternatives */
.dialog-card {
  @apply bg-white/90 backdrop-blur-2xl rounded-[20px] border border-[#D3E4FD] shadow-lg overflow-hidden transition-all duration-300;
}

.popup-card {
  @apply bg-white/90 backdrop-blur-2xl rounded-[20px] border border-[#D3E4FD] shadow-lg p-6 transition-all duration-300;
}

/* Business theme cards */
.business-card {
  @apply bg-white/90 backdrop-blur-sm rounded-[20px] border border-[#D3E4FD] text-kojaDark transition-all duration-300 hover:translate-y-[-2px] shadow-md hover:shadow-xl;
}

/* Other specialized cards */
.fraud-alert {
  @apply bg-red-50/90 backdrop-blur-sm border-l-4 border-red-500 p-4 rounded-r-lg my-4 animate-pulse-light;
}

.highlight-border {
  @apply border-l-4 border-kojaYellow pl-4;
}

/* Admin portal specific cards */
.admin-dashboard-card {
  @apply bg-white/80 backdrop-blur-lg rounded-[20px] border border-[#D3E4FD] transition-all duration-300 shadow-md hover:shadow-xl overflow-hidden font-futura;
}

.admin-stat-card {
  @apply bg-white/70 backdrop-blur-xl rounded-[20px] border border-[#D3E4FD] p-5 transition-all duration-300 hover:translate-y-[-3px] shadow-md hover:shadow-xl font-futura;
}

.admin-status-card {
  @apply bg-white/60 backdrop-blur-2xl rounded-[20px] border border-[#D3E4FD] overflow-hidden shadow-md hover:shadow-xl font-futura;
}

.admin-action-card {
  @apply admin-dashboard-card flex flex-col items-center justify-center p-4 text-center cursor-pointer shadow-md hover:shadow-xl;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .responsive-card {
    @apply p-4;
  }
}
