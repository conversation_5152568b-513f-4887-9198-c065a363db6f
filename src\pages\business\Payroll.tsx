import React, { useState, useEffect } from 'react';
import BusinessLayout from '@/components/BusinessLayout';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle, 
  CardFooter 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  AlertDialog, 
  AlertDialogAction, 
  AlertDialogCancel, 
  AlertDialogContent, 
  AlertDialogDescription, 
  AlertDialogFooter, 
  AlertDialogHeader, 
  AlertDialogTitle 
} from '@/components/ui/alert-dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  Avatar, 
  AvatarFallback, 
  AvatarImage 
} from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  BadgeDollarSign, 
  CalendarDays, 
  Users, 
  FileText, 
  Plus,
  UserCheck, 
  Filter, 
  Download,
  Search,
  RefreshCw,
  MoreHorizontal,
  Pencil,
  Trash2,
  ArrowUpDown,
  ChevronDown,
  Banknote,
  Building,
  Mail,
  Phone,
  User,
  Clock,
  Calendar,
  PlusCircle,
  MinusCircle,
  Loader2,
  FileSpreadsheet,
  Receipt
} from 'lucide-react';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  payrollService, 
  PayrollRecord, 
  Deduction, 
  Allowance, 
  PayrollFilters, 
  CreatePayrollParams, 
  PayrollSummary, 
  PayrollStatistics 
} from '@/services/payrollService';
import { staffService, Staff } from '@/services/staffService';

const BusinessPayroll = () => {
  // States for payroll management
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('payroll');
  const [selectedPayroll, setSelectedPayroll] = useState<PayrollRecord | null>(null);
  const [payrollDetails, setPayrollDetails] = useState<PayrollRecord | null>(null);
  const [showPayrollForm, setShowPayrollForm] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [payrollToDelete, setPayrollToDelete] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterMonth, setFilterMonth] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [payrollStats, setPayrollStats] = useState<PayrollStatistics | null>(null);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showRunPayrollForm, setShowRunPayrollForm] = useState(false);
  const [runPayrollData, setRunPayrollData] = useState({
    payDate: '',
    startDate: '',
    endDate: '',
    description: '',
  });
  const [selectedStaffIds, setSelectedStaffIds] = useState<string[]>([]);
  const [showPayslipForm, setShowPayslipForm] = useState(false);
  const [currentPayslip, setCurrentPayslip] = useState<PayrollRecord | null>(null);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 2
    }).format(amount);
  };
  
  // Get status badge
  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { color: string; bgColor: string }> = {
      paid: { color: 'text-green-700', bgColor: 'bg-green-100' },
      pending: { color: 'text-yellow-700', bgColor: 'bg-yellow-100' },
      processing: { color: 'text-blue-700', bgColor: 'bg-blue-100' },
      failed: { color: 'text-red-700', bgColor: 'bg-red-100' },
    };
    
    const { color, bgColor } = statusMap[status.toLowerCase()] || { color: 'text-gray-700', bgColor: 'bg-gray-100' };
    
    return (
      <Badge variant="outline" className={`${color} ${bgColor} border-0`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };
  
  // Get initials for avatar
  const getInitials = (firstName: string, lastName: string): string => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`;
  };
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    if (showRunPayrollForm) {
      setRunPayrollData({
        ...runPayrollData,
        [name]: value
      });
    } else if (selectedPayroll) {
      setSelectedPayroll({
        ...selectedPayroll,
        [name]: name === 'grossSalary' || name === 'netSalary' || name === 'year' ? Number(value) : value
      });
    }
  };
  
  // Handle select change
  const handleSelectChange = (name: string, value: string) => {
    if (selectedPayroll) {
      setSelectedPayroll({
        ...selectedPayroll,
        [name]: value
      });
    }
  };
  
  // Handle add payroll
  const handleAddPayroll = () => {
    setFormMode('create');
    setSelectedPayroll({
      id: '',
      employeeId: '',
      employeeName: '',
      month: '',
      year: new Date().getFullYear(),
      grossSalary: 0,
      netSalary: 0,
      deductions: [],
      allowances: [],
      status: 'pending',
      paymentDate: new Date().toISOString(),
      createdAt: new Date().toISOString(),
    });
    setShowPayrollForm(true);
  };
  
  // Handle edit payroll
  const handleEditPayroll = (payroll: PayrollRecord) => {
    setFormMode('edit');
    setSelectedPayroll({ ...payroll });
    setShowPayrollForm(true);
  };
  
  // Handle view payroll details
  const handleViewPayrollDetails = async (id: string) => {
    try {
      const payroll = await payrollService.getPayrollById(id);
      setPayrollDetails(payroll);
    } catch (error) {
      console.error('Error fetching payroll details:', error);
    }
  };
  
  // Handle payroll form submit
  const handlePayrollFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPayroll) return;
    
    setIsSubmitting(true);
    
    try {
      if (formMode === 'create') {
        const createParams: CreatePayrollParams = {
          employeeId: selectedPayroll.employeeId,
          employeeName: selectedPayroll.employeeName,
          month: selectedPayroll.month,
          year: selectedPayroll.year,
          grossSalary: selectedPayroll.grossSalary,
          netSalary: selectedPayroll.netSalary,
          deductions: selectedPayroll.deductions,
          allowances: selectedPayroll.allowances,
          status: selectedPayroll.status,
          paymentDate: selectedPayroll.paymentDate,
        };
        await payrollService.createPayrollRecord(createParams);
      } else {
        await payrollService.updatePayrollRecord(selectedPayroll.id, selectedPayroll);
      }
      
      await fetchPayrollData();
      await fetchPayrollStatistics();
      setShowPayrollForm(false);
    } catch (error) {
      console.error('Error submitting payroll form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle delete payroll
  const handleDeletePayroll = async () => {
    if (!payrollToDelete) return;
    
    setIsSubmitting(true);
    try {
      await payrollService.deletePayrollRecord(payrollToDelete);
      await fetchPayrollData();
      await fetchPayrollStatistics();
      setShowDeleteConfirm(false);
      setPayrollToDelete(null);
    } catch (error) {
      console.error('Error deleting payroll:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle run payroll
  const handleRunPayroll = () => {
    setRunPayrollData({
      payDate: new Date().toISOString().split('T')[0],
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      description: `Payroll for ${new Date().toLocaleString('default', { month: 'long' })} ${new Date().getFullYear()}`,
    });
    setSelectedStaffIds([]);
    setShowRunPayrollForm(true);
  };
  
  // Handle run payroll submit
  const handleRunPayrollSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      await payrollService.processPayroll({
        payDate: runPayrollData.payDate,
        startDate: runPayrollData.startDate,
        endDate: runPayrollData.endDate,
        description: runPayrollData.description,
        employeeIds: selectedStaffIds.length > 0 ? selectedStaffIds : undefined,
      });
      
      await fetchPayrollData();
      await fetchPayrollStatistics();
      setShowRunPayrollForm(false);
    } catch (error) {
      console.error('Error running payroll:', error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle staff selection for payroll
  const handleStaffSelection = (staffId: string) => {
    setSelectedStaffIds(prev => {
      if (prev.includes(staffId)) {
        return prev.filter(id => id !== staffId);
      } else {
        return [...prev, staffId];
      }
    });
  };
  
  // Handle view payslip
  const handleViewPayslip = (payroll: PayrollRecord) => {
    setCurrentPayslip(payroll);
    setShowPayslipForm(true);
  };
  
  // Handle sorting
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    
    if (sortConfig?.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    
    setSortConfig({ key, direction });
    
    const sortedRecords = [...payrollRecords].sort((a, b) => {
      if (a[key as keyof PayrollRecord] < b[key as keyof PayrollRecord]) {
        return direction === 'asc' ? -1 : 1;
      }
      if (a[key as keyof PayrollRecord] > b[key as keyof PayrollRecord]) {
        return direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
    
    setPayrollRecords(sortedRecords);
  };
  
  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = payrollRecords.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(payrollRecords.length / itemsPerPage);
  
  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };
  
  // Load payroll records and staff on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchPayrollData();
      await fetchStaffData();
      await fetchPayrollStatistics();
    };
    
    loadInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // Apply filters
  useEffect(() => {
    fetchPayrollData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterMonth, filterStatus, searchQuery]);
  
  // Fetch payroll data with filters
  const fetchPayrollData = async () => {
    setIsLoading(true);
    try {
      const filters: PayrollFilters = {};
      
      if (searchQuery) {
        filters.search = searchQuery;
      }
      
      if (filterMonth) {
        filters.month = filterMonth;
      }
      
      if (filterStatus) {
        filters.status = filterStatus;
      }
      
      const records = await payrollService.getPayrollRecords(filters);
      setPayrollRecords(records);
    } catch (error) {
      console.error('Error fetching payroll data:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Fetch staff data
  const fetchStaffData = async () => {
    try {
      const staffData = await staffService.getStaff({});
      setStaff(staffData);
    } catch (error) {
      console.error('Error fetching staff data:', error);
    }
  };
  
  // Fetch payroll statistics
  const fetchPayrollStatistics = async () => {
    try {
      const stats = await payrollService.getPayrollStatistics();
      setPayrollStats(stats);
    } catch (error) {
      console.error('Error fetching payroll statistics:', error);
    }
  };
  
  return (
    <BusinessLayout pageTitle="Payroll Management">
      <div className="space-y-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Total Payroll</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-32" />
                    ) : (
                      formatCurrency(payrollStats?.totalPayroll || 0)
                    )}
                  </div>
                  <div className="text-xs text-gray-500">This Month</div>
                </div>
                <BadgeDollarSign className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Employees</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-16" />
                    ) : (
                      payrollStats?.activeEmployees || 0
                    )}
                  </div>
                  <div className="text-xs text-gray-500">Active</div>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Next Payday</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      new Date(payrollStats?.nextPayday || new Date()).toLocaleDateString('en-US', { 
                        month: 'short', 
                        day: 'numeric' 
                      })
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {isLoading ? (
                      <Skeleton className="h-4 w-16" />
                    ) : (
                      `In ${Math.ceil((new Date(payrollStats?.nextPayday || new Date()).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} days`
                    )}
                  </div>
                </div>
                <CalendarDays className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Pending Payroll</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold">
                    {isLoading ? (
                      <Skeleton className="h-8 w-32" />
                    ) : (
                      formatCurrency(payrollStats?.pendingPayroll || 0)
                    )}
                  </div>
                  <div className="text-xs text-gray-500">To be processed</div>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Tabs for Payroll and Reports */}
        <Tabs defaultValue="payroll" className="w-full" onValueChange={setActiveTab}>
          <div className="flex items-center justify-between mb-4">
            <TabsList className="bg-gray-100">
              <TabsTrigger value="payroll" className="data-[state=active]:bg-[#1231B8] data-[state=active]:text-white">
                <FileText className="mr-2 h-4 w-4" /> Payroll
              </TabsTrigger>
              <TabsTrigger value="reports" className="data-[state=active]:bg-[#1231B8] data-[state=active]:text-white">
                <FileSpreadsheet className="mr-2 h-4 w-4" /> Reports
              </TabsTrigger>
            </TabsList>
            
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                onClick={() => {
                  // Export payroll data
                  alert('Export functionality would be implemented here');
                }}
              >
                <Download className="mr-2 h-4 w-4" /> Export
              </Button>
              
              <Button 
                className="bg-[#1231B8] hover:bg-[#1231B8]/90"
                onClick={handleRunPayroll}
              >
                <BadgeDollarSign className="mr-2 h-4 w-4" /> Run Payroll
              </Button>
            </div>
          </div>
          
          {/* Payroll Tab Content */}
          <TabsContent value="payroll" className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
              <div className="relative w-full md:w-64">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search payroll..." 
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex gap-2 w-full md:w-auto">
                <Select value={filterMonth} onValueChange={setFilterMonth}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Month" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Months</SelectItem>
                    <SelectItem value="january">January</SelectItem>
                    <SelectItem value="february">February</SelectItem>
                    <SelectItem value="march">March</SelectItem>
                    <SelectItem value="april">April</SelectItem>
                    <SelectItem value="may">May</SelectItem>
                    <SelectItem value="june">June</SelectItem>
                    <SelectItem value="july">July</SelectItem>
                    <SelectItem value="august">August</SelectItem>
                    <SelectItem value="september">September</SelectItem>
                    <SelectItem value="october">October</SelectItem>
                    <SelectItem value="november">November</SelectItem>
                    <SelectItem value="december">December</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={filterStatus} onValueChange={setFilterStatus}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button variant="outline" size="icon" onClick={() => {
                  setSearchQuery('');
                  setFilterMonth('');
                  setFilterStatus('');
                }}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
              <CardContent className="p-0">
                {isLoading ? (
                  <div className="p-8">
                    <div className="space-y-4">
                      {Array(5).fill(0).map((_, index) => (
                        <div key={index} className="flex items-center gap-4">
                          <Skeleton className="h-10 w-10 rounded-full" />
                          <div className="space-y-2 flex-1">
                            <Skeleton className="h-4 w-48" />
                            <Skeleton className="h-3 w-32" />
                          </div>
                          <Skeleton className="h-8 w-24" />
                        </div>
                      ))}
                    </div>
                  </div>
                ) : payrollRecords.length === 0 ? (
                  <div className="flex items-center justify-center py-16">
                    <div className="text-center">
                      <Receipt size={48} className="mx-auto text-gray-400 mb-4" />
                      <h3 className="text-lg font-medium mb-1">No payroll records found</h3>
                      <p className="text-muted-foreground">
                        {searchQuery || filterMonth || filterStatus 
                          ? "Try adjusting your filters" 
                          : "Run your first payroll to get started"}
                      </p>
                      {(searchQuery || filterMonth || filterStatus) && (
                        <Button 
                          variant="outline" 
                          className="mt-4"
                          onClick={() => {
                            setSearchQuery('');
                            setFilterMonth('');
                            setFilterStatus('');
                          }}
                        >
                          Clear Filters
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="rounded-lg overflow-hidden">
                    <table className="w-full text-sm">
                      <thead className="bg-gray-50">
                        <tr>
                          <th 
                            className="px-4 py-3 text-left font-medium text-gray-500 cursor-pointer"
                            onClick={() => handleSort('employeeName')}
                          >
                            <div className="flex items-center">
                              Employee
                              {sortConfig?.key === 'employeeName' && (
                                <ChevronDown 
                                  className={`ml-1 h-4 w-4 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} 
                                />
                              )}
                            </div>
                          </th>
                          <th 
                            className="px-4 py-3 text-left font-medium text-gray-500 cursor-pointer"
                            onClick={() => handleSort('month')}
                          >
                            <div className="flex items-center">
                              Month
                              {sortConfig?.key === 'month' && (
                                <ChevronDown 
                                  className={`ml-1 h-4 w-4 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} 
                                />
                              )}
                            </div>
                          </th>
                          <th 
                            className="px-4 py-3 text-left font-medium text-gray-500 cursor-pointer"
                            onClick={() => handleSort('grossSalary')}
                          >
                            <div className="flex items-center">
                              Gross Salary
                              {sortConfig?.key === 'grossSalary' && (
                                <ChevronDown 
                                  className={`ml-1 h-4 w-4 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} 
                                />
                              )}
                            </div>
                          </th>
                          <th 
                            className="px-4 py-3 text-left font-medium text-gray-500 cursor-pointer"
                            onClick={() => handleSort('netSalary')}
                          >
                            <div className="flex items-center">
                              Net Salary
                              {sortConfig?.key === 'netSalary' && (
                                <ChevronDown 
                                  className={`ml-1 h-4 w-4 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} 
                                />
                              )}
                            </div>
                          </th>
                          <th 
                            className="px-4 py-3 text-left font-medium text-gray-500 cursor-pointer"
                            onClick={() => handleSort('status')}
                          >
                            <div className="flex items-center">
                              Status
                              {sortConfig?.key === 'status' && (
                                <ChevronDown 
                                  className={`ml-1 h-4 w-4 ${sortConfig.direction === 'desc' ? 'rotate-180' : ''}`} 
                                />
                              )}
                            </div>
                          </th>
                          <th className="px-4 py-3 text-right font-medium text-gray-500">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        {currentItems.map((payroll) => (
                          <tr key={payroll.id} className="bg-white hover:bg-gray-50">
                            <td className="px-4 py-3">
                              <div className="flex items-center gap-3">
                                <Avatar className="h-8 w-8 border">
                                  <AvatarFallback className="bg-[#1231B8]/10 text-[#1231B8]">
                                    {payroll.employeeName.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <span>{payroll.employeeName}</span>
                              </div>
                            </td>
                            <td className="px-4 py-3">
                              {payroll.month.charAt(0).toUpperCase() + payroll.month.slice(1)} {payroll.year}
                            </td>
                            <td className="px-4 py-3 font-medium">
                              {formatCurrency(payroll.grossSalary)}
                            </td>
                            <td className="px-4 py-3 font-medium">
                              {formatCurrency(payroll.netSalary)}
                            </td>
                            <td className="px-4 py-3">
                              {getStatusBadge(payroll.status)}
                            </td>
                            <td className="px-4 py-3 text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => handleViewPayslip(payroll)}>
                                    <FileText className="mr-2 h-4 w-4" /> View Payslip
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleEditPayroll(payroll)}>
                                    <Pencil className="mr-2 h-4 w-4" /> Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    className="text-red-600"
                                    onClick={() => {
                                      setPayrollToDelete(payroll.id);
                                      setShowDeleteConfirm(true);
                                    }}
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" /> Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
              
              {/* Pagination */}
              {!isLoading && payrollRecords.length > 0 && (
                <CardFooter className="flex items-center justify-between border-t p-4">
                  <div className="text-sm text-gray-500">
                    Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, payrollRecords.length)} of {payrollRecords.length} records
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                        className={currentPage === page ? "bg-[#1231B8] hover:bg-[#1231B8]/90" : ""}
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </CardFooter>
              )}
            </Card>
          </TabsContent>
          
          {/* Reports Tab Content */}
          <TabsContent value="reports" className="space-y-6">
            <Card className="backdrop-blur-sm bg-white/80 shadow-lg border border-gray-100">
              <CardHeader>
                <CardTitle>Payroll Reports</CardTitle>
                <CardDescription>Generate and view payroll reports</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Monthly Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      View monthly payroll summaries and trends
                    </CardContent>
                    <CardFooter className="pt-2">
                      <Button variant="outline" className="w-full">Generate</Button>
                    </CardFooter>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Department Analysis</CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      Analyze payroll by department and roles
                    </CardContent>
                    <CardFooter className="pt-2">
                      <Button variant="outline" className="w-full">Generate</Button>
                    </CardFooter>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-base">Tax Reports</CardTitle>
                    </CardHeader>
                    <CardContent className="text-sm">
                      Generate tax reports for compliance
                    </CardContent>
                    <CardFooter className="pt-2">
                      <Button variant="outline" className="w-full">Generate</Button>
                    </CardFooter>
                  </Card>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="text-lg font-medium mb-4">Recent Reports</h3>
                  <div className="space-y-3">
                    {[
                      { name: 'April 2023 Payroll Summary', date: 'May 5, 2023', type: 'Monthly Summary' },
                      { name: 'Q1 2023 Department Analysis', date: 'April 15, 2023', type: 'Department Analysis' },
                      { name: 'March 2023 Tax Report', date: 'April 10, 2023', type: 'Tax Report' },
                    ].map((report, index) => (
                      <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{report.name}</div>
                          <div className="text-xs text-gray-500">{report.type} • Generated on {report.date}</div>
                        </div>
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Run Payroll Dialog */}
      <Dialog open={showRunPayrollForm} onOpenChange={setShowRunPayrollForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Run Payroll</DialogTitle>
            <DialogDescription>
              Process payroll for your staff members. You can select specific staff or process for all active staff.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleRunPayrollSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="description">Payroll Description</Label>
                <Input 
                  id="description" 
                  name="description" 
                  value={runPayrollData.description} 
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="payDate">Payment Date</Label>
                <Input 
                  id="payDate" 
                  name="payDate" 
                  type="date" 
                  value={runPayrollData.payDate} 
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Period Start</Label>
                  <Input 
                    id="startDate" 
                    name="startDate" 
                    type="date" 
                    value={runPayrollData.startDate} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">Period End</Label>
                  <Input 
                    id="endDate" 
                    name="endDate" 
                    type="date" 
                    value={runPayrollData.endDate} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Select Staff Members</Label>
                  <div className="text-sm text-muted-foreground">
                    {selectedStaffIds.length} selected
                  </div>
                </div>
                <div className="max-h-60 overflow-y-auto border rounded-md p-2">
                  {staff.filter(s => s.status === 'active').map((staffMember) => (
                    <div key={staffMember.id} className="flex items-center space-x-2 py-2">
                      <Checkbox 
                        id={`staff-${staffMember.id}`} 
                        checked={selectedStaffIds.includes(staffMember.id)}
                        onCheckedChange={() => handleStaffSelection(staffMember.id)}
                      />
                      <Label 
                        htmlFor={`staff-${staffMember.id}`}
                        className="flex items-center gap-2 cursor-pointer"
                      >
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="bg-[#1231B8]/10 text-[#1231B8] text-xs">
                            {getInitials(staffMember.firstName, staffMember.lastName)}
                          </AvatarFallback>
                        </Avatar>
                        <span>{staffMember.firstName} {staffMember.lastName}</span>
                        <span className="text-sm text-muted-foreground">({formatCurrency(staffMember.salary)})</span>
                      </Label>
                    </div>
                  ))}
                </div>
                <div className="text-xs text-muted-foreground">
                  Leave empty to process payroll for all active staff members
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowRunPayrollForm(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting} className="bg-[#1231B8] hover:bg-[#1231B8]/90">
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    <BadgeDollarSign className="mr-2 h-4 w-4" />
                    Process Payroll
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Payroll Form Dialog */}
      <Dialog open={showPayrollForm} onOpenChange={setShowPayrollForm}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{formMode === 'create' ? 'Add Payroll Record' : 'Edit Payroll Record'}</DialogTitle>
            <DialogDescription>
              {formMode === 'create' 
                ? 'Create a new payroll record for an employee.' 
                : 'Update the payroll record details.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handlePayrollFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="employeeId">Employee</Label>
                <Select 
                  name="employeeId" 
                  value={selectedPayroll?.employeeId || ''} 
                  onValueChange={(value) => {
                    const staffMember = staff.find(s => s.id === value);
                    if (staffMember && selectedPayroll) {
                      setSelectedPayroll({
                        ...selectedPayroll,
                        employeeId: staffMember.id,
                        employeeName: `${staffMember.firstName} ${staffMember.lastName}`,
                        grossSalary: staffMember.salary,
                        netSalary: staffMember.salary,
                      });
                    }
                  }}
                  disabled={formMode === 'edit'}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select employee" />
                  </SelectTrigger>
                  <SelectContent>
                    {staff.map((staffMember) => (
                      <SelectItem key={staffMember.id} value={staffMember.id}>
                        {staffMember.firstName} {staffMember.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="month">Month</Label>
                  <Select 
                    name="month" 
                    value={selectedPayroll?.month || ''} 
                    onValueChange={(value) => handleSelectChange('month', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select month" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="january">January</SelectItem>
                      <SelectItem value="february">February</SelectItem>
                      <SelectItem value="march">March</SelectItem>
                      <SelectItem value="april">April</SelectItem>
                      <SelectItem value="may">May</SelectItem>
                      <SelectItem value="june">June</SelectItem>
                      <SelectItem value="july">July</SelectItem>
                      <SelectItem value="august">August</SelectItem>
                      <SelectItem value="september">September</SelectItem>
                      <SelectItem value="october">October</SelectItem>
                      <SelectItem value="november">November</SelectItem>
                      <SelectItem value="december">December</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <Input 
                    id="year" 
                    name="year" 
                    type="number" 
                    value={selectedPayroll?.year || new Date().getFullYear()} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="grossSalary">Gross Salary</Label>
                  <Input 
                    id="grossSalary" 
                    name="grossSalary" 
                    type="number" 
                    value={selectedPayroll?.grossSalary || 0} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="netSalary">Net Salary</Label>
                  <Input 
                    id="netSalary" 
                    name="netSalary" 
                    type="number" 
                    value={selectedPayroll?.netSalary || 0} 
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  name="status" 
                  value={selectedPayroll?.status || 'pending'} 
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="processing">Processing</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="paymentDate">Payment Date</Label>
                <Input 
                  id="paymentDate" 
                  name="paymentDate" 
                  type="date" 
                  value={selectedPayroll?.paymentDate ? new Date(selectedPayroll.paymentDate).toISOString().split('T')[0] : ''} 
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowPayrollForm(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {formMode === 'create' ? 'Creating...' : 'Updating...'}
                  </>
                ) : (
                  formMode === 'create' ? 'Create Record' : 'Update Record'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Payslip Dialog */}
      <Dialog open={showPayslipForm} onOpenChange={setShowPayslipForm}>
        <DialogContent className="max-w-2xl">
          {currentPayslip && (
            <>
              <DialogHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <DialogTitle className="text-xl">Payslip</DialogTitle>
                    <DialogDescription>
                      {currentPayslip.month.charAt(0).toUpperCase() + currentPayslip.month.slice(1)} {currentPayslip.year}
                    </DialogDescription>
                  </div>
                  <Button variant="outline" size="sm" onClick={() => {
                    // Print payslip functionality
                    window.print();
                  }}>
                    <FileText className="mr-2 h-4 w-4" /> Print
                  </Button>
                </div>
              </DialogHeader>
              <div className="space-y-6">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-semibold">KojaPay Ltd</h3>
                      <p className="text-sm text-muted-foreground">123 Business Avenue, Lagos</p>
                    </div>
                    <div className="text-right">
                      <h4 className="font-medium">Payment Date</h4>
                      <p className="text-sm">{new Date(currentPayslip.paymentDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <h3 className="text-base font-medium mb-3">Employee Information</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Name</p>
                      <p className="font-medium">{currentPayslip.employeeName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Employee ID</p>
                      <p className="font-medium">{currentPayslip.employeeId.slice(0, 8)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Position</p>
                      <p className="font-medium">
                        {staff.find(s => s.id === currentPayslip.employeeId)?.position || 'N/A'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Department</p>
                      <p className="font-medium">
                        {staff.find(s => s.id === currentPayslip.employeeId)?.department || 'N/A'}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4">
                  <h3 className="text-base font-medium mb-3">Earnings & Deductions</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">Earnings</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Basic Salary</span>
                          <span className="text-sm font-medium">{formatCurrency(currentPayslip.grossSalary)}</span>
                        </div>
                        {currentPayslip.allowances.map((allowance, index) => (
                          <div key={index} className="flex justify-between">
                            <span className="text-sm">{allowance.name}</span>
                            <span className="text-sm font-medium">{formatCurrency(allowance.amount)}</span>
                          </div>
                        ))}
                        <div className="flex justify-between border-t pt-2">
                          <span className="font-medium">Total Earnings</span>
                          <span className="font-medium">{formatCurrency(currentPayslip.grossSalary + currentPayslip.allowances.reduce((sum, a) => sum + a.amount, 0))}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium mb-2">Deductions</h4>
                      <div className="space-y-2">
                        {currentPayslip.deductions.map((deduction, index) => (
                          <div key={index} className="flex justify-between">
                            <span className="text-sm">{deduction.name}</span>
                            <span className="text-sm font-medium">{formatCurrency(deduction.amount)}</span>
                          </div>
                        ))}
                        <div className="flex justify-between border-t pt-2">
                          <span className="font-medium">Total Deductions</span>
                          <span className="font-medium">{formatCurrency(currentPayslip.deductions.reduce((sum, d) => sum + d.amount, 0))}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="border rounded-lg p-4 bg-gray-50">
                  <div className="flex justify-between items-center">
                    <h3 className="text-base font-medium">Net Pay</h3>
                    <p className="text-lg font-semibold">{formatCurrency(currentPayslip.netSalary)}</p>
                  </div>
                </div>
                
                <div className="text-center text-sm text-muted-foreground">
                  <p>This is a computer-generated payslip and does not require a signature.</p>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the payroll record
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              className="bg-red-600 hover:bg-red-700"
              onClick={handleDeletePayroll}
            >
              {isSubmitting ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Trash2 className="mr-2 h-4 w-4" />
              )}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </BusinessLayout>
  );
};

export default BusinessPayroll;
