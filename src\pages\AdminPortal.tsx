
import React, { useState } from 'react';
import DashboardLayout from '@/components/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { EnhancedCard, EnhancedCardContent, EnhancedCardHeader, EnhancedCardTitle } from "@/components/ui/enhanced-card";
import { Button } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { motion } from 'framer-motion';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import StatCard from '@/components/StatCard';
import { 
  BarChart3, 
  Users, 
  CreditCard, 
  Wallet, 
  ArrowUpRight, 
  ArrowDownRight, 
  DollarSign,
  Globe,
  ShieldCheck,
  Settings,
  BellRing,
  UserPlus,
  BadgeDollarSign,
  Building,
  Landmark
} from 'lucide-react';
import { cn } from '@/lib/utils';

const AdminPortal: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const firstName = (user as any)?.fullName?.split(' ')[0] || 'Admin';
  
  // Admin stats data
  const adminStats = [
    { title: "Total Users", value: "24,563", icon: <Users size={20} />, trend: { value: "12%", positive: true } },
    { title: "Active Cards", value: "5,823", icon: <CreditCard size={20} />, trend: { value: "8%", positive: true } },
    { title: "Transaction Volume", value: "₦142.5M", icon: <BarChart3 size={20} />, trend: { value: "5%", positive: true } },
    { title: "Cash Flow", value: "₦75.3M", icon: <Wallet size={20} />, trend: { value: "3%", positive: false } }
  ];

  // Admin quick actions data
  const adminActions = [
    { title: "User Management", icon: <Users size={24} />, color: "bg-blue-500/10 text-blue-500", path: "/admin/users" },
    { title: "Transactions", icon: <BadgeDollarSign size={24} />, color: "bg-green-500/10 text-green-500", path: "/admin/transactions" },
    { title: "Card Management", icon: <CreditCard size={24} />, color: "bg-purple-500/10 text-purple-500", path: "/admin/cards" },
    { title: "Security", icon: <ShieldCheck size={24} />, color: "bg-red-500/10 text-red-500", path: "/admin/security" },
    { title: "System Settings", icon: <Settings size={24} />, color: "bg-orange-500/10 text-orange-500", path: "/admin/settings" },
    { title: "Notifications", icon: <BellRing size={24} />, color: "bg-cyan-500/10 text-cyan-500", path: "/admin/notifications" },
    { title: "Partner Banks", icon: <Landmark size={24} />, color: "bg-indigo-500/10 text-indigo-500", path: "/admin/partners" },
    { title: "Business Accounts", icon: <Building size={24} />, color: "bg-pink-500/10 text-pink-500", path: "/admin/businesses" },
  ];

  // Fade in animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } }
  };

  const fadeInRight = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.4 } }
  };

  return (
    <DashboardLayout pageTitle="Admin Portal">
      <div className="py-6 space-y-8">
        {/* Welcome Banner */}
        <motion.div
          className="bg-gradient-to-r from-kojaPrimary to-kojaPrimary/80 p-6 rounded-[20px] text-white shadow-xl"
          initial="hidden"
          animate="visible"
          variants={fadeInUp}
        >
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div className="space-y-2">
              <h1 className="text-2xl font-bold">Welcome, {firstName}</h1>
              <p className="text-white/80">Admin Portal Dashboard</p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-3">
              <Button 
                variant="outline" 
                className="border-white/20 bg-white/10 hover:bg-white/20 text-white rounded-[20px]"
                onClick={() => navigate('/admin/reports')}
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                Reports
              </Button>
              <Button 
                className="bg-kojaYellow text-kojaDark hover:bg-kojaYellow/90 rounded-[20px]"
                onClick={() => navigate('/admin/users/new')}
              >
                <UserPlus className="mr-2 h-4 w-4" />
                New User
              </Button>
            </div>
          </div>
        </motion.div>

        {/* Stats Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
          {adminStats.map((stat, index) => (
            <motion.div
              key={stat.title}
              variants={fadeInUp}
              initial="hidden"
              animate="visible"
              transition={{ delay: 0.1 + index * 0.1 }}
            >
              <StatCard
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                trend={stat.trend}
                className={index % 2 === 0 ? "border-l-4 border-kojaPrimary" : "border-l-4 border-kojaYellow"}
              />
            </motion.div>
          ))}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Admin Actions */}
          <motion.div
            className="lg:col-span-2"
            variants={fadeInUp}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.3 }}
          >
            <Card className="rounded-[20px] border border-[#D3E4FD] shadow-sm hover:shadow-md transition-all duration-300">
              <CardHeader>
                <CardTitle>Admin Actions</CardTitle>
                <CardDescription>Manage system and user operations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {adminActions.map((action, index) => (
                    <button
                      key={action.title}
                      className="flex flex-col items-center justify-center p-4 rounded-[20px] border border-[#D3E4FD]/50 hover:shadow-md hover:border-[#D3E4FD] transition-all duration-300 bg-white/50 backdrop-blur-sm"
                      onClick={() => navigate(action.path)}
                    >
                      <div className={cn("p-3 rounded-full mb-3", action.color)}>
                        {action.icon}
                      </div>
                      <span className="text-sm font-medium text-center">{action.title}</span>
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* System Status */}
          <motion.div
            variants={fadeInRight}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.4 }}
          >
            <EnhancedCard animation="float" className="rounded-[20px]">
              <EnhancedCardHeader>
                <EnhancedCardTitle>System Status</EnhancedCardTitle>
              </EnhancedCardHeader>
              <EnhancedCardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-green-500/10 rounded-[20px] flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-sm font-medium">API Services</span>
                    </div>
                    <span className="text-xs text-green-600">Operational</span>
                  </div>
                  
                  <div className="p-4 bg-green-500/10 rounded-[20px] flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-sm font-medium">Payment Gateway</span>
                    </div>
                    <span className="text-xs text-green-600">Operational</span>
                  </div>
                  
                  <div className="p-4 bg-yellow-500/10 rounded-[20px] flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-yellow-500 rounded-full mr-3"></div>
                      <span className="text-sm font-medium">Authentication</span>
                    </div>
                    <span className="text-xs text-yellow-600">Partial Outage</span>
                  </div>
                  
                  <div className="p-4 bg-green-500/10 rounded-[20px] flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="h-3 w-3 bg-green-500 rounded-full mr-3"></div>
                      <span className="text-sm font-medium">Database</span>
                    </div>
                    <span className="text-xs text-green-600">Operational</span>
                  </div>
                  
                  <div className="mt-4">
                    <Button 
                      variant="outline" 
                      className="w-full rounded-[20px] border-kojaPrimary/20 text-kojaPrimary hover:bg-kojaPrimary/5"
                      onClick={() => navigate('/admin/system/status')}
                    >
                      View Full Status
                    </Button>
                  </div>
                </div>
              </EnhancedCardContent>
            </EnhancedCard>
          </motion.div>
        </div>

        {/* Recent Activity and System Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Recent Activity */}
          <motion.div
            className="lg:col-span-2"
            variants={fadeInUp}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.5 }}
          >
            <Card className="rounded-[20px] border border-[#D3E4FD] shadow-sm hover:shadow-md transition-all duration-300">
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((_, index) => (
                    <div key={index} className="p-4 border-b border-gray-100 last:border-0">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3">
                          <Avatar className="h-8 w-8">
                            <img src={`https://ui-avatars.com/api/?name=User${index + 1}&background=random&size=32`} alt={`User ${index + 1}`} />
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">
                              {index % 3 === 0 ? 'New user registration' : 
                               index % 3 === 1 ? 'Transaction approved' : 'Security alert'}
                            </p>
                            <p className="text-xs text-gray-500 mt-1">
                              {index % 3 === 0 ? 'John Doe registered a new account' : 
                               index % 3 === 1 ? 'Admin approved transaction #45872' : 'Multiple failed login attempts detected'}
                            </p>
                          </div>
                        </div>
                        <span className="text-xs text-gray-500">{index + 1}h ago</span>
                      </div>
                    </div>
                  ))}
                  <Button 
                    variant="ghost" 
                    className="w-full text-kojaPrimary hover:text-kojaPrimary/80 hover:bg-kojaPrimary/5"
                    onClick={() => navigate('/admin/activity')}
                  >
                    View All Activity
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            variants={fadeInRight}
            initial="hidden"
            animate="visible"
            transition={{ delay: 0.6 }}
          >
            <Card className="rounded-[20px] border border-[#D3E4FD] shadow-sm hover:shadow-md transition-all duration-300">
              <CardHeader>
                <CardTitle>KPIs Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">User Growth</span>
                    <div className="flex items-center text-green-500 text-sm">
                      <ArrowUpRight size={14} className="mr-1" />
                      <span>12%</span>
                    </div>
                  </div>
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div className="h-full bg-green-500" style={{ width: '75%' }}></div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Transaction Success</span>
                    <div className="flex items-center text-green-500 text-sm">
                      <ArrowUpRight size={14} className="mr-1" />
                      <span>98.2%</span>
                    </div>
                  </div>
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div className="h-full bg-kojaPrimary" style={{ width: '98%' }}></div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Revenue</span>
                    <div className="flex items-center text-green-500 text-sm">
                      <ArrowUpRight size={14} className="mr-1" />
                      <span>7.8%</span>
                    </div>
                  </div>
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div className="h-full bg-kojaYellow" style={{ width: '65%' }}></div>
                  </div>
                </div>

                <Button 
                  className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90 text-white rounded-[20px]"
                  onClick={() => navigate('/admin/analytics')}
                >
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Detailed Analytics
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default AdminPortal;
