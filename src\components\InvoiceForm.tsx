
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Trash2, Send } from 'lucide-react';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  price: number;
}

const InvoiceForm: React.FC = () => {
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [dueDate, setDueDate] = useState('');
  const [items, setItems] = useState<InvoiceItem[]>([{ 
    id: '1', 
    description: '', 
    quantity: 1, 
    price: 0 
  }]);
  const [notes, setNotes] = useState('');
  const [paymentTerms, setPaymentTerms] = useState('immediate');
  
  const { toast } = useToast();

  const addItem = () => {
    setItems([
      ...items,
      {
        id: String(Date.now()),
        description: '',
        quantity: 1,
        price: 0
      }
    ]);
  };

  const removeItem = (id: string) => {
    if (items.length === 1) {
      toast({
        title: "Cannot remove item",
        description: "Invoice must have at least one item",
        variant: "destructive"
      });
      return;
    }
    setItems(items.filter(item => item.id !== id));
  };

  const updateItem = (id: string, field: keyof InvoiceItem, value: string | number) => {
    setItems(items.map(item => {
      if (item.id === id) {
        return { ...item, [field]: value };
      }
      return item;
    }));
  };

  const calculateTotal = () => {
    return items.reduce((total, item) => total + (item.quantity * item.price), 0);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!customerName || !customerEmail || !dueDate) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }
    
    const invalidItems = items.some(item => !item.description || item.quantity <= 0 || item.price <= 0);
    if (invalidItems) {
      toast({
        title: "Invalid items",
        description: "All items must have a description, quantity, and price",
        variant: "destructive"
      });
      return;
    }
    
    // In a real app, this would submit to a backend
    toast({
      title: "Invoice created",
      description: `Invoice for ${customerName} has been created successfully`,
      variant: "success"
    });
    
    // Reset the form
    setCustomerName('');
    setCustomerEmail('');
    setDueDate('');
    setItems([{ id: '1', description: '', quantity: 1, price: 0 }]);
    setNotes('');
    setPaymentTerms('immediate');
  };

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h2 className="text-xl font-semibold mb-6">Create New Invoice</h2>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <Label htmlFor="customerName">Customer Name*</Label>
            <Input 
              id="customerName" 
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              className="mt-1"
              placeholder="Enter customer name"
            />
          </div>
          
          <div>
            <Label htmlFor="customerEmail">Customer Email*</Label>
            <Input 
              id="customerEmail" 
              type="email"
              value={customerEmail}
              onChange={(e) => setCustomerEmail(e.target.value)}
              className="mt-1"
              placeholder="Enter customer email"
            />
          </div>
          
          <div>
            <Label htmlFor="dueDate">Due Date*</Label>
            <Input 
              id="dueDate" 
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              className="mt-1"
            />
          </div>
          
          <div>
            <Label htmlFor="paymentTerms">Payment Terms</Label>
            <Select value={paymentTerms} onValueChange={setPaymentTerms}>
              <SelectTrigger id="paymentTerms" className="mt-1">
                <SelectValue placeholder="Select payment terms" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="immediate">Due immediately</SelectItem>
                <SelectItem value="7days">Net 7 days</SelectItem>
                <SelectItem value="14days">Net 14 days</SelectItem>
                <SelectItem value="30days">Net 30 days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <Label>Invoice Items*</Label>
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={addItem}
              className="flex items-center"
            >
              <PlusCircle className="h-4 w-4 mr-1" />
              Add Item
            </Button>
          </div>
          
          <div className="space-y-4">
            {items.map((item, index) => (
              <div key={item.id} className="grid grid-cols-12 gap-2 items-center p-3 border rounded-lg bg-gray-50">
                <div className="col-span-12 md:col-span-5">
                  <Label htmlFor={`item-${item.id}-desc`} className="sr-only">Description</Label>
                  <Input
                    id={`item-${item.id}-desc`}
                    placeholder="Item description"
                    value={item.description}
                    onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                  />
                </div>
                
                <div className="col-span-4 md:col-span-2">
                  <Label htmlFor={`item-${item.id}-qty`} className="sr-only">Quantity</Label>
                  <Input
                    id={`item-${item.id}-qty`}
                    type="number"
                    min="1"
                    placeholder="Qty"
                    value={item.quantity}
                    onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                  />
                </div>
                
                <div className="col-span-6 md:col-span-3">
                  <Label htmlFor={`item-${item.id}-price`} className="sr-only">Price</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2">₦</span>
                    <Input
                      id={`item-${item.id}-price`}
                      type="number"
                      min="0"
                      step="0.01"
                      className="pl-7"
                      placeholder="0.00"
                      value={item.price}
                      onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>
                
                <div className="col-span-2 md:col-span-1 text-right">
                  <span className="text-sm font-medium">
                    ₦{(item.quantity * item.price).toLocaleString('en-NG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </span>
                </div>
                
                <div className="col-span-12 md:col-span-1 flex justify-end">
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="icon"
                    onClick={() => removeItem(item.id)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="flex justify-end mt-4">
            <div className="w-full max-w-xs space-y-2">
              <div className="flex justify-between border-t pt-2">
                <span className="font-medium">Total:</span>
                <span className="font-bold">
                  ₦{calculateTotal().toLocaleString('en-NG', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mb-6">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            placeholder="Additional notes for customer"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={4}
            className="mt-1"
          />
        </div>
        
        <div className="flex justify-end">
          <Button type="submit" className="flex items-center">
            <Send className="h-4 w-4 mr-2" />
            Create & Send Invoice
          </Button>
        </div>
      </form>
    </div>
  );
};

export default InvoiceForm;
