
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Users,
  Search,
  Filter,
  Download,
  UserPlus,
  CheckCircle2,
  XCircle,
  AlertCircle,
  MoreHorizontal,
  Eye,
  Edit,
  Lock,
  UserX,
  ArrowUpDown,
  Check,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';

// Mock data for demo purposes
const usersMockData = [
  {
    id: '001',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'personal',
    status: 'active',
    balance: '₦245,780',
    joinDate: '2023-01-12',
    kycStatus: 'verified'
  },
  {
    id: '002',
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'personal',
    status: 'active',
    balance: '₦125,340',
    joinDate: '2023-02-24',
    kycStatus: 'pending'
  },
  {
    id: '003',
    name: 'Acme Corporation',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'business',
    status: 'active',
    balance: '₦1,245,650',
    joinDate: '2023-01-05',
    kycStatus: 'verified'
  },
  {
    id: '004',
    name: 'Michael Johnson',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'personal',
    status: 'suspended',
    balance: '₦78,900',
    joinDate: '2023-03-18',
    kycStatus: 'verified'
  },
  {
    id: '005',
    name: 'Global Solutions Ltd',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'business',
    status: 'active',
    balance: '₦3,567,890',
    joinDate: '2022-11-30',
    kycStatus: 'verified'
  },
  {
    id: '006',
    name: 'Sarah Williams',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'personal',
    status: 'inactive',
    balance: '₦12,450',
    joinDate: '2023-05-02',
    kycStatus: 'rejected'
  },
  {
    id: '007',
    name: 'Tech Innovations',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'business',
    status: 'active',
    balance: '₦879,320',
    joinDate: '2023-02-14',
    kycStatus: 'verified'
  },
  {
    id: '008',
    name: 'Robert Brown',
    email: '<EMAIL>',
    phone: '+234 **********',
    accountType: 'personal',
    status: 'active',
    balance: '₦56,780',
    joinDate: '2023-04-01',
    kycStatus: 'verified'
  },
];

const UserManagement = () => {
  const [selectedTab, setSelectedTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;
  
  // Filter users based on the active tab, search query, and status
  const filteredUsers = usersMockData.filter(user => {
    const matchesTab = selectedTab === 'all' || 
                       (selectedTab === 'personal' && user.accountType === 'personal') ||
                       (selectedTab === 'business' && user.accountType === 'business');
    
    const matchesSearch = user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          user.phone.includes(searchQuery);
    
    const matchesStatus = selectedStatus === 'all' || user.status === selectedStatus;
    
    return matchesTab && matchesSearch && matchesStatus;
  });

  // Pagination
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage);
  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleStatusChange = (status: string) => {
    if (selectedUser) {
      toast.success(`User ${selectedUser.name}'s status changed to ${status}`);
      setIsDialogOpen(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>User Management | KojaPay Admin</title>
      </Helmet>
      <AdminLayout pageTitle="User Management">
        <div className="space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
              <p className="text-gray-600">Manage and monitor all user accounts</p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" className="flex items-center gap-2">
                <Download size={16} />
                Export
              </Button>
              <Button className="flex items-center gap-2 bg-[#1231B8]">
                <UserPlus size={16} />
                Add User
              </Button>
            </div>
          </div>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle>User Accounts</CardTitle>
              <CardDescription>
                View and manage all user accounts on the platform
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 mb-6 justify-between">
                <Tabs 
                  value={selectedTab} 
                  onValueChange={setSelectedTab}
                  className="w-full max-w-md"
                >
                  <TabsList className="grid grid-cols-3 w-full">
                    <TabsTrigger value="all">All Users</TabsTrigger>
                    <TabsTrigger value="personal">Personal</TabsTrigger>
                    <TabsTrigger value="business">Business</TabsTrigger>
                  </TabsList>
                </Tabs>
                
                <div className="flex gap-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search users..."
                      className="pl-10 pr-4 w-full"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  
                  <Select 
                    value={selectedStatus} 
                    onValueChange={setSelectedStatus}
                  >
                    <SelectTrigger className="w-[140px]">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button variant="outline" size="icon">
                    <Filter size={16} />
                  </Button>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">ID</TableHead>
                      <TableHead className="min-w-[180px]">
                        <div className="flex items-center gap-1">
                          User
                          <ArrowUpDown size={14} className="text-gray-400" />
                        </div>
                      </TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>KYC Status</TableHead>
                      <TableHead>Balance</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Join Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.id}</TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-500">{user.email}</p>
                            <p className="text-xs text-gray-400">{user.phone}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={user.accountType === 'personal' ? 'outline' : 'default'}>
                            {user.accountType === 'personal' ? 'Personal' : 'Business'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {user.kycStatus === 'verified' && (
                            <div className="flex items-center gap-1 text-green-600">
                              <CheckCircle2 className="h-4 w-4" />
                              <span>Verified</span>
                            </div>
                          )}
                          {user.kycStatus === 'pending' && (
                            <div className="flex items-center gap-1 text-yellow-600">
                              <AlertCircle className="h-4 w-4" />
                              <span>Pending</span>
                            </div>
                          )}
                          {user.kycStatus === 'rejected' && (
                            <div className="flex items-center gap-1 text-red-600">
                              <XCircle className="h-4 w-4" />
                              <span>Rejected</span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>{user.balance}</TableCell>
                        <TableCell>
                          <Badge 
                            variant={
                              user.status === 'active' ? 'default' : 
                              user.status === 'inactive' ? 'outline' : 
                              'destructive'
                            }
                          >
                            {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>{new Date(user.joinDate).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => {
                                setSelectedUser(user);
                                setIsDialogOpen(true);
                              }}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                Edit User
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Lock className="h-4 w-4 mr-2" />
                                Reset Password
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-red-600">
                                <UserX className="h-4 w-4 mr-2" />
                                Suspend Account
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                    
                    {paginatedUsers.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={8} className="text-center py-8">
                          <div className="flex flex-col items-center justify-center text-gray-500">
                            <Users className="h-12 w-12 text-gray-300 mb-2" />
                            <p>No users found</p>
                            <p className="text-sm">Try adjusting your search or filters</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
              
              {/* Pagination */}
              {filteredUsers.length > 0 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-500">
                    Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, filteredUsers.length)} of {filteredUsers.length} users
                  </div>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                      <Button
                        key={page}
                        variant={page === currentPage ? 'default' : 'outline'}
                        size="icon"
                        onClick={() => handlePageChange(page)}
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
      
      {/* User Details Dialog */}
      {selectedUser && (
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>User Details</DialogTitle>
              <DialogDescription>
                Complete information about the selected user
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">User ID</p>
                  <p className="font-medium">{selectedUser.id}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Full Name</p>
                  <p className="font-medium">{selectedUser.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Email Address</p>
                  <p className="font-medium">{selectedUser.email}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Phone Number</p>
                  <p className="font-medium">{selectedUser.phone}</p>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">Account Type</p>
                  <Badge variant={selectedUser.accountType === 'personal' ? 'outline' : 'default'}>
                    {selectedUser.accountType === 'personal' ? 'Personal' : 'Business'}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm text-gray-500">KYC Status</p>
                  <div className="flex items-center gap-1 mt-1">
                    {selectedUser.kycStatus === 'verified' && (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        Verified
                      </Badge>
                    )}
                    {selectedUser.kycStatus === 'pending' && (
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                    )}
                    {selectedUser.kycStatus === 'rejected' && (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                        <XCircle className="h-3 w-3 mr-1" />
                        Rejected
                      </Badge>
                    )}
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Account Balance</p>
                  <p className="font-medium">{selectedUser.balance}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500">Join Date</p>
                  <p className="font-medium">{new Date(selectedUser.joinDate).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
            
            <div className="p-4 border rounded-lg bg-gray-50">
              <h3 className="font-medium mb-2">Account Status</h3>
              <div className="flex flex-wrap gap-2">
                <Button 
                  size="sm"
                  variant={selectedUser.status === 'active' ? 'default' : 'outline'}
                  className={selectedUser.status === 'active' ? 'bg-green-500' : ''}
                  onClick={() => handleStatusChange('active')}
                >
                  <Check className="h-4 w-4 mr-1" />
                  Active
                </Button>
                <Button 
                  size="sm"
                  variant={selectedUser.status === 'inactive' ? 'default' : 'outline'}
                  className={selectedUser.status === 'inactive' ? 'bg-gray-500' : ''}
                  onClick={() => handleStatusChange('inactive')}
                >
                  <Check className="h-4 w-4 mr-1" />
                  Inactive
                </Button>
                <Button 
                  size="sm"
                  variant={selectedUser.status === 'suspended' ? 'default' : 'outline'}
                  className={selectedUser.status === 'suspended' ? 'bg-red-500' : ''}
                  onClick={() => handleStatusChange('suspended')}
                >
                  <Check className="h-4 w-4 mr-1" />
                  Suspended
                </Button>
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Close
              </Button>
              <Button>
                <Edit className="h-4 w-4 mr-2" />
                Edit User
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default UserManagement;
