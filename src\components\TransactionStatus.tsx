
import React from 'react';
import { CheckCircle, AlertCircle, Clock, Ban, Activity, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';

export type TransactionStatusType = 
  | 'success' 
  | 'failed' 
  | 'processing' 
  | 'cancelled'
  | 'idle'
  | 'pending'; // Added 'pending' to support existing code

export interface TransactionStatusProps {
  status: TransactionStatusType;
  className?: string;
  message?: string; // Added for informational messages
  progress?: number; // Added for progress tracking
  compact?: boolean; // Added for compact display
  errorMessage?: string; // Added for custom error messages
  onClose?: () => void; // Added for dismissible status indicators
}

// Helper function to normalize transaction status
export const normalizeTransactionStatus = (status: string): TransactionStatusType => {
  status = status.toLowerCase();
  
  if (['completed', 'success', 'successful', 'released'].includes(status)) {
    return 'success';
  } else if (['failed', 'rejected', 'declined', 'refunded'].includes(status)) {
    return 'failed';
  } else if (['processing', 'pending', 'active', 'in_progress', 'awaiting', 'disputed'].includes(status)) {
    return 'processing';
  } else if (['cancelled', 'revoked'].includes(status)) {
    return 'cancelled';
  }
  
  return 'idle';
};

const TransactionStatus: React.FC<TransactionStatusProps> = ({
  status,
  className,
  message,
  progress,
  compact = false,
  errorMessage,
  onClose
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          icon: CheckCircle,
          text: 'Successful',
          bgColor: 'bg-green-100',
          textColor: 'text-green-700',
          iconColor: 'text-green-500'
        };
      case 'failed':
        return {
          icon: AlertCircle,
          text: 'Failed',
          bgColor: 'bg-red-100',
          textColor: 'text-red-700',
          iconColor: 'text-red-500'
        };
      case 'processing':
      case 'pending':
        return {
          icon: Clock,
          text: status === 'processing' ? 'Processing' : 'Pending',
          bgColor: 'bg-blue-100',
          textColor: 'text-blue-700',
          iconColor: 'text-blue-500'
        };
      case 'cancelled':
        return {
          icon: Ban,
          text: 'Cancelled',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-500'
        };
      default:
        return {
          icon: Activity,
          text: 'Idle',
          bgColor: 'bg-gray-100',
          textColor: 'text-gray-700',
          iconColor: 'text-gray-500'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  if (compact) {
    return (
      <div 
        className={cn(
          'inline-flex items-center px-2 py-0.5 rounded-full text-xs',
          config.bgColor,
          config.textColor,
          className
        )}
      >
        <Icon className={cn('w-3 h-3 mr-1', config.iconColor)} />
        <span>{config.text}</span>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div 
        className={cn(
          'flex items-center justify-between px-3 py-2 rounded-md',
          config.bgColor,
          config.textColor
        )}
      >
        <div className="flex items-center">
          <Icon className={cn('w-5 h-5 mr-2', config.iconColor)} />
          <div>
            <span className="font-medium">{config.text}</span>
            {message && <p className="text-sm">{message}</p>}
            {errorMessage && status === 'failed' && <p className="text-sm">{errorMessage}</p>}
          </div>
        </div>
        {onClose && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-8 w-8 p-0 rounded-full hover:bg-gray-200"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
      
      {progress !== undefined && (
        <div className="w-full">
          <Progress value={progress} className="h-1" />
        </div>
      )}
    </div>
  );
};

export default TransactionStatus;
