
import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Printer, Share2 } from 'lucide-react';
import TransactionStatus from './TransactionStatus';
import { format } from 'date-fns';
import { ReceiptData, ReceiptItem } from '@/utils/receipt-generator';
import { printPOSReceipt } from '@/utils/receipt-generator';

interface POSReceiptProps {
  receiptData: ReceiptData;
  onClose: () => void;
  onPrint: () => void;
  onDownload: () => void;
}

const POSReceipt: React.FC<POSReceiptProps> = ({
  receiptData,
  onClose,
  onPrint,
  onDownload
}) => {
  return (
    <Card className="receipt-paper max-w-sm">
      <CardHeader className="border-b">
        <div className="flex justify-center mb-2">
          <img 
            src="/lovable-uploads/75ec110c-bfb2-41d3-8599-2425175ac9cb.png" 
            alt="KojaPay Logo" 
            className="h-14 w-14"
          />
        </div>
        <CardTitle className="text-center">Payment Receipt</CardTitle>
      </CardHeader>
      
      <CardContent className="p-4">
        <div className="text-center mb-4">
          <h3 className="font-bold text-xl">KojaPay</h3>
          <p className="text-sm text-gray-500">{receiptData.merchantAddress}</p>
        </div>
        
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Transaction ID:</span>
            <span>{receiptData.id}</span>
          </div>
          <div className="flex justify-between">
            <span>Date:</span>
            <span>{receiptData.transactionDate}</span>
          </div>
          <div className="flex justify-between">
            <span>Time:</span>
            <span>{receiptData.transactionTime}</span>
          </div>
          <div className="flex justify-between">
            <span>Payment Method:</span>
            <span>{receiptData.paymentMethod}</span>
          </div>
        </div>
        
        <div className="my-4 border-t border-b border-dashed py-3">
          <p className="font-semibold mb-2">Items:</p>
          {receiptData.items && receiptData.items.map((item: ReceiptItem, index: number) => (
            <div key={index} className="flex justify-between text-sm mb-1">
              <span>{item.name} x{item.quantity}</span>
              <span>₦{(item.price * item.quantity).toLocaleString()}</span>
            </div>
          ))}
        </div>
        
        <div className="space-y-1">
          <div className="flex justify-between text-sm">
            <span>Subtotal:</span>
            <span>₦{receiptData.subtotal?.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Tax (7.5%):</span>
            <span>₦{receiptData.tax?.toLocaleString()}</span>
          </div>
          <div className="flex justify-between font-bold mt-2">
            <span>Total:</span>
            <span>₦{receiptData.totalAmount.toLocaleString()}</span>
          </div>
        </div>
        
        <div className="mt-4 mb-2 flex justify-center">
          <TransactionStatus 
            status={receiptData.status as any} 
            message="Transaction Approved" 
            compact={true}
          />
        </div>
        
        <div className="text-center text-sm text-gray-500 mt-4 border-t border-dashed pt-3">
          <p>Thank you for your business!</p>
          <p className="mt-1">www.kojapay.com</p>
          <p className="mt-1 text-xs">Reference: {receiptData.reference}</p>
        </div>
        
        <div className="flex justify-between mt-6">
          <Button variant="outline" className="w-1/3" onClick={onClose}>
            Close
          </Button>
          <Button className="w-1/3 bg-blue-600 hover:bg-blue-700" onClick={onPrint}>
            <Printer size={16} className="mr-1" /> Print
          </Button>
          <Button className="w-1/3 bg-purple-600 hover:bg-purple-700" onClick={onDownload}>
            <Download size={16} className="mr-1" /> Download
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default POSReceipt;
