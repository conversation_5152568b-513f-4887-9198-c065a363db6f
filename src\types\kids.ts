
export interface CreateChildDTO {
  firstName: string;
  lastName: string;
  dob: string; // ISO format
}

export interface FirstLoginDTO {
  username: string;
  secretKey: string;
}

export interface SetupChildCredentialsDTO {
  username: string;
  password: string;
  pin: string;
}

export interface LoginDTO {
  username: string;
  password: string;
}

export interface ChildTransferDTO {
  fromChildId: string;
  toAccountId: string;
  amount: number;
  pin: string;
}

export interface Child {
  id: string;
  firstName: string;
  lastName: string;
  username: string;
  dob: string;
  parentId: string;
  role: 'child';
  balance: number;
  isSetup: boolean;
  createdAt: Date;
  age: number;
}

export interface ChildCredentials {
  username: string;
  secretKey: string;
}

export interface ChildTransaction {
  id: string;
  childId: string;
  type: 'transfer' | 'withdrawal' | 'card_payment' | 'received_transfer';
  amount: number;
  description: string;
  timestamp: Date;
  status: 'completed' | 'pending' | 'failed';
}
