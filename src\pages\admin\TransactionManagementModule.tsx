import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  FileText, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  AlertTriangle, 
  CheckCircle,
  XCircle,
  Calendar,
  ArrowUpDown,
  ArrowDownUp
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const TransactionManagementModule = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  const handleTransactionAction = (action: string, txId: string) => {
    toast({
      title: `${action} Transaction`,
      description: `Successfully ${action.toLowerCase()}ed transaction ${txId}`,
    });
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // Mock data
  const transactions = [
    { 
      id: 'TX001', 
      user: 'John Doe', 
      userType: 'personal',
      type: 'transfer', 
      amount: '₦25,000', 
      status: 'completed', 
      date: '2023-08-15 14:30',
      recipient: 'Sarah Williams' 
    },
    { 
      id: 'TX002', 
      user: 'Acme Corp', 
      userType: 'business',
      type: 'payment', 
      amount: '₦125,000', 
      status: 'pending', 
      date: '2023-08-15 10:15',
      recipient: 'TechSolutions LLC' 
    },
    { 
      id: 'TX003', 
      user: 'Jane Smith', 
      userType: 'personal',
      type: 'withdrawal', 
      amount: '₦50,000', 
      status: 'completed', 
      date: '2023-08-14 16:45',
      recipient: 'Bank Account' 
    },
    { 
      id: 'TX004', 
      user: 'Global Enterprises', 
      userType: 'business',
      type: 'deposit', 
      amount: '₦500,000', 
      status: 'completed', 
      date: '2023-08-14 11:20',
      recipient: 'Account Wallet' 
    },
    { 
      id: 'TX005', 
      user: 'Michael Johnson', 
      userType: 'personal',
      type: 'transfer', 
      amount: '₦15,000', 
      status: 'failed', 
      date: '2023-08-13 09:30',
      recipient: 'Local Shop' 
    },
    { 
      id: 'TX006', 
      user: 'TechSolutions LLC', 
      userType: 'business',
      type: 'payment', 
      amount: '₦75,000', 
      status: 'disputed', 
      date: '2023-08-12 13:45',
      recipient: 'Global Enterprises' 
    },
  ];

  const renderStatusBadge = (status: string) => {
    let color = '';
    let icon = null;
    
    switch(status) {
      case 'completed':
        color = 'bg-green-100 text-green-800';
        icon = <CheckCircle className="h-3 w-3 mr-1" />;
        break;
      case 'pending':
        color = 'bg-yellow-100 text-yellow-800';
        icon = <Calendar className="h-3 w-3 mr-1" />;
        break;
      case 'failed':
        color = 'bg-red-100 text-red-800';
        icon = <XCircle className="h-3 w-3 mr-1" />;
        break;
      case 'disputed':
        color = 'bg-orange-100 text-orange-800';
        icon = <AlertTriangle className="h-3 w-3 mr-1" />;
        break;
      default:
        color = 'bg-blue-100 text-blue-800';
    }
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
        {icon}
        {status}
      </span>
    );
  };

  const renderTransactionTypeIcon = (type: string) => {
    switch(type) {
      case 'transfer':
        return <ArrowUpDown className="h-4 w-4 text-blue-500" />;
      case 'payment':
        return <ArrowDownUp className="h-4 w-4 text-green-500" />;
      case 'withdrawal':
        return <ArrowUpDown className="h-4 w-4 text-orange-500" />;
      case 'deposit':
        return <ArrowDownUp className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const filteredTransactions = transactions.filter(tx => {
    if (activeTab !== 'all' && activeTab !== tx.status) {
      return false;
    }
    
    if (searchQuery) {
      return tx.id.toLowerCase().includes(searchQuery.toLowerCase()) || 
             tx.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
             tx.recipient.toLowerCase().includes(searchQuery.toLowerCase());
    }
    
    return true;
  });

  return (
    <>
      <Helmet>
        <title>Transaction Management | KojaPay Admin Portal</title>
      </Helmet>
      <AdminLayout pageTitle="Transaction Management">
        <div className="space-y-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold">Transaction Management</h1>
              <p className="text-gray-600">Monitor and manage all platform transactions</p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search transactions..." 
                  className="pl-10"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>
              
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Total Transactions</p>
                  <p className="text-2xl font-bold">₦862,500</p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Completed</p>
                  <p className="text-2xl font-bold">₦575,000</p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Pending</p>
                  <p className="text-2xl font-bold">₦125,000</p>
                </div>
                <div className="p-3 rounded-full bg-yellow-100">
                  <Calendar className="h-6 w-6 text-yellow-600" />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Disputed</p>
                  <p className="text-2xl font-bold">₦75,000</p>
                </div>
                <div className="p-3 rounded-full bg-orange-100">
                  <AlertTriangle className="h-6 w-6 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-6">
              <TabsTrigger value="all">All Transactions</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="pending">Pending</TabsTrigger>
              <TabsTrigger value="failed">Failed</TabsTrigger>
              <TabsTrigger value="disputed">Disputed</TabsTrigger>
            </TabsList>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Transaction Records</CardTitle>
                <CardDescription>
                  {filteredTransactions.length} transactions found
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Transaction ID</th>
                        <th className="text-left py-3 px-4">User</th>
                        <th className="text-left py-3 px-4">Type</th>
                        <th className="text-left py-3 px-4">Amount</th>
                        <th className="text-left py-3 px-4">Recipient</th>
                        <th className="text-left py-3 px-4">Date</th>
                        <th className="text-left py-3 px-4">Status</th>
                        <th className="text-right py-3 px-4">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredTransactions.map(tx => (
                        <tr key={tx.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium">{tx.id}</td>
                          <td className="py-3 px-4">
                            <div className="flex flex-col">
                              <span>{tx.user}</span>
                              <span className="text-xs text-gray-500 capitalize">{tx.userType}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              {renderTransactionTypeIcon(tx.type)}
                              <span className="ml-2 capitalize">{tx.type}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4 font-medium">{tx.amount}</td>
                          <td className="py-3 px-4">{tx.recipient}</td>
                          <td className="py-3 px-4 text-sm">{tx.date}</td>
                          <td className="py-3 px-4">{renderStatusBadge(tx.status)}</td>
                          <td className="py-3 px-4 text-right">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8 text-blue-600"
                              onClick={() => handleTransactionAction('View', tx.id)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {tx.status === 'disputed' && (
                              <Button 
                                variant="ghost" 
                                size="icon" 
                                className="h-8 w-8 text-orange-600"
                                onClick={() => handleTransactionAction('Resolve', tx.id)}
                              >
                                <AlertTriangle className="h-4 w-4" />
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                  
                  {filteredTransactions.length === 0 && (
                    <div className="text-center py-10">
                      <p className="text-gray-500">No transactions found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </Tabs>
        </div>
      </AdminLayout>
    </>
  );
};

export default TransactionManagementModule;
