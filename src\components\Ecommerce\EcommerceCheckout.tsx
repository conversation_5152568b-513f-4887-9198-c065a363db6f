
import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  Di<PERSON><PERSON>ontent,
  <PERSON><PERSON>Header, 
  DialogTitle, 
  DialogDescription, 
  DialogFooter,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { ShieldCheck, ArrowRight, CheckCircle2, Package, Truck, Banknote, User, AlertCircle, Lock } from 'lucide-react';
import { motion } from 'framer-motion';
import { useToast } from '@/hooks/use-toast';
import { EnhancedCard } from '@/components/ui/enhanced-card';
import { Product, ecommerceService } from '@/services/ecommerceService';
import { Escrow } from '@/types/escrow';

type CheckoutStage = 'cart' | 'shipping' | 'payment' | 'processing' | 'complete' | 'failed';

interface EcommerceCheckoutProps {
  children: React.ReactNode;
  products: Product[];
  onCheckoutComplete?: (order: Record<string, any>) => void;
  onCheckoutCancel?: () => void;
  customerId: string;
  customerName: string;
}

const EcommerceCheckout: React.FC<EcommerceCheckoutProps> = ({ 
  children, 
  products,
  onCheckoutComplete,
  onCheckoutCancel,
  customerId,
  customerName
}) => {
  const [open, setOpen] = useState(false);
  const [stage, setStage] = useState<CheckoutStage>('cart');
  const [progress, setProgress] = useState(0);
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [shippingAddress, setShippingAddress] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [useEscrow, setUseEscrow] = useState(true);
  const [paymentMethod, setPaymentMethod] = useState('wallet');
  const [order, setOrder] = useState<Record<string, any>>(null);
  const [escrow, setEscrow] = useState<Escrow | null>(null);
  const { toast } = useToast();

  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      setTimeout(() => {
        setStage('cart');
        setProgress(0);
        setAgreedToTerms(false);
        setShippingAddress('');
        setPhoneNumber('');
        setOrder(null);
        setEscrow(null);
      }, 300);
    }
    setOpen(isOpen);
  };

  const subtotal = products.reduce((sum, product) => sum + product.price, 0);
  const escrowFee = useEscrow ? subtotal * 0.02 : 0;
  const shippingFee = 1500;
  const totalAmount = subtotal + escrowFee + shippingFee;

  const handleProceedToShipping = () => {
    setStage('shipping');
  };

  const handleProceedToPayment = () => {
    if (!shippingAddress || !phoneNumber) {
      toast({
        title: "Required Fields Missing",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }
    setStage('payment');
  };

  const handleStartPayment = async () => {
    if (!agreedToTerms) {
      toast({
        title: "Terms Agreement Required",
        description: "Please agree to the terms and conditions.",
        variant: "destructive",
      });
      return;
    }

    setStage('processing');
    setProgress(0);
    
    try {
      const orderData = {
        customerId,
        customerName,
        products: products.map(product => ({
          productId: String(product.id), // Convert ID to string to match expected type
          quantity: 1
        })),
        shippingAddress
      };

      const result = await ecommerceService.createOrder(orderData);
      
      if ('order' in result && 'escrow' in result) {
        setOrder(result.order);
        setEscrow(result.escrow);
      
        const interval = setInterval(() => {
          setProgress((prev) => {
            const newProgress = prev + 5;
            if (newProgress >= 100) {
              clearInterval(interval);
              setTimeout(() => {
                if (Math.random() > 0.1) {
                  setStage('complete');
                  if (result.order && result.order.id) {
                    ecommerceService.updatePaymentStatus(result.order.id, 'completed');
                  }
                } else {
                  setStage('failed');
                }
              }, 500);
            }
            return Math.min(newProgress, 100);
          });
        }, 200);
      } else {
        throw new Error('Failed to create order');
      }
    } catch (error) {
      console.error('Error creating order:', error);
      toast({
        title: "Checkout Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
      setStage('failed');
    }
  };

  const handleClose = () => {
    if (stage === 'complete' && onCheckoutComplete && order) {
      onCheckoutComplete(order);
    } else if (onCheckoutCancel) {
      onCheckoutCancel();
    }
    handleOpenChange(false);
  };

  const handleRetry = () => {
    setStage('payment');
    setProgress(0);
  };

  const renderContent = () => {
    switch (stage) {
      case 'cart':
        return (
          <div className="space-y-4">
            <div className="max-h-[300px] overflow-y-auto space-y-3 pr-2">
              {products.map((product) => (
                <Card key={product.id} className="border border-kojaPrimary/20">
                  <CardContent className="p-3 flex items-center space-x-3">
                    <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
                      <img src={product.image} alt={product.name} className="w-full h-full object-cover" />
                    </div>
                    <div className="flex-grow">
                      <div className="font-medium">{product.name}</div>
                      <div className="text-sm text-muted-foreground">{product.storeName}</div>
                      <div className="font-bold mt-1">₦{product.price.toLocaleString()}</div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            
            <Separator />
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>₦{subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Shipping</span>
                <span>₦{shippingFee.toLocaleString()}</span>
              </div>
              <div className="flex items-center space-x-2 mb-2">
                <Checkbox 
                  id="use-escrow" 
                  checked={useEscrow}
                  onCheckedChange={(checked) => {
                    setUseEscrow(checked === true);
                  }}
                />
                <label 
                  htmlFor="use-escrow" 
                  className="text-sm font-medium leading-none flex items-center"
                >
                  <ShieldCheck className="h-4 w-4 text-kojaPrimary mr-1" />
                  Use Escrow Protection (2% fee)
                </label>
              </div>
              {useEscrow && (
                <div className="flex justify-between text-kojaPrimary">
                  <span>Escrow Fee (2%)</span>
                  <span>₦{escrowFee.toLocaleString()}</span>
                </div>
              )}
            </div>
            
            <Separator />
            
            <div className="flex justify-between items-center font-bold">
              <div>Total:</div>
              <div>₦{totalAmount.toLocaleString()}</div>
            </div>
            
            <Button 
              className="w-full bg-kojaPrimary hover:bg-kojaPrimary/90"
              onClick={handleProceedToShipping}
            >
              Proceed to Shipping
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            
            {useEscrow && (
              <div className="bg-gray-50 p-3 rounded-lg border border-kojaPrimary/10">
                <div className="flex items-start gap-2">
                  <ShieldCheck className="h-5 w-5 text-kojaPrimary mt-0.5" />
                  <p className="text-xs text-muted-foreground">
                    Our escrow service holds your payment until you confirm receipt of the items in good condition. This protects both buyers and sellers.
                  </p>
                </div>
              </div>
            )}
          </div>
        );
        
      case 'shipping':
        return (
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="shipping-address">Shipping Address <span className="text-red-500">*</span></Label>
                <Input 
                  id="shipping-address" 
                  placeholder="Enter your complete shipping address"
                  value={shippingAddress}
                  onChange={(e) => setShippingAddress(e.target.value)}
                  className="bg-white"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone-number">Phone Number <span className="text-red-500">*</span></Label>
                <Input 
                  id="phone-number" 
                  placeholder="Enter your phone number"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="bg-white"
                  required
                />
              </div>
            </div>
            
            <div className="pt-4 flex gap-3">
              <Button 
                variant="outline"
                className="flex-1"
                onClick={() => setStage('cart')}
              >
                Back
              </Button>
              <Button 
                className="flex-1 bg-kojaPrimary hover:bg-kojaPrimary/90"
                onClick={handleProceedToPayment}
              >
                Continue to Payment
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        );
        
      case 'payment':
        return (
          <div className="space-y-4">
            <EnhancedCard variant="gradient" animation="none" className="p-4">
              <div className="space-y-3">
                <div className="text-center">
                  <h3 className="font-medium text-lg mb-1">Payment Details</h3>
                  <p className="text-sm text-muted-foreground">Complete your purchase securely</p>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Items:</div>
                  <div className="font-medium">{products.length} product(s)</div>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm text-muted-foreground">Shipping:</div>
                  <div>₦{shippingFee.toLocaleString()}</div>
                </div>
                
                {useEscrow && (
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-muted-foreground">Escrow Fee:</div>
                    <div>₦{escrowFee.toLocaleString()}</div>
                  </div>
                )}
                
                <Separator />
                
                <div className="flex justify-between items-center font-bold">
                  <div>Total Amount:</div>
                  <div>₦{totalAmount.toLocaleString()}</div>
                </div>
              </div>
            </EnhancedCard>
            
            <div className="space-y-3">
              <h4 className="font-medium">Payment Method</h4>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="wallet" 
                    name="payment-method"
                    value="wallet"
                    checked={paymentMethod === 'wallet'}
                    onChange={() => setPaymentMethod('wallet')}
                    className="text-kojaPrimary"
                  />
                  <label htmlFor="wallet" className="text-sm font-medium">KojaPay Wallet</label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <input 
                    type="radio" 
                    id="card" 
                    name="payment-method"
                    value="card"
                    checked={paymentMethod === 'card'}
                    onChange={() => setPaymentMethod('card')}
                    className="text-kojaPrimary"
                  />
                  <label htmlFor="card" className="text-sm font-medium">Credit/Debit Card</label>
                </div>
              </div>
              
              <div className="flex items-center space-x-2 mt-4">
                <Checkbox 
                  id="terms" 
                  checked={agreedToTerms}
                  onCheckedChange={(checked) => {
                    setAgreedToTerms(checked === true);
                  }}
                />
                <label 
                  htmlFor="terms" 
                  className="text-sm font-medium leading-none"
                >
                  I agree to the <a href="/escrow/terms" className="text-kojaPrimary hover:underline" target="_blank">terms & conditions</a>
                </label>
              </div>
            </div>
            
            <div className="pt-4 flex gap-3">
              <Button 
                variant="outline"
                className="flex-1"
                onClick={() => setStage('shipping')}
              >
                Back
              </Button>
              <Button 
                className="flex-1 bg-kojaPrimary hover:bg-kojaPrimary/90"
                disabled={!agreedToTerms}
                onClick={handleStartPayment}
              >
                <Lock className="mr-2 h-4 w-4" />
                Pay Securely
              </Button>
            </div>
          </div>
        );
        
      case 'processing':
        return (
          <div className="py-6 space-y-6 text-center">
            <div className="w-16 h-16 mx-auto rounded-full bg-kojaPrimary/10 flex items-center justify-center">
              <Banknote className="h-8 w-8 text-kojaPrimary animate-pulse" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-1">Processing Payment</h3>
              <p className="text-sm text-muted-foreground">Please wait while we process your payment</p>
            </div>
            
            <div className="space-y-2">
              <Progress value={progress} className="h-2 w-full" />
              <p className="text-xs text-muted-foreground">{progress}% complete</p>
            </div>
            
            {useEscrow && (
              <div className="bg-gray-50 p-3 rounded-lg border border-kojaPrimary/10 mt-4">
                <div className="flex items-start gap-2">
                  <ShieldCheck className="h-5 w-5 text-kojaPrimary mt-0.5" />
                  <p className="text-xs text-muted-foreground text-left">
                    Your payment is being processed through our secure escrow system. Funds will be held safely until you confirm receipt of your items.
                  </p>
                </div>
              </div>
            )}
          </div>
        );
        
      case 'complete':
        return (
          <motion.div 
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="py-6 space-y-6 text-center"
          >
            <div className="w-16 h-16 mx-auto rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle2 className="h-8 w-8 text-green-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-1">Payment Successful!</h3>
              <p className="text-sm text-muted-foreground">Your order has been placed successfully</p>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg border text-left">
              <h4 className="font-medium mb-2">Order Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Order ID:</span>
                  <span className="font-medium">{order?.id || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Amount:</span>
                  <span className="font-medium">₦{totalAmount.toLocaleString()}</span>
                </div>
                {useEscrow && escrow && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Escrow ID:</span>
                    <span className="font-medium">{escrow.id}</span>
                  </div>
                )}
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                <Package className="h-4 w-4" />
                <span>Your order will be processed shortly</span>
              </div>
              
              {useEscrow && (
                <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
                  <ShieldCheck className="h-4 w-4 text-kojaPrimary" />
                  <span>Protected by KojaPay Escrow</span>
                </div>
              )}
            </div>
            
            <Button 
              className="bg-kojaPrimary hover:bg-kojaPrimary/90"
              onClick={handleClose}
            >
              Continue Shopping
            </Button>
          </motion.div>
        );
        
      case 'failed':
        return (
          <div className="py-6 space-y-6 text-center">
            <div className="w-16 h-16 mx-auto rounded-full bg-red-100 flex items-center justify-center">
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-1">Payment Failed</h3>
              <p className="text-sm text-muted-foreground">We couldn't process your payment</p>
            </div>
            
            <div className="bg-red-50 p-4 rounded-lg border border-red-200 text-left">
              <p className="text-sm text-red-800">
                There was an issue processing your payment. This could be due to insufficient funds, network issues, or a temporary system error.
              </p>
            </div>
            
            <div className="flex gap-3">
              <Button 
                variant="outline"
                className="flex-1"
                onClick={handleClose}
              >
                Cancel
              </Button>
              <Button 
                className="flex-1 bg-kojaPrimary hover:bg-kojaPrimary/90"
                onClick={handleRetry}
              >
                Try Again
              </Button>
            </div>
          </div>
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] p-0">
        <DialogHeader>
          <DialogTitle>
            {stage === 'cart' && 'Your Shopping Cart'}
            {stage === 'shipping' && 'Shipping Information'}
            {stage === 'payment' && 'Payment'}
            {stage === 'processing' && 'Processing'}
            {stage === 'complete' && 'Order Confirmed'}
            {stage === 'failed' && 'Payment Failed'}
          </DialogTitle>
          <DialogDescription>
            {stage === 'cart' && 'Review your items before checkout'}
            {stage === 'shipping' && 'Enter your shipping details'}
            {stage === 'payment' && 'Complete your purchase securely'}
            {stage === 'processing' && 'Please wait while we process your payment'}
            {stage === 'complete' && 'Your order has been placed successfully'}
            {stage === 'failed' && 'We encountered an issue with your payment'}
          </DialogDescription>
        </DialogHeader>
        
        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};

export default EcommerceCheckout;
