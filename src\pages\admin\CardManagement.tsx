import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  LockIcon, 
  UnlockIcon,
  CreditCard,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  X
} from 'lucide-react';

const CardManagement = () => {
  const cards = [
    { 
      id: 'VCD001', 
      cardType: 'Virtual Debit Card',
      cardNumber: '**** **** **** 4589',
      holder: '<PERSON>',
      expiryDate: '05/25',
      status: 'Active',
      issuedDate: '2023-01-15'
    },
    { 
      id: 'PCD002', 
      cardType: 'Physical Debit Card',
      cardNumber: '**** **** **** 7532',
      holder: 'Sarah Johnson',
      expiryDate: '07/24',
      status: 'Active',
      issuedDate: '2022-11-20'
    },
    { 
      id: 'VCD003', 
      cardType: 'Virtual Debit Card',
      cardNumber: '**** **** **** 1268',
      holder: 'Robert Wilson',
      expiryDate: '09/26',
      status: 'Blocked',
      issuedDate: '2023-02-05'
    },
    { 
      id: 'PCD004', 
      cardType: 'Physical Credit Card',
      cardNumber: '**** **** **** 9632',
      holder: 'TechSolutions Inc.',
      expiryDate: '11/25',
      status: 'Active',
      issuedDate: '2023-03-10'
    },
    { 
      id: 'VCD005', 
      cardType: 'Virtual Debit Card',
      cardNumber: '**** **** **** 5147',
      holder: 'Alice Cooper',
      expiryDate: '04/27',
      status: 'Pending',
      issuedDate: '2023-05-12'
    },
  ];

  return (
    <AdminLayout pageTitle="Card Management">
      <Helmet>
        <title>Card Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Cards</CardTitle>
              <CardDescription>Currently in use</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">243</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Pending</CardTitle>
              <CardDescription>Awaiting activation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">18</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Blocked</CardTitle>
              <CardDescription>Temporarily disabled</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">7</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Cards</CardTitle>
              <CardDescription>All cards in system</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">268</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Card Management</CardTitle>
              <CardDescription>Manage all debit and credit cards on the platform</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Issue New Card
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search by holder name or card number..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <CheckCircle size={12} />
                  <span>Active</span>
                  <X size={12} className="ml-1" />
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <CreditCard size={12} />
                  <span>Virtual Cards</span>
                  <X size={12} className="ml-1" />
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>List of cards on the platform</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Card ID</TableHead>
                    <TableHead>Card Type</TableHead>
                    <TableHead>Card Number</TableHead>
                    <TableHead>Card Holder</TableHead>
                    <TableHead>Expiry Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Issued Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {cards.map((card) => (
                    <TableRow key={card.id}>
                      <TableCell className="font-medium">{card.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <CreditCard size={16} className="text-blue-600" />
                          {card.cardType}
                        </div>
                      </TableCell>
                      <TableCell>{card.cardNumber}</TableCell>
                      <TableCell>{card.holder}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar size={16} />
                          {card.expiryDate}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          card.status === 'Active' ? 'outline' : 
                          card.status === 'Inactive' ? 'destructive' : 
                          'secondary'
                        }>
                          {card.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                          {card.status === 'Pending Activation' && <Clock size={12} className="mr-1" />}
                          {card.status === 'Inactive' && <XCircle size={12} className="mr-1" />}
                          {card.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{card.issuedDate}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            {card.status === 'Active' ? (
                              <DropdownMenuItem>
                                <LockIcon className="mr-2 h-4 w-4 text-amber-600" />
                                <span className="text-amber-600">Block Card</span>
                              </DropdownMenuItem>
                            ) : card.status === 'Blocked' ? (
                              <DropdownMenuItem>
                                <UnlockIcon className="mr-2 h-4 w-4 text-green-600" />
                                <span className="text-green-600">Unblock Card</span>
                              </DropdownMenuItem>
                            ) : null}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <AlertTriangle className="mr-2 h-4 w-4" />
                              <span>Report Fraud</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default CardManagement;
