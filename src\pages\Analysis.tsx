
import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import PersonalAnalysis from './analytics/PersonalAnalysis';
import BusinessAnalysis from './business/Analysis';
import AdminAnalytics from './admin/AdminAnalytics';

const Analysis = () => {
  const { accountType } = useAuth();

  // Render the appropriate analysis page based on account type
  if (accountType === 'business') {
    return <BusinessAnalysis />;
  } else if (accountType === 'admin') {
    return <AdminAnalytics />;
  }

  // Default to personal analysis
  return <PersonalAnalysis />;
};

export default Analysis;
