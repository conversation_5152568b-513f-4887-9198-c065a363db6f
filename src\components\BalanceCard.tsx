
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { BadgeDollarSign } from 'lucide-react';

interface BalanceCardProps {
  balance: string;
  accountNumber: string;
  currency?: string;
}

const BalanceCard: React.FC<BalanceCardProps> = ({ 
  balance, 
  accountNumber,
  currency = '₦' 
}) => {
  // Get a truncated account number with last 4 digits visible
  const getTruncatedAccountNumber = () => {
    if (!accountNumber) return '****';
    const last4 = accountNumber.slice(-4);
    return `**** **** **** ${last4}`;
  };

  return (
    <Card className="balance-card bg-white shadow-md hover:shadow-lg transition-shadow">
      <CardContent className="p-0 space-y-3">
        <div className="flex justify-between items-center">
          <p className="text-sm text-kojaGray">Current Balance</p>
          <BadgeDollarSign className="h-5 w-5 text-kojaPrimary" />
        </div>
        
        <div>
          <p className="text-2xl font-semibold text-kojaDark">{balance}</p>
          <p className="text-xs text-kojaGray">{getTruncatedAccountNumber()}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default BalanceCard;
