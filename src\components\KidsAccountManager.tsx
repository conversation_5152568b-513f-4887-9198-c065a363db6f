
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Users, 
  Plus, 
  Settings, 
  Eye, 
  DollarSign, 
  Shield,
  Calendar,
  User,
  TrendingUp,
  PiggyBank
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { Bar, Doughnut } from 'react-chartjs-2';
import AOS from 'aos';
import 'aos/dist/aos.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface KidsAccount {
  id: string;
  childName: string;
  dateOfBirth: string;
  balance: number;
  weeklyLimit: number;
  isActive: boolean;
  createdDate: string;
  weeklySpent: number;
  savingsGoal: number;
}

const KidsAccountManager = () => {
  const { toast } = useToast();
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [kidsAccounts, setKidsAccounts] = useState<KidsAccount[]>([
    {
      id: '1',
      childName: 'Alex Johnson',
      dateOfBirth: '2010-05-15',
      balance: 2500,
      weeklyLimit: 1000,
      isActive: true,
      createdDate: '2024-01-15',
      weeklySpent: 650,
      savingsGoal: 5000
    },
    {
      id: '2',
      childName: 'Emma Johnson',
      dateOfBirth: '2012-08-22',
      balance: 1200,
      weeklyLimit: 500,
      isActive: true,
      createdDate: '2024-02-10',
      weeklySpent: 250,
      savingsGoal: 2000
    }
  ]);

  const [newChild, setNewChild] = useState({
    childName: '',
    dateOfBirth: '',
    weeklyLimit: 500,
    savingsGoal: 1000
  });

  // Initialize AOS
  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: 'ease-out-cubic',
      once: true
    });
  }, []);

  const handleCreateKidsAccount = () => {
    if (!newChild.childName || !newChild.dateOfBirth) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const newAccount: KidsAccount = {
      id: Date.now().toString(),
      childName: newChild.childName,
      dateOfBirth: newChild.dateOfBirth,
      balance: 0,
      weeklyLimit: newChild.weeklyLimit,
      isActive: true,
      createdDate: new Date().toISOString().split('T')[0],
      weeklySpent: 0,
      savingsGoal: newChild.savingsGoal
    };

    setKidsAccounts([...kidsAccounts, newAccount]);
    setNewChild({ childName: '', dateOfBirth: '', weeklyLimit: 500, savingsGoal: 1000 });
    setShowCreateDialog(false);

    toast({
      title: 'Kids Account Created',
      description: `Account created for ${newChild.childName}`,
    });
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  // Chart data for spending overview
  const spendingChartData = {
    labels: kidsAccounts.map(account => account.childName),
    datasets: [
      {
        label: 'Weekly Spent',
        data: kidsAccounts.map(account => account.weeklySpent),
        backgroundColor: '#1231B8',
        borderRadius: 8,
      },
      {
        label: 'Weekly Limit',
        data: kidsAccounts.map(account => account.weeklyLimit),
        backgroundColor: '#FDE314',
        borderRadius: 8,
      },
    ],
  };

  // Doughnut chart for savings progress
  const savingsData = {
    labels: ['Saved', 'Remaining'],
    datasets: [
      {
        data: [
          kidsAccounts.reduce((sum, acc) => sum + acc.balance, 0),
          kidsAccounts.reduce((sum, acc) => sum + (acc.savingsGoal - acc.balance), 0)
        ],
        backgroundColor: ['#1231B8', '#FDE314'],
        borderWidth: 0,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between" data-aos="fade-down">
        <div className="flex items-center gap-3">
          <Users className="h-6 w-6 text-[#1231B8]" />
          <h2 className="text-2xl font-bold text-gray-900">Kids Accounts</h2>
        </div>
        
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="bg-[#1231B8] hover:bg-[#1231B8]/90 text-white rounded-xl">
              <Plus size={16} className="mr-2" />
              Add Child Account
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Create Kids Account</DialogTitle>
              <DialogDescription>
                Create a savings account for your child with parental controls.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="childName">Child's Full Name</Label>
                <Input
                  id="childName"
                  placeholder="Enter child's name"
                  value={newChild.childName}
                  onChange={(e) => setNewChild({...newChild, childName: e.target.value})}
                  className="rounded-xl"
                />
              </div>
              
              <div>
                <Label htmlFor="dateOfBirth">Date of Birth</Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={newChild.dateOfBirth}
                  onChange={(e) => setNewChild({...newChild, dateOfBirth: e.target.value})}
                  className="rounded-xl"
                />
              </div>
              
              <div>
                <Label htmlFor="weeklyLimit">Weekly Spending Limit (₦)</Label>
                <Input
                  id="weeklyLimit"
                  type="number"
                  placeholder="500"
                  value={newChild.weeklyLimit}
                  onChange={(e) => setNewChild({...newChild, weeklyLimit: Number(e.target.value)})}
                  className="rounded-xl"
                />
              </div>

              <div>
                <Label htmlFor="savingsGoal">Savings Goal (₦)</Label>
                <Input
                  id="savingsGoal"
                  type="number"
                  placeholder="1000"
                  value={newChild.savingsGoal}
                  onChange={(e) => setNewChild({...newChild, savingsGoal: Number(e.target.value)})}
                  className="rounded-xl"
                />
              </div>
              
              <Button 
                onClick={handleCreateKidsAccount}
                className="w-full bg-[#1231B8] hover:bg-[#1231B8]/90 rounded-xl"
              >
                Create Account
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Analytics Cards */}
      <div className="grid gap-4 md:grid-cols-3" data-aos="fade-up">
        <Card className="rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Savings</p>
                <p className="text-2xl font-bold">
                  ₦{kidsAccounts.reduce((sum, acc) => sum + acc.balance, 0).toLocaleString()}
                </p>
              </div>
              <PiggyBank className="h-10 w-10 text-[#1231B8]" />
            </div>
          </CardContent>
        </Card>

        <Card className="rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Accounts</p>
                <p className="text-2xl font-bold">{kidsAccounts.filter(acc => acc.isActive).length}</p>
              </div>
              <Users className="h-10 w-10 text-[#FDE314]" />
            </div>
          </CardContent>
        </Card>

        <Card className="rounded-xl">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Weekly Budget</p>
                <p className="text-2xl font-bold">
                  ₦{kidsAccounts.reduce((sum, acc) => sum + acc.weeklyLimit, 0).toLocaleString()}
                </p>
              </div>
              <TrendingUp className="h-10 w-10 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2" data-aos="fade-up" data-aos-delay="200">
        <Card className="rounded-xl">
          <CardHeader>
            <CardTitle>Spending Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <Bar data={spendingChartData} options={chartOptions} />
          </CardContent>
        </Card>

        <Card className="rounded-xl">
          <CardHeader>
            <CardTitle>Total Savings Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <div className="w-64 h-64">
                <Doughnut data={savingsData} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Kids Accounts Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3" data-aos="fade-up" data-aos-delay="400">
        {kidsAccounts.map((account, index) => (
          <Card key={account.id} className="hover:shadow-lg transition-shadow rounded-xl" data-aos="zoom-in" data-aos-delay={index * 100}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <User size={18} className="text-[#1231B8]" />
                  <span className="text-lg">{account.childName}</span>
                </div>
                <span className="text-sm text-gray-500">
                  {calculateAge(account.dateOfBirth)} years
                </span>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Balance</span>
                <span className="font-semibold text-lg">₦{account.balance.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Weekly Limit</span>
                <span className="text-sm font-medium">₦{account.weeklyLimit.toLocaleString()}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Weekly Spent</span>
                <span className="text-sm font-medium text-red-600">₦{account.weeklySpent.toLocaleString()}</span>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Savings Goal</span>
                  <span className="font-medium">₦{account.savingsGoal.toLocaleString()}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-[#1231B8] h-2 rounded-full" 
                    style={{ width: `${Math.min((account.balance / account.savingsGoal) * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  account.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {account.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>
              
              <div className="flex gap-2 mt-4">
                <Button size="sm" variant="outline" className="flex-1 rounded-xl">
                  <Eye size={14} className="mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1 rounded-xl">
                  <DollarSign size={14} className="mr-1" />
                  Fund
                </Button>
                <Button size="sm" variant="outline" className="flex-1 rounded-xl">
                  <Settings size={14} className="mr-1" />
                  Manage
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {kidsAccounts.length === 0 && (
        <Card className="p-8 text-center rounded-xl" data-aos="fade-up">
          <Users size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Kids Accounts Yet</h3>
          <p className="text-gray-600 mb-4">
            Create savings accounts for your children with parental controls and spending limits.
          </p>
          <Button 
            onClick={() => setShowCreateDialog(true)}
            className="bg-[#1231B8] hover:bg-[#1231B8]/90 rounded-xl"
          >
            <Plus size={16} className="mr-2" />
            Create First Kids Account
          </Button>
        </Card>
      )}

      <Card className="bg-blue-50 border-blue-200 rounded-xl" data-aos="fade-up" data-aos-delay="600">
        <CardContent className="p-6">
          <div className="flex items-start gap-3">
            <Shield className="h-5 w-5 text-blue-600 mt-1" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Parental Controls</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Set weekly spending limits and savings goals</li>
                <li>• Monitor all transactions in real-time</li>
                <li>• Approve or decline large purchases</li>
                <li>• Teach financial responsibility with visual progress</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default KidsAccountManager;
