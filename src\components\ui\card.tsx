
import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const cardVariants = cva(
  "rounded-[20px] text-card-foreground transition-all duration-300",
  {
    variants: {
      variant: {
        default: "bg-white/90 backdrop-blur-2xl border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        glass: "bg-white/60 backdrop-blur-2xl border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        gradient: "bg-gradient-to-br from-kojaPrimary/5 to-kojaPrimary/10 border border-[#D3E4FD] backdrop-blur-2xl shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        outline: "border border-[#D3E4FD] bg-transparent backdrop-blur-xl shadow-sm hover:shadow-md transform hover:translate-y-[-3px]",
        ghost: "border-none shadow-none bg-transparent",
        frosted: "bg-white/50 backdrop-blur-2xl border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        product: "bg-white/90 backdrop-blur-2xl border border-[#D3E4FD] shadow-md hover:shadow-xl overflow-hidden transform hover:translate-y-[-5px]",
        responsive: "bg-white/90 backdrop-blur-2xl border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        admin: "bg-white/90 backdrop-blur-2xl border border-[#D3E4FD] shadow-md hover:shadow-lg transform hover:translate-y-[-3px]",
        tier1: "bg-gradient-to-br from-gray-50 to-gray-100 border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        tier2: "bg-gradient-to-br from-gray-200 to-white border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
        tier3: "bg-gradient-to-br from-amber-100 to-yellow-50 border border-[#D3E4FD] shadow-md hover:shadow-xl transform hover:translate-y-[-5px]",
      }
    },
    defaultVariants: {
      variant: "default",
    }
  }
)

export interface CardProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {}

const Card = React.forwardRef<
  HTMLDivElement,
  CardProps
>(({ className, variant, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(cardVariants({ variant, className }))}
    {...props}
  />
))
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1 p-3 sm:p-4 md:p-6", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-lg font-semibold text-gray-900 leading-none tracking-tight font-unica",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-gray-600", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-3 sm:p-4 md:p-6 pt-0", className)} {...props} />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-3 sm:p-4 md:p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, cardVariants }
