/**
 * BankOne API Test Utility
 * 
 * This script helps test the BankOne API functions with various test cases
 * to ensure the error handling is working as expected.
 */

import * as BankOneApi from '../services/bankOneApi';

// Helper to print test results
const printResult = (testName: string, result: any) => {
  console.log(`\n----- ${testName} -----`);
  console.log(JSON.stringify(result, null, 2));
  console.log('-'.repeat(50));
};

// Test functions
const runTests = async () => {
  try {
    // 1. Test account verification with valid account
    const validAccountTest = await BankOneApi.verifyAccount('**********');
    printResult('Valid Account Verification', validAccountTest);

    // 2. Test account verification with invalid account
    const invalidAccountTest = await BankOneApi.verifyAccount('12345');
    printResult('Invalid Account Verification', invalidAccountTest);
    
    // 3. Test balance inquiry with valid account
    const validBalanceTest = await BankOneApi.getAccountBalance('**********');
    printResult('Valid Balance Inquiry', validBalanceTest);
    
    // 4. Test balance inquiry with invalid account
    const invalidBalanceTest = await BankOneApi.getAccountBalance('');
    printResult('Invalid Balance Inquiry', invalidBalanceTest);
    
    // 5. Test transaction history with valid params
    const validHistoryTest = await BankOneApi.getTransactionHistory(
      '**********',
      '2025-01-01',
      '2025-02-01'
    );
    printResult('Valid Transaction History', validHistoryTest);
    
    // 6. Test transaction history with invalid date format
    const invalidDateFormatTest = await BankOneApi.getTransactionHistory(
      '**********',
      '2025/01/01',  // Wrong format
      '2025-02-01'
    );
    printResult('Invalid Date Format', invalidDateFormatTest);
    
    // 7. Test transaction history with invalid date range
    const invalidDateRangeTest = await BankOneApi.getTransactionHistory(
      '**********',
      '2025-02-01',  // End date before start date
      '2025-01-01'
    );
    printResult('Invalid Date Range', invalidDateRangeTest);
    
    // 8. Test same bank transfer with invalid amount
    const invalidAmountTest = await BankOneApi.transferSameBank(
      '**********',
      '**********',
      0,  // Invalid amount
      'Test transaction'
    );
    printResult('Invalid Transfer Amount', invalidAmountTest);
    
    // 9. Test same bank transfer with missing account numbers
    const missingAccountTest = await BankOneApi.transferSameBank(
      '',
      '**********',
      100,
      'Test transaction'
    );
    printResult('Missing Account Number', missingAccountTest);
    
    // 10. Test get banks list
    const banksListTest = await BankOneApi.getBanksList();
    printResult('Get Banks List', banksListTest);

    console.log('\nAll tests completed!');
  } catch (error) {
    console.error('Error running tests:', error);
  }
};

// Export for running from another file
export { runTests };

// Self-executing when directly run
if (require.main === module) {
  runTests().catch(console.error);
}
