
.koja-card {
  position: relative;
  border-radius: 12px;
  background: linear-gradient(90deg, #1231b8 0%, #09125a 100%);
  color: white;
  padding: 20px;
  height: 180px;
  width: 100%;
  min-width: 280px;
  max-width: 340px;
  box-shadow: 0 6px 20px rgba(18, 49, 184, 0.12);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.koja-card.personal {
  background: #fde314;
  color: #222;
}

.koja-card.business {
  background: linear-gradient(90deg, #1231b8 0%, #09125a 100%);
  color: white;
}

.koja-card.virtual {
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.25) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.2);
}

.koja-card.virtual.personal {
  background: #fde314;
  color: #222;
}

.koja-card.virtual.business {
  background: linear-gradient(90deg, #1231b8 0%, #09125a 100%);
  color: white;
}

.koja-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px rgba(18, 49, 184, 0.2);
}

.koja-card::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url("/lovable-uploads/cacdd1f9-7979-45c9-b3ba-298996dc82cf.png");
  background-size: cover;
  background-position: center;
  opacity: 0.08;
  mix-blend-mode: overlay;
}

.card-wave {
  position: absolute;
  bottom: -20px;
  left: 0;
  width: 100%;
  height: 60px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 50% 50% 0 0;
  transform: scaleX(1.5);
}

.card-number {
  letter-spacing: 2px;
  font-size: 0.95rem;
  font-weight: 500;
}

.card-name-date {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
}

.card-chip {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 36px;
  height: 26px;
  border-radius: 4px;
  background: linear-gradient(120deg, #fde314 0%, #ffcb00 100%);
}

.koja-logo {
  position: absolute;
  top: 20px;
  left: 20px;
  height: 24px;
  width: auto;
  z-index: 1;
}

.card-network-logo {
  position: absolute;
  bottom: 20px;
  right: 20px;
  height: 24px;
  width: auto;
  z-index: 1;
}

.mastercard-logo {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
}

.mastercard-circle {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.mastercard-red {
  background-color: #eb001b;
  margin-right: -8px;
  position: relative;
  z-index: 1;
}

.mastercard-yellow {
  background-color: #fde314;
}

/* Card slide animation */
.card-slide-container {
  width: 100%;
  overflow: hidden;
  position: relative;
}

.card-slider {
  display: flex;
  transition: transform 0.3s ease;
}

.card-slide {
  flex: 0 0 100%;
}

.card-controls {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.card-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #ddd;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.card-dot.active {
  background-color: #1231b8;
  width: 8px;
  height: 8px;
}

.card-nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-nav-prev {
  left: 8px;
}

.card-nav-next {
  right: 8px;
}

/* Responsive breakpoints for cards */
@media (max-width: 1280px) {
  .koja-card {
    max-width: 320px;
  }
}

@media (max-width: 1024px) {
  .koja-card {
    max-width: 300px;
    height: 170px;
  }
}

@media (max-width: 768px) {
  .koja-card {
    max-width: 100%;
    height: 180px;
  }
}

@media (max-width: 640px) {
  .koja-card {
    height: 160px;
    padding: 14px;
    min-width: 250px;
  }
  
  .card-number {
    font-size: 0.8rem;
  }
  
  .card-chip {
    width: 30px;
    height: 22px;
  }
  
  .koja-logo,
  .card-network-logo {
    height: 20px;
  }
  
  .mastercard-circle {
    width: 16px;
    height: 16px;
  }
}

/* Prevent card shrinking */
.card-container {
  width: 100%;
  min-height: 180px;
}

@media (max-width: 480px) {
  .card-container {
    min-height: 160px;
  }
}
