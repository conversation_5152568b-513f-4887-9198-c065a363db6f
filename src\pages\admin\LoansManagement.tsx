import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  User,
  Banknote,
  Percent,
  Calendar,
  TrendingUp
} from 'lucide-react';

const LoansManagement = () => {
  const loans = [
    { 
      id: 'LN001', 
      borrower: '<PERSON>', 
      amount: '₦50,000',
      interestRate: '10%',
      loanTerm: '6 months',
      startDate: '2023-01-15',
      endDate: '2023-07-15',
      status: 'Active'
    },
    { 
      id: 'LN002', 
      borrower: 'Sarah Johnson', 
      amount: '₦120,000',
      interestRate: '12%',
      loanTerm: '12 months',
      startDate: '2022-11-01',
      endDate: '2023-11-01',
      status: 'Overdue'
    },
    { 
      id: 'LN003', 
      borrower: 'Michael Brown', 
      amount: '₦80,000',
      interestRate: '9%',
      loanTerm: '9 months',
      startDate: '2023-02-28',
      endDate: '2023-11-28',
      status: 'Pending'
    },
    { 
      id: 'LN004', 
      borrower: 'Alice Cooper', 
      amount: '₦250,000',
      interestRate: '11%',
      loanTerm: '18 months',
      startDate: '2022-09-10',
      endDate: '2024-03-10',
      status: 'Active'
    },
    { 
      id: 'LN005', 
      borrower: 'Robert Wilson', 
      amount: '₦60,000',
      interestRate: '8%',
      loanTerm: '6 months',
      startDate: '2023-03-01',
      endDate: '2023-09-01',
      status: 'Active'
    },
  ];

  return (
    <AdminLayout pageTitle="Loans Management">
      <Helmet>
        <title>Loans Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Loans</CardTitle>
              <CardDescription>Loans currently being repaid</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">234</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">New Applications</CardTitle>
              <CardDescription>Last 30 days</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">45</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Overdue Loans</CardTitle>
              <CardDescription>Loans past due date</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-red-500">12</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Value</CardTitle>
              <CardDescription>Outstanding loan value</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">₦15.8M</div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="active" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="active">Active Loans</TabsTrigger>
            <TabsTrigger value="pending">Pending Loans</TabsTrigger>
            <TabsTrigger value="closed">Closed Loans</TabsTrigger>
          </TabsList>
          
          <TabsContent value="active">
            <Card>
              <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
                <div>
                  <CardTitle className="text-2xl font-bold">Active Loans</CardTitle>
                  <CardDescription>Manage active loans and track repayments</CardDescription>
                </div>

                <div className="flex flex-col sm:flex-row gap-2">
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    <span className="hidden sm:inline">Filter</span>
                  </Button>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                    Add New Loan
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
                  <div className="relative w-full md:w-96">
                    <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                    <Input 
                      placeholder="Search loans..." 
                      className="pl-9"
                    />
                  </div>
                </div>

                <div className="rounded-md border overflow-hidden">
                  <Table>
                    <TableCaption>Active loans on the platform</TableCaption>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Loan ID</TableHead>
                        <TableHead>Borrower</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Interest Rate</TableHead>
                        <TableHead>Loan Term</TableHead>
                        <TableHead>Start Date</TableHead>
                        <TableHead>End Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {loans.map((loan) => (
                        <TableRow key={loan.id}>
                          <TableCell className="font-medium">{loan.id}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <User size={16} className="text-blue-600" />
                              {loan.borrower}
                            </div>
                          </TableCell>
                          <TableCell>{loan.amount}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Percent size={14} className="text-green-600" />
                              {loan.interestRate}
                            </div>
                          </TableCell>
                          <TableCell>{loan.loanTerm}</TableCell>
                          <TableCell>
                            <Calendar size={14} className="text-purple-600 mr-1" />
                            {loan.startDate}
                          </TableCell>
                          <TableCell>
                            <Calendar size={14} className="text-purple-600 mr-1" />
                            {loan.endDate}
                          </TableCell>
                          <TableCell>
                            <Badge variant={
                              loan.status === 'Active' ? 'outline' : 
                              loan.status === 'Overdue' ? 'destructive' : 
                              'secondary'
                            }>
                              {loan.status === 'Active' && <CheckCircle size={12} className="mr-1 text-green-600" />}
                              {loan.status === 'Overdue' && <AlertTriangle size={12} className="mr-1" />}
                              {loan.status === 'Pending' && <Clock size={12} className="mr-1" />}
                              {loan.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View Details</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem>
                                  <Edit className="mr-2 h-4 w-4" />
                                  <span>Edit Loan</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-red-600">
                                  <Trash className="mr-2 h-4 w-4" />
                                  <span>Remove Loan</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle>Pending Loan Applications</CardTitle>
                <CardDescription>Review and approve new loan applications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <Banknote size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">New Loan Requests</h3>
                  <p className="text-gray-500 mb-6">Review new loan applications and make approval decisions</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">Review Applications</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="closed">
            <Card>
              <CardHeader>
                <CardTitle>Closed Loans</CardTitle>
                <CardDescription>View history of closed and completed loans</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-8 text-center">
                  <TrendingUp size={64} className="mx-auto text-blue-500 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">Loan History</h3>
                  <p className="text-gray-500 mb-6">View details of closed and fully repaid loans</p>
                  <Button className="bg-[#1231B8] hover:bg-[#09125a]">View Loan History</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default LoansManagement;
