
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  Filter, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash,
  ShoppingBag,
  Tag,
  Package,
  Star,
  BarChart3,
  Check,
  AlertTriangle,
  X,
  StoreIcon
} from 'lucide-react';

const EcommerceManagement = () => {
  const products = [
    { 
      id: 'PRD001', 
      name: 'Premium Wireless Earbuds', 
      category: 'Electronics',
      store: 'TechGadgets',
      price: '₦25,000',
      stock: 45,
      status: 'Active',
      rating: 4.7,
      sales: 253
    },
    { 
      id: 'PRD002', 
      name: 'Organic Cotton T-Shirt', 
      category: 'Apparel',
      store: 'GreenFashion',
      price: '₦8,500',
      stock: 85,
      status: 'Active',
      rating: 4.2,
      sales: 175
    },
    { 
      id: 'PRD003', 
      name: 'Smart Home Speaker', 
      category: 'Electronics',
      store: 'HomeConnect',
      price: '₦35,000',
      stock: 0,
      status: 'Out of Stock',
      rating: 4.9,
      sales: 312
    },
    { 
      id: 'PRD004', 
      name: 'Handcrafted Leather Wallet', 
      category: 'Accessories',
      store: 'LeatherCraft',
      price: '₦12,500',
      stock: 28,
      status: 'Active',
      rating: 4.5,
      sales: 98
    },
    { 
      id: 'PRD005', 
      name: 'Natural Skin Care Set', 
      category: 'Beauty',
      store: 'OrganicBeauty',
      price: '₦18,000',
      stock: 12,
      status: 'Low Stock',
      rating: 4.8,
      sales: 127
    },
  ];

  return (
    <AdminLayout pageTitle="E-commerce Management">
      <Helmet>
        <title>E-commerce Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Products</CardTitle>
              <CardDescription>Products currently for sale</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">487</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Stores</CardTitle>
              <CardDescription>Active merchant stores</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">38</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Sales</CardTitle>
              <CardDescription>Month to date</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">₦8.2M</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Out of Stock</CardTitle>
              <CardDescription>Products needing restock</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">24</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Product Management</CardTitle>
              <CardDescription>Manage products and merchant stores on the platform</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                Add New Product
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search products..." 
                  className="pl-9"
                />
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <Tag size={12} />
                  <span>Electronics</span>
                  <X size={12} className="ml-1" />
                </Badge>
                <Badge variant="outline" className="flex items-center gap-1 cursor-pointer hover:bg-slate-100">
                  <Check size={12} />
                  <span>Active</span>
                  <X size={12} className="ml-1" />
                </Badge>
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>List of products on the platform</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product ID</TableHead>
                    <TableHead>Product Name</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Store</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Stock</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Sales</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {products.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">{product.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 rounded bg-blue-100 flex items-center justify-center">
                            <Package size={16} className="text-blue-600" />
                          </div>
                          {product.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {product.category}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <StoreIcon size={14} className="text-purple-600" />
                          {product.store}
                        </div>
                      </TableCell>
                      <TableCell>{product.price}</TableCell>
                      <TableCell>
                        {product.stock > 0 ? product.stock : "Out of stock"}
                      </TableCell>
                      <TableCell>
                        <Badge variant={
                          product.status === 'Active' ? 'outline' : 
                          product.status === 'Low Stock' ? 'secondary' : 
                          'destructive'
                        }>
                          {product.status === 'Active' && <Check size={12} className="mr-1" />}
                          {product.status === 'Low Stock' && <AlertTriangle size={12} className="mr-1" />}
                          {product.status === 'Out of Stock' && <X size={12} className="mr-1" />}
                          {product.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <BarChart3 size={14} className="text-green-600" />
                          {product.sales}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Edit Product</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Remove Product</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default EcommerceManagement;
