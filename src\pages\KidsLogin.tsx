
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { KidsLoginForm } from '@/modules/kids';

const KidsLogin = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FDE314]/20 via-blue-50 to-[#1231B8]/20 p-4 py-8">
      <Helmet>
        <title>Kids Login | KojaPay</title>
      </Helmet>
      
      <div className="container mx-auto max-w-md">
        <div className="text-center mb-8">
          <Link to="/" className="inline-flex items-center justify-center mb-6">
            <img 
              src="/lovable-uploads/037922b1-235b-42f7-86e4-7a09b957d9ee.png" 
              alt="KojaPay Logo"
              className="h-16 w-16"
            />
          </Link>
          <h1 className="text-3xl font-bold text-[#1231B8] mb-2">Kids Login</h1>
          <p className="text-gray-600">
            Welcome to your safe banking space!
          </p>
        </div>

        <KidsLoginForm />
        
        <div className="text-center mt-6">
          <p className="text-gray-600 text-sm">
            Need help? Ask your parent or guardian to visit{' '}
            <Link to="/parent-control" className="text-[#1231B8] font-medium hover:underline">
              Parental Controls
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

export default KidsLogin;
