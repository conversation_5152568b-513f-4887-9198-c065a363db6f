
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Search, 
  MoreHorizontal, 
  PiggyBank, 
  Coins, 
  Eye, 
  Edit, 
  Trash,
  Plus,
  Filter,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Wallet,
  LockIcon
} from 'lucide-react';

const SavingsManagement = () => {
  const savingsPlans = [
    { 
      id: 'SP001', 
      name: 'KojaSave Regular', 
      type: 'Regular Savings',
      users: 125,
      totalSavings: '₦5,500,000',
      interestRate: '10% p.a.',
      startDate: '2023-01-15',
      endDate: 'N/A',
      status: 'Active'
    },
    { 
      id: 'SP002', 
      name: 'KojaTarget Save', 
      type: 'Target Savings',
      users: 87,
      totalSavings: '₦3,250,000',
      interestRate: '12% p.a.',
      startDate: '2023-02-20',
      endDate: '2023-12-20',
      status: 'Active'
    },
    { 
      id: 'SP003', 
      name: 'KojaLock Save', 
      type: 'Fixed Savings',
      users: 53,
      totalSavings: '₦2,100,000',
      interestRate: '15% p.a.',
      startDate: '2023-03-05',
      endDate: '2024-03-05',
      status: 'Completed'
    },
    { 
      id: 'SP004', 
      name: 'KojaFlex Save', 
      type: 'Flexible Savings',
      users: 112,
      totalSavings: '₦4,800,000',
      interestRate: '8% p.a.',
      startDate: '2023-04-12',
      endDate: 'N/A',
      status: 'Active'
    },
    { 
      id: 'SP005', 
      name: 'KojaEdu Save', 
      type: 'Education Savings',
      users: 68,
      totalSavings: '₦2,750,000',
      interestRate: '11% p.a.',
      startDate: '2023-05-08',
      endDate: '2025-05-08',
      status: 'Active'
    },
  ];

  return (
    <AdminLayout pageTitle="Savings Management">
      <Helmet>
        <title>Savings Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Savings Plans</CardTitle>
              <CardDescription>Active savings plans</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">5</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Savers</CardTitle>
              <CardDescription>Users with savings accounts</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">435</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Savings</CardTitle>
              <CardDescription>Amount saved on the platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">₦18.4M</div>
            </CardContent>
          </Card>
          
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Average Interest</CardTitle>
              <CardDescription>Average interest rate</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">12.5%</div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Savings Management</CardTitle>
              <CardDescription>Manage savings plans and accounts on the platform</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                <Plus size={16} className="mr-2" />
                Create New Plan
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search savings plans..." 
                  className="pl-9"
                />
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>Savings plans and accounts on the platform</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan ID</TableHead>
                    <TableHead>Plan Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Total Savings</TableHead>
                    <TableHead>Interest Rate</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {savingsPlans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {plan.type === 'Regular Savings' && <Wallet size={16} className="text-blue-600" />}
                          {plan.type === 'Target Savings' && <PiggyBank size={16} className="text-green-600" />}
                          {plan.type === 'Fixed Savings' && <LockIcon size={16} className="text-amber-600" />}
                          {plan.type === 'Flexible Savings' && <Coins size={16} className="text-purple-600" />}
                          {plan.type === 'Education Savings' && <AlertTriangle size={16} className="text-red-600" />}
                          {plan.name}
                        </div>
                      </TableCell>
                      <TableCell>{plan.type}</TableCell>
                      <TableCell>{plan.users}</TableCell>
                      <TableCell>{plan.totalSavings}</TableCell>
                      <TableCell>{plan.interestRate}</TableCell>
                      <TableCell>{plan.startDate}</TableCell>
                      <TableCell>{plan.endDate}</TableCell>
                      <TableCell>
                        <Badge variant={
                          plan.status === 'Active' ? 'outline' : 
                          plan.status === 'Completed' ? 'outline' : 
                          'secondary'
                        }>
                          {plan.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                          {plan.status === 'Completed' && <CheckCircle size={12} className="mr-1" />}
                          {plan.status === 'Pending' && <Clock size={12} className="mr-1" />}
                          {plan.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Edit Plan</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Remove Plan</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default SavingsManagement;
