
import React from 'react';
import { Helmet } from 'react-helmet-async';
import AdminLayout from '@/components/AdminLayout';
import { 
  Table, 
  TableBody, 
  TableCaption, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Search,
  MoreHorizontal,
  PiggyBank,
  Coins,
  Eye,
  Edit,
  Trash,
  Plus,
  Filter,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Wallet,
  LockIcon,
  Users
} from 'lucide-react';

const SavingsManagement = () => {
  const savingsPlans = [
    {
      id: 'SP001',
      name: 'Dream Vacation Fund',
      type: 'Personal Plan',
      creator: '<PERSON>',
      users: 8, // contributors
      totalSavings: '₦450,000',
      targetAmount: '₦500,000',
      startDate: '2023-01-15',
      endDate: '2023-12-31',
      status: 'Active',
      isPublic: true
    },
    {
      id: 'SP002',
      name: 'New Car Fund',
      type: 'Personal Plan',
      creator: 'Jane Smith',
      users: 12,
      totalSavings: '₦1,250,000',
      targetAmount: '₦2,000,000',
      startDate: '2023-02-20',
      endDate: '2024-06-20',
      status: 'Active',
      isPublic: true
    },
    {
      id: 'SP003',
      name: 'Emergency Fund',
      type: 'Personal Plan',
      creator: 'Mike Johnson',
      users: 3,
      totalSavings: '₦800,000',
      targetAmount: '₦1,000,000',
      startDate: '2023-03-05',
      endDate: 'N/A',
      status: 'Active',
      isPublic: false
    },
    {
      id: 'GS001',
      name: 'Office Fitness Challenge',
      type: 'Group Savings',
      creator: 'Sarah Wilson',
      users: 25, // group members
      totalSavings: '₦1,500,000',
      targetAmount: '₦100,000', // individual target
      startDate: '2023-04-12',
      endDate: '2023-10-12',
      status: 'Active',
      isPublic: true
    },
    {
      id: 'GS002',
      name: 'Family Vacation Fund',
      type: 'Group Savings',
      creator: 'David Brown',
      users: 8,
      totalSavings: '₦600,000',
      targetAmount: '₦75,000',
      startDate: '2023-05-08',
      endDate: '2024-01-08',
      status: 'Active',
      isPublic: false
    },
  ];

  return (
    <AdminLayout pageTitle="Savings Management">
      <Helmet>
        <title>Savings Management | KojaPay Admin</title>
      </Helmet>

      <div className="grid gap-6">
        <div className="flex flex-col md:flex-row gap-4">
          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Personal Plans</CardTitle>
              <CardDescription>GoFundMe-style savings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-500">3</div>
              <p className="text-sm text-muted-foreground">₦2.5M total</p>
            </CardContent>
          </Card>

          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Group Savings</CardTitle>
              <CardDescription>Individual target groups</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-500">2</div>
              <p className="text-sm text-muted-foreground">33 members</p>
            </CardContent>
          </Card>

          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Total Contributions</CardTitle>
              <CardDescription>Cross-plan contributions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-500">₦4.6M</div>
              <p className="text-sm text-muted-foreground">56 contributors</p>
            </CardContent>
          </Card>

          <Card className="w-full md:w-1/4">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg font-semibold">Active Users</CardTitle>
              <CardDescription>Users with active savings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-amber-500">89</div>
              <p className="text-sm text-muted-foreground">+12% this month</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader className="flex flex-col md:flex-row md:items-center md:justify-between space-y-2 md:space-y-0">
            <div>
              <CardTitle className="text-2xl font-bold">Savings Management</CardTitle>
              <CardDescription>Manage savings plans and accounts on the platform</CardDescription>
            </div>

            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline" className="flex items-center gap-2">
                <Filter size={16} />
                <span className="hidden sm:inline">Filter</span>
              </Button>
              <Button className="bg-[#1231B8] hover:bg-[#09125a]">
                <Plus size={16} className="mr-2" />
                Create New Plan
              </Button>
            </div>
          </CardHeader>
          
          <CardContent>
            <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
              <div className="relative w-full md:w-96">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input 
                  placeholder="Search savings plans..." 
                  className="pl-9"
                />
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableCaption>Savings plans and accounts on the platform</TableCaption>
                <TableHeader>
                  <TableRow>
                    <TableHead>Plan ID</TableHead>
                    <TableHead>Plan Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Creator</TableHead>
                    <TableHead>Contributors/Members</TableHead>
                    <TableHead>Current Amount</TableHead>
                    <TableHead>Target Amount</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {savingsPlans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell className="font-medium">{plan.id}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {plan.type === 'Personal Plan' && <PiggyBank size={16} className="text-purple-600" />}
                          {plan.type === 'Group Savings' && <Users size={16} className="text-blue-600" />}
                          <div>
                            <div className="font-medium">{plan.name}</div>
                            {!plan.isPublic && <span className="text-xs text-gray-500">Private</span>}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={plan.type === 'Personal Plan' ? 'default' : 'secondary'}>
                          {plan.type}
                        </Badge>
                      </TableCell>
                      <TableCell>{plan.creator}</TableCell>
                      <TableCell>
                        <div className="text-center">
                          <div className="font-medium">{plan.users}</div>
                          <div className="text-xs text-gray-500">
                            {plan.type === 'Personal Plan' ? 'contributors' : 'members'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">{plan.totalSavings}</TableCell>
                      <TableCell>
                        {plan.type === 'Personal Plan' ? plan.targetAmount : `₦${plan.targetAmount} each`}
                      </TableCell>
                      <TableCell>{plan.startDate}</TableCell>
                      <TableCell>
                        <Badge variant={
                          plan.status === 'Active' ? 'outline' : 
                          plan.status === 'Completed' ? 'outline' : 
                          'secondary'
                        }>
                          {plan.status === 'Active' && <CheckCircle size={12} className="mr-1" />}
                          {plan.status === 'Completed' && <CheckCircle size={12} className="mr-1" />}
                          {plan.status === 'Pending' && <Clock size={12} className="mr-1" />}
                          {plan.status}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              <span>View Details</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              <span>Edit Plan</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash className="mr-2 h-4 w-4" />
                              <span>Remove Plan</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default SavingsManagement;
