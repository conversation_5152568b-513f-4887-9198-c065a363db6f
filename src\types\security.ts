export type SecurityEventSeverity = 'low' | 'medium' | 'high' | 'critical';
export type SecurityEventStatus = 'recorded' | 'reviewed' | 'resolved' | 'ignored';
export type FraudAlertStatus = 'new' | 'reviewing' | 'resolved' | 'false_positive';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type FraudAction = 'allow' | 'block' | 'review';

export interface SecurityNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'access' | 'transaction' | 'security' | 'system' | 'login';
  severity: SecurityEventSeverity;
  read: boolean;
  actionRequired: boolean;
  actionLink?: string;
  createdAt: string;
  metadata?: Record<string, any>;
}

export interface SecurityEvent {
  id: string;
  userId: string;
  eventType: string;
  timestamp: Date;
  severity: SecurityEventSeverity;
  details: Record<string, any>;
  ipAddress: string;
  deviceInfo: string;
  status: SecurityEventStatus;
}

export interface FraudAlert {
  id: string;
  userId: string;
  transactionId: string;
  description: string;
  riskScore: number;
  status: FraudAlertStatus;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

export interface RiskAssessment {
  userId: string;
  overallRisk: RiskLevel;
  factors: string[];
  lastUpdated: string;
}

export interface SecuritySettings {
  userId: string;
  twoFactorAuth: boolean;
  biometricAuthentication: boolean;
  locationTracking: boolean;
  transactionPin: boolean;
  notificationPreferences: Record<string, boolean>;
  securityQuestions: SecurityQuestion[];
  trustedDevices: SecurityDevice[];
}

export interface SecurityQuestion {
  id: string;
  question: string;
  answer: string;
}

export interface SecurityDevice {
  id: string;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  lastLogin: string;
  location: string;
  isTrusted: boolean;
}

export interface Device {
  deviceId: string;
  ipAddress: string;
  location: {
    latitude: number;
    longitude: number;
    country: string;
  };
  userAgent: string;
  os: string;
  browser: string;
  isMobile: boolean;
}
