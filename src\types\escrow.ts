
export interface EscrowParticipant {
  name: string;
  email: string;
}

export type EscrowStatus = 'active' | 'completed' | 'cancelled' | 'disputed' | 'refunded' | 'released' | 'pending';

export interface Escrow {
  id: string;
  orderId: string;
  title?: string;
  description?: string;
  amount: number;
  currency: string;
  buyerId: string;
  sellerId: string;
  buyer?: EscrowParticipant;
  seller?: EscrowParticipant;
  status: EscrowStatus;
  createdAt: string;
  updatedAt: string;
  dispute?: Dispute;
}

export type DisputeStatus = 'pending' | 'resolved' | 'in_review' | 'reviewing' | 'escalated' | 'resolved_buyer' | 'resolved_seller' | 'closed';

export type DisputeSender = 'buyer' | 'seller' | 'admin';

export interface DisputeMessage {
  id: string;
  disputeId: string;
  sender: DisputeSender;
  message: string;
  content?: string; // Added for backward compatibility
  createdAt: string;
  timestamp?: string; // Added for backward compatibility
}

export interface Dispute {
  id: string;
  escrowId: string;
  orderId?: string;
  title?: string;
  reason: string;
  details?: string; // Making details optional to accommodate different implementations
  description?: string; // Added for backward compatibility
  status: DisputeStatus;
  resolution?: string;
  sender?: DisputeSender;
  createdBy?: DisputeSender;
  createdAt: string;
  updatedAt: string;
  messages?: DisputeMessage[];
}
